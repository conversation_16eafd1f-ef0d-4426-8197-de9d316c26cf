# 编辑项目视图幂等性优化实现

## 概述

为了防止用户连续点击生成按钮和保存按钮导致重复发送接口请求，我们实现了幂等性优化机制。该机制包含两个层面的保护：

1. **服务端幂等性控制**：通过 `uniqueId` 机制确保相同操作不会重复执行
2. **客户端防重复点击**：通过锁机制防止短时间内的重复请求

## 实现方案

### 1. 服务端幂等性控制

#### 1.1 新增获取唯一ID接口

**接口地址**：`/api/project/getUniqueId`

**请求参数**：无

**响应数据**：
```typescript
interface GetUniqueIdResponse {
  uniqueId: string;
}
```

#### 1.2 修改现有接口参数

为以下两个接口添加 `uniqueIdForModify` 参数：

- `/api/project/update` (保存项目)
- `/api/project/submit` (生成预览)

**参数结构**：
```typescript
interface UpdateProjectParams {
  // ... 其他现有参数
  /** 幂等性唯一标识，用于防重复提交 */
  uniqueIdForModify?: string;
}
```

#### 1.3 接口调用流程

```mermaid
sequenceDiagram
    participant Client as 客户端
    participant API as API服务
    
    Client->>API: 1. 调用 /api/project/getUniqueId
    API-->>Client: 2. 返回 uniqueId
    Client->>API: 3. 调用 /api/project/update 或 /api/project/submit
    Note over Client,API: 携带 uniqueIdForModify 参数
    API-->>Client: 4. 返回操作结果
```

### 2. 客户端防重复点击

#### 2.1 锁机制实现

在 `useFirstStep.ts` 中添加防重复点击锁：

```typescript
// 防重复点击锁
let saveProjectLock = false;
let generatePreviewLock = false;
```

#### 2.2 保存项目防重复逻辑

```typescript
const saveProject = async (...) => {
  // 防重复点击检查
  if (saveProjectLock) {
    console.log('🚀 ADI-LOG ~ 保存项目操作正在进行中，忽略重复点击');
    return;
  }
  saveProjectLock = true;

  try {
    // 执行保存逻辑
    // ...
  } finally {
    // 确保在所有情况下都重置锁
    saveProjectLock = false;
  }
};
```

#### 2.3 生成预览防重复逻辑

```typescript
const generateProjectPreview = async (...) => {
  // 防重复点击检查
  if (generatePreviewLock) {
    console.log('🚀 ADI-LOG ~ 生成预览操作正在进行中，忽略重复点击');
    return;
  }
  generatePreviewLock = true;

  try {
    // 执行生成逻辑
    // ...
  } finally {
    // 确保在所有情况下都重置锁
    generatePreviewLock = false;
  }
};
```

## 修改的文件列表

### 1. 类型定义文件

- `src/api/EditProjectView/types/request.ts`
  - 为 `UpdateProjectParams` 添加 `uniqueIdForModify` 字段
  - 新增 `GetUniqueIdParams` 接口

- `src/api/EditProjectView/types/response.ts`
  - 新增 `GetUniqueIdResponse` 接口

### 2. API接口文件

- `src/api/EditProjectView/index.ts`
  - 新增 `getUniqueId()` 函数
  - 修改 `saveProjectData()` 函数，添加获取uniqueId逻辑
  - 修改 `generatePreview()` 函数，添加获取uniqueId逻辑

### 3. 数据转换文件

- `src/api/EditProjectView/utils/outputDataTransform.ts`
  - 修改 `transformProjectDataToEditProjectApiFormat()` 函数
  - 支持传入 `uniqueId` 参数并添加到请求数据中

### 4. 业务逻辑文件

- `src/views/EditProjectView/composables/useFirstStep.ts`
  - 添加防重复点击锁变量
  - 在 `saveProject()` 方法中添加锁机制
  - 在 `generateProjectPreview()` 方法中添加锁机制

## 优化效果

### 1. 防重复提交

- **服务端**：通过 `uniqueId` 确保相同操作的幂等性
- **客户端**：通过锁机制防止短时间内的重复请求

### 2. 用户体验提升

- 避免因网络延迟导致的重复点击问题
- 减少不必要的服务器请求
- 提供更稳定的操作体验

### 3. 系统稳定性

- 降低服务器负载
- 避免数据不一致问题
- 提高系统整体可靠性

## 注意事项

1. **错误处理**：如果获取 `uniqueId` 失败，系统会记录警告但不会阻止操作继续进行
2. **锁的重置**：确保在所有情况下（成功、失败、异常）都正确重置锁状态
3. **向后兼容**：`uniqueIdForModify` 参数为可选，不影响现有功能
4. **调试信息**：添加了详细的日志输出，便于问题排查

## 测试建议

1. **功能测试**：验证正常的保存和生成功能
2. **重复点击测试**：快速连续点击按钮，验证防重复机制
3. **网络异常测试**：模拟网络延迟和错误情况
4. **边界条件测试**：测试各种异常情况下的锁重置
