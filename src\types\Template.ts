import { PROJECT_TYPE } from '@/constants/project';
import { ScriptSegment } from '@/types';

/** 模板类 */
export interface Template {
  /** 模板id */
  id: string;
  /** 模板名称 */
  name: string;
  /** 模板类型 */
  type: PROJECT_TYPE;
  /** 行业分类 */
  industry: number;
  /** 场景分类 */
  scene: number;
  /** 封面图 */
  cover: {
    /** 资源id */
    resId: string;
    /** 资源类型 */
    resType: number;
  };
  /** 生成的视频/图片资源 */
  setting?: {
    /** 图文 */
    imgList?: {
      /** 图片资源id */
      resId: string;
      /** 图片资源类型 */
      resType: number;
    }[];
    /** 视频 */
    video?: {
      /** 视频资源id */
      videoId: string;
      /** 视频资源类型 */
      videoType: number;
    };
  };
  /** 文案内容 */
  script: {
    /** 标题，仅当类型为图文时存在 */
    title?: string;
    /** 文案内容，仅当类型为图文时存在 */
    content?: string;
    /** 脚本，仅当类型为视频时存在 */
    segments?: ScriptSegment[];
  };
  /** 脚本列表 */
  scriptList?: string[];
}
