# 差异化表单校验功能实现说明

## 📋 功能概述

在 FirstStep 组件中实现了差异化表单校验功能，支持两种校验模式：

- **生成模式**：完整校验（必填校验 + 最小长度校验 + 最大长度校验）
- **保存模式**：仅最大长度校验（跳过必填校验和最小长度校验）

## 🎯 实现方案

### 1. 校验模式管理

在 `useForm.ts` 中添加了校验模式枚举和状态管理：

```typescript
// 校验模式枚举
const VALIDATION_MODE = {
  GENERATE: 'generate', // 生成模式：完整校验
  SAVE: 'save', // 保存模式：仅最大长度校验
} as const;

// 当前校验模式
let currentValidationMode: string = VALIDATION_MODE.GENERATE;

// 设置校验模式
const setValidationMode = (mode: string): void => {
  currentValidationMode = mode;
};
```

### 2. 校验规则修改

#### 必填校验规则

```typescript
const createRequiredRule = (
  _label: string,
  _componentType: string,
): FormRule => {
  return {
    validator: (
      _rule: FormRule,
      value: unknown,
      callback: (error?: Error) => void,
    ) => {
      // 保存模式跳过必填校验
      if (currentValidationMode === VALIDATION_MODE.SAVE) {
        console.log('保存模式：跳过必填校验');
        return callback();
      }

      // 生成模式进行必填校验
      const isEmpty = Array.isArray(value)
        ? value.length === 0
        : !value || String(value).trim() === '';

      if (isEmpty) {
        callback(new Error('输入不能为空'));
      } else {
        callback();
      }
    },
  };
};
```

#### 文件必填校验规则

```typescript
// 在 createFileFormItem 函数中
if (item.required) {
  formItem.rules.push({
    validator: (
      _rule: FormRule,
      value: unknown,
      callback: (error?: Error) => void,
    ) => {
      // 保存模式跳过文件必填校验
      if (currentValidationMode === VALIDATION_MODE.SAVE) {
        console.log('保存模式：跳过文件必填校验');
        return callback();
      }

      // 检查文件列表是否为空
      const isEmpty = !value || (Array.isArray(value) && value.length === 0);

      if (isEmpty) {
        // 根据媒体类型显示不同的错误信息
        const errorMessage =
          mediaType === MEDIA_TYPES.VIDEO
            ? '视频素材总时长需超过15s，建议上传多段视频素材'
            : '请上传相关素材';
        callback(new Error(errorMessage));
      } else {
        callback();
      }
    },
  });
}
```

#### 长度校验规则

```typescript
const createLengthRule = (
  item: InputFormItem,
  componentType: string,
): FormRule => {
  return {
    validator: (
      _rule: FormRule,
      value: unknown,
      callback: (error?: Error) => void,
    ) => {
      // 保存模式下跳过最小长度校验
      const shouldCheckMinLength =
        currentValidationMode !== VALIDATION_MODE.SAVE;

      if (shouldCheckMinLength && minLength && length < minLength) {
        callback(new Error(`${label}不能少于${minLength}个字符`));
      } else if (maxLength && length > maxLength) {
        callback(new Error(`输入超出${maxLength}个字符限制`));
      } else {
        callback();
      }
    },
  };
};
```

### 3. 差异化校验方法

#### 生成模式校验

```typescript
const validateAllForms = (
  dynamicForm: DynamicFormRef | null,
  dynamicFormFiles: DynamicFileFormInstance | null,
): Promise<ValidationResult> => {
  // 设置为生成模式
  setValidationMode(VALIDATION_MODE.GENERATE);
  return performValidation(dynamicForm, dynamicFormFiles);
};
```

#### 保存模式校验

```typescript
const validateAllFormsForSave = (
  dynamicForm: DynamicFormRef | null,
  dynamicFormFiles: DynamicFileFormInstance | null,
): Promise<ValidationResult> => {
  // 设置为保存模式
  setValidationMode(VALIDATION_MODE.SAVE);
  return performValidation(dynamicForm, dynamicFormFiles);
};
```

### 4. 组件集成

#### FirstStep 组件

```typescript
// 验证所有表单的方法（保存模式：仅最大长度校验）
const validateAllFormsForSaveWithRef = async (): Promise<ValidationResult> => {
  if (!dynamicForm.value || !dynamicFormFiles.value) {
    return { valid: false, data: {} };
  }

  // 先清除校验错误信息
  dynamicForm.value.clearValidate();
  dynamicFormFiles.value.clearValidate();

  return validateAllFormsForSave(dynamicForm.value, dynamicFormFiles.value);
};

// 对外暴露方法
return {
  validateAllForms: validateAllFormsWithRef,
  validateAllFormsForSave: validateAllFormsForSaveWithRef,
  // ... 其他方法
};
```

#### useFirstStep 保存方法

```typescript
const saveProject = async (
  formComponent: FirstStepComponentShape,
  projectId?: number,
  generateNum?: number,
): Promise<void> => {
  // 使用保存模式校验（仅最大长度校验）
  const validationResult: ValidationResult =
    await formComponent.validateAllFormsForSave();

  if (!validationResult.valid) {
    message.error(validationResult?.message || '表单验证失败');
    return;
  }

  // 继续保存逻辑...
};
```

## 🔧 使用方式

### 生成按钮

点击生成按钮时，会调用 `validateAllForms` 方法，进行完整校验：

- ✅ 基础表单必填校验
- ✅ 文件上传必填校验
- ✅ 最小长度校验
- ✅ 最大长度校验

### 保存按钮

点击保存按钮时，会调用 `validateAllFormsForSave` 方法，仅进行最大长度校验：

- ❌ 跳过基础表单必填校验
- ❌ 跳过文件上传必填校验
- ❌ 跳过最小长度校验
- ✅ 最大长度校验

## 📁 涉及文件

### 主要修改文件

- `src/views/EditProjectView/pages/FirstStep/composables/useForm.ts` - 校验规则重构
- `src/views/EditProjectView/pages/FirstStep/index.vue` - 新增保存模式校验方法
- `src/views/EditProjectView/composables/useFirstStep.ts` - 保存方法使用差异化校验
- `src/views/EditProjectView/types/components.ts` - 类型定义更新

### 按钮位置

- **保存按钮**：`src/views/EditProjectView/components/ProjectHeader/index.vue`
- **生成按钮**：`src/views/EditProjectView/components/ProjectFooter/index.vue`

## 🔄 向后兼容性

- ✅ 保持了现有 API 的完全兼容性
- ✅ 不影响现有的表单校验功能
- ✅ 生成按钮的校验逻辑保持不变
- ✅ 新增的保存模式校验不影响其他功能

## 🧪 测试验证

可以通过以下方式验证功能：

1. **基础表单必填字段为空时**：

   - 点击生成按钮：显示"请完善基础信息必填项"
   - 点击保存按钮：跳过必填校验，直接保存

2. **文件上传必填字段为空时**：

   - 点击生成按钮：显示"请完善文件上传必填项"
   - 点击保存按钮：跳过文件必填校验，直接保存

3. **字段长度不足时**：

   - 点击生成按钮：显示"不能少于 X 个字符"
   - 点击保存按钮：跳过最小长度校验，直接保存

4. **字段长度超限时**：
   - 点击生成按钮：显示"输入超出 X 个字符限制"
   - 点击保存按钮：同样显示"输入超出 X 个字符限制"

## 📝 注意事项

1. 保存模式校验会先调用 `clearValidate()` 清除之前的校验错误信息
2. 校验模式是全局状态，在校验开始时会自动设置
3. 错误提示信息在保存模式下会更加友好（如"请检查基础信息输入格式"）
4. 保存按钮位于页面头部，生成按钮位于页面底部
