<template>
  <div class="create-card" @click="handleCreate">
    <div class="create-content">
      <div class="create-icon"></div>
      <span class="create-text">开始创作</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { defineEmits } from 'vue';

const emit = defineEmits(['create']);

const handleCreate = () => {
  emit('create');
};
</script>

<style lang="scss" scoped>
.create-card {
  @apply h-[313px] cursor-pointer border border-dashed border-edge rounded-[12px] transition-all duration-300 hover:(border-[#B4C5FF] bg-[#FBFBFF]);
  .create-content {
    @apply h-full flex flex-col justify-center items-center;
  }
  .create-icon {
    @apply w-[48px] h-[48px] mb-[16px];
    background-image: url(@/assets/ProjectView/createIcon.svg);
  }
  .create-text {
    @apply text-[16px] text-[#BFBFBF];
  }
  &:hover {
    .create-icon {
      background-image: url(@/assets/ProjectView/createIcon-hover.svg);
    }
    .create-text {
      @apply text-assist;
    }
  }
}
</style>
