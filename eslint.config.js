// eslint.config.js
import globals from 'globals';
import pluginJs from '@eslint/js';
import tsParser from '@typescript-eslint/parser';
import tsPlugin from '@typescript-eslint/eslint-plugin';
import vueParser from 'vue-eslint-parser';

export default [
  // 全局忽略配置
  {
    ignores: [
      'dist',
      'node_modules',
      '.husky',
      '*.log*',
      '*.lock',
      'components.d.ts',
      'test-results',
      'coverage',
    ],
  },

  // JS 推荐配置
  pluginJs.configs.recommended,

  // 基础配置
  {
    files: ['**/*.{js,jsx,mjs,cjs,ts,tsx}'], // 应用于广泛的文件类型
    plugins: {
      // 注册 TS 插件
      '@typescript-eslint': tsPlugin,
    },
    rules: {
      ...tsPlugin.configs.recommended.rules,
      'quotes': ['error', 'single', { allowTemplateLiterals: true }],
      'semi': ['error', 'always'],
      '@typescript-eslint/ban-ts-comment': [
        'error',
        {
          'ts-check': 'allow-with-description',
          'ts-expect-error': 'allow-with-description',
          'ts-ignore': 'allow-with-description',
          'ts-nocheck': 'allow-with-description',
          'minimumDescriptionLength': 5,
        },
      ],
      // 禁止使用 any 类型
      '@typescript-eslint/no-explicit-any': 'error',
      // 模拟 TypeScript 豁免以 _ 开头的名称的风格
      '@typescript-eslint/no-unused-vars': [
        'error',
        {
          args: 'all',
          argsIgnorePattern: '^_',
          caughtErrors: 'all',
          caughtErrorsIgnorePattern: '^_',
          destructuredArrayIgnorePattern: '^_',
          varsIgnorePattern: '^_',
          ignoreRestSiblings: true,
        },
      ],
    },
  },

  // TypeScript 配置
  {
    files: ['**/*.{ts,tsx,mts,cts}'],
    languageOptions: {
      parser: tsParser,
      parserOptions: {
        project: true,
        tsconfigRootDir: import.meta.dirname,
      },
    },
  },

  // vite.config.ts规则覆盖
  {
    files: [
      'vite.config.ts',
      'vitest.config.ts',
      'uno.config.ts',
      'scripts/**/*.ts',
    ],
    languageOptions: {
      parserOptions: {
        project: ['tsconfig.node.json'],
        tsconfigRootDir: import.meta.dirname, // 确保路径正确
      },
      globals: {
        ...globals.node,
      },
    },
    rules: {
      '@typescript-eslint/no-unsafe-assignment': 'off',
      '@typescript-eslint/no-unsafe-call': 'off',
      '@typescript-eslint/no-unsafe-member-access': 'off',
      '@typescript-eslint/no-unsafe-return': 'off',
      '@typescript-eslint/no-var-requires': 'off',
    },
  },

  // 全局环境变量定义
  {
    languageOptions: {
      globals: {
        ...globals.browser,
        // Vue 编译宏
        defineProps: 'readonly',
        defineEmits: 'readonly',
        defineExpose: 'readonly',
        withDefaults: 'readonly',
      },
    },
  },
  // 测试文件配置
  {
    files: [
      '**/*.test.ts',
      '**/*.spec.ts',
      '**/test/**/*.ts',
      '**/tests/**/*.ts',
    ],
    languageOptions: {
      globals: {
        ...globals.node,
        vi: 'readonly',
        describe: 'readonly',
        it: 'readonly',
        expect: 'readonly',
        beforeEach: 'readonly',
        afterEach: 'readonly',
        beforeAll: 'readonly',
        afterAll: 'readonly',
      },
    },
    rules: {
      '@typescript-eslint/no-explicit-any': 'off', // 测试文件中允许使用 any
      'no-undef': 'off', // 测试环境中的全局变量
    },
  },

  // Vue 配置
  {
    files: ['**/*.vue', '*.vue'],
    plugins: {
      // 注册 TS 插件
      '@typescript-eslint': tsPlugin,
    },
    languageOptions: {
      parser: vueParser,
      sourceType: 'module',
      ecmaVersion: 'latest',
      parserOptions: {
        parser: {
          // Script parser for `<script>`
          'js': 'espree',
          // Script parser for `<script lang="ts">`
          'ts': '@typescript-eslint/parser',
          '<template>': 'espree',
        },
        extraFileExtensions: ['.vue'],
      },
    },
    rules: {
      ...tsPlugin.configs.recommended.rules,
      'quotes': ['error', 'single', { allowTemplateLiterals: true }],
      'semi': ['error', 'always'],
      'no-unused-vars': ['off'],
      '@typescript-eslint/ban-ts-comment': [
        'error',
        {
          'ts-check': 'allow-with-description',
          'ts-expect-error': 'allow-with-description',
          'ts-ignore': 'allow-with-description',
          'ts-nocheck': 'allow-with-description',
          'minimumDescriptionLength': 5,
        },
      ],
      // 禁止使用 any 类型
      '@typescript-eslint/no-explicit-any': 'error',
      // 模拟 TypeScript 豁免以 _ 开头的名称的风格
      '@typescript-eslint/no-unused-vars': [
        'error',
        {
          args: 'all',
          argsIgnorePattern: '^_',
          caughtErrors: 'all',
          caughtErrorsIgnorePattern: '^_',
          destructuredArrayIgnorePattern: '^_',
          varsIgnorePattern: '^_',
          ignoreRestSiblings: true,
        },
      ],
    },
  },
];
