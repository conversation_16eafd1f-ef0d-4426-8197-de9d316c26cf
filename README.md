# 速创开发指南

## 环境配置

执行`pnpm dev`后，会自动在根目录创建一个 `.env.development.local` 文件。该文件的内容从 `.env.development` 文件中复制。

`.env.development.local` 文件会覆盖 `.env.development` 中的配置，确保其仅用于开发环境。启动项目后，如需修改环境，直接修改 `.env.development.local` 文件即可，vite 会自动重启项目。

## 文档目录

- [API 文档](/src/api/README.md)
- [Mock 接口文档](/src/mock/README.md)
- [ICONS 文档](/src/components/comm/docs/README-ICON.md)
- [UNOCSS 文档](/src/style/docs/README-unocss.md)
- [图片引用说明](/src/assets/README-IMG.md)
- [工具方法文档](/src/utils/README.md)
- [骨架屏文档](/src/hook/docs/README-SKELETON.md)
