import { computed, type Ref } from 'vue';
import type { VideoPlayerConfig } from '../types';
import { playerConfig as defaultConfig } from '../config/player.config';

/**
 * 播放器配置管理Hook
 * @description 统一管理播放器配置，支持动态配置和默认值合并
 */
export function usePlayerConfig(
  videoUrl: Ref<string>,
  posterUrl?: Ref<string>,
  customConfig: Partial<VideoPlayerConfig> = {}
) {
  /**
   * 合并后的播放器配置
   */
  const playerConfiguration = computed(() => {
    const baseConfig = {
      ...defaultConfig.player,
      ...customConfig,
    };

    // 动态配置源
    const sources = videoUrl.value ? [
      {
        src: videoUrl.value,
        type: 'video/mp4',
      },
    ] : [];

    // 动态配置封面图
    const poster = posterUrl?.value || customConfig.poster;

    return {
      ...baseConfig,
      sources,
      ...(poster && { poster }),
    };
  });

  /**
   * 获取VideoJS配置对象
   */
  const getVideoJSConfig = () => {
    return playerConfiguration.value;
  };

  /**
   * 检查配置是否有效
   */
  const isConfigValid = computed(() => {
    return Boolean(videoUrl.value && playerConfiguration.value.sources.length > 0);
  });

  /**
   * 获取特定配置项
   */
  const getConfigValue = <K extends keyof VideoPlayerConfig>(key: K): VideoPlayerConfig[K] => {
    return playerConfiguration.value[key];
  };

  /**
   * 更新配置项
   */
  const updateConfig = (updates: Partial<VideoPlayerConfig>) => {
    Object.assign(customConfig, updates);
  };

  return {
    playerConfiguration,
    isConfigValid,
    getVideoJSConfig,
    getConfigValue,
    updateConfig,
  };
}
