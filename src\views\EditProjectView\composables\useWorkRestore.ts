/**
 * @fileoverview 恢复旧作品功能的可复用 composable
 * @description 提供统一的恢复旧作品逻辑，支持直接调用和 eventBus 异步处理两种使用场景
 */

import { restoreOldWork } from '@/api/WorkView/index';
import { message } from '@fk/faicomponent';

/**
 * 恢复旧作品的参数接口
 */
export interface RestoreWorkParams {
  /** 关联作品ID（旧作品ID） */
  relWorkId: number;
}

/**
 * 恢复旧作品的选项配置
 */
export interface UseWorkRestoreOptions {
  /** 是否显示成功消息提示（默认：true） */
  showSuccessMessage?: boolean;
  /** 是否显示错误消息提示（默认：true） */
  showErrorMessage?: boolean;
  /** 自定义成功消息文本 */
  successMessage?: string;
  /** 自定义错误消息文本 */
  errorMessage?: string;
}

/**
 * 恢复旧作品的返回结果
 */
export type RestoreWorkResult = [Error | null, boolean];

/**
 * 恢复旧作品功能的可复用 composable
 *
 * @description 提供统一的恢复旧作品逻辑，包含：
 * 1. 参数验证
 * 2. API 调用
 * 3. 错误处理
 * 4. 成功/失败回调
 * 5. 消息提示
 *
 * @param options 配置选项
 * @returns 恢复旧作品的相关方法
 */
export function useWorkRestore(options: UseWorkRestoreOptions = {}) {
  const {
    showSuccessMessage = true,
    showErrorMessage = true,
    successMessage = '恢复旧作品成功',
    errorMessage = '恢复旧作品失败',
  } = options;

  /**
   * 统一的错误处理函数
   */
  const handleError = (error: Error, context: string) => {
    console.error(`[useWorkRestore] ${context}:`, error);

    if (showErrorMessage) {
      message.error(error.message || errorMessage);
    }

    return [error, false] as RestoreWorkResult;
  };

  /**
   * 执行恢复旧作品操作
   *
   * @param params 恢复参数
   * @returns Promise<RestoreWorkResult> 使用 await-to 范式返回结果
   */
  const restoreWork = async (
    params: RestoreWorkParams,
  ): Promise<RestoreWorkResult> => {
    // 参数验证：确保关联作品ID存在且有效
    if (!params.relWorkId || typeof params.relWorkId !== 'number') {
      const error = new Error('无法获取有效的关联作品ID，无法恢复旧作品');
      console.warn('[useWorkRestore] 参数验证失败:', {
        params,
        relWorkId: params.relWorkId,
      });
      return handleError(error, '参数验证失败');
    }

    // 调用恢复旧作品 API
    const [err, response] = await restoreOldWork({
      oldId: params.relWorkId.toString(),
    });

    if (err) {
      // 将 StandardizedError 转换为 Error 对象
      const error = new Error(err.message);
      return handleError(error, 'API 调用失败');
    }

    // 恢复成功处理
    console.log('[useWorkRestore] 恢复旧作品成功:', {
      relWorkId: params.relWorkId,
      response,
    });

    if (showSuccessMessage) {
      message.success(successMessage);
    }

    return [null, true];
  };

  return {
    /** 执行恢复旧作品操作 */
    restoreWork,
  };
}
