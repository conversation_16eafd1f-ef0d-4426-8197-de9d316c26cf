# 图片预览切换功能实现

## 功能描述

实现了图片预览的双向同步功能：

1. 点击 ImageSidebar 中的图片时，预览区`src\views\EditProjectView\pages\SecondStepImage\components\ImagePreview\index.vue`能够切换显示对应图片
2. 点击轮播图下方指示器切换图片时，ImageSidebar 的 selectedImgIdx 也会同步更新

## 重构说明

为了确保向后兼容性，对公共组件进行了重构，添加了可选的功能开关：

- **ImageSidebar.vue**：添加了`enableImagePreview` prop，默认为`false`
- **ImageWorkPreview.vue**：添加了`enableCarouselControl` prop，默认为`false`
- **ImageWorkPreview.vue**：添加了`disableAutoplay` prop，默认为`false`
- 只有明确传入相应 prop 为`true`的地方才会启用新功能
- 所有现有使用这些组件的地方都不需要修改，保持原有行为

## 实现方案

### 1. 整体架构

使用事件总线模式实现组件间的双向通信，避免深层 prop 传递：

```text
ContentPanel (ImageSidebar) ←→ EventBus ←→ ImagePreview (ImageWorkPreview)

事件流向：
1. ImageSidebar 点击 → IMAGE_PREVIEW_CHANGE 事件 → ImageWorkPreview 切换
2. ImageWorkPreview 轮播图切换 → CAROUSEL_CHANGE 事件 → ImageSidebar 更新选中状态
```

### 2. 核心组件修改

#### 2.1 ContentPanel/index.vue

**添加状态管理：**

```typescript
data() {
  return {
    selectedImageIndex: 0, // 当前选中的图片索引
  };
}
```

**添加事件处理：**

```typescript
// 处理图片选择
handleImageSelect(index: number) {
  this.selectedImageIndex = index;
  // 通过事件总线通知预览组件切换图片
  eventBus.emit(EVENT_NAMES.IMAGE_PREVIEW_CHANGE, index);
}
```

**监听 workData 变化：**

```typescript
workData: {
  handler(newVal: ImageWorkItem | undefined) {
    if (newVal) {
      this.updatePanelData();
      // 重置选中的图片索引为第一张
      this.selectedImageIndex = 0;
      // 通知预览组件切换到第一张图片
      eventBus.emit(EVENT_NAMES.IMAGE_PREVIEW_CHANGE, 0);
    }
  },
  immediate: true,
  deep: true,
}
```

**添加轮播图切换事件监听：**

```typescript
// 处理轮播图切换事件
handleCarouselChange(...args: unknown[]) {
  const activeIndex = args[0] as number;
  this.selectedImageIndex = activeIndex;
},

// 生命周期钩子
mounted() {
  // 监听轮播图切换事件
  eventBus.on(EVENT_NAMES.CAROUSEL_CHANGE, this.handleCarouselChange);
},
beforeDestroy() {
  // 移除轮播图切换事件监听
  eventBus.off(EVENT_NAMES.CAROUSEL_CHANGE, this.handleCarouselChange);
},
```

#### 2.2 ImageSidebar.vue

**添加功能开关 prop：**

```typescript
const props = withDefaults(
  defineProps<{
    // ... 其他props
    enableImagePreview?: boolean;
  }>(),
  {
    enableImagePreview: false,
  },
);
```

**条件性支持水平模式点击选择：**

```vue
<!-- 只有在垂直模式或启用图片预览功能时才支持点击选择 -->
@click="(direction === 'vertical' || enableImagePreview) ? selectImg(idx) :
undefined"

<!-- 只有在垂直模式或启用图片预览功能时才应用选中状态样式 -->
:class="[ 'image-text-editor-sidebar__item', (direction === 'vertical' ||
enableImagePreview) && idx === selectedImgIdx ?
'image-text-editor-sidebar__item--active' : '', ]"
```

**条件性监听 selectedIndex prop 变化：**

```typescript
// 监听selectedIndex prop的变化（仅在启用图片预览功能时）
watch(
  () => props.selectedIndex,
  newIndex => {
    if (props.enableImagePreview && newIndex !== undefined) {
      selectedImgIdx.value = newIndex;
    }
  },
  { immediate: true },
);
```

#### 2.3 ImageWorkPreview.vue

**添加功能开关 prop：**

```typescript
const props = withDefaults(
  defineProps<{
    // ... 其他props
    enableCarouselControl?: boolean;
    disableAutoplay?: boolean;
  }>(),
  {
    enableCarouselControl: false,
    disableAutoplay: false,
  },
);
```

**添加轮播图控制和事件监听：**

```vue
<!-- 添加ref引用、自动轮播控制和change事件监听 -->
<el-carousel
  ref="carousel"
  :autoplay="!disableAutoplay"
  @change="handleCarouselChange">
```

```typescript
// 轮播图引用
const carousel = ref();

// 设置轮播图当前显示项
const setActiveItem = (index: number) => {
  if (carousel.value && typeof carousel.value.setActiveItem === 'function') {
    carousel.value.setActiveItem(index);
  }
};

// 监听图片预览切换事件
const handleImagePreviewChange = (...args: unknown[]) => {
  const index = args[0] as number;
  setActiveItem(index);
};

// 处理轮播图切换事件
const handleCarouselChange = (activeIndex: number, _oldActiveIndex: number) => {
  // 只有在启用轮播图控制功能时才发送事件
  if (props.enableCarouselControl) {
    // 通过事件总线通知ImageSidebar更新选中状态
    eventBus.emit(EVENT_NAMES.CAROUSEL_CHANGE, activeIndex);
  }
};

// 组件挂载时监听事件（仅在启用轮播图控制功能时）
onMounted(() => {
  if (props.enableCarouselControl) {
    eventBus.on(EVENT_NAMES.IMAGE_PREVIEW_CHANGE, handleImagePreviewChange);
  }
});

// 组件卸载时移除事件监听（仅在启用轮播图控制功能时）
onUnmounted(() => {
  if (props.enableCarouselControl) {
    eventBus.off(EVENT_NAMES.IMAGE_PREVIEW_CHANGE, handleImagePreviewChange);
  }
});
```

#### 2.4 eventBus.ts

**添加新的事件常量：**

```typescript
export const EVENT_NAMES = {
  // ... 其他事件
  // 图片预览切换事件
  IMAGE_PREVIEW_CHANGE: 'imagePreviewChange',
  // 轮播图切换事件
  CAROUSEL_CHANGE: 'carouselChange',
  // ...
} as const;
```

## 3. 功能流程

### 3.1 ImageSidebar → ImageWorkPreview（点击缩略图切换）

1. **用户交互：** 用户点击 ContentPanel 中 ImageSidebar 的某张图片
2. **事件触发：** ImageSidebar 触发 select 事件，传递图片索引
3. **状态更新：** ContentPanel 的 handleImageSelect 方法被调用，更新 selectedImageIndex 状态
4. **事件发送：** 通过 eventBus 发送 IMAGE_PREVIEW_CHANGE 事件
5. **事件接收：** ImageWorkPreview 监听到事件，调用 setActiveItem 方法
6. **视图更新：** el-carousel 组件切换到对应的图片，预览区显示选中的图片

### 3.2 ImageWorkPreview → ImageSidebar（点击轮播图指示器切换）

1. **用户交互：** 用户点击轮播图下方的指示器或通过其他方式切换轮播图
2. **事件触发：** el-carousel 组件触发 change 事件，传递当前激活的图片索引
3. **事件处理：** ImageWorkPreview 的 handleCarouselChange 方法被调用
4. **事件发送：** 通过 eventBus 发送 CAROUSEL_CHANGE 事件
5. **事件接收：** ContentPanel 监听到事件，调用 handleCarouselChange 方法
6. **状态更新：** ContentPanel 更新 selectedImageIndex 状态，ImageSidebar 显示对应的选中状态

## 4. 技术要点

### 4.1 事件总线模式

- 使用全局 eventBus 实现组件间通信
- 避免了深层 prop 传递和复杂的父子组件通信
- 支持事件的订阅和取消订阅，防止内存泄漏

### 4.2 Element UI Carousel API

- 使用`setActiveItem(index)`方法程序化控制轮播图
- 通过 ref 获取 carousel 组件实例
- 支持按索引切换到指定的轮播项
- 使用`:autoplay="!disableAutoplay"`控制自动轮播功能
- 监听`@change`事件获取轮播图切换状态，事件参数为`(activeIndex, oldActiveIndex)`

### 4.3 Vue 3 Composition API

- 使用`ref`管理组件状态
- 使用`onMounted`和`onUnmounted`管理生命周期
- 使用`watch`监听 prop 变化

### 4.4 TypeScript 类型安全

- 为事件回调添加正确的类型注解
- 使用`unknown[]`类型处理事件参数，然后进行类型断言
- 确保方法参数的类型安全

### 4.5 向后兼容性设计

- 通过可选的 boolean 类型 prop 控制新功能的启用
- 默认值为`false`，确保现有代码不受影响
- 只有明确传入`enableImagePreview="true"`和`enableCarouselControl="true"`的地方才会启用新功能
- 公共组件的原有行为完全保持不变

## 5. 测试建议

1. **基础功能测试：**

   - 在 SecondStepImage 页面中测试点击不同图片
   - 验证预览区是否正确切换到对应图片

2. **视觉反馈测试：**

   - 确认 ImageSidebar 中选中状态的视觉反馈是否正确
   - 验证选中边框是否正确显示

3. **切换流畅性测试：**

   - 确认轮播图切换是否流畅，无卡顿现象
   - 测试快速连续点击的响应性

4. **状态同步测试：**

   - 测试作品切换时是否正确重置到第一张图片
   - 验证 ImageSidebar 的选中状态与预览区显示是否一致

5. **内存泄漏测试：**

   - 验证事件监听器是否正确清理
   - 测试组件销毁时是否正确移除事件监听

6. **向后兼容性测试：**
   - 验证其他使用 ImageSidebar 的页面功能是否正常
   - 确认其他使用 ImageWorkPreview 的页面轮播图行为未改变
   - 测试默认情况下（不传入新 prop）组件行为与修改前一致

## 6. 注意事项

1. **事件命名：** 使用 EVENT_NAMES 常量确保事件名称的一致性
2. **类型安全：** 事件回调使用`unknown[]`类型，需要进行类型断言
3. **生命周期管理：** 确保在组件卸载时清理事件监听器
4. **状态重置：** 作品切换时需要重置选中索引到第一张图片
5. **兼容性：** 确保 Element UI 的 setActiveItem 方法存在后再调用
6. **向后兼容性：** 新增的 prop 都有默认值`false`，确保现有代码无需修改
7. **功能隔离：** 只有明确启用相应功能的组件才会有新的行为
8. **自动轮播控制：** 在图文预览页面中使用`disableAutoplay="true"`关闭自动轮播
9. **双向同步：** 实现了 ImageSidebar 和 ImageWorkPreview 之间的双向状态同步
10. **事件防重复：** 只有在启用轮播图控制功能时才发送 CAROUSEL_CHANGE 事件，避免不必要的事件触发

## 7. 相关文件

- `src/views/EditProjectView/pages/SecondStepImage/components/ContentPanel/index.vue`
- `src/components/ImageTextEditor/components/ImageSidebar.vue`
- `src/components/TemplateView/ImageWorkPreview.vue`
- `src/utils/eventBus.ts`
