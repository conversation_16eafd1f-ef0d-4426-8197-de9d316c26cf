import axios, { AxiosInstance, AxiosError } from 'axios';

// 定义基础配置
const baseURL = '/api';
const timeout = 10000; // 超时时间

// 创建 Axios 实例
const api: AxiosInstance = axios.create({
  baseURL,
  timeout,
  headers: {
    'Content-Type': 'application/json',
  },
});

// 请求拦截器
api.interceptors.request.use(
  config => {
    // 待补充：请求拦截处理 (例如，添加token、通用参数、处理请求取消等)
    console.log('Request sent:', config);
    return config;
  },
  (error: AxiosError) => {
    console.error('Request error:', error);
    // 待补充：请求错误处理
    return Promise.reject(error);
  },
);

// 响应拦截器
api.interceptors.response.use(
  response => {
    if (response.data.code !== 0) {
      console.error('API Error:', response.data);
      return Promise.reject(response.data);
    }
    return response.data.data;
    // 待补充：其他响应成功处理 (例如，数据转换、日志记录等)
  },
  (error: AxiosError) => {
    console.error('Response error:', error);
    // 待补充：响应错误处理 (例如，刷新 token、统一错误提示、错误日志记录等)
    return Promise.reject(error);
  },
);

export default api;
