<template>
  <div class="current-integral">
    <span class="current-integral__text">当前账号剩余</span>
    <img class="current-integral__icon" src="@/assets/common/score.svg" />
    <span class="current-integral__num">{{ point }}</span>
  </div>
</template>

<script>
/**
 * @description 用户积分显示组件
 */
export default {
  name: 'UserPointDisplay',
  props: {
    /**
     * 用户积分
     * @type {Number | String}
     */
    point: {
      type: [Number, String],
      default: 0,
    },
  },
};
</script>

<style lang="scss" scoped>
// 积分显示
.current-integral {
  /* 布局相关 */
  @apply flex items-center justify-center;
}

.current-integral__text {
  @apply text-14px font-400
         text-left text-[#666];
}

.current-integral__icon {
  @apply w-[16px] h-[16px]
         ml-4px;
}

.current-integral__num {
  /* 尺寸相关 - 最大宽度限制 */
  @apply max-w-400px;

  /* 文字相关 - 超出省略 */
  @apply text-16px font-400 text-left text-[#666] ml-3px;
  @apply truncate;
}
</style>
