import { ref, Ref } from 'vue';
import { message } from '@fk/faicomponent';
import {
  generatePreview,
  saveProjectData,
  getConsumePoint,
} from '@/api/EditProjectView';
import {
  formatFirstStepDataForSave,
  createInputFormItem,
} from '@/views/EditProjectView/utils/index';
import type {
  EditProjectViewData,
  ValidationResult,
  FirstStepComponentShape,
  FormItemInternal,
} from '@/views/EditProjectView/types/index';
import type { WorkTypeValue } from '@/components/WorkListBar/types';
import {
  PROJECT_TYPE_VIDEO,
  PROJECT_TYPE_IMAGE,
} from '@/views/EditProjectView/constants';
import { generateTypeList } from '@/constants/pointConsume';

/**
 * @description 根据项目类型获取默认生成数量
 * @param projectType 项目类型
 * @returns 默认生成数量
 */
const getDefaultGenerateNum = (projectType: WorkTypeValue): number => {
  switch (projectType) {
    case PROJECT_TYPE_VIDEO:
      return 5; // 视频类型项目默认值
    case PROJECT_TYPE_IMAGE:
      return 5; // 图文类型项目默认值
    default:
      return 5; // 默认为视频类型的值
  }
};

/**
 * @description 根据项目类型获取最大生成数量
 * @param projectType 项目类型
 * @returns 最大生成数量
 */
const getMaxGenerateCount = (projectType: WorkTypeValue): number => {
  switch (projectType) {
    case PROJECT_TYPE_VIDEO:
      return 10; // 视频类型项目最大值
    case PROJECT_TYPE_IMAGE:
      return 10; // 图文类型项目最大值
    default:
      return 10; // 默认为视频类型的值
  }
};

/**
 * @description useFirstStep 配置选项接口
 */
export interface UseFirstStepOptions {
  /** 模板ID */
  templateId: number;
  /** 项目类型 */
  projectType: WorkTypeValue;
}

/**
 * @description `useFirstStep` 组合式API的返回值类型定义。
 * @interface UseFirstStepReturn
 */
export interface UseFirstStepReturn {
  /**
   * 保存操作的加载状态。
   * @type {Ref<boolean>}
   * @memberof UseFirstStepReturn
   */
  saveLoading: Ref<boolean>;
  /**
   * 生成预览操作的加载状态。
   * @type {Ref<boolean>}
   * @memberof UseFirstStepReturn
   */
  generateLoading: Ref<boolean>;
  /**
   * 生成按钮相关的配置，如生成数量和所需积分。
   * @type {Ref<{ num: number; maxCount: number; minCount: number; integral: number; showIntegral: boolean; }>}
   * @memberof UseFirstStepReturn
   */
  generateButtonConfig: Ref<{
    /** 生成数量 */
    num: number;
    /** 最大生成数量 */
    maxCount: number;
    /** 最小生成数量 */
    minCount: number;
    /** 总消耗积分（已根据数量计算） */
    integral: number;
    /** 是否显示积分 */
    showIntegral: boolean;
  }>;
  /**
   * 处理生成预览按钮点击事件的函数。
   * @param {FirstStepComponentShape} formComponent - `FirstStep`组件的实例引用。
   * @param {() => void} onSuccess - 生成成功后的回调函数。
   * @param {number} [projectId] - 项目ID。
   * @param {number} [generateNum] - （可选）指定生成数量，覆盖默认配置。
   * @param {number} [userPoint] - （可选）用户当前点数，用于点数校验。
   * @returns {Promise<{ shouldProceed: boolean; validationPassed: boolean }>} 返回是否应该继续执行和表单验证是否通过
   * @memberof UseFirstStepReturn
   */
  handleGenerateButtonClick: (
    formComponent: FirstStepComponentShape,
    onSuccess: () => void,
    projectId?: number,
    generateNum?: number,
    userPoint?: number,
  ) => Promise<{ shouldProceed: boolean; validationPassed: boolean }>;
  /**
   * 保存项目数据的函数。
   * @param {FirstStepComponentShape} formComponent - `FirstStep`组件的实例引用。
   * @param {number} [projectId] - 项目ID。
   * @param {number} [generateNum] - （可选）指定生成数量，用于保存时一并记录。
   * @returns {Promise<void>}
   * @memberof UseFirstStepReturn
   */
  saveProject: (
    formComponent: FirstStepComponentShape,
    projectId?: number,
    generateNum?: number,
  ) => Promise<void>;
  /**
   * 生成项目预览的函数。
   * @param {FirstStepComponentShape} formComponent - `FirstStep`组件的实例引用。
   * @param {() => void} onSuccess - 生成成功后的回调函数。
   * @param {number} [projectId] - 项目ID。
   * @param {number} [generateNum] - （可选）指定生成数量。
   * @returns {Promise<void>}
   * @memberof UseFirstStepReturn
   */
  generateProjectPreview: (
    formComponent: FirstStepComponentShape,
    onSuccess: () => void,
    projectId?: number,
    generateNum?: number,
  ) => Promise<void>;
  /**
   * 更新积分数据的函数。
   * @param {FirstStepComponentShape} [formComponent] - `FirstStep`组件的实例引用（可选）。
   * @returns {void}
   * @memberof UseFirstStepReturn
   */
  updateIntegralData: (formComponent?: FirstStepComponentShape) => void;
}

/**
 * @description 第一步相关的业务逻辑
 * 处理表单验证、生成预览和保存项目数据
 * @param options 配置选项
 * @returns {UseFirstStepReturn} 第一步相关状态和方法
 */
export function useFirstStep(options: UseFirstStepOptions): UseFirstStepReturn {
  const { templateId, projectType } = options;
  const saveLoading = ref<boolean>(false);
  const generateLoading = ref<boolean>(false);
  const generateButtonConfig = ref({
    num: getDefaultGenerateNum(projectType), // 根据项目类型设置默认生成数量
    maxCount: getMaxGenerateCount(projectType), // 根据项目类型设置最大生成数量
    minCount: 1, // 最小生成数量
    integral: 1,
    showIntegral: false, // 控制是否显示积分
  });

  /**
   * 检测是否有AI改嘴型开关开启
   * @param formComponent 表单组件实例
   * @returns 是否开启了AI改嘴型
   */
  const checkAiMouthShapeEnabled = (
    formComponent?: FirstStepComponentShape,
  ): boolean => {
    if (!formComponent?.fileFormItems) {
      return false;
    }

    // 检查文件表单项中是否有开启AI改嘴型的项
    return formComponent.fileFormItems.some(item => {
      // 使用类型断言来访问扩展属性
      const extendedItem = item as FormItemInternal;
      return extendedItem.hasModifyMouth && extendedItem.openModifyMouth;
    });
  };

  /**
   * 获取消耗点数
   * @param count 生成数量
   * @param formComponent 表单组件实例（可选，用于检测AI改嘴型状态）
   */
  const fetchConsumePoint = async (
    count: number = 1,
    formComponent?: FirstStepComponentShape,
  ): Promise<void> => {
    const hasAiMouthShape = checkAiMouthShapeEnabled(formComponent);

    // 根据项目类型生成 typeList 参数
    const typeList = generateTypeList(projectType, hasAiMouthShape);

    generateButtonConfig.value.showIntegral = false;

    const [err, res] = await getConsumePoint(typeList, count);

    if (err) {
      console.error('获取消耗点数失败:', err.message);
      return;
    }

    if (res?.data !== undefined) {
      generateButtonConfig.value.integral = res.data;
      generateButtonConfig.value.showIntegral = true;
    }
  };

  /**
   * 准备表单数据用于API调用
   * @param formComponent - 表单组件实例
   * @param projectId - 项目ID
   * @param generateNum - 生成数量
   * @returns 处理后的数据或null（如果处理失败）
   */
  const prepareFormData = (
    formComponent: FirstStepComponentShape,
    projectId?: number,
    generateNum?: number,
  ): EditProjectViewData | null => {
    if (!formComponent) {
      message.error('表单组件实例无效');
      return null;
    }
    const baseDataFromUtil = formatFirstStepDataForSave(formComponent);
    if (!baseDataFromUtil) {
      message.error('表单数据格式化失败');
      return null;
    }

    const result: EditProjectViewData = {
      name: baseDataFromUtil.name as string,
      inputForm: baseDataFromUtil.inputForm,
      resForm: baseDataFromUtil.resForm,
      bgMusic: baseDataFromUtil.bgMusic,
      voice: baseDataFromUtil.voice,
      script: baseDataFromUtil.script,
      projectId: projectId,
      templateId: templateId,
      type: projectType,
    };

    if (generateNum !== undefined && result.inputForm) {
      const outputQuantityItem: FormItemInternal = {
        prop: 'output_quantity',
        type: 'text',
        label: '生成数量',
        rules: [],
        attrs: {},
      };
      result.inputForm.push(
        createInputFormItem(outputQuantityItem, generateNum),
      );
    }
    return result;
  };

  /**
   * 处理生成按钮点击事件
   * @param formComponent - 表单组件实例
   * @param onSuccess - 生成成功的回调函数
   * @param projectId - 项目ID
   * @param generateNum - 生成数量
   * @param userPoint - 用户当前点数
   * @returns Promise<{ shouldProceed: boolean; validationPassed: boolean }> 返回是否应该继续执行和表单验证是否通过
   */
  const handleGenerateButtonClick = async (
    formComponent: FirstStepComponentShape,
    onSuccess: () => void,
    projectId?: number,
    generateNum?: number,
    userPoint?: number,
  ): Promise<{ shouldProceed: boolean; validationPassed: boolean }> => {
    // 检查是否正在生成中
    if (generateLoading.value) {
      return { shouldProceed: false, validationPassed: false };
    }

    if (
      !formComponent ||
      typeof formComponent.validateAllForms !== 'function'
    ) {
      return { shouldProceed: false, validationPassed: false };
    }

    // 第一步：校验表单
    const validationResult: ValidationResult =
      await formComponent.validateAllForms();
    if (!validationResult.valid) {
      console.log(
        `🚀 ADI-LOG ~ useFirstStep ~ validationResult:`,
        validationResult,
      );
      message.error(validationResult?.message || '表单验证失败');
      return { shouldProceed: false, validationPassed: false };
    }

    // 第二步：校验点数（如果提供了用户点数）
    if (userPoint !== undefined) {
      const requiredPoints = generateButtonConfig.value.integral;
      if (userPoint < requiredPoints) {
        // 点数不足，返回特殊状态，让调用方处理点数不足的情况
        return { shouldProceed: false, validationPassed: true };
      }
    }

    // 第三步：执行生成预览
    await generateProjectPreview(
      formComponent,
      onSuccess,
      projectId,
      generateNum !== undefined ? generateNum : generateButtonConfig.value.num,
    );

    return { shouldProceed: true, validationPassed: true };
  };

  /**
   * 保存项目数据（使用差异化校验）
   * @param formComponent - 表单组件实例
   * @param projectId - 项目ID
   * @param generateNum - 生成数量
   */
  const saveProject = async (
    formComponent: FirstStepComponentShape,
    projectId?: number,
    generateNum?: number,
  ): Promise<void> => {
    // 检查是否正在保存中
    if (saveLoading.value) {
      return;
    }

    if (
      !formComponent ||
      typeof formComponent.validateAllFormsForSave !== 'function'
    ) {
      message.error('表单组件实例无效或不支持保存模式校验');
      return;
    }

    saveLoading.value = true;
    message.loading('正在保存项目...');

    // 使用保存模式校验（仅最大长度校验）
    const validationResult: ValidationResult =
      await formComponent.validateAllFormsForSave();
    if (!validationResult.valid) {
      console.log(
        `🚀 ADI-LOG ~ useFirstStep ~ saveProject validationResult:`,
        validationResult,
      );
      message.error(validationResult?.message || '表单验证失败');
      saveLoading.value = false;
      return;
    }

    const dataToSave = prepareFormData(
      formComponent,
      projectId,
      generateNum !== undefined ? generateNum : generateButtonConfig.value.num,
    );
    if (!dataToSave) {
      saveLoading.value = false;
      return;
    }
    const [err, _res] = await saveProjectData(dataToSave);
    setTimeout(() => (saveLoading.value = false), 300);
    if (err) {
      message.error(err.message || '保存项目失败，请重试');
      return;
    }

    // 保存成功后重置未保存状态
    if (typeof formComponent.resetUnsavedChanges === 'function') {
      formComponent.resetUnsavedChanges();
    }

    message.success('保存成功');
  };

  /**
   * 生成项目预览
   * @param formComponent - 表单组件实例
   * @param onSuccess - 生成成功的回调函数
   * @param projectId - 项目ID
   * @param generateNum - 生成数量
   */
  const generateProjectPreview = async (
    formComponent: FirstStepComponentShape,
    onSuccess: () => void,
    projectId?: number,
    generateNum?: number,
  ): Promise<void> => {
    generateLoading.value = true;
    message.loading('正在生成预览...');
    const dataToPreview = prepareFormData(
      formComponent,
      projectId,
      generateNum,
    );
    if (!dataToPreview) {
      generateLoading.value = false;
      return;
    }
    const [err, _res] = await generatePreview(dataToPreview);
    setTimeout(() => (generateLoading.value = false), 300);
    if (err) {
      message.error(err.message || '生成预览失败，请重试');
      return;
    }
    message.success('生成预览成功');
    onSuccess && onSuccess();
  };

  /**
   * 更新积分数据（供外部调用）
   * @param formComponent 表单组件实例
   */
  const updateIntegralData = (
    formComponent?: FirstStepComponentShape,
  ): void => {
    fetchConsumePoint(generateButtonConfig.value.num, formComponent);
  };

  return {
    saveLoading,
    generateLoading,
    generateButtonConfig,
    handleGenerateButtonClick,
    saveProject,
    generateProjectPreview,
    updateIntegralData,
  };
}
