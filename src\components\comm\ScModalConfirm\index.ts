import Vue from 'vue';
import ScModalConfirm from './ScModalConfirm.vue';

/**
 * 确认弹窗组件
 * @param props 弹窗参数
 * @param props.content 弹窗内容
 * @param props.okText 确定按钮文本
 * @param props.cancelText 取消按钮文本
 * @param props.okType 确定按钮类型
 * @param props.width 弹窗宽度
 * @param props.minHeight 弹窗最小高度
 * @param props.customClass 自定义弹窗类名
 * @param props.onOk 点击确定按钮回调
 * @param props.onCancel 点击取消按钮回调
 * @returns 弹窗实例
 */
export const showConfirmModal = (props: {
  content: string;
  okText?: string;
  cancelText?: string;
  okType?: 'danger' | 'primary';
  width?: number | string;
  minHeight?: number | string;
  customClass?: string;
  onOk?: () => void;
  onCancel?: () => void;
}) => {
  Vue.prototype.$modal.open('ScModalConfirm', {
    component: ScModalConfirm,
    props: {
      visible: true,
      ...props,
    },
    onClose: () => {
      console.log('ScConfirmModal已关闭');
    },
  });
};

/**
 * 关闭弹窗组件
 */
export const closeImageViewer = () => {
  Vue.prototype.$modal.close('ScModalConfirm');
};
