import { VERSION } from '@/constants/version';

/**
 * 版本校验结果接口
 */
export interface VersionCheckResult {
  /** 是否有权限 */
  hasPermission: boolean;
  /** 要求的版本 */
  requiredVersion: VERSION;
  /** 功能名称 */
  featureName: string;
  /** 提示消息 */
  message?: string;
}

/**
 * 版本权限检查函数类型
 */
export type VersionPermissionChecker = (
  requiredVersion: VERSION,
  currentVersion?: VERSION,
) => boolean;

/**
 * 版本降级回调函数类型
 */
export type VersionDowngradeCallback = () => void;

/**
 * 功能版本检查函数类型
 */
export type FeatureVersionChecker = (
  isEnabled: boolean,
  onDowngrade?: VersionDowngradeCallback,
) => boolean;

/**
 * 降级处理函数类型
 */
export type DowngradeHandler<T = Record<string, unknown>> = (data: T) => T;

/**
 * 版本校验操作函数类型
 */
export type VersionCheckOperation = () => void;

/**
 * 版本校验模式枚举
 */
export enum VersionCheckMode {
  /** UI交互式校验 */
  UI_INTERACTIVE = 'UI_INTERACTIVE',
  /** 消息提示式校验 */
  MESSAGE_PROMPT = 'MESSAGE_PROMPT',
  /** 接口级自动降级 */
  API_DOWNGRADE = 'API_DOWNGRADE',
}

/**
 * 版本校验事件类型
 */
export interface VersionCheckEvents {
  /** 权限检查事件 */
  'permission-check': {
    featureKey: string;
    hasPermission: boolean;
  };
  /** 权限拒绝事件 */
  'permission-denied': {
    featureKey: string;
    requiredVersion: VERSION;
  };
  /** 降级处理事件 */
  'downgrade-applied': {
    featureKey: string;
    dataCount: number;
  };
}
