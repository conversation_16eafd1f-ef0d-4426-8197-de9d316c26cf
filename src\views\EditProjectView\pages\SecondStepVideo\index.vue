<template>
  <BaseStepLayout>
    <!-- 左侧作品列表 -->
    <template #left-panel>
      <WorkListBar
        :workList="displayedWorkList"
        :loading="isFirstPageLoading"
        :workListBarType="WorkType.VIDEO"
        :selectedWorkIds="selectedWorkIds"
        :current-page="currentPage"
        :page-size="pageSize"
        :total-items="sucNum"
        :is-loading-more="isLoadingMoreData"
        :has-loaded-all="hasLoadedAll"
        :has-loading-error="hasLoadingError"
        :loading-error="loadingError"
        :can-retry-loading="canRetryLoading"
        @refresh="fetchWorkList"
        @select-change="handleSelectChange"
        @all-select-change="handleAllSelectChange"
        @scroll-end="handleScrollEnd"
        @retry="retryLoad"
      />
    </template>

    <!-- 中间视频预览 -->
    <template #center-panel>
      <!-- 骨架屏 -->
      <fa-skeleton
        v-if="shouldShowSkeleton"
        :loading="shouldShowSkeleton"
        :title="false"
        active
        :paragraph="{ rows: 2, width: ['100%', '100%'] }"
        class="center-panel-skeleton"
      />
      <!-- 实际内容 -->
      <VideoPlayer
        v-else
        :key="currentWork?.id || 'loading-work-data'"
        class="mb-[-8px]"
        :videoUrl="currentWork?.contentUrl || ''"
        :workData="currentWork"
        :progress="currentProgress"
        :coverImg="currentWork?.coverImg || ''"
        :coverImgType="currentWork?.coverImgType || 0"
        @video-player:action="handleVideoPlayerAction"
      />
    </template>

    <!-- 右侧内容面板 -->
    <template #right-panel>
      <!-- 骨架屏 -->
      <fa-skeleton
        v-if="shouldShowSkeleton"
        :loading="shouldShowSkeleton"
        :title="false"
        active
        :paragraph="{
          rows: 7,
          width: ['60px', '70px', '100%', '60px', '100%', '60px', '100%'],
        }"
        class="right-panel-skeleton"
      />
      <!-- 实际内容 -->
      <ContentPanel
        v-else
        :work-id="currentWorkId"
        :loading="isLoading"
        :work-data="currentWork"
        @action="handleContentPanelAction"
      />
    </template>
  </BaseStepLayout>
</template>

<script lang="ts">
import { defineComponent, watch, computed, onMounted, onUnmounted } from 'vue';
import BaseStepLayout from '@/views/EditProjectView/components/BaseStepLayout/index.vue';
import WorkListBar from '@/components/WorkListBar/index.vue';
import VideoPlayer from '@/views/EditProjectView/pages/SecondStepVideo/components/VideoPlayer/index.vue';
import ContentPanel from '@/views/EditProjectView/pages/SecondStepVideo/components/ContentPanel/index.vue';

import { ContentPanelActionEventParam } from '@/views/EditProjectView/pages/SecondStepVideo/components/ContentPanel/types';
import { WorkType } from '@/components/WorkListBar/types';
import { useInfiniteVideoWorkList } from '@/views/EditProjectView/composables/useInfiniteWorkList';
import { showVideoEditor } from '@/components/VideoEditor';
import {
  VIDEO_EDITOR_TAB,
  VIDEO_EDITOR_CLOSE_STATUS,
} from '@/components/VideoEditor/constants';
import { eventBus, EVENT_NAMES } from '@/utils/eventBus';
import { CONTENT_PANEL_ACTION_TYPES } from './components/ContentPanel/constants';
import { getWorkStatusInfo, isRecompletedWork } from '@/constants/workStatus';

export default defineComponent({
  name: 'SecondStepVideo',
  components: {
    BaseStepLayout,
    WorkListBar,
    VideoPlayer,
    ContentPanel,
  },
  emits: ['selected-works-change', 'all-select-change'],
  props: {
    // 类型（视频：0，图文：1）
    type: {
      type: Number,
      default: 0,
    },
    // 状态：add-新建项目，edit-编辑项目
    status: {
      type: String,
      default: 'edit',
    },
    // 表单数据（从父组件传入）
    formData: {
      type: Object,
      default: () => null,
    },
    // 项目ID
    projectId: {
      type: Number,
      required: true,
    },
  },
  setup(props, { emit }) {
    const {
      currentWorkId,
      isLoading,
      displayedWorkList,
      currentWork,
      currentProgress,
      fetchWorkList,
      selectedWorkIds,
      handleSelectChange,
      // 分页相关
      pageSize,
      currentPage,
      totalItems,
      sucNum,
      isLoadingMore,
      hasLoadedAll,
      handleScrollEnd,
      retryLoad,
      onPageChange,
      isLoadingDetail,
      // 新的加载状态
      isFirstPageLoading,
      isLoadingMoreData,
      // 错误处理相关
      hasLoadingError,
      loadingError,
      canRetryLoading,
      // 轮询相关
      polling,
      // 编辑后更新方法
      handleEditUpdate,
      // 生成完成后更新方法
      handleGenerateUpdate,
      // 刷新作品列表方法
      refreshWorkList,
    } = useInfiniteVideoWorkList(props.projectId);

    /**
     * 是否应该显示骨架屏
     * 当作品列表首次加载或作品详情加载时显示骨架屏
     */
    const shouldShowSkeleton = computed(() => {
      return isLoadingDetail.value || isFirstPageLoading.value;
    });

    // 监听 selectedWorkIds 变化，向父组件发送事件
    watch(
      selectedWorkIds,
      newIds => {
        emit('selected-works-change', newIds);
      },
      { immediate: true },
    );

    /**
     * 处理全选状态变化事件
     * @param isAll 是否全选
     */
    const handleAllSelectChange = (isAll: boolean): void => {
      emit('all-select-change', isAll);
    };

    // 监听projectId变化，重新获取数据
    watch(
      () => props.projectId,
      newProjectId => {
        if (newProjectId) {
          // 项目切换时，清空所有作品详情缓存并重新刷新作品列表，清空选中状态
          refreshWorkList();
        }
      },
      { immediate: true },
    );

    /**
     * 处理视频播放器操作事件
     * @param event 事件参数
     */
    const handleVideoPlayerAction = async (event: {
      actionType: string;
    }): Promise<void> => {
      console.log(
        `视频播放器操作: ${event.actionType}, 作品ID: ${currentWork.value?.id}`,
      );

      // 处理播放器相关操作
      switch (event.actionType) {
        case 'refresh-full-list': {
          // 完整刷新作品列表：恢复旧作品成功后触发
          console.log(
            '🔄 VideoPlayer: 处理完整刷新作品列表事件，使用 refreshWorkList 完整刷新',
          );
          const [err] = await refreshWorkList();
          if (err) {
            console.error('恢复旧作品后刷新作品列表失败:', err.message);
          }
          break;
        }
        default:
          // 其他播放器操作（play, pause, ended, error, fullscreen）
          break;
      }
    };

    /**
     * 处理内容面板操作事件
     * @param event 事件参数
     */
    const handleContentPanelAction = (
      event: ContentPanelActionEventParam,
    ): void => {
      console.log(
        '🚀 ADI-LOG ~ 处理内容面板操作事件 ~ event:',
        event,
        currentWork.value,
      );

      if (!currentWork.value) return;

      // 如果当前作品是重新生成完成状态，直接显示新旧选择弹窗，而不是打开编辑器
      const statusInfo = getWorkStatusInfo(currentWork.value);
      if (isRecompletedWork(statusInfo)) {
        console.log('重新生成完成状态，显示新旧选择弹窗');
        eventBus.emit(EVENT_NAMES.SHOW_NEW_OLD_CHOICE_MODAL, currentWork.value);
        return;
      }

      const tabMap = {
        [CONTENT_PANEL_ACTION_TYPES.SCRIPT]: VIDEO_EDITOR_TAB.SCRIPT,
        [CONTENT_PANEL_ACTION_TYPES.MUSIC]: VIDEO_EDITOR_TAB.MUSIC,
        [CONTENT_PANEL_ACTION_TYPES.VOICE]: VIDEO_EDITOR_TAB.MUSIC,
        [CONTENT_PANEL_ACTION_TYPES.FONT_TAG]: VIDEO_EDITOR_TAB.STICKER,
      };

      // 在打开视频编辑器前通过 eventBus 停止当前视频播放
      eventBus.emit(EVENT_NAMES.STOP_CURRENT_VIDEO, {
        workId: currentWork.value.id,
      });

      // 处理内容面板相关操作 - 只有非重新生成完成状态才打开编辑器
      showVideoEditor(
        currentWork.value.relWorkId ?? currentWork.value.id,
        tabMap[event.type],
        async closeStatus => {
          // 视频编辑器关闭回调
          if (!currentWork.value) return;

          console.log('视频编辑器关闭，状态:', closeStatus);

          switch (closeStatus) {
            case VIDEO_EDITOR_CLOSE_STATUS.UNMODIFIED:
              // 未修改，不需要处理
              console.log('未修改，无需处理');
              break;

            case VIDEO_EDITOR_CLOSE_STATUS.SAVED:
              // 已保存，需要调用 handleEditUpdate
              console.log('已保存，调用 handleEditUpdate');
              await handleEditUpdate(currentWork.value.id);
              break;

            case VIDEO_EDITOR_CLOSE_STATUS.GENERATED:
              // 已生成，需要调用 handleGenerateUpdate
              console.log('已生成，调用 handleGenerateUpdate');
              await handleGenerateUpdate(currentWork.value.id);
              break;

            default:
              console.warn('未知的关闭状态:', closeStatus);
              break;
          }
        },
      );
    };

    /**
     * 处理刷新作品列表事件
     * @description 当用户在预览弹窗中选择保存时触发，清空所有作品详情缓存并重新刷新作品列表
     */
    const handleRefreshWorkList = async (): Promise<void> => {
      console.log('处理刷新作品列表事件');
      // 清空所有作品详情缓存并重新刷新作品列表，清空选中状态
      const [err] = await refreshWorkList();
      if (err) {
        console.error('刷新作品列表失败:', err.message);
      }
    };

    // 监听 EventBus 刷新事件
    onMounted(() => {
      eventBus.on(EVENT_NAMES.REFRESH_WORK_LIST, handleRefreshWorkList);
    });

    // 组件卸载时清理事件监听
    onUnmounted(() => {
      eventBus.off(EVENT_NAMES.REFRESH_WORK_LIST, handleRefreshWorkList);
    });

    return {
      currentWorkId,
      isLoading,
      currentWork,
      currentProgress,
      handleVideoPlayerAction,
      handleContentPanelAction,
      handleRefreshWorkList,
      WorkType,
      fetchWorkList,
      selectedWorkIds,
      handleSelectChange,
      handleAllSelectChange,
      // 分页相关
      currentPage,
      pageSize,
      totalItems,
      sucNum,
      onPageChange,
      displayedWorkList,
      // 无限滚动相关
      isLoadingMore,
      hasLoadedAll,
      handleScrollEnd,
      retryLoad,
      // 错误处理相关
      hasLoadingError,
      loadingError,
      canRetryLoading,
      // 详情相关
      isLoadingDetail,
      // 新的加载状态
      isFirstPageLoading,
      isLoadingMoreData,
      // 骨架屏显示状态
      shouldShowSkeleton,
      // 轮询相关
      polling,
      // 暴露作品列表供外部访问（用于检查作品状态）
      workList: displayedWorkList,
    };
  },
});
</script>

<style lang="scss" scoped>
/* 中间面板骨架屏样式 */
.center-panel-skeleton {
  /* 布局相关 */
  @apply w-full h-full;
  /* 尺寸相关 */
  @apply p-0;

  ::v-deep .fa-skeleton-content {
    /* 完全填充容器 */
    @apply w-full h-full;

    .fa-skeleton-paragraph {
      /* 使用 flex 布局，垂直排列 */
      @apply w-full h-full m-0 flex flex-col;

      li {
        /* 第一行：视频区域，占剩余高度 */
        &:nth-child(1) {
          @apply w-full flex-1 m-0;
        }

        /* 第二行：视频控制器区域，固定高度28px，上间距24px */
        &:nth-child(2) {
          @apply w-full h-28px mt-24px m-0;
        }
      }
    }
  }
}

/* 右侧面板骨架屏样式 */
.right-panel-skeleton {
  /* 布局相关 */
  @apply w-full h-full;
  /* 尺寸相关 */
  @apply p-[32px_32px_0px];

  ::v-deep .fa-skeleton-content {
    .fa-skeleton-paragraph {
      li {
        /* 默认间距重置 */
        @apply mb-0 mt-0;

        /* 第1行：第一个小标题 */
        &:nth-child(1) {
          @apply h-32px mb-18px;
        }

        /* 第2行：脚本结构 - 4个行内块元素 */
        &:nth-child(2) {
          @apply h-24px mb-18px;
          /* 模拟4个行内块的效果 */
          @apply relative;

          /* 使用多个伪元素模拟4个块 */
          &::before {
            content: '';
            @apply absolute top-0 left-78px w-70px h-24px bg-gray-200 rounded-4px;
          }

          &::after {
            content: '';
            @apply absolute top-0 left-156px w-70px h-24px bg-gray-200 rounded-4px;
          }
        }

        /* 第3行：内容盒子 */
        &:nth-child(3) {
          @apply h-200px mb-24px;
        }

        /* 第4行：第二个小标题（偶数行） */
        &:nth-child(4) {
          @apply h-23px mb-8px;
        }

        /* 第5行：第二个内容区域（奇数行） */
        &:nth-child(5) {
          /* 音乐区域 */
          @apply h-68px mb-24px;
        }

        /* 第6行：第三个小标题（偶数行） */
        &:nth-child(6) {
          @apply h-23px mb-8px;
        }

        /* 第7行：第三个内容区域（奇数行） */
        &:nth-child(7) {
          /* 配音区域 */
          @apply h-68px mb-0;
        }
      }
    }
  }
}
</style>
