<template>
  <div class="cover-video">
    <!-- 不传type，图片默认webp格式 -->
    <ScImg
      class="cover-img"
      :src="file.extra.cover"
      fit="scale-down"
      :style="imgStyle"
      :maxWidth="maxWidth"
    />
    <div class="duration" v-if="duration">{{ duration }}</div>
  </div>
</template>

<script lang="ts" setup>
import { computed, defineProps } from 'vue';
import ScImg from '@/components/comm/ScImg.vue';

interface FileExtra {
  duration?: number;
  cover?: string;
}

interface FileProps {
  file: {
    extra: FileExtra;
    type: number;
  };
  maxWidth?: number;
  imgStyle?: Record<string, unknown>;
}

const props = defineProps<FileProps>();

const duration = computed(() => {
  if (!props.file) return '';
  const { duration } = props.file.extra;
  if (!duration) return '';
  const hour = Math.floor(duration / 3600);
  const minutes = Math.floor((duration - 3600 * hour) / 60);
  const seconds = duration % 60;

  const formattedHour = hour > 0 ? `${String(hour).padStart(2, '0')}:` : '';
  const formattedMinutes = String(minutes).padStart(2, '0');
  const formattedSeconds = String(seconds).padStart(2, '0');

  return `${formattedHour}${formattedMinutes}:${formattedSeconds}`;
});
</script>

<style lang="scss" scoped>
.cover-video {
  @apply size-full p-[3px] box-border absolute top-0 left-0;
  .cover-img {
    @apply absolute top-50% transform-translate-y-[-50%];
  }
  .duration {
    @apply text-[11px] text-white p-[3px_4px] bg-[rgba(0,0,0,0.6)] absolute right-[5px] bottom-[5px] rounded-[8px] leading-[11px];
  }
}
</style>
