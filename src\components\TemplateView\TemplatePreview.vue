<template>
  <div class="absolute z-1000">
    <el-dialog
      :visible.sync="visible"
      center
      class="template-preview"
      :show-close="false"
      :close-on-click-modal="false"
      top="0"
      :modal-append-to-body="false"
    >
      <div
        class="flex bg-background rounded-[16px]"
        v-click-outside="handleClickOutside"
      >
        <!-- 左侧预览 -->
        <div class="p-[40px] rounded-l-[16px] relative">
          <div
            v-show="templateInfo?.type === _PROJECT_TYPE.VIDEO"
            class="w-[324px] h-[576px] rounded-[12px] overflow-hidden"
          >
            <VideoWorkPreview
              ref="videoPlayerRef"
              class="w-full h-full rounded-[12px]"
              :src="templateInfo?.setting?.video?.videoId"
              :type="templateInfo?.setting?.video?.videoType"
              belong="oss"
              :cover-id="templateInfo?.cover.resId"
            />
          </div>
          <ImageWorkPreview
            v-show="templateInfo?.type === _PROJECT_TYPE.IMAGE"
            :title="templateInfo?.script?.title || ''"
            :content="templateInfo?.script?.content || ''"
            :imgList="imgList"
            carouselMaxHeight="432px"
            belong="oss"
            class="w-[324px] h-[588px] rounded-[12px] overflow-hidden"
          />
          <div class="type-tag">
            <Icon class="w-[16px] h-[16px] mr-[4px]" :type="typeIcon" />
            {{ typeName }}
          </div>
        </div>
        <!-- 右侧信息 -->
        <div
          class="min-w-[370px] w-[370px] p-[40px] pt-[64px] bg-white rounded-[16px] relative"
        >
          <Icon
            type="guanbi-tancong"
            slot="closeIcon"
            class="absolute top-[24px] right-[24px] w-[12px] h-[12px] cursor-pointer text-assist hover:text-subText"
            @click.stop="handleClose"
          />
          <div class="text-title font-bold text-[24px] mb-[24px]">
            {{ templateInfo?.name }}
          </div>
          <div class="mb-[24px]">
            <span class="info-title">适用场景</span
            ><span class="name-tag">{{ sceneName }}</span>
          </div>
          <div class="mb-[24px]">
            <span class="info-title">适用行业</span
            ><span>{{ industryName }}</span>
          </div>
          <div v-if="templateInfo?.type === _PROJECT_TYPE.VIDEO">
            <div class="info-title mb-[2px]">脚本结构</div>
            <div class="line-height-[36px]">
              <template v-for="(item, index) in templateInfo.scriptList">
                <span class="name-tag">
                  {{ item }}
                </span>
                <span class="px-[4px]">
                  {{
                    index === (templateInfo.scriptList?.length || 0) - 1
                      ? ''
                      : '+'
                  }}
                </span>
              </template>
            </div>
          </div>
          <fa-button
            class="btn-special absolute bottom-[40px] left-[40px] w-[calc(100%-80px)]"
            type=""
            shape="round"
            @click="templateInfo && handleUseTemplate(templateInfo)"
          >
            使用模板
          </fa-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import ImageWorkPreview from '@/components/TemplateView/ImageWorkPreview.vue';
import { PROJECT_TYPE, PROJECT_TYPE_NAME } from '@/constants/project';
import { PAGE_SOURCE } from '@/constants/navigation';
import store from '@/store';
import { computed, ref, watch } from 'vue';
import VideoWorkPreview from './VideoWorkPreview.vue';
import { Template } from '@/types';
import router from '@/router';
import { getTemplateStatus } from '@/api/TemplateView';
import { message } from '@fk/faicomponent';

const props = defineProps<{
  /** 模板信息 */
  templateInfo: Template | null;
  /** 是否显示预览 */
  value: boolean;
}>();

/** 视频ref */
const videoPlayerRef = ref<InstanceType<typeof VideoWorkPreview> | null>(null);

/** 行业名称 */
const industryName = computed(() => {
  if (!props.templateInfo) return '';
  return store.state.system.industryMap?.[props.templateInfo.industry] || '';
});
/** 场景名称 */
const sceneName = computed(() => {
  if (!props.templateInfo) return '';
  return store.state.system.sceneMap?.[props.templateInfo.scene] || '';
});
// 类型名称，使用computed计算
const typeName = computed(() => {
  return PROJECT_TYPE_NAME[props.templateInfo?.type || PROJECT_TYPE.VIDEO];
});

// 类型图标，使用computed计算
const typeIcon = computed(() => {
  return props.templateInfo?.type === PROJECT_TYPE.VIDEO ? 'shipin' : 'tuwen';
});

/** 图片列表 */
const imgList = computed(() => {
  if (
    props.templateInfo &&
    props.templateInfo.setting &&
    props.templateInfo.type === PROJECT_TYPE.IMAGE
  ) {
    return [
      props.templateInfo.cover,
      ...(props.templateInfo.setting.imgList || []),
    ];
  }
  return [];
});

const _PROJECT_TYPE = PROJECT_TYPE;
const visible = ref(false);
watch(
  () => props.value,
  newVal => {
    visible.value = newVal;
  },
);
watch(visible, newVal => {
  if (newVal === false) {
    // 如果关闭了弹窗，暂停视频
    if (videoPlayerRef.value) {
      (videoPlayerRef.value as { stopVideo?: () => void })?.stopVideo?.();
    }
  }
  emit('input', newVal);
});
const emit = defineEmits<{
  (e: 'input', visible: boolean): void;
}>();
const handleClose = () => {
  visible.value = false;
};

/** 处理点击外部区域关闭弹窗 */
const handleClickOutside = (event: MouseEvent) => {
  // 检查是否点击在遮罩层上
  const dialogWrapper = (event.target as Element)?.closest(
    '.el-dialog__wrapper',
  );
  const dialogContent = (event.target as Element)?.closest('.el-dialog');

  // 如果点击在遮罩层上但不在对话框内容上，则关闭弹窗
  if (dialogWrapper && !dialogContent) {
    visible.value = false;
  }
};

/**
 * 点击使用模板
 * @param templateInfo 模板信息
 */
const handleUseTemplate = async (templateInfo: Template) => {
  const [err, res] = await getTemplateStatus(templateInfo.id);
  if (err) {
    message.error(err.message);
    throw new Error(err.message);
  }
  if (!res.data.isCanUseProto || !res.data.isCanUseTemplate) {
    message.error('当前模板不可用，请创建其他模板使用');
    return;
  }
  if (templateInfo.type === PROJECT_TYPE.VIDEO && videoPlayerRef.value) {
    // 暂停视频播放
    (videoPlayerRef.value as { stopVideo?: () => void })?.stopVideo?.();
  }
  const resolvedPath = router.resolve({
    path: `/${
      templateInfo.type === PROJECT_TYPE.VIDEO
        ? 'video-project'
        : 'image-project'
    }`,
    query: {
      templateId: templateInfo.id,
      from: PAGE_SOURCE.TEMPLATE, // 添加来源标识：模板页
    },
  });
  window.open(resolvedPath.href, '_blank');
};
</script>

<style lang="scss" scoped>
.template-preview {
  @apply flex justify-center items-center;
  .info-title {
    @apply text-assist text-[14px] mr-[24px];
  }
  .name-tag {
    @apply inline-block line-height-[16px];
    @apply text-text text-center text-[13px] px-[8px] py-[4px] bg-background rounded-[4px] whitespace-nowrap;
  }
  .type-tag {
    @apply absolute top-[52px] right-[52px] bg-[#000000c0] text-white text-[12px] font-bold px-[8px] py-[4px] rounded-full flex items-center justify-center;
  }
  ::v-deep {
    .el-dialog {
      @apply rounded-[16px] mb-0 w-auto;
      .el-dialog__header {
        @apply p-0;
      }
      .el-dialog__body {
        @apply p-0;
      }
    }
  }
}
</style>
