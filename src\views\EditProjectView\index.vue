<template>
  <div class="edit-project">
    <!-- 顶部栏 -->
    <EditProjectHeader
      :current-step="currentStep"
      :save-loading="saveLoading"
      :project-id="currentProjectId"
      @step-change="handleStepChange"
      @save-project="handleSaveProject"
    />

    <!-- 主要内容区域 -->
    <main class="edit-project__main">
      <!-- 加载状态 -->
      <fa-spin v-if="loading" class="edit-project__main-loading" size="large" />

      <!-- 项目编辑内容 -->
      <component
        v-if="currentProjectId && !loading"
        :is="currentComponent"
        :type="currentComponentType"
        :form-data="formData"
        :project-id="currentProjectId"
        @selected-works-change="handleSelectedWorksChange"
        @all-select-change="handleAllSelectChange"
        ref="activeComponentRef"
      />
    </main>

    <!-- 页脚操作区 -->
    <ProjectFooter
      :num.sync="generateButtonConfig.num"
      :integral="generateButtonConfig.integral"
      :show-integral="generateButtonConfig.showIntegral"
      :loading="generateLoading"
      :user-point="userPoint"
      :current-step="currentStep"
      :project-id="currentProjectId || 0"
      :min-count="generateButtonConfig.minCount"
      :max-count="generateButtonConfig.maxCount"
      :selected-work-ids="selectedWorkIds"
      :is-all-selected="isAllSelected"
      :work-list="currentWorkList"
      @step-change="handleStepChange"
      @generate-click="handleGenerateClick"
      @save-to-works="handleSaveToWorks"
    />

    <!-- 新旧选择弹窗 -->
    <WorkSelectModal
      :visible="showNewOldChoiceModal"
      :workId="currentRegeneratedWork?.id"
      :useCustomSuccessMessage="true"
      @save-new="handleSaveNewFromModal"
      @save-all="handleSaveAllFromModal"
      @close="showNewOldChoiceModal = false"
    />

    <!-- 点数不足提示弹窗 -->
    <InsufficientPointsModal
      :visible.sync="insufficientPointsVisible"
      @recharge="handleRecharge"
      @cancel="handleCancel"
    />
  </div>
</template>

<script lang="ts">
import {
  defineComponent,
  computed,
  getCurrentInstance,
  ref,
  toRefs,
  nextTick,
  watch,
  onMounted,
  onUnmounted,
} from 'vue';
import { debounce } from 'lodash-es';

// Import Constants
import {
  FIRST_STEP_INDEX,
  SECOND_STEP_INDEX,
  PROJECT_TYPE_VIDEO,
  PROJECT_TYPE_IMAGE,
  DEBOUNCE_DELAY,
} from '@/views/EditProjectView/constants';

// Import Components
import FirstStep from '@/views/EditProjectView/pages/FirstStep/index.vue';
import SecondStepVideo from '@/views/EditProjectView/pages/SecondStepVideo/index.vue';
import SecondStepImage from '@/views/EditProjectView/pages/SecondStepImage/index.vue';
import EditProjectHeader from '@/views/EditProjectView/components/ProjectHeader/index.vue';
import ProjectFooter from '@/views/EditProjectView/components/ProjectFooter/index.vue';
import WorkSelectModal from '@/components/WorkView/WorkModal/WorkSelectModal.vue';
import InsufficientPointsModal from '@/components/InsufficientPointsModal/index.vue';

// Import Types
import type {
  FirstStepComponentShape,
  ComponentRef,
  ProjectType,
  RouteLike,
  EditProjectViewData,
} from '@/views/EditProjectView/types/index';

// Import Composables
import { useFirstStep } from '@/views/EditProjectView/composables/useFirstStep';
import { useSecondStep } from '@/views/EditProjectView/composables/useSecondStep';
import { useNewOldChoiceModal } from '@/views/EditProjectView/composables/useNewOldChoiceModal';
import { usePointsRecharge } from '@/composables/usePointsRecharge';

// Import Utils
import { useProjectData } from '@/views/EditProjectView/composables/useProjectData';
import { eventBus, EVENT_NAMES } from '@/utils/eventBus';
import { getExternalDynamicUrl } from '@/constants/system';

/**
 * 项目编辑视图组件 - 支持图片和视频项目的创建和编辑
 */
export default defineComponent({
  name: 'EditProjectView',
  components: {
    FirstStep,
    SecondStepVideo,
    SecondStepImage,
    EditProjectHeader,
    ProjectFooter,
    WorkSelectModal,
    InsufficientPointsModal,
  },
  props: {
    step: {
      type: Number,
      default: undefined,
      validator: (value: number | undefined): boolean => {
        return (
          value === undefined ||
          [FIRST_STEP_INDEX, SECOND_STEP_INDEX].includes(value)
        );
      },
    },
  },
  setup(props) {
    // Vue 实例和路由
    const instance = getCurrentInstance();
    const $store = instance?.proxy?.$store;
    const $route = instance?.proxy?.$route as RouteLike;

    // 组件引用
    const activeComponentRef = ref<ComponentRef | null>(null);

    // 点数不足弹窗状态
    const insufficientPointsVisible = ref(false);

    // 解构props
    const { step } = toRefs(props);

    // 项目类型 - 从路由名称判断
    const projectType = computed<ProjectType>(() =>
      $route?.name === 'imageProject' ? PROJECT_TYPE_IMAGE : PROJECT_TYPE_VIDEO,
    );
    // 项目ID - 从路由参数中获取
    const routeProjectId = $route?.query?.projectId
      ? $route.query.projectId
      : '';
    // 模板ID - 从路由参数中获取
    const templateId = $route?.query?.templateId ? $route.query.templateId : '';

    // 用户积分
    const userPoint = computed(() => $store?.state?.user?.point ?? 0);

    /**
     * 更新用户积分
     */
    const updateUserPoint = () => $store?.dispatch('updatePoints');

    /**
     * 安全地更新积分数据
     * @param useNextTick 是否使用 nextTick 延迟执行
     */
    const safeUpdateIntegralData = (useNextTick = false): void => {
      const executeUpdate = () => {
        if (currentStep.value === FIRST_STEP_INDEX) {
          const component = activeComponentRef.value;
          if (component && typeof updateIntegralData === 'function') {
            updateIntegralData(component as FirstStepComponentShape);
          }
        }
      };

      if (useNextTick) {
        nextTick(executeUpdate);
      } else {
        executeUpdate();
      }
    };

    /**
     * 处理项目数据加载完成事件
     * 解决项目初始化时已开启AI改嘴型但积分计算不正确的问题
     */
    const handleDataLoaded = (_data: EditProjectViewData): void => {
      // 当数据加载完成且当前在第一步时，重新获取积分
      // 使用 nextTick 确保组件完全渲染后再获取积分
      // 这样可以正确检测到已开启的AI改嘴型状态
      safeUpdateIntegralData(true);
    };

    // 使用项目数据管理
    const {
      currentStep,
      currentComponentType,
      loading,
      formData,
      currentComponent,
      stepChange,
      goToNextStep,
      currentProjectId,
      hasValidProjectId,
    } = useProjectData({
      initialStep: step,
      initialType: projectType.value,
      templateId: Number(templateId),
      updatePoints: updateUserPoint,
      onDataLoaded: handleDataLoaded,
    });

    // 使用第一步逻辑
    const {
      saveLoading,
      generateLoading,
      generateButtonConfig,
      handleGenerateButtonClick,
      saveProject,
      updateIntegralData,
    } = useFirstStep({
      templateId: Number(templateId),
      projectType: projectType.value,
    });

    // 使用第二步逻辑
    const {
      selectedWorkIds,
      isAllSelected,
      handleSelectedWorksChange,
      handleAllSelectChange,
      handleSaveToWorks: saveToWorks,
    } = useSecondStep();

    // 使用充值功能
    const { handleRecharge: performRecharge } = usePointsRecharge({
      updatePoints: updateUserPoint,
      rechargeUrl: getExternalDynamicUrl().VERSION_BUY,
    });

    /**
     * 刷新作品列表
     * @description 通过EventBus发送刷新事件，避免直接调用子组件方法
     */
    const refreshWorkList = async (): Promise<[Error | null, void | null]> => {
      try {
        eventBus.emit(EVENT_NAMES.REFRESH_WORK_LIST);
        return [null, null];
      } catch (error) {
        console.error('刷新作品列表失败:', error);
        return [error as Error, null];
      }
    };

    // 使用新旧选择弹窗逻辑
    const {
      showNewOldChoiceModal,
      currentRegeneratedWork,
      handleSaveNewFromModal,
      handleSaveAllFromModal,
    } = useNewOldChoiceModal({
      refreshWorkList,
    });

    /**
     * 处理步骤变化事件
     * @param step - 目标步骤索引
     */
    const handleStepChange = (step: number): void => {
      stepChange(step);
    };

    /**
     * 验证操作前置条件
     * @param requiredStep 要求的步骤
     * @param operationName 操作名称（用于错误日志）
     * @returns 是否通过验证
     */
    const validateOperationPreconditions = (
      requiredStep: number,
      operationName: string,
    ): boolean => {
      if (currentStep.value !== requiredStep) {
        return false;
      }

      if (!hasValidProjectId.value) {
        console.error(`项目ID无效，无法执行${operationName}操作`);
        return false;
      }

      return true;
    };

    /**
     * 处理生成按钮点击事件的核心逻辑
     */
    const executeGenerateClick = async (): Promise<void> => {
      if (!validateOperationPreconditions(FIRST_STEP_INDEX, '生成')) return;

      const component = activeComponentRef.value;
      if (!component) return;

      const result = await handleGenerateButtonClick(
        component as FirstStepComponentShape,
        () => {
          updateUserPoint();
          goToNextStep();
        },
        currentProjectId.value,
        generateButtonConfig.value.num,
        userPoint.value,
      );

      // 如果表单验证通过但点数不足，显示点数不足弹窗
      if (result.validationPassed && !result.shouldProceed) {
        insufficientPointsVisible.value = true;
      }
    };

    /**
     * 处理生成按钮点击事件（带防抖）
     */
    const handleGenerateClick = debounce(executeGenerateClick, DEBOUNCE_DELAY);

    /**
     * 处理保存项目事件的核心逻辑
     */
    const executeSaveProject = async (): Promise<void> => {
      if (!validateOperationPreconditions(FIRST_STEP_INDEX, '保存')) return;

      const component = activeComponentRef.value as FirstStepComponentShape;
      if (!component) return;

      // 检查组件是否支持保存模式校验
      if (typeof component.validateAllFormsForSave !== 'function') {
        console.error('组件不支持保存模式校验，回退到普通保存');
        await saveProject(
          component,
          currentProjectId.value,
          generateButtonConfig.value.num,
        );
        return;
      }

      // 使用差异化校验进行保存
      await saveProject(
        component,
        currentProjectId.value,
        generateButtonConfig.value.num,
      );
    };

    /**
     * 处理保存项目事件（使用差异化校验，带防抖）
     */
    const handleSaveProject = debounce(executeSaveProject, DEBOUNCE_DELAY);

    /**
     * 处理保存至作品事件
     */
    const handleSaveToWorks = async (): Promise<void> => {
      if (!validateOperationPreconditions(SECOND_STEP_INDEX, '保存至作品'))
        return;

      const component = activeComponentRef.value;
      if (!component) return;

      // 获取当前组件的作品列表数据
      const workList = component.workList || [];
      await saveToWorks(currentProjectId.value, workList);
    };

    // 监听生成数量变化，重新获取积分（需要传入组件实例以正确检测AI改嘴型状态）
    watch(
      () => generateButtonConfig.value.num,
      () => {
        // 只在第一步时监听数量变化
        safeUpdateIntegralData();
      },
    );

    /**
     * 处理取消按钮点击事件
     */
    const handleCancel = (): void => {
      insufficientPointsVisible.value = false;
    };

    /**
     * 获取当前作品列表
     */
    const currentWorkList = computed(() => {
      const component = activeComponentRef.value;
      return component?.workList || [];
    });

    /**
     * 处理去充值按钮点击事件
     */
    const handleRecharge = (): void => {
      insufficientPointsVisible.value = false;
      // 调用 composable 中的充值处理方法
      performRecharge();
    };

    // ============= 生命周期钩子 =============

    /**
     * 组件挂载时设置 EventBus 监听器
     */
    onMounted(() => {
      // 监听 AI 改嘴型开关变化事件
      eventBus.on(EVENT_NAMES.AI_MOUTH_SHAPE_TOGGLE, () => {
        // 重新计算积分
        safeUpdateIntegralData();
      });
    });

    /**
     * 组件卸载时清理 EventBus 监听器
     */
    onUnmounted(() => {
      // 清理 AI 改嘴型事件监听器
      eventBus.off(EVENT_NAMES.AI_MOUTH_SHAPE_TOGGLE);
    });

    return {
      // 状态
      currentComponentType,
      currentStep,
      saveLoading,
      generateLoading,
      generateButtonConfig,
      loading,
      formData,
      selectedWorkIds,
      isAllSelected,
      activeComponentRef,
      insufficientPointsVisible,
      showNewOldChoiceModal,
      currentRegeneratedWork,

      // 计算属性
      userPoint,
      currentComponent,
      currentProjectId,
      routeProjectId,
      currentWorkList,

      // 方法
      handleStepChange,
      handleGenerateClick,
      handleSaveProject,
      handleSelectedWorksChange,
      handleAllSelectChange,
      handleSaveToWorks,
      handleCancel,
      handleRecharge,
      handleSaveNewFromModal,
      handleSaveAllFromModal,
    };
  },
});
</script>

<style lang="scss" scoped>
@import '@/style/mixins/scrollbar.scss';

.edit-project {
  /* 布局相关 */
  @apply flex flex-col;

  /* 尺寸相关 */
  @apply w-full h-screen min-h-200px min-w-1360px;

  /* 外观相关 */
  @apply bg-[#f5f6f8] overflow-hidden;
}

.edit-project__main {
  /* 布局相关 */
  @apply flex flex-1 justify-center;

  /* 尺寸相关 */
  @apply mx-16px;

  /* 外观相关 */
  @apply bg-white border-rd-16px overflow-y-auto;
  @apply shadow-[0_0_5px_#00000008];

  /* 滚动条样式 */
  @include scrollbar-style;
}

.edit-project__main-loading {
  /* 布局相关 */
  @apply absolute top-1/2 left-1/2;
  @apply transform -translate-x-1/2 -translate-y-1/2;
}
</style>
