import { Ref, ref } from 'vue';
import type Player from 'video.js/dist/types/player';
import { VideoPlayerEventParams } from '../types';
import { logger } from '@/utils/logger';

interface UsePlayerEventsOptions {
  emit: (actionType: VideoPlayerEventParams['actionType']) => void;
  player: Ref<Player | null>;
  isPlaying: Ref<boolean>;
  currentTime: Ref<number>;
  duration: Ref<number>;
  isDragging?: Ref<boolean>; // 可选的拖动状态
}

/**
 * Video.js 事件处理函数类型
 * 所有 Video.js 事件处理函数都是无参数的
 */
type VideoEventHandler = () => void;

/**
 * 播放器事件处理 hook
 */
export function usePlayerEvents({
  emit,
  player,
  isPlaying,
  currentTime,
  duration,
  isDragging,
}: UsePlayerEventsOptions) {
  // 存储事件处理函数的引用，用于清理
  const eventHandlers = ref<Map<string, VideoEventHandler>>(new Map());

  /**
   * 设置播放器事件监听
   */
  const setupPlayerEvents = () => {
    if (!player.value) return;

    const instance = player.value;

    // 清理之前的事件监听器
    cleanupPlayerEvents();

    // 播放事件处理函数
    const handlePlay = () => {
      isPlaying.value = true;
      emit('play');
    };

    // 暂停事件处理函数
    const handlePause = () => {
      isPlaying.value = false;
      emit('pause');
    };

    // 结束事件处理函数
    const handleEnded = () => {
      isPlaying.value = false;
      emit('ended');
    };

    // 错误事件处理函数
    const handleError = () => {
      isPlaying.value = false;
      emit('error');
    };

    // 时间更新事件处理函数
    const handleTimeUpdate = () => {
      if (!instance) return;
      const currentTimeValue = instance.currentTime();
      const durationValue = instance.duration();

      // 只在非拖动状态下更新 currentTime，避免干扰拖动操作
      if (typeof currentTimeValue === 'number' && !isDragging?.value) {
        currentTime.value = currentTimeValue;
      }

      if (typeof durationValue === 'number') {
        duration.value = durationValue;
      }
    };

    // 全屏事件处理函数
    const handleFullscreenChange = () => {
      emit('fullscreen');
    };

    // 注册事件监听器并保存引用
    instance.on('play', handlePlay);
    instance.on('pause', handlePause);
    instance.on('ended', handleEnded);
    instance.on('error', handleError);
    instance.on('timeupdate', handleTimeUpdate);
    instance.on('fullscreenchange', handleFullscreenChange);

    // 保存事件处理函数引用，用于后续清理
    eventHandlers.value.set('play', handlePlay);
    eventHandlers.value.set('pause', handlePause);
    eventHandlers.value.set('ended', handleEnded);
    eventHandlers.value.set('error', handleError);
    eventHandlers.value.set('timeupdate', handleTimeUpdate);
    eventHandlers.value.set('fullscreenchange', handleFullscreenChange);

    logger.debug('🎬 VideoPlayer: 事件监听器已设置');
  };

  /**
   * 清理播放器事件监听
   */
  const cleanupPlayerEvents = () => {
    if (!player.value || eventHandlers.value.size === 0) return;

    const instance = player.value;

    // 移除所有事件监听器
    eventHandlers.value.forEach((handler, eventType) => {
      instance.off(eventType, handler);
    });

    // 清空事件处理函数引用
    eventHandlers.value.clear();

    logger.debug('🧹 VideoPlayer: 事件监听器已清理');
  };

  return {
    setupPlayerEvents,
    cleanupPlayerEvents,
  };
}
