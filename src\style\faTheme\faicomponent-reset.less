/**
 * @fk/faicomponent 组件库样式覆盖
 * 用于自定义组件库的默认样式，以符合业务设计规范
 */

// 定义通用变量
@border-radius-lg: 16px;
@primary-color: #3261FD;
@text-color: #333333;

/* --------------------------------
 * Modal 对话框
 * -------------------------------- */
.fa-modal {
  &-content {
    // 调整弹窗圆角样式
    border-radius: @border-radius-lg;
  }

  &-header {
    // 调整标题的间距 & 弹窗圆角样式
    padding: 18px 28px;
    border-top-left-radius: @border-radius-lg;
    border-top-right-radius: @border-radius-lg;
  }

  &-footer {
    // 调整底部内容间距
    text-align: center;
    padding-top: 12px;
    padding-bottom: 12px;
  }
}

/* --------------------------------
 * Popover 气泡卡片
 * -------------------------------- */
.fa-popover {
  &-inner {
    // 调整气泡卡片的圆角
    border-radius: 8px;
    // 调整气泡卡片的间距
    &-content {
      padding-top: 16px;
      padding-bottom: 16px;
      // 调换主按钮位置，确认在左边，取消在右边
      .fa-popover-buttons {
        display: flex;
        flex-direction: row-reverse;
        justify-content: center;
        align-items: center;
        gap: 8px;
        .fa-btn {
          margin-left: 0;
        }
      }
    }
  }
  &-buttons {
    // 调整气泡卡片的按钮样式
    text-align: center;
  }
}

/* --------------------------------
 * table 表格
 * -------------------------------- */
.fa-table {
  color: @text-color;
  // 筛选菜单项
  &-filter-dropdown-link {
    cursor: pointer;
  }
}
// 新规范皮肤
.fa-table-skin__specification--base {
  // 选中筛选需要高亮显示
  .fa-table-filter-selected {
    svg {
      fill: @primary-color;
    }
  }
}

/* --------------------------------
 * skeleton 骨架屏
 * -------------------------------- */
.fa-skeleton {
  .fa-skeleton-content {
    .fa-skeleton-title {
      margin-top: 0;
      border-radius: 8px;
    }
    .fa-skeleton-paragraph {
      li {
        border-radius: 8px;
      }
    }
  }
}

/* --------------------------------
 * message 全局提示
 * -------------------------------- */
.fa-message  {
  .anticon {
    top: -3px;
  }
}

/* --------------------------------
 * pagination 页码组件
 * -------------------------------- */
.fa-pagination  {
  .anticon {
    svg {
      vertical-align: baseline;
    }
  }
}

/** --------------------------------
  * button 按钮
  * -------------------------------- */
// 设计规范：特殊2 用于深色背景
.fa-btn.special-dark__btn {
  border: none;
  color: @text-color;
  &:hover,
  &:focus {
    opacity: 0.9;
  }
}

/* --------------------------------
 * menu 导航菜单
 * -------------------------------- */
//  新增fa-menu__spec-horizontal类名处理业务样式
.fa-menu.fa-menu__spec-horizontal-round {
  border-radius: 20px 20px 0 0;
}
.fa-menu.fa-menu__spec-horizontal {
  line-height: 57px;
  color: @text-color;
  border-bottom: 1px solid #eee;
  .fa-menu-item:nth-child(2) {
    margin-left: 20px;
  }
  .fa-menu-item {
    margin-right: 32px;
    padding: 0 4px;
    font-size: 15px;
  }
  .fa-menu-item-selected {
    font-weight: bold;
  }
}
.fa-menu__spec-horizontal.fa-menu-horizontal {
  > .fa-menu-item,
  > .fa-menu-submenu,
  > .fa-menu-item:hover,
  > .fa-menu-submenu:hover {
    border-bottom: 3px solid transparent;
  }

  > .fa-menu-item-active,
  > .fa-menu-submenu-active,
  > .fa-menu-item-open,
  > .fa-menu-submenu-open,
  > .fa-menu-item-selected,
  > .fa-menu-submenu-selected {
    border-bottom: 3px solid @primary-color;
  }
}

/* --------------------------------
 * message 全局提示
 * -------------------------------- */
.fa-message-notice-content {
  border-radius: 8px;
}

/** --------------------------------
  * input输入框
  * -------------------------------- */
.fa-input {
  &:hover {
    border-color: @primary-color;
  }
  &:focus {
    border-color: @primary-color;
  }
}

/** --------------------------------
  * textarea文本框
  * -------------------------------- */
.text-editor__textarea.fa-input {
  &:hover {
    border-color: @primary-color;
  }
  &:focus {
    border-color: @primary-color;
  }
}

/** --------------------------------
  * fa-select下拉框
  * -------------------------------- */
.fa-select-selection {
  &:hover {
    border-color: @primary-color;
  }
  &:focus {
    border-color: @primary-color;
  }
}
