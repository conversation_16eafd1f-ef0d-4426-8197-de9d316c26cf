<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" id="&#x6587;&#x4EF6;&#x5939;" width="94" height="94" viewBox="0 0 94 94">
  <defs>
    <linearGradient id="linear-gradient" x1="0.5" x2="0.5" y2="1" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#f8c546"></stop>
      <stop offset="1" stop-color="#ffdf8a"></stop>
    </linearGradient>
    <linearGradient id="linear-gradient-2" x1="0.5" x2="0.5" y2="1" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#ffe99b"></stop>
      <stop offset="1" stop-color="#ffda60"></stop>
    </linearGradient>
    <filter id="&#x77E9;&#x5F62;_13722">
      <feOffset dy="1"></feOffset>
      <feGaussianBlur stdDeviation="0.5" result="blur"></feGaussianBlur>
      <feFlood flood-color="#fff" flood-opacity="0.502" result="color"></feFlood>
      <feComposite operator="out" in="SourceGraphic" in2="blur"></feComposite>
      <feComposite operator="in" in="color"></feComposite>
      <feComposite operator="in" in2="SourceGraphic"></feComposite>
    </filter>
  </defs>
  <g id="&#x7EC4;_14255" data-name="&#x7EC4; 14255" transform="translate(0 2)">
    <g id="&#x7EC4;_14272" data-name="&#x7EC4; 14272" transform="translate(0 0)">
      <path id="&#x8054;&#x5408;_350" data-name="&#x8054;&#x5408; 350" d="M-301-883v-24a8,8,0,0,1,8-8h26a16,16,0,0,1,13.86,8H-215a8,8,0,0,1,8,8v16Z" transform="translate(301 917)" fill="url(#linear-gradient)"></path>
      <path id="&#x77E9;&#x5F62;_13721" data-name="&#x77E9;&#x5F62; 13721" d="M6,0H68a6,6,0,0,1,6,6V6a0,0,0,0,1,0,0H0A0,0,0,0,1,0,6V6A6,6,0,0,1,6,0Z" transform="translate(10 16)" fill="#fff"></path>
      <g data-type="innerShadowGroup">
        <rect id="&#x77E9;&#x5F62;_13722-2" data-name="&#x77E9;&#x5F62; 13722" width="94" height="66" rx="8" transform="translate(0 22)" fill="url(#linear-gradient-2)"></rect>
        <g transform="matrix(1, 0, 0, 1, 0, -2)" filter="url(#&#x77E9;&#x5F62;_13722)">
          <rect id="&#x77E9;&#x5F62;_13722-3" data-name="&#x77E9;&#x5F62; 13722" width="94" height="66" rx="8" transform="translate(0 24)" fill="#fff"></rect>
        </g>
      </g>
      <rect id="&#x77E9;&#x5F62;_13723" data-name="&#x77E9;&#x5F62; 13723" width="94" height="94" rx="2" transform="translate(0 -2)" fill="none"></rect>
    </g>
  </g>
</svg>