import store from '@/store';
import { message as FaMessage } from '@fk/faicomponent';
import { showImageViewer } from '@/components/comm/ImageViewer/index';
import {
  INFO_KEYS,
  SORT_MODE_KEY,
  INPUT_NAME_MAX_LENGTH,
  FOLDER_NAME_MAX_LENGTH,
} from '@/constants/material';
import { SORT_BY_KEY } from '../constants';
import { FileData, FolderData } from '@/types/Material';
import { MaterialUploadFile } from '../types';
import { limitInput, getMaterialFullUrl } from '../utils/index.ts';
import {
  addFolder,
  deleteFolder,
  updateFolder,
  updateResource,
  deleteResource,
  moveResource,
} from '@/api/Material/index';
import {
  uploadFileData,
  createTempFileData,
  updateFileUploadComplete,
} from '../utils/uploadTempStorage';
import {
  commonUploadFiles,
  abortCurrentUpload,
  UPLOAD_TYPE_MAP,
} from '@/utils/upload/commonUpload';
import { UseMaterialBasicUploadStateReturns } from './useMaterialBasicUploadState';
import { UseMaterialBasicUploadUtilsReturns } from './useMaterialBasicUploadUtils';
import { showConfirmModal } from '@/components/comm/ScModalConfirm/index';
import { FILE_TYPES } from '@/constants/fileType';
import { getExternalDynamicUrl } from '@/constants/system';
import { useUploadingFileState } from './useUploadingFileState';

// Emits 类型
type EmitFunction = (
  event: 'update:visible' | 'close' | 'file-choose-over',
  ...args: unknown[]
) => void;

// 返回值类型
export interface UseMaterialBasicUploadEventsReturns {
  onScrollEnd: () => void;
  onOwnerFileSearch: (val: string) => void;
  onFolderChoose: (data: FolderData) => void;
  onFileChoose: (data: FileData) => void;
  onFileUnchoose: (data: FileData) => void;
  onFilePreview: (data: FileData) => void;
  onSelectChange: (e: { type: string; selects: boolean[] }) => void;
  onTriggerUpload: () => Promise<void>;
  onAddFolder: () => Promise<void>;
  onFolderClose: (data: FolderData) => Promise<void>;
  onFileClose: (data: FileData) => Promise<void>;
  onFolderDrop: ({
    folder,
    file,
  }: {
    folder: FolderData;
    file: FileData | FileData[];
  }) => Promise<void>;
  onPopupOk: () => Promise<void>;
  onPopupCancel: () => void;
  onPopupDelete: () => void;
  onFileLabelChange: (e: { data: FileData; label: string }) => Promise<void>;
  onFolderLabelChange: (e: {
    data: FolderData;
    label: string;
  }) => Promise<void>;
  onChoosedFileListSort: (val: MaterialUploadFile[]) => void;
  onSortByChange: (val: keyof typeof SORT_BY_KEY) => void;
  onSortModeChange: (val: SORT_MODE_KEY) => void;
  onSettingOk: (newSetting: {
    originPictureUpload: boolean;
    autoAddWaterMark: boolean;
    showSystemFolder: boolean;
  }) => Promise<void>;
  onCapacityExpand: () => void;
  onOwnerFilePageChange: () => void;
}

/**
 * 处理用户界面事件的函数
 * @param state 组件状态对象
 * @param emit 事件触发函数
 * @param utils 工具方法集合
 * @returns 事件处理方法集合
 */
export function useMaterialBasicUploadEvents(
  state: UseMaterialBasicUploadStateReturns,
  emit: EmitFunction,
  utils: UseMaterialBasicUploadUtilsReturns,
): UseMaterialBasicUploadEventsReturns {
  // 使用上传文件状态管理 hook（单例）
  const {
    cleanupCompletedUploads,
    addFileToUploadingList,
    updateFileUploadProgress,
    getUploadingFileList,
    removeFileFromUploadingList,
  } = useUploadingFileState();

  const {
    ownerFileSearchKeywords,
    currentFolder,
    chosenFileList,
    maxChosenFileCount,
    sortBy,
    sortMode,
    setting,
    folderList,
    fileList,
    acceptList,
    fileUploading,
  } = state;

  const {
    reloadOwnerFile,
    doFileUnchoose,
    doFileChooseOver,
    doFileChoose,
    handlerFileSort,
    doChosenFileUnchoose,
  } = utils;

  const { checkFileChosen } = utils;

  const onScrollEnd = () => {
    console.log('onScrollEnd');
  };

  const onOwnerFileSearch = (val: string) => {
    if (val) {
      ownerFileSearchKeywords.value = val;
    } else if (ownerFileSearchKeywords.value) {
      ownerFileSearchKeywords.value = val;
    }
    reloadOwnerFile();
  };

  const onFolderChoose = (data: FolderData) => {
    ownerFileSearchKeywords.value = '';
    const currentFolderId = data[INFO_KEYS.ID];
    store.commit('materialUpload/setCurrentFolder', currentFolderId);
    // 清理已完成的上传文件
    cleanupCompletedUploads(currentFolderId);
    reloadOwnerFile();
  };

  // 文件取消选择
  const onFileUnchoose = (data: FileData) => {
    doFileUnchoose(data);
  };

  // 文件选择
  const onFileChoose = (data: FileData) => {
    if (checkFileChosen(data)) {
      doFileUnchoose(data);
    } else {
      // 体验优化:只允许选择1张时超过即覆盖,否则弹出提示
      if (
        chosenFileList.value.length >= maxChosenFileCount &&
        maxChosenFileCount === 1
      ) {
        doChosenFileUnchoose(chosenFileList.value[0]);
      }
      if (chosenFileList.value.length < maxChosenFileCount) {
        doFileChoose(data);
      } else {
        doFileChooseOver(data);
      }
    }
  };

  /**
   * 支持圈选文件功能
   * @param e 选择事件
   * - type: 事件类型，'all-cancel'表示取消所有选择，'frame-select'表示框选文件
   * - selects: 选择状态数组，表示某个文件是否被选中
   */
  const onSelectChange = (e: { type: string; selects: boolean[] }) => {
    if (e.type === 'all-cancel') {
      chosenFileList.value = [];
    } else if (e.type === 'frame-select') {
      const list = fileList.value
        .map((item: FileData, index) => {
          if (e.selects[index]) {
            return {
              type: 'owner',
              data: item,
            };
          }
          return null;
        })
        .filter(Boolean);

      const filteredList = list.filter(Boolean) as MaterialUploadFile[];
      if (list.length > maxChosenFileCount) {
        chosenFileList.value = filteredList.slice(0, maxChosenFileCount);
      } else {
        chosenFileList.value = filteredList;
      }
    }
  };

  // 图片预览
  const onFilePreview = (data: FileData) => {
    const previewImg = getMaterialFullUrl(
      data[INFO_KEYS.RES_ID],
      FILE_TYPES.WEBP || data[INFO_KEYS.FILE_TYPE],
      'user',
    );
    showImageViewer({ imgList: [previewImg] });
  };

  // 上传资源
  const onTriggerUpload = async () => {
    if (fileUploading.value) {
      FaMessage.error('文件正在上传中，请稍后再试');
      return;
    }
    commonUploadFiles({
      acceptList: acceptList.value,
      currentFolder: currentFolder.value,
      onUploadStart: (files: File[]) => {
        const fileListTmp: FileData[] = [];
        // 遍历文件列表 转换数据 并添加到列表中
        files.forEach(file => {
          const fileType =
            UPLOAD_TYPE_MAP[file.type as keyof typeof UPLOAD_TYPE_MAP];
          const tmpFile = createTempFileData(
            file,
            currentFolder.value,
            fileType as FILE_TYPES,
          );
          fileListTmp.unshift(tmpFile);
        });
        addFileToUploadingList(currentFolder.value, fileListTmp);
        fileUploading.value = true;
      },
      onProgress: (file, percent) => {
        // 更新文件上传进度
        updateFileUploadProgress(currentFolder.value, file, percent);
      },
      onSuccess: (uploadFileData: uploadFileData, file: File) => {
        updateFileUploadComplete(
          getUploadingFileList(currentFolder.value),
          file,
          uploadFileData,
        );
        store.dispatch('materialUpload/updateSpaceUsage');
      },
      onAllComplete: () => {
        fileUploading.value = false;
      },
      onError: (file: File) => {
        removeFileFromUploadingList(currentFolder.value, file);
        fileUploading.value = false;
      },
    });
  };

  const onAddFolder = async () => {
    const reg = /新建文件夹\((\d+)\)/;
    let maxNum = 0;
    folderList.value.forEach((folder: FolderData) => {
      const matching = String(folder[INFO_KEYS.NAME]).match(reg);
      if (matching && matching[1]) {
        maxNum = Math.max(maxNum, parseInt(matching[1], 10));
      }
    });
    const preset = {
      parentId: currentFolder.value,
      name: `新建文件夹(${maxNum + 1})`,
    };
    const [err, res] = await addFolder(preset);
    if (err) {
      FaMessage.error(err.message || '创建文件夹失败');
      return;
    }

    folderList.value.splice(0, 0, {
      ...res.data,
      createTime: new Date().getTime(),
    });
  };

  const onFolderClose = async (data: FolderData) => {
    const id = data[INFO_KEYS.ID];
    showConfirmModal({
      content: '删除文件夹，将同时删除该文件夹下的所有文件，确定删除？',
      okType: 'danger',
      onOk: async () => {
        const [err] = await deleteFolder({ id });
        if (err) {
          FaMessage.error(err.message || '删除失败');
          return;
        }
        const delIdx = folderList.value.findIndex(
          (item: FolderData) => item[INFO_KEYS.ID] === id,
        );
        folderList.value.splice(delIdx, 1);
        FaMessage.success('删除成功');
        store.dispatch('materialUpload/updateSpaceUsage');
      },
    });
  };

  // 删除文件
  const onFileClose = async (data: FileData) => {
    const id = data[INFO_KEYS.ID];
    showConfirmModal({
      content: `是否删除文件${data[INFO_KEYS.NAME]}？`,
      okType: 'danger',
      onOk: async () => {
        const [err] = await deleteResource({
          resIds: [data[INFO_KEYS.RES_ID]],
        });
        if (err) {
          FaMessage.error(err.message || '删除失败');
          return;
        }
        const delIdx = fileList.value.findIndex(
          (item: FileData) => item[INFO_KEYS.ID] === id,
        );
        fileList.value.splice(delIdx, 1);
        FaMessage.success('删除成功');
        store.dispatch('materialUpload/updateSpaceUsage');
        doFileUnchoose(data);
      },
    });
  };

  // 文件拖拽到文件夹
  const onFolderDrop = async ({
    folder,
    file,
  }: {
    folder: FolderData;
    file: FileData | FileData[];
  }) => {
    const parentId = folder[INFO_KEYS.ID];
    let resIds = [],
      ids = [];
    // 批量拖拽
    if (Array.isArray(file)) {
      resIds = file.map(item => item[INFO_KEYS.RES_ID]);
      ids = file.map(item => item[INFO_KEYS.ID]);
    } else {
      // 单个移动
      resIds = [file[INFO_KEYS.RES_ID]];
      ids = [file[INFO_KEYS.ID]];
    }
    await onFolderDropFlie({ parentId, resIds, ids });
  };

  const onFolderDropFlie = async ({
    parentId,
    resIds,
    ids,
  }: {
    parentId: number;
    resIds: string[];
    ids: number[];
  }) => {
    const [err] = await moveResource({
      parentId,
      resIds,
    });
    if (err) {
      FaMessage.error(err.message || '移动失败');
      return;
    }
    // 从本文件夹中删除
    ids.forEach((id: number) => {
      const idx = fileList.value.findIndex(
        (f: FileData) => f[INFO_KEYS.ID] === id,
      );
      fileList.value.splice(idx, 1);
    });
    for (let i = 0; i < fileList.value.length; i++) {
      if (fileList.value[i][INFO_KEYS.FOLDER_ID] === parentId) {
        fileList.value.splice(i, 1);
        i--;
      }
    }
    FaMessage.success('移动成功');
  };

  // 点击确定
  const onPopupOk = async () => {
    if (!chosenFileList.value.length) {
      FaMessage.error('未选择文件');
      return;
    }
    // 筛选出需要转换的选中的文件列表
    state.onConfirm && state.onConfirm(chosenFileList.value);
    emit('close');
  };

  // 取消
  const onPopupCancel = () => {
    if (fileUploading.value) {
      showConfirmModal({
        content: '文件正在上传中，退出将取消上传？',
        okType: 'danger',
        onOk: async () => {
          abortCurrentUpload();
          emit('close');
        },
      });
    } else {
      emit('close');
    }
  };

  // 清空已选的文件
  const onPopupDelete = () => {
    if (chosenFileList.value.length) {
      showConfirmModal({
        content: '是否清空所有已选文件？',
        okType: 'danger',
        onOk: async () => {
          chosenFileList.value = [];
        },
      });
    }
  };

  // 修改文件名字
  const onFileLabelChange = async (e: { data: FileData; label: string }) => {
    const name = e.label.trim();
    if (
      name === e.data[INFO_KEYS.NAME] ||
      limitInput(name, INPUT_NAME_MAX_LENGTH)
    )
      return;

    const [err] = await updateResource({
      resId: e.data[INFO_KEYS.RES_ID],
      name,
    });
    if (err) {
      FaMessage.error(err.message || '修改失败');
      return;
    }
    e.data[INFO_KEYS.NAME] = name;
    FaMessage.success('修改成功');
  };

  // 修改文件夹名字
  const onFolderLabelChange = async (e: {
    data: FolderData;
    label: string;
  }) => {
    const name = e.label.trim();
    if (
      name === e.data[INFO_KEYS.NAME] ||
      limitInput(name, FOLDER_NAME_MAX_LENGTH)
    )
      return;

    const preset = {
      id: e.data[INFO_KEYS.ID],
      name,
    };
    const [err] = await updateFolder(preset);
    if (err) {
      FaMessage.error(err.message || '修改失败');
      return;
    }
    e.data[INFO_KEYS.NAME] = preset.name;
    FaMessage.success('修改成功');
  };

  // 右侧已选文件拖拽排序
  const onChoosedFileListSort = (val: MaterialUploadFile[]) => {
    chosenFileList.value = val;
  };

  const onSortByChange = (val: string) => {
    sortBy.value = val;
    handlerFileSort();
  };

  const onSortModeChange = (val: SORT_MODE_KEY) => {
    sortMode.value = val;
    handlerFileSort();
  };

  // 齿轮设置 - 确认
  const onSettingOk = async (newSetting: {
    originPictureUpload: boolean;
    autoAddWaterMark: boolean;
    showSystemFolder: boolean;
  }) => {
    setting.value = newSetting;
  };

  // 扩容点击事件
  const onCapacityExpand = () => {
    const versionBuyUrl = getExternalDynamicUrl().VERSION_BUY;
    window.open(versionBuyUrl, '_blank');
  };

  // 暂时不开启分页 - 所以无须设置切换页码回调
  const onOwnerFilePageChange = () => {
    console.log('onOwnerFilePageChange');
  };

  return {
    onScrollEnd,
    onOwnerFileSearch,
    onFolderChoose,
    onFileChoose,
    onFileUnchoose,
    onFilePreview,
    onSelectChange,
    onTriggerUpload,
    onAddFolder,
    onFolderClose,
    onFileClose,
    onFolderDrop,
    onPopupOk,
    onPopupCancel,
    onPopupDelete,
    onFileLabelChange,
    onFolderLabelChange,
    onChoosedFileListSort,
    onSortByChange,
    onSortModeChange,
    onSettingOk,
    onCapacityExpand,
    onOwnerFilePageChange,
  } as const;
}
