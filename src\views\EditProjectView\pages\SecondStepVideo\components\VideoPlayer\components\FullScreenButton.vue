<template>
  <button
    class="fullscreen-button"
    :class="{
      'fullscreen-button--active': isFullscreen,
      'fullscreen-button--disabled': isDisabled,
    }"
    @click="$emit('toggle')"
    :disabled="isDisabled"
  >
    <Icon type="quanping" class="w-[20px] h-[20px]" />
  </button>
</template>

<script lang="ts">
import { defineComponent } from 'vue';

export default defineComponent({
  name: 'FullScreenButton',
  props: {
    /** 是否处于全屏状态 */
    isFullscreen: {
      type: Boolean,
      required: true,
    },
    /** 是否禁用按钮 */
    isDisabled: {
      type: Boolean,
      default: false,
    },
  },
  emits: ['toggle'],
});
</script>

<style lang="scss">
.fullscreen-button {
  /* 布局相关 */
  @apply flex items-center justify-center;
  @apply absolute right-12px bottom-12px;

  /* 尺寸相关 */
  @apply w-40px h-40px;

  /* 外观相关 */
  @apply bg-white border-none border-rd-8px;
  @apply shadow-[0_3px_6px_#0000000f];

  /* 文字相关 */
  @apply text-[#999];

  /* 交互相关 */
  @apply cursor-pointer;

  /* 层级相关 - 确保显示在视频容器之上 */
  @apply zi-video-controls-1;

  /* 动画相关 */
  @apply transition-all duration-300 ease-out;
  @apply hover:opacity-80 hover:text-[#666];
  @apply hover:shadow-[0_6px_12px_#00000015];
  @apply active:scale-95;

  &.fullscreen-button--disabled {
    /* 禁用状态样式 */
    @apply opacity-50 cursor-not-allowed;
    @apply hover:opacity-50 hover:text-[#999] cursor-not-allowed;
    @apply hover:shadow-[0_3px_6px_#0000000f];
  }
}
</style>
