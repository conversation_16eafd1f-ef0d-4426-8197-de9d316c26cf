# 视频播放器进度条拖拽功能修复

## 问题描述

VideoControls组件的进度条在拖拽过程中存在交互问题：当用户按下鼠标开始拖拽进度条后，如果鼠标移出进度条区域，拖拽操作会立即中断。这导致用户体验不佳，因为用户在快速拖拽时很容易意外移出进度条范围。

## 问题原因

原始实现中，事件监听器（`@mousemove`、`@mouseup`、`@mouseleave`）都绑定在进度条元素上：

```vue
<!-- 原始实现 - 有问题的版本 -->
<div
  class="video-controls__progress-wrapper"
  ref="progressRef"
  @mousedown="handleSeek"
  @mousemove="handleSeeking"
  @mouseup="handleSeekEnd"
  @mouseleave="handleSeekEnd"  <!-- 问题所在：鼠标离开时中断拖拽 -->
  :class="{ disabled: isDisabled }"
>
```

当鼠标移出进度条区域时，`@mouseleave` 事件会触发 `handleSeekEnd()`，导致拖拽中断。

## 解决方案

### 1. 移除局部事件监听器

将进度条上的 `@mousemove`、`@mouseup`、`@mouseleave` 事件移除，只保留 `@mousedown` 事件：

```vue
<!-- 修复后的实现 -->
<div
  class="video-controls__progress-wrapper"
  ref="progressRef"
  @mousedown="handleProgressMouseDown"
  :class="{ disabled: isDisabled }"
>
```

### 2. 实现全局事件监听

在 `mousedown` 事件开始时，将 `mousemove` 和 `mouseup` 事件监听器绑定到 `document` 对象上：

```typescript
/** 添加全局事件监听器 */
const addGlobalListeners = () => {
  mouseMoveHandler = handleMouseMove;
  mouseUpHandler = handleMouseUp;
  
  document.addEventListener('mousemove', mouseMoveHandler, { passive: false });
  document.addEventListener('mouseup', mouseUpHandler);
};

/** 处理进度条鼠标按下事件 */
const handleProgressMouseDown = (event: MouseEvent) => {
  if (props.isDisabled) return;

  const { time, percentage } = calculatePositionFromEvent(event);
  
  // 开始拖拽状态
  isDragging.value = true;
  
  // 防止拖拽时选中文本
  document.body.style.userSelect = 'none';
  
  // 更新悬浮显示
  updateHoverDisplay(percentage, time);
  
  // 添加全局事件监听器
  addGlobalListeners();
  
  // 防止事件冒泡
  event.preventDefault();
};
```

### 3. 正确清理事件监听器

在 `mouseup` 事件触发时和组件卸载时，移除全局事件监听器：

```typescript
/** 移除全局事件监听器 */
const removeGlobalListeners = () => {
  if (mouseMoveHandler) {
    document.removeEventListener('mousemove', mouseMoveHandler);
    mouseMoveHandler = null;
  }
  if (mouseUpHandler) {
    document.removeEventListener('mouseup', mouseUpHandler);
    mouseUpHandler = null;
  }
};

/** 处理鼠标抬起事件（全局监听） */
const handleMouseUp = (event: MouseEvent) => {
  if (!isDragging.value || props.isDisabled) return;

  const { time } = calculatePositionFromEvent(event);
  
  // 发送最终的seek事件
  emit('seek', time);
  
  // 结束拖拽状态
  isDragging.value = false;
  
  // 恢复文本选择
  document.body.style.userSelect = '';
  
  // 移除全局事件监听器
  removeGlobalListeners();
};

// 组件卸载时清理事件监听器
onUnmounted(() => {
  removeGlobalListeners();
  // 恢复文本选择
  document.body.style.userSelect = '';
});
```

## 修复效果

### 修复前
- 鼠标移出进度条区域时拖拽立即中断
- 用户体验不佳，难以进行精确的进度控制

### 修复后
- 用户按下鼠标开始拖拽后，即使鼠标移出进度条区域，拖拽操作也能继续
- 只有当用户松开鼠标按键（mouseup事件）时，拖拽操作才结束
- 在拖拽过程中，鼠标在整个页面范围内移动都能够更新进度条位置
- 正确处理边界情况和内存泄漏防护

## 测试验证

创建了完整的测试套件来验证修复效果：

- ✅ 基本拖拽功能测试
- ✅ 边界位置处理测试
- ✅ 禁用状态测试
- ✅ 全局事件监听器管理测试
- ✅ 内存泄漏防护测试

所有测试均通过，确保修复的可靠性。

## 涉及文件

- `src/views/EditProjectView/pages/SecondStepVideo/components/VideoPlayer/components/VideoControls.vue` - 主要修复文件
- `src/views/EditProjectView/pages/SecondStepVideo/components/VideoPlayer/components/__tests__/VideoControls.test.ts` - 测试文件

## 技术要点

1. **全局事件监听**: 使用 `document.addEventListener` 确保拖拽在整个页面范围内有效
2. **内存管理**: 正确添加和移除事件监听器，防止内存泄漏
3. **用户体验**: 防止拖拽时选中文本，提供流畅的交互体验
4. **边界处理**: 正确限制拖拽位置在有效范围内
5. **状态管理**: 合理管理拖拽状态和相关数据

这次修复显著改善了视频播放器的用户体验，使进度条拖拽功能更加稳定和易用。
