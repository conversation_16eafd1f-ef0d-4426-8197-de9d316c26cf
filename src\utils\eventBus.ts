/**
 * 简单的事件总线实现
 * 用于组件间的事件通信
 */

type EventCallback = (...args: unknown[]) => void;

class EventBus {
  private events: Map<string, EventCallback[]> = new Map();

  /**
   * 监听事件
   * @param event 事件名称
   * @param callback 回调函数
   */
  on(event: string, callback: EventCallback): void {
    if (!this.events.has(event)) {
      this.events.set(event, []);
    }
    this.events.get(event)!.push(callback);
  }

  /**
   * 移除事件监听
   * @param event 事件名称
   * @param callback 回调函数
   */
  off(event: string, callback?: EventCallback): void {
    if (!this.events.has(event)) return;

    if (callback) {
      const callbacks = this.events.get(event)!;
      const index = callbacks.indexOf(callback);
      if (index > -1) {
        callbacks.splice(index, 1);
      }
    } else {
      this.events.delete(event);
    }
  }

  /**
   * 触发事件
   * @param event 事件名称
   * @param args 参数
   */
  emit(event: string, ...args: unknown[]): void {
    if (!this.events.has(event)) return;

    const callbacks = this.events.get(event)!;
    callbacks.forEach(callback => {
      try {
        callback(...args);
      } catch (error) {
        console.error(`EventBus error in event "${event}":`, error);
      }
    });
  }

  /**
   * 监听一次性事件
   * @param event 事件名称
   * @param callback 回调函数
   */
  once(event: string, callback: EventCallback): void {
    const onceCallback = (...args: unknown[]) => {
      callback(...args);
      this.off(event, onceCallback);
    };
    this.on(event, onceCallback);
  }

  /**
   * 清除所有事件监听
   */
  clear(): void {
    this.events.clear();
  }
}

// 创建全局事件总线实例
export const eventBus = new EventBus();

// 导出事件名称常量
export const EVENT_NAMES = {
  // 新旧选择弹窗相关事件
  SHOW_NEW_OLD_CHOICE_MODAL: 'showNewOldChoiceModal',
  HIDE_NEW_OLD_CHOICE_MODAL: 'hideNewOldChoiceModal',
  SAVE_NEW_WORK: 'saveNewWork',
  SAVE_ALL_WORKS: 'saveAllWorks',
  // 作品列表刷新事件
  REFRESH_WORK_LIST: 'refreshWorkList',
  // 作品操作事件
  WORK_REMOVE_REQUEST: 'workRemoveRequest',
  WORK_SAVE_REQUEST: 'workSaveRequest',
  // 作品操作响应事件
  WORK_REMOVE_SUCCESS: 'workRemoveSuccess',
  WORK_REMOVE_ERROR: 'workRemoveError',
  WORK_SAVE_SUCCESS: 'workSaveSuccess',
  WORK_SAVE_ERROR: 'workSaveError',
  // 视频播放控制事件
  STOP_CURRENT_VIDEO: 'stopCurrentVideo',
  // 图片预览切换事件
  IMAGE_PREVIEW_CHANGE: 'imagePreviewChange',
  // 轮播图切换事件
  CAROUSEL_CHANGE: 'carouselChange',
  // 图文编辑弹窗相关事件
  CHANGE_RATIO: 'changeRatio',
  CURRENT_SPACE_CHANGE: 'currentSpaceChange',
  DISABLED_SAVE_BUTTON: 'disabledSaveButton',
  RESET_ACTIVE_IMAGE_INDEX: 'resetActiveImageIndex',
  // 数据保护相关事件
  CHECK_UNSAVED_CHANGES: 'checkUnsavedChanges',
  UNSAVED_CHANGES_RESPONSE: 'unsavedChangesResponse',
  // UI 临时更新事件
  TEMP_UPDATE_TOTAL_ITEMS: 'TEMP_UPDATE_TOTAL_ITEMS',
  // AI 改嘴型功能事件
  AI_MOUTH_SHAPE_TOGGLE: 'aiMouthShapeToggle',
} as const;

export type EventName = (typeof EVENT_NAMES)[keyof typeof EVENT_NAMES];

// 事件数据类型定义
export interface WorkRemoveRequestData {
  workId: number;
}

export interface WorkSaveRequestData {
  workIds: number[];
  isBatchSave?: boolean;
}

export interface WorkOperationSuccessData {
  workIds: number[];
  message?: string;
}

export interface WorkOperationErrorData {
  workIds: number[];
  error: Error;
  message?: string;
}

export interface StopCurrentVideoData {
  /** 当前作品ID，用于识别需要停止的视频实例 */
  workId: number;
}

export interface TempUpdateTotalItemsData {
  /** 操作类型：增加或减少 */
  operation: 'decrease' | 'increase';
  /** 变更数量 */
  amount: number;
}

export interface CheckUnsavedChangesData {
  /** 请求ID，用于匹配响应 */
  requestId: string;
  /** 当前步骤索引 */
  currentStep: number;
}

export interface UnsavedChangesResponseData {
  /** 请求ID，用于匹配请求 */
  requestId: string;
  /** 是否有未保存的更改 */
  hasUnsavedChanges: boolean;
}

/**
 * AI 改嘴型开关切换事件数据
 */
export interface AiMouthShapeToggleData {
  /** 表单字段名 */
  prop: string;
  /** 开关状态 */
  value: boolean;
  /** 表单项配置对象 */
  formItem: {
    prop: string;
    hasModifyMouth?: boolean;
    openModifyMouth?: boolean;
    [key: string]: unknown;
  };
}
