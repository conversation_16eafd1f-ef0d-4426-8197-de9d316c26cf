import { GET } from '@/api/request';

export const getUserInfo = () => {
  // 从 URL 中获取参数
  const urlParams = new URLSearchParams(window.location.search);
  const _sourceBiz = urlParams.get('source_biz');
  const _openSource = urlParams.get('open_source');

  // 组装可选参数
  const params: Record<string, string> = {};
  if (_sourceBiz) params._sourceBiz = _sourceBiz;
  if (_openSource) params._openSource = _openSource;

  return GET<{
    /** 企业账号（凡科网账号） */
    aAcct: string;
    /** 企业名 */
    acctName: string;
    /** 用户名 */
    staffName: string;
    /** 头像URL */
    avatarUrl: string;
    /** 版本标识 */
    version: number;
    /** 过期时间戳 */
    expireTime: number;
    /** 是否为内部用户 */
    isStaff: boolean;
    /** 用户AID标识 */
    aid: number;
    /** 是否有购买权限 */
    buyAuth: boolean;
    /** 是否为boss账号 */
    isBoss: boolean;
    /** 是否初始开通速创 */
    isFirstOpen: boolean;
  }>('/api/scProf/getProfInfo', params);
};

export const getPoint = async () => {
  const [err, res] = await GET<number>('/api/scProf/getPoint');
  if (err) {
    return [err, null] as const;
  }
  return [null, { ...res, data: { point: res.data } }] as const;
};

export const logoutReq = () => {
  return GET('/api/scProf/logout');
};
