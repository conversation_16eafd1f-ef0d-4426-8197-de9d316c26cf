import store from '@/store';
import { upload as faiupload } from '@fk/faiupload';
import { getAccessKey } from '@/api/Material/index';
import {
  getFileSuffix,
  transMimeTypes,
} from '@/components/MaterialBasicUpload/utils/index.ts';
import {
  validateCapacity,
  validateFileSize,
  validateFileType,
  validateImageResolution,
} from '@/utils/upload/uploadValidations';
import { message as FaMessage } from '@fk/faicomponent';
import { VERSION_CAPACITY_LIMIT_TIP, VERSION } from '@/constants/version';
import { showVersionMsg } from '@/utils/version';
import { VIDEO_UPLOAD_LIST, MIMETYPE_KEY } from '@/constants/material';
import { FILE_TYPES } from '@/constants/fileType';
import { uploadFileData } from '@/components/MaterialBasicUpload/utils/uploadTempStorage';
import { runUploadQueue } from '@/utils/upload/uploadQueue';

// 常量定义
const DEFAULT_FOLDER_ID = 0; // 默认文件夹id
const UPLOAD_PROGRESS_DEFAULT = 0;

interface ErrorObject {
  code: number; // 请求错误状态码，只有在 err.isRequestError 为 true 的时候才有效
  message: string; // 错误信息，包含错误码，当后端返回提示信息时也会有相应的错误信息
  isRequestError: boolean; // 用于区分是否 xhr 请求错误；当 xhr 请求出现错误并且后端通过 HTTP 状态码返回了错误信息时，该参数为 true；否则为 undefined
}

interface UploadResult {
  file: uploadFileData;
  success: boolean;
  error?: ErrorObject;
}

interface UploadProgress {
  chunks?: {
    loaded: number;
    total: number;
    percent: number;
  };
  total: {
    loaded: number;
    total: number;
    percent: number;
  };
}

// 全局AbortController实例
let globalAbortController: AbortController | null = null;

export const UPLOAD_TYPE_MAP = {
  'video/mp4': FILE_TYPES.MP4,
  'video/mov': FILE_TYPES.MOV,
  'image/jpeg': FILE_TYPES.JPEG,
  'image/png': FILE_TYPES.PNG,
  'image/gif': FILE_TYPES.GIF,
  'image/bmp': FILE_TYPES.BMP,
} as const;

/**
 * 中止当前正在进行的文件上传
 * @returns {boolean} 是否成功中止上传
 */
export function abortCurrentUpload(): boolean {
  if (globalAbortController) {
    globalAbortController.abort();
    globalAbortController = null;
    console.log('-------【文件上传】上传已中止');
    return true;
  }
  return false;
}

export interface CommonUploadOptions {
  acceptList: string[];
  currentFolder?: number;
  onProgress?: (file: File, percent: number) => void;
  onSuccess?: (result: uploadFileData, file: File) => void;
  onError?: (file: File, error: ErrorObject | unknown) => void;
  /**
   * 所有文件上传完成后回调
   * @param results 每个文件的上传结果 { file, success, result, error }
   */
  onAllComplete?: (results: Array<UploadResult>) => void;
  onUploadStart?: (files: File[]) => void; // 修改类型定义，接收文件列表参数
}

/**
 * 创建文件输入元素
 * @param acceptList 接受的文件类型列表
 * @returns {HTMLInputElement} 文件输入元素
 */
function createFileInput(acceptList: string[]): HTMLInputElement {
  const input = document.createElement('input');
  input.type = 'file';
  input.multiple = true;
  input.accept = transMimeTypes(acceptList).join(',');
  input.style.display = 'none';
  return input;
}

/**
 * 上传单个文件
 * @param file 文件对象
 * @param currentFolder 当前文件夹ID
 * @param onProgress 进度回调
 * @param onSuccess 成功回调
 * @param onError 错误回调
 * @param signal 中止信号
 * @returns {Promise<UploadResult>} 上传结果
 */
async function uploadSingleFile(
  file: File,
  currentFolder: number,
  onProgress?: (file: File, percent: number) => void,
  onSuccess?: (result: uploadFileData, file: File) => void,
  onError?: (file: File, error: ErrorObject | unknown) => void,
  signal?: AbortSignal,
): Promise<UploadResult> {
  try {
    // 获取上传凭证
    const preset: { folderId: number } = { folderId: currentFolder };
    const [err, res] = await getAccessKey(preset);

    if (err) {
      const error = { message: err.message || '获取上传凭证失败' };
      FaMessage.error(error.message);
      throw new Error(error.message);
    }

    const accessKey = res?.data?.[0];
    if (!accessKey) {
      throw new Error('获取上传凭证失败：凭证为空');
    }

    const putExtra = {
      params: {
        fileName: file.name,
        accessKey,
        copyUseMoveFileByName: true,
      },
      mimeType: null,
    };

    const ext = getFileSuffix(file.name) || '';
    // 音频视频走断点续传，图片走直传
    const forceDirect = !VIDEO_UPLOAD_LIST.includes(ext as MIMETYPE_KEY);
    const config = { forceDirect };

    const observable = faiupload(file, accessKey, putExtra, config);

    return new Promise<UploadResult>((resolve, reject) => {
      // 监听中止信号
      signal?.addEventListener('abort', () => {
        reject(new Error('上传已中止'));
      });

      const observer = {
        next: (res: UploadProgress) => {
          const percent = res.total?.percent || UPLOAD_PROGRESS_DEFAULT;
          onProgress?.(file, percent);
        },
        error: (uploadErr: ErrorObject) => {
          const errorMessage = `${file.name}上传失败，${uploadErr.message}`;
          FaMessage.error(errorMessage);
          onError?.(file, uploadErr);
          reject(uploadErr);
        },
        complete: (uploadFileData: uploadFileData) => {
          onSuccess?.(uploadFileData, file);
          resolve({ file: uploadFileData, success: true });
        },
      };

      observable.subscribe(observer);
    });
  } catch (error) {
    console.error('上传文件失败:', error);
    onError?.(file, error);
    return {
      file: {} as uploadFileData,
      success: false,
      error: error as ErrorObject,
    };
  }
}

/**
 * 通用文件上传方法
 * @param options 上传配置
 * @param options.acceptList 接受的文件类型列表
 * @param options.currentFolder 当前文件夹id
 * @param options.onProgress 上传进度回调
 * @param options.onSuccess 上传成功回调
 * @param options.onError 上传失败回调
 * @param options.onAllComplete 所有文件上传完成后回调
 * @param options.onUploadStart 上传开始回调
 * @returns {Promise<void>}
 */
export async function commonUploadFiles(
  options: CommonUploadOptions,
): Promise<void> {
  const {
    acceptList,
    currentFolder = DEFAULT_FOLDER_ID,
    onProgress,
    onSuccess,
    onError,
    onAllComplete,
    onUploadStart,
  } = options;

  // 1. 获取当前账号版本和容量信息
  const version: VERSION = store.state.user.version;
  const capacityTotalSize: number =
    store.state.materialUpload.capacityTotalSize; // 总容量(byte)
  const capacityUsedSize: number = store.state.materialUpload.capacityUsedSize; // 已用容量(byte)

  // 2. 文件选择前判断已用容量是否超限
  if (capacityUsedSize >= capacityTotalSize) {
    showVersionMsg(VERSION_CAPACITY_LIMIT_TIP[version], version);
    return;
  }

  // 3. 选择文件
  const input = createFileInput(acceptList);

  /**
   * 功能说明: 上传文件组件
   * http://gitlab.faidev.cc/frontend/faiupload
   */
  return new Promise<void>(resolve => {
    input.onchange = async () => {
      const files = input.files ? Array.from(input.files) : [];
      let totalSize = capacityUsedSize;
      const validFiles: File[] = [];

      // 文件验证
      for (const file of files) {
        // 类型校验
        if (!validateFileType(file, acceptList)) continue;

        // 图片分辨率校验
        const isValidResolution = await validateImageResolution(file);
        if (!isValidResolution) continue;

        // 单文件大小校验（根据文件类型使用不同限制）
        if (!validateFileSize(file, version)) continue;

        totalSize += file.size;
        if (!validateCapacity(totalSize, capacityTotalSize, version)) break;

        validFiles.push(file); // 按顺序添加文件
      }
      document.body.removeChild(input);

      if (!validFiles.length) {
        resolve();
        return;
      }

      // 4. 基于队列的并发上传（最多3个）
      globalAbortController = new AbortController();
      const { signal } = globalAbortController;

      onUploadStart?.(validFiles);

      await runUploadQueue(
        validFiles,
        currentFolder,
        uploadSingleFile,
        onProgress,
        onSuccess,
        onError,
        onAllComplete,
        signal,
      );

      globalAbortController = null;
      resolve();
    };

    document.body.appendChild(input);
    input.click();
  });
}
