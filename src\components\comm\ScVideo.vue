<template>
  <video
    v-bind="$attrs"
    v-on="$listeners"
    ref="videoPlayer"
    class="video-js"
    :src="currSrc"
    @canplay="emit('canplay')"
  ></video>
</template>

<script lang="ts" setup>
// 引入组合式 API
import {
  ref,
  watch,
  defineProps,
  defineEmits,
  onMounted,
  onBeforeUnmount,
} from 'vue';
import { getMaterialFullUrl } from '../MaterialBasicUpload/utils/index.ts';
import videojs from 'video.js';
import type Player from 'video.js/dist/types/player';
import 'video.js/dist/video-js.css';
import langZhData from 'video.js/dist/lang/zh-CN.json';

videojs.addLanguage('zh-CN', langZhData);

/**
 * 透传canplay事件到父组件
 */
const emit = defineEmits(['canplay']);

// 定义组件的 props
const props = defineProps<{
  /** 视频地址或资源id */
  src?: string;
  /** 视频资源类型，后端接口会和资源id一起返回，没有就找后端要 */
  type?: number;
  /** 资源归属，一般不用传值，默认是用户资源。*/
  belong?: 'user' | 'system' | 'oss';
  /** 是否循环播放 */
  loop?: boolean;
}>();

const videoPlayer = ref<HTMLVideoElement | null>(null);

const currSrc = ref('');
let videoJsInstance: Player | null = null;
function setSrc() {
  if (!props.src) return;
  if (props.src.startsWith('http') || props.src.startsWith('//')) {
    currSrc.value = props.src;
  } else if (props.type !== undefined) {
    const belong = props.belong || 'user';
    currSrc.value = getMaterialFullUrl(props.src, props.type, belong);
  }
  if (videoPlayer.value) {
    videoJsInstance = videojs(videoPlayer.value, {
      languages: 'zh-CN',
      controlBar: {
        volumePanel: {
          inline: false,
        },
        pictureInPictureToggle: false,
      },
      bigPlayButton: false,
      controls: true,
      loop: props.loop,
      autoplay: true,
    });
  }
}
onMounted(setSrc);
onBeforeUnmount(() => {
  if (videoJsInstance) {
    videoJsInstance.dispose();
  }
});
watch(
  () => [props.src, props.type, props.belong],
  () => {
    setSrc();
  },
);
</script>

<style lang="scss" scoped>
.video-js {
  ::v-deep .vjs-control-bar {
    @apply box-content pt-[24px] pb-[8px];
    background: linear-gradient(180deg, #00000000 0%, #000 100%);
  }
  .vjs-tech {
    @apply cursor-pointer;
  }
}
</style>
