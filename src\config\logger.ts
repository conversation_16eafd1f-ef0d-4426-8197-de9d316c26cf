/**
 * 日志配置文件
 * 统一管理应用的日志设置
 */

import { configureLogger, LogLevel } from '@/utils/logger';

// 从URL查询参数、环境变量或本地存储获取日志配置
const getLogConfig = () => {
  // 首先检查URL查询参数中的log参数
  const urlParams = new URLSearchParams(window.location.search);
  const logParam = urlParams.get('log');

  if (logParam !== null) {
    const isLogEnabled = logParam === '1';

    if (isLogEnabled) {
      // URL参数log=1，启用所有日志
      return {
        level: LogLevel.DEBUG,
        enableWorkOperations: true,
        enableApiRequests: true,
        enableCacheOperations: true,
        enablePerformance: true,
        enableGrouping: true,
      };
    } else {
      // URL参数log=0，禁用所有日志
      return {
        level: LogLevel.OFF,
        enableWorkOperations: false,
        enableApiRequests: false,
        enableCacheOperations: false,
        enablePerformance: false,
        enableGrouping: false,
      };
    }
  }

  // 其次从 localStorage 获取用户设置
  const userConfig = localStorage.getItem('logger-config');
  if (userConfig) {
    try {
      return JSON.parse(userConfig);
    } catch (_e) {
      console.warn('解析日志配置失败，使用默认配置');
    }
  }

  // 默认配置
  return {
    level: import.meta.env.DEV ? LogLevel.DEBUG : LogLevel.WARN,
    enableWorkOperations: import.meta.env.DEV,
    enableApiRequests: import.meta.env.DEV,
    enableCacheOperations: import.meta.env.DEV,
    enablePerformance: true,
    enableGrouping: true,
  };
};

// 初始化日志配置
export const initLogger = () => {
  const config = getLogConfig();
  configureLogger(config);

  // 在开发环境下提供全局日志控制方法
  if (import.meta.env.DEV) {
    // @ts-ignore 在开发环境下为调试方便，将日志控制方法挂载到全局对象上
    window.loggerControl = {
      // 启用所有日志
      enableAll: () => {
        const newConfig = {
          level: LogLevel.DEBUG,
          enableWorkOperations: true,
          enableApiRequests: true,
          enableCacheOperations: true,
          enablePerformance: true,
          enableGrouping: true,
        };
        configureLogger(newConfig);
        localStorage.setItem('logger-config', JSON.stringify(newConfig));
        console.log('✅ 已启用所有日志');
      },

      // 禁用所有日志
      disableAll: () => {
        const newConfig = {
          level: LogLevel.OFF,
          enableWorkOperations: false,
          enableApiRequests: false,
          enableCacheOperations: false,
          enablePerformance: false,
          enableGrouping: false,
        };
        configureLogger(newConfig);
        localStorage.setItem('logger-config', JSON.stringify(newConfig));
        console.log('❌ 已禁用所有日志');
      },

      // 只启用作品操作日志
      onlyWorkOperations: () => {
        const newConfig = {
          level: LogLevel.INFO,
          enableWorkOperations: true,
          enableApiRequests: false,
          enableCacheOperations: false,
          enablePerformance: true,
          enableGrouping: true,
        };
        configureLogger(newConfig);
        localStorage.setItem('logger-config', JSON.stringify(newConfig));
        console.log('🎯 只启用作品操作日志');
      },

      // 只启用API请求日志
      onlyApiRequests: () => {
        const newConfig = {
          level: LogLevel.DEBUG,
          enableWorkOperations: false,
          enableApiRequests: true,
          enableCacheOperations: false,
          enablePerformance: true,
          enableGrouping: true,
        };
        configureLogger(newConfig);
        localStorage.setItem('logger-config', JSON.stringify(newConfig));
        console.log('📡 只启用API请求日志');
      },

      // 只启用性能监控
      onlyPerformance: () => {
        const newConfig = {
          level: LogLevel.INFO,
          enableWorkOperations: false,
          enableApiRequests: false,
          enableCacheOperations: false,
          enablePerformance: true,
          enableGrouping: true,
        };
        configureLogger(newConfig);
        localStorage.setItem('logger-config', JSON.stringify(newConfig));
        console.log('⚡ 只启用性能监控');
      },

      // 重置为默认配置
      reset: () => {
        localStorage.removeItem('logger-config');
        const defaultConfig = getLogConfig();
        configureLogger(defaultConfig);
        console.log('🔄 已重置为默认日志配置');
      },

      // 显示当前配置
      showConfig: () => {
        const config = getLogConfig();
        console.table(config);
      },

      // 显示帮助信息
      help: () => {
        console.log(
          `
%c🛠️ 日志控制台帮助

🔗 URL参数控制（优先级最高）：
• ?log=1  - 启用所有日志
• ?log=0  - 禁用所有日志

📋 控制台命令：
• loggerControl.enableAll()      - 启用所有日志
• loggerControl.disableAll()     - 禁用所有日志
• loggerControl.onlyWorkOperations() - 只启用作品操作日志
• loggerControl.onlyApiRequests()    - 只启用API请求日志
• loggerControl.onlyPerformance()    - 只启用性能监控
• loggerControl.reset()          - 重置为默认配置
• loggerControl.showConfig()     - 显示当前配置
• loggerControl.help()           - 显示此帮助信息

💡 示例：
loggerControl.onlyWorkOperations(); // 只看作品操作日志
loggerControl.enableAll();         // 查看所有详细日志
loggerControl.disableAll();        // 关闭所有日志

⚠️ 注意：URL参数优先级最高，会覆盖控制台设置
        `,
          'color: #2196F3; font-weight: bold;',
        );
      },
    };

    // 检查是否通过URL参数控制日志
    const urlParams = new URLSearchParams(window.location.search);
    const logParam = urlParams.get('log');

    if (logParam !== null) {
      const isLogEnabled = logParam === '1';
      console.log(
        `
%c🎯 日志系统已初始化 (通过URL参数控制)

URL参数 log=${logParam} ${isLogEnabled ? '✅ 启用所有日志' : '❌ 禁用所有日志'}
在控制台中输入 loggerControl.help() 查看可用命令`,
        'color: #FF9800; font-weight: bold;',
      );
    } else {
      console.log(
        `
%c🎯 日志系统已初始化

💡 提示：可通过URL参数控制日志开关
  • ?log=1 启用所有日志
  • ?log=0 禁用所有日志
在控制台中输入 loggerControl.help() 查看可用命令`,
        'color: #4CAF50; font-weight: bold;',
      );
    }
  }
};

// 导出配置获取函数
export { getLogConfig };
