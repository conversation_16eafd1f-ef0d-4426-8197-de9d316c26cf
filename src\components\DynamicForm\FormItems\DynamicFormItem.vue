<!-- 动态表单项组件 -->
<template>
  <fa-form-model-item
    :prop="formItem.prop"
    :rules="rules"
    :ref="formItem.prop"
    class="dynamic-form-item"
  >
    <!-- label -->
    <template slot="label">
      <label
        class="dynamic-form-item__label"
        :class="{ 'fa-form-item-required': formItem.required }"
        :title="formItem.hasModifyMouth ? '' : formItem.label"
        >{{ formItem.hasModifyMouth ? '' : formItem.label }}</label
      >
    </template>

    <template slot="extra">
      <!-- AI推荐组件 -->
      <OpenAiTipButton
        v-if="formItem.openAiTip"
        :field-name="formItem.prop"
        :context-data="formValues"
        :dependency-fields="formItem.prompt || []"
        :fields-config="formItem"
        :disabled="formItem.disabled"
        :is-focused="isFocused"
        @select="handleSelectOpenAiTip"
      />
    </template>

    <!-- 输入框类型 -->
    <template v-if="formItem.type === 'input'">
      <div
        class="dynamic-form-item__container dynamic-form-item__container--input"
      >
        <fa-input
          v-model.trim="formValues[formItem.prop]"
          v-bind="formItem.attrs || {}"
          :placeholder="formItem.placeholder"
          :disabled="formItem.disabled"
          size="large"
          @focus="handleFocus"
          @blur="handleBlur"
        />
      </div>
    </template>

    <!-- 选择框类型 -->
    <template v-else-if="formItem.type === 'select'">
      <div
        class="dynamic-form-item__container dynamic-form-item__container--select"
      >
        <fa-select
          v-model="formValues[formItem.prop]"
          v-bind="formItem.attrs || {}"
          size="large"
          :placeholder="formItem.placeholder"
          :disabled="formItem.disabled"
          @focus="handleFocus"
          @blur="handleBlur"
        >
          <fa-select-option
            v-for="option in formItem.options || []"
            :key="option.value"
            :value="option.value"
            >{{ option.label }}</fa-select-option
          >
        </fa-select>
      </div>
    </template>

    <!-- 标签选择框类型 -->
    <template v-else-if="formItem.type === 'selectTags'">
      <div
        class="dynamic-form-item__container dynamic-form-item__container--tags"
      >
        <fa-select
          v-model="formValues[formItem.prop]"
          class="dynamic-form-item__tags-select"
          :style="getTagsSelectStyle"
          size="large"
          dropdownClassName="dynamic-form-item__tags-dropdown-hidden"
          mode="tags"
          :maxTagCount="getTagsLimit"
          :maxTagTextLength="formItem?.maxTagTextLength || 20"
          v-bind="formItem.attrs || {}"
          :placeholder="formItem.placeholder"
          :disabled="formItem.disabled"
          @change="handleTagsChange"
          @inputKeydown="handleTagsInputKeydown"
          @focus="handleFocus"
          @blur="handleBlur"
        >
        </fa-select>
        <!-- 标签计数显示 -->
        <span
          v-if="
            formItem.attrs &&
            formItem.attrs.showCount &&
            formItem.attrs.maxLength
          "
          ref="tagsCountRef"
          class="dynamic-form-item__tags-count"
        >
          {{ getTagsCount }} / {{ formItem.attrs.maxLength }}
        </span>
      </div>
    </template>

    <!-- 日期选择类型 -->
    <template v-else-if="formItem.type === 'date'">
      <fa-date-picker
        v-model="formValues[formItem.prop]"
        v-bind="formItem.attrs || {}"
        size="large"
        :placeholder="formItem.placeholder"
        :disabled="formItem.disabled"
      />
    </template>

    <!-- 复选框类型 -->
    <template v-else-if="formItem.type === 'checkbox'">
      <fa-checkbox
        v-model="formValues[formItem.prop]"
        v-bind="formItem.attrs || {}"
        size="large"
        :disabled="formItem.disabled"
      >
        {{ formItem.label }}
      </fa-checkbox>
    </template>

    <!-- 单选框组类型 -->
    <template v-else-if="formItem.type === 'radio'">
      <fa-radio-group
        v-model="formValues[formItem.prop]"
        v-bind="formItem.attrs || {}"
        size="large"
        :disabled="formItem.disabled"
      >
        <fa-radio
          v-for="option in formItem.options || []"
          :key="option.value"
          :label="option.value"
        >
          {{ option.label }}
        </fa-radio>
      </fa-radio-group>
    </template>

    <!-- 文本域类型 -->
    <template v-else-if="formItem.type === 'textarea'">
      <fa-input
        v-model="formValues[formItem.prop]"
        type="textarea"
        size="large"
        v-bind="formItem.attrs || {}"
        :placeholder="formItem.placeholder"
        :disabled="formItem.disabled"
      />
    </template>

    <!-- 文件上传类型 -->
    <template v-else-if="formItem.type === 'upload'">
      <div class="dynamic-form-item__upload-wrapper">
        <div
          v-if="formItem.hasModifyMouth"
          slot="label"
          class="fa-col fa-col-24 fa-form-item-label dynamic-form-item__upload-header"
        >
          <label
            :title="formItem.label"
            class="dynamic-form-item__upload-label"
            :class="{ 'fa-form-item-required': formItem.required }"
            >{{ formItem.label }}</label
          >
          <!-- AI改嘴型开关 -->
          <span
            v-if="formItem.hasModifyMouth"
            class="dynamic-form-item__mouth-shape"
            :class="{ 'ml-[32px]!': formItem.required }"
          >
            <VersionSwitch
              feature-key="AI_MOUTH_SHAPE"
              :label="mouthShapeText"
              v-model="formItem.openModifyMouth"
              @change="handleMouthShapeToggle"
            />
          </span>
        </div>
        <div
          class="dynamic-form-item__container dynamic-form-item__container--upload"
        >
          <!-- 提示 -->
          <p
            v-show="formItem.hasModifyMouth && formItem.openModifyMouth"
            class="dynamic-form-item__upload-tip"
          >
            {{ mouthShapeTip }}
          </p>
          <p v-if="formItem.description" class="dynamic-form-item__upload-tip">
            {{ formItem.description }}
          </p>

          <CustomUpload
            :key="formItem.prop"
            :type="formItem.mediaType || 'image'"
            :file-list="getFileList"
            :disabled="formItem.disabled"
            :max-length="formItem.maxLength"
            :defaultMediaType="getDefaultMediaType"
            :exampleImageUrl="exampleImageUrl"
            :exampleVideoUrl="exampleVideoUrl"
            :exampleVideoThumbnail="exampleVideoThumbnail"
            @upload-click="handleUploadClick"
            @delete="handleFileDeleteByIndex"
            @replace="handleReplaceByIndex"
          />
        </div>
      </div>
    </template>

    <!-- 自定义插槽 -->
    <template v-else-if="formItem.type === 'slot'">
      <slot :name="formItem.slotName" :form-data="formValues"></slot>
    </template>

    <!-- 默认类型 -->
    <template v-else>
      <slot name="default" :form-item="formItem" :form-data="formValues"></slot>
    </template>
  </fa-form-model-item>
</template>

<script lang="ts">
import {
  defineComponent,
  ref,
  computed,
  watch,
  onMounted,
  type PropType,
} from 'vue';
import { message } from '@fk/faicomponent';
import OpenAiTipButton from '@/components/DynamicForm/FormItems/OpenAiTipButton/index.vue';
import CustomUpload from '@/components/DynamicForm/FormItems/CustomUpload.vue';
import VersionIcon from '@/components/comm/ScVersionIcon.vue';
import {
  MOUTH_SHAPE_TEXT,
  MOUTH_SHAPE_TIP,
  MAX_TAGS_LIMIT,
} from '@/components/DynamicForm/constants/index';
import { getMaterialFullUrl } from '@/components/MaterialBasicUpload/utils/index.ts';
import type {
  FormItemInternal,
  DynamicFormItemProps as ComponentProps,
  FormEventParams,
  FileInfo,
  FormRule,
  MediaType,
} from '@/views/EditProjectView/types/index';
import { MEDIA_TYPES } from '@/views/EditProjectView/types/index';
import { FILE_TYPES } from '@/constants/fileType';
import { VERSION } from '@/constants/version';
import { VersionSwitch } from '@/components/version';
import { eventBus, EVENT_NAMES } from '@/utils/eventBus';
import type { AiMouthShapeToggleData } from '@/utils/eventBus';

/**
 * 动态表单项组件
 * @description 根据配置动态渲染各种类型的表单控件，支持输入框、选择器、文件上传、AI推荐等多种类型
 * @example
 * ```vue
 * <DynamicFormItem
 *   :form-item="formItemConfig"
 *   :form-values="formData"
 *   :form-items="allFormItems"
 *   :form-config="formConfig"
 *   :rules="validationRules"
 *   @upload-click="handleUploadClick"
 * />
 * ```

 * @since 1.0.0
 */
export default defineComponent({
  name: 'DynamicFormItem',
  components: {
    OpenAiTipButton,
    CustomUpload,
    VersionIcon,
    VersionSwitch,
  },
  props: {
    /**
     * 表单项配置对象
     * @description 包含表单项的类型、属性、验证规则等完整配置信息
     * @required
     */
    formItem: {
      type: Object as PropType<FormItemInternal>,
      required: true,
    },
    /**
     * 表单数据对象
     * @description 存储所有表单字段的当前值，通过字段名进行索引
     * @required
     */
    formValues: {
      type: Object as PropType<Record<string, unknown>>,
      required: true,
    },
    /**
     * 所有表单项配置数组
     * @description 包含整个表单的所有字段配置，用于AI推荐等功能的上下文信息
     * @default []
     */
    formItems: {
      type: Array as PropType<FormItemInternal[]>,
      default: () => [],
    },
    /**
     * 表单全局配置
     * @description 包含表单的全局设置，如布局、样式、默认行为等
     * @default {}
     */
    formConfig: {
      type: Object as PropType<ComponentProps['formConfig']>,
      default: () => ({}),
    },
    /**
     * 表单验证规则数组
     * @description 当前表单项的验证规则列表
     * @default []
     */
    rules: {
      type: Array as PropType<FormRule[]>,
      default: () => [],
    },
  },
  emits: {
    /**
     * 文件上传点击事件
     * @description 点击文件上传按钮时触发
     * @param data 包含字段名、媒体类型等上传相关信息的事件数据
     */
    'upload-click': (_data: FormEventParams['upload']) => true,
    /**
     * 字段值变化事件
     * @description 当字段值发生变化时触发，用于通知父组件进行验证
     * @param prop 发生变化的字段名
     */
    'field-change': (_prop: string) => true,
    /**
     * @description 文件删除事件（基于索引）。
     * @event file-delete
     * @param {{ prop: string; index: number; mediaType: MediaType; formItem?: FormItemInternal }} data - 删除事件数据。
     */
    'file-delete': (_data: {
      prop: string;
      index: number;
      mediaType: MediaType;
      formItem?: FormItemInternal;
    }) => true,
    /**
     * @description 文件替换事件（基于索引）。
     * @event replace
     * @param {{ prop: string; index: number; mediaType: MediaType; formItem?: FormItemInternal }} data - 替换事件数据。
     */
    'replace': (_data: {
      prop: string;
      index: number;
      mediaType: MediaType;
      formItem?: FormItemInternal;
    }) => true,
  },
  setup(props, { emit }) {
    // ============= 获取组件实例 =============

    /**
     * 示例图片URL
     * @description 存储从API获取的示例图片URL
     */
    const exampleImageUrl = ref<string>('');

    /**
     * 示例视频URL
     * @description 存储从API获取的示例视频URL
     */
    const exampleVideoUrl = ref<string>('');

    /**
     * 示例视频缩略图URL
     * @description 存储从API获取的示例视频缩略图URL
     */
    const exampleVideoThumbnail = ref<string>('');

    /**
     * 之前的标签列表
     * @description 用于检测重复标签，存储上一次的标签状态
     */
    const previousTagsList = ref<string[]>([]);

    /**
     * 表单项焦点状态
     * @description 记录当前表单项是否获得焦点，用于控制AI推荐词的显示时机
     */
    const isFocused = ref<boolean>(false);

    /**
     * 标签计数元素引用
     * @description 用于获取标签计数元素的DOM引用，以便计算其宽度
     */
    const tagsCountRef = ref<HTMLElement | null>(null);

    /**
     * 标签计数元素宽度
     * @description 存储标签计数元素的实际宽度，用于动态设置右边距
     */
    const tagsCountWidth = ref<number>(50);

    // ============= 常量定义 =============

    /**
     * AI改嘴型开关文本
     * @description 显示在开关组件上的文本标签
     */
    const mouthShapeText = MOUTH_SHAPE_TEXT;

    /**
     * AI改嘴型功能提示文本
     * @description 显示给用户的功能说明提示信息
     */
    const mouthShapeTip = MOUTH_SHAPE_TIP;

    // ============= 计算属性 =============

    /**
     * 获取标签数量限制
     * @description 获取当前表单项的标签数量限制，支持可配置，默认为10
     * @returns 标签数量限制
     */
    const getTagsLimit = computed<number>(() => {
      if (props.formItem.type !== 'selectTags') return 10;
      // 优先使用表单项配置的maxTagCount，否则使用默认值10
      return props.formItem.maxTagCount || 10;
    });

    /**
     * 获取已选标签数量
     * @description 计算标签选择类型字段当前已选择的标签数量
     * @returns 已选择的标签数量
     */
    const getTagsCount = computed<number>(() => {
      if (props.formItem.type !== 'selectTags') return 0;

      const fieldValue = props.formValues[props.formItem.prop];
      if (!fieldValue) return 0;
      if (!Array.isArray(fieldValue)) return 0;

      // 将所有标签合并成一个字符串并计算长度
      return fieldValue.join('').length;
    });

    /**
     * 获取文件列表
     * @description 获取当前表单项的文件列表，进行类型安全处理
     * @returns 文件信息数组
     */
    const getFileList = computed<FileInfo[]>(() => {
      const fileList = props.formValues[props.formItem.prop];
      return (fileList as FileInfo[]) || [];
    });

    /**
     * 获取默认媒体类型
     * @description 处理表单配置中的默认媒体类型，确保类型安全
     * @returns 符合CustomUpload组件要求的媒体类型
     */
    const getDefaultMediaType = computed<MediaType>(() => {
      const mediaType = props.formItem?.mediaType;
      if (mediaType === MEDIA_TYPES.IMAGE || mediaType === MEDIA_TYPES.VIDEO) {
        return mediaType as MediaType;
      }
      return MEDIA_TYPES.IMAGE;
    });

    /**
     * 获取标签选择框的动态样式
     * @description 根据标签计数元素的宽度动态计算右边距
     * @returns 包含动态右边距的样式对象
     */
    const getTagsSelectStyle = computed(() => {
      if (props.formItem.type !== 'selectTags') return {};

      // 计算右边距：标签计数宽度 + 额外间距（11px）
      const marginRight = `${tagsCountWidth.value + 11}px`;

      return {
        '--dynamic-margin-right': marginRight,
      };
    });

    // ============= 方法定义 =============

    /**
     * 处理AI推荐选择
     * @description 处理用户选择AI推荐内容的逻辑，根据字段类型进行相应处理
     * @param value AI推荐的值
     * @public
     */
    const handleSelectOpenAiTip = (value: string): void => {
      if (!value || !props.formItem.prop) return;

      const fieldName = props.formItem.prop;

      // 根据表单项类型处理推荐值
      if (props.formItem.type === 'selectTags') {
        // 标签选择类型：将推荐值添加到已选标签数组中
        const currentTags = (props.formValues[fieldName] as string[]) || [];

        // 检查是否已达到标签数量限制
        const tagsLimit = getTagsLimit.value;
        if (currentTags.length >= tagsLimit) {
          message.warning(`最多只能输入${tagsLimit}个标签`);
          return;
        }

        if (!currentTags.includes(value)) {
          const newTags = [...currentTags, value];
          props.formValues[fieldName] = newTags;
        }
      } else if (
        props.formItem.type === 'input' ||
        props.formItem.type === 'textarea'
      ) {
        // 文本输入类型：处理推荐值的添加逻辑
        const currentValue = (props.formValues[fieldName] as string) || '';

        // 如果当前已有内容（非空值），在现有内容后添加中文逗号作为分隔符
        if (currentValue.trim()) {
          props.formValues[fieldName] = currentValue + '，' + value;
        } else {
          // 如果当前为空值，直接设置推荐值
          props.formValues[fieldName] = value;
        }
      } else {
        // 其他类型：直接替换当前值
        props.formValues[fieldName] = value;
      }

      // 触发字段变化事件，确保验证错误信息能够正确清除
      handleFieldChange();
    };

    /**
     * 处理字段值变化
     * @description 字段值发生变化时的通用处理逻辑，用于触发验证等操作
     * @public
     */
    const handleFieldChange = (): void => {
      // 触发字段变化事件，通知父组件进行验证
      if (props.formItem.prop) {
        emit('field-change', props.formItem.prop);
      }
    };

    /**
     * 处理表单控件获得焦点
     * @description 当表单控件获得焦点时，设置焦点状态为true
     * @public
     */
    const handleFocus = (): void => {
      isFocused.value = true;
    };

    /**
     * 处理表单控件失去焦点
     * @description 当表单控件失去焦点时，设置焦点状态为false
     * @public
     */
    const handleBlur = (): void => {
      isFocused.value = false;
    };

    /**
     * 处理标签变化
     * @description 处理标签选择器值变化，包含标签数量限制验证
     * @param value 新的标签数组值
     * @public
     */
    const handleTagsChange = (value: string[]): void => {
      if (!value || !Array.isArray(value)) {
        previousTagsList.value = [];
        handleFieldChange();
        return;
      }

      // 对每个标签去掉空格，只过滤掉去掉空格后为空的标签
      const processedValue = value
        .map(tag => {
          if (typeof tag !== 'string') return '';
          return tag.replace(/\s/g, '');
        })
        .filter(tag => tag.length > 0);

      // 检查是否有标签被处理或过滤
      const hasChanged =
        processedValue.length !== value.length ||
        !processedValue.every((tag, index) => tag === value[index]);

      if (hasChanged) {
        // 更新表单值为处理后的值
        props.formValues[props.formItem.prop] = processedValue;
        previousTagsList.value = [...processedValue];

        handleFieldChange();
        return;
      }

      // 检查标签数量是否超过限制
      const tagsLimit = getTagsLimit.value;
      if (processedValue.length > tagsLimit) {
        // 截取到限制数量的标签
        const limitedTags = processedValue.slice(0, tagsLimit);
        props.formValues[props.formItem.prop] = limitedTags;
        previousTagsList.value = [...limitedTags];

        // 显示警告消息
        message.warning(`最多只能输入${tagsLimit}个标签`);
        return;
      }

      // 延迟更新 previousTagsList，让 inputKeydown 事件先执行完
      setTimeout(() => {
        previousTagsList.value = [...processedValue];
      }, 0);

      // 调用通用字段变化处理
      handleFieldChange();
    };

    /**
     * 处理标签输入键盘事件
     * @description 处理用户在标签选择器中的键盘输入，检测重复标签并显示友好提示
     * @param event 键盘事件对象
     * @public
     */
    const handleTagsInputKeydown = (event: KeyboardEvent): void => {
      // 只处理回车键事件
      if (event.key !== 'Enter') return;

      // 获取输入框的值
      const target = event.target as HTMLInputElement;
      if (!target || !target.value) return;

      // 使用正则表达式去掉所有空格
      const inputValue = target.value.replace(/\s/g, '');
      // 只有在去掉空格后为空白时才提示错误
      if (!inputValue || inputValue.length === 0) {
        message.warning('标签不能为空，请输入有效内容');
        event.preventDefault();
        return;
      }

      // 获取之前的标签列表（使用 previousTagsList 而不是当前的 formValues）
      const previousTags = [...previousTagsList.value];

      // 检查是否为重复标签（使用 previousTags）
      if (previousTags.includes(inputValue)) {
        // 显示友好提示
        message.warning(`标签"${inputValue}"已存在，请输入其他标签`);
        return;
      }

      // 检查是否已达到标签数量限制
      const tagsLimit = getTagsLimit.value;
      if (previousTags.length >= tagsLimit) {
        // 显示数量限制提示
        message.warning(`最多只能输入${tagsLimit}个标签`);
        return;
      }
    };

    /**
     * 处理AI改嘴型开关切换事件
     * @description 当VersionSwitch状态变化时调用，通过EventBus通知相关组件
     * @param value 新的开关状态
     * @public
     */
    const handleMouthShapeToggle = (value: boolean): void => {
      // 通过 EventBus 通知状态变化，触发积分重算等副作用
      eventBus.emit(EVENT_NAMES.AI_MOUTH_SHAPE_TOGGLE, {
        prop: props.formItem.prop,
        value,
        formItem: props.formItem,
      } as AiMouthShapeToggleData);
    };

    /**
     * @description 处理来自 CustomUpload 的上传按钮点击事件，并向上层组件透传。
     * @param {{ mediaType: MediaType }} data - CustomUpload 发出的事件负载。
     */
    const handleUploadClick = (data: { mediaType: MediaType }): void => {
      emit('upload-click', {
        prop: props.formItem.prop,
        mediaType: data.mediaType,
        formItem: props.formItem,
      });
    };

    /**
     * @description 处理来自 CustomUpload 的文件删除事件（基于索引），并向上层组件透传。
     * @param {{ index: number }} data - CustomUpload 发出的事件负载，包含要删除文件的索引。
     */
    const handleFileDeleteByIndex = (data: { index: number }): void => {
      emit('file-delete', {
        prop: props.formItem.prop,
        index: data.index,
        mediaType: (props.formItem.mediaType ||
          getDefaultMediaType.value) as MediaType,
        formItem: props.formItem,
      });
    };

    /**
     * @description 处理来自 CustomUpload 的文件替换事件（基于索引），并向上层组件透传。
     * @param {{ index: number; mediaType: MediaType }} data - CustomUpload 发出的事件负载，包含文件索引和新媒体类型。
     */
    const handleReplaceByIndex = (data: {
      index: number;
      mediaType: MediaType;
    }): void => {
      emit('replace', {
        prop: props.formItem.prop,
        index: data.index,
        mediaType: data.mediaType,
        formItem: props.formItem,
      });
    };

    /**
     * 更新标签计数元素宽度
     * @description 测量标签计数元素的实际宽度并更新响应式变量
     */
    const updateTagsCountWidth = (): void => {
      if (!tagsCountRef.value) return;

      // 使用 getBoundingClientRect 获取元素的实际宽度
      const rect = tagsCountRef.value.getBoundingClientRect();
      tagsCountWidth.value = Math.ceil(rect.width);
    };

    /**
     * 设置标签计数元素的ResizeObserver
     * @description 监听标签计数元素的尺寸变化，自动更新宽度
     */
    const setupTagsCountObserver = (): void => {
      if (!tagsCountRef.value) return;

      // 创建 ResizeObserver 监听元素尺寸变化
      const resizeObserver = new ResizeObserver(() => {
        updateTagsCountWidth();
      });

      // 开始观察元素
      resizeObserver.observe(tagsCountRef.value);

      // 初始测量
      updateTagsCountWidth();
    };

    /**
     * 加载资源URL
     * @description 根据表单项配置加载示例资源URL
     */
    const loadResourceUrls = (): void => {
      if (props.formItem.resId) {
        // 根据媒体类型设置相应的URL
        if (props.formItem.mediaType === MEDIA_TYPES.IMAGE) {
          exampleImageUrl.value = getMaterialFullUrl(
            props.formItem.resId,
            FILE_TYPES.WEBP || Number(props.formItem.resType),
            'oss',
            108,
          );
        } else if (props.formItem.mediaType === MEDIA_TYPES.VIDEO) {
          exampleVideoUrl.value = getMaterialFullUrl(
            props.formItem.resId,
            FILE_TYPES.WEBP || Number(props.formItem.resType),
            'oss',
            108,
          );
        }
      }

      // 加载封面图URL，使用原始的coverType值
      if (props.formItem.coverId) {
        // 只有视频类型需要设置缩略图
        if (props.formItem.mediaType === MEDIA_TYPES.VIDEO) {
          exampleVideoThumbnail.value = getMaterialFullUrl(
            props.formItem.coverId,
            FILE_TYPES.WEBP || Number(props.formItem.coverType),
            'oss',
            100,
          );
        }
      }
    };

    // ============= 监听器 =============

    /**
     * 监听标签字段值变化
     * @description 当标签字段值从外部变化时，同步更新 previousTagsList
     */
    watch(
      () => {
        if (props.formItem.type === 'selectTags') {
          return props.formValues[props.formItem.prop] as string[];
        }
        return null;
      },
      newValue => {
        if (props.formItem.type === 'selectTags' && Array.isArray(newValue)) {
          // 只在初始化时设置，避免在我们自己的 handleTagsChange 中触发循环
          if (previousTagsList.value.length === 0 && newValue.length > 0) {
            previousTagsList.value = [...newValue];
          }
        }
      },
      { immediate: true },
    );

    // ============= 生命周期 =============

    /**
     * 组件挂载生命周期
     * @description 组件挂载时初始化AI改嘴型开关的本地状态并加载资源URL
     */
    onMounted(() => {
      // 加载资源URL
      loadResourceUrls();

      // 如果是标签选择类型，设置计数元素观察器
      if (props.formItem.type === 'selectTags') {
        // 使用 nextTick 确保DOM已渲染
        setTimeout(() => {
          setupTagsCountObserver();
        }, 0);
      }
    });

    // ============= 返回模板所需数据 =============

    return {
      // 响应式数据
      exampleImageUrl,
      exampleVideoUrl,
      exampleVideoThumbnail,
      isFocused,
      tagsCountRef,
      tagsCountWidth,

      // 常量
      mouthShapeText,
      mouthShapeTip,
      MAX_TAGS_LIMIT,

      // 计算属性
      getTagsLimit,
      getTagsCount,
      getFileList,
      getDefaultMediaType,
      getTagsSelectStyle,

      // 方法
      handleSelectOpenAiTip,
      handleFieldChange,
      handleFocus,
      handleBlur,
      handleTagsChange,
      handleTagsInputKeydown,
      handleMouthShapeToggle,
      handleUploadClick,
      handleFileDeleteByIndex,
      handleReplaceByIndex,
      updateTagsCountWidth,
      setupTagsCountObserver,
      // 常量
      VERSION,
    };
  },
});
</script>

<style lang="scss" scoped>
.dynamic-form-item {
  /* 尺寸相关 */
  @apply mb-16px;

  .dynamic-form-item__container {
    /* 布局相关 */
    @apply relative;
  }

  .dynamic-form-item__container--upload {
    .dynamic-form-item__upload-tip {
      /* 尺寸相关 */
      @apply pb-8px;
      /* 文字相关 */
      @apply font-normal text-14px text-left text-[#999];
    }
  }

  .dynamic-form-item__container--select {
    :deep(.fa-select) {
      /* 尺寸相关 */
      @apply w-[300px];
    }
  }

  .dynamic-form-item__container--tags {
    overflow: hidden;
  }

  .dynamic-form-item__upload-wrapper {
    /* 布局相关 */
    @apply flex flex-col;
  }

  .dynamic-form-item__upload-header {
    /* 布局相关 */
    @apply flex items-center;
  }

  .dynamic-form-item__mouth-shape {
    /* 布局相关 */
    @apply flex items-center;
    /* 尺寸相关 */
    @apply ml-16px;

    :deep(.fa-switch) {
      /* 尺寸相关 */
      @apply my-0;
    }
  }

  .dynamic-form-item__tags-count {
    /* 布局相关 */
    @apply absolute top-0 right-11px flex items-center;
    /* 尺寸相关 */
    @apply ml-6px h-full;
    /* 文字相关 */
    @apply text-[#999] text-13px leading-none;
  }
}
</style>

<style lang="scss">
/* 隐藏标签选择框下拉菜单 */
.dynamic-form-item__tags-dropdown-hidden {
  /* 布局相关 */
  @apply hidden!;
}

/* 标签选择框中的移除按钮样式 */
.dynamic-form-item__tags-select {
  .fa-select-selection__rendered {
    /* 尺寸相关 - 使用CSS变量实现动态右边距 */
    margin-right: var(--dynamic-margin-right, 50px);
  }

  .fa-select-selection--multiple .fa-select-selection__choice {
    /* 尺寸相关 */
    @apply rounded-4px;
  }

  .fa-select-selection--multiple .fa-select-selection__choice__remove {
    /* 文字相关 */
    @apply leading-[2.1]!;

    .fa-select-remove-icon {
      /* 布局相关 */
      @apply align-middle;
    }
  }

  .fa-select-selection--multiple .fa-select-selection__choice__content {
    /* 文字相关 */
    @apply text-[#333];
  }
}
</style>

<!-- 覆盖公司组件样式 -->
<style lang="scss">
.dynamic-form-item {
  .fa-input-show-count-wrapper,
  .fa-select-selection--multiple {
    border-radius: 8px;
    width: 100%;
  }

  .fa-form-extra {
    @apply min-h-0 my-0 pt-0;
  }
}
</style>
