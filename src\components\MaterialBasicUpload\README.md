# 选择素材弹窗

## 文档目录

- [API 文档](http://fa-cus.docs.aaa.cn/#/basic-upload)
- [DEMO 代码](http://gitlab.faidev.cc/frontend/fa-component-cus/blob/master/packages/website/src/components/basic-upload/src/basic-upload-demo.vue)
- [DEMO 示例](http://h5.fa-cus.docs.aaa.cn/#/basic-upload-demo)

## API

### Props

| 参数 | 说明 | 类型 | 默认值 | 必传 | 版本 |
| --- | --- | --- | --- | --- | --- | --- |
| setting | 上传图片设置和文件夹设置的参数 | `BasicUploadSetting` | `{ originPictureUpload: true, autoAddWaterMark: true, showSystemFolder: true }` | 否 |
| loading | 是否显示加载图 | boolean | false | 否 |
| visible | 是否显示弹窗 | boolean | false | 否 |
| title | 弹窗顶部主标题 | string | "资源库" | 否 |
| subTitle | 弹窗顶部副标题 | string | "" | 否 |
| folder-list | 文件夹列表 | `array<object>` | [] | 否 |
| file-list | 文件列表 | `array<object>` | [] | 否 |
| fodder-list | 素材列表 | `array<object>` | [] | 否 |
| category-list | 素材类型列表 | `array<object>` | [] | 否 |
| topic-list | 素材主题列表 | `array<object>` | [] | 否 |
| chosen-file-list | 已选文件列表 | `array<BasicUploadChosenFileData>` | [] | 否 |
| tab-props | Tabs 标签页组件的属性 | object | `{ defaultActiveKey: TAB_PANE_KEY.MyFile }` | 否 |
| tab-listeners | Tabs 标签页组件的监听函数 | object | {} | 否 |
| tab-pane-list | 标签页列表 | array | `TAB_PANE_LIST` | 否 |
| max-chosen-file-count | 最大可选文件数 | number | 1 | 否 |
| owner-file-search-placeholder | “我的文件”搜索框占位符 | string | "请搜索所有文件" | 否 |
| public-file-search-placeholder | “图片库”搜索框占位符 | string | "请搜索所有素材" | 否 |
| owner-file-page-current | “我的文件”分页组件当前页码 | number | 1 | 否 |
| owner-file-page-total | “我的文件”分页组件总大小 | number | 0 | 否 |
| owner-file-page-size | “我的文件”分页组件单页大小 | number | 28 | 否 |
| owner-file-page-current | “我的文件”分页组件当前页码 | number | 1 | 否 |
| owner-file-force-show-pagination | “我的文件”分页组件强制显示 | boolean | false | 否 |
| public-file-age-total | “图片库”分页组件总大小 | number | 0 | 否 |
| public-file-age-size | “图片库”分页组件单页大小 | number | 40 | 否 |
| public-file-force-show-pagination | “图片库”分页组件强制显示 | boolean | false | 否 |
| show-file-desc | 显示已选文件名称 | boolean | false | 否 |
| folder-key | 文件夹数据 id 字段键值 | string | "id" | 否 |
| folder-label-key | 文件夹数据名称字段键值 | string | "label" | 否 |
| folder-parent-key | 文件夹数据父文件夹 id 字段键值 | string | "parentId" | 否 |
| folder-size-key | 文件数据大小字段键值 | string | "size" | 否 | 0.12.7 |
| folder-time-key | 文件数据时间字段键值 | string | "time" | 否 | 0.12.7 |
| file-key | 文件数据 id 字段键值 | string | "id" | 否 |
| file-preview-key | 文件数据预览字段键值 | string | "url" | 否 |
| file-percent-key | 文件数据进度字段键值 | string | "percent" | 否 |
| file-type-key | 文件数据类型字段键值 | string | "type" | 否 |
| file-size-key | 文件数据大小字段键值 | string | "size" | 否 | 0.12.7 |
| file-time-key | 文件数据时间字段键值 | string | "time" | 否 | 0.12.7 |
| fodder-key | 素材数据 id 字段键值 | string | "id" | 否 |
| fodder-preview-key | 素材数据预览字段键值 | string | "url" | 否 |
| fodder-type-key | 素材数据类型字段键值 | string | "type" | 否 |
| fodder-code-key | 素材数据来源字段键值 | string | "comeFrom" | 否 |
| current-folder | 当前文件夹 id | string/number | 0 | 否 |
| fodder-label-key | 素材数据名称字段键值 | string | "name" | 否 |
| category-key | 素材类型数据 id 字段键值 | string | "id" | 否 |
| category-label-key | 素材类型数据名称字段键值 | string | "name" | 否 |
| current-category | 当前素材类型 id | string/number | 0 | 否 |
| topic-key | 素材主题数据 id 字段键值 | string | "id" | 否 |
| topic-label-key | 素材主题数据名称字段键值 | string | "name" | 否 |
| current-topic | 当前素材主题 id | string/number | 0 | 否 |
| sort-by | 文件排序方式依据 | string | `SORT_BY_KEY.Time` | 否 |
| sort-mode | 文件排序方式方向 | string | `SORT_MODE_KEY.ASCE` | 否 |
| root-folder | 根文件夹 id | string/number | 0 | 否 |
| owner-file-search-keywords | “我的文件”搜索框关键词 | string | "" | 否 |
| public-file-search-keywords | “图片库”搜索框关键词 | string | "" | 否 |
| capacity-used-size | 容量的已使用大小 | number | 0 | 否 |
| capacity-total-size | 容量的总大小 | number | 1024 | 否 |
| capacity-custom-bar-style | 容量条的样式 | object | - | 否 | - | 0.11.19 |
| show-capacity | 显示容量提示 | boolean | true | 否 |
| owner-file-svg-data-url | “我的文件” svg 编辑器的图片链接 | string | "" | 否 |
| public-file-svg-data-url | “图片库” svg 编辑器的图片链接 | string | "" | 否 |
| modal-options | 详见 [Popup](#/popup) | object | - | 否 | 0.10.4 |
| fodder-source-message | 自定义素材来源信息 | object | - | 否 | 0.11.18 |
| capacity-overlay-disabled | 容量浮层是否禁用 | boolean | false | 否 | 0.11.27 |
| capacity-expand-btn-visible | 容量扩容按钮是否显示 | boolean | true | 否 | 0.11.27 |
| default-owner-file-view-type | 默认“我的文件”视图类型 | string | list | table | 否 | 0.12.7 |
| imageUploadSetting | 上传图片配置项(目前支持隐藏原画上传)`{ hideOriginalImage: true }` | object | {} | 否 | 0.17.10 |
| hidePhotoLibSearch | 是否隐藏"图片库"搜索框 | boolean | false | 否 | 0.17.103 |
| dropdownProps | 下拉菜单属性, 详见 [Dropdown](http://fa.docs.aaa.cn/components/dropdown-cn/#API) | object | - | 否 | 0.20.43 |
| popoverProps | 气泡框属性, 详见 [Popover](http://fa.docs.aaa.cn/components/popover-cn/#API) | object | - | 否 | 0.20.46 |
| autoScrollDisabled | 是否禁用自动滚动 | boolean | false | 否 | 0.24.17 |

### 常量

`TAB_PANE_KEY`: 标签页列表参数，通过组件对象 `BasicUpload.TAB_PANE_KEY` 获取，详细见文件位置 `packages/basic-upload/constants.js`，下同

`TAB_PANE_LIST`： 标签页列表

`File_View_Type_KEY_LIST`： “我的文件”展现方式

`SORT_BY_KEY`： 文件排序方式根据

`SORT_MODE_KEY`： 文件排序方式方向

### 类型

`BasicUploadChosenFileData: { type: "owner" | "public", data: object }`： 已选文件数据

- `type`： 标识属性从属

`BasicUploadSetting`: `{ originPictureUpload: boolean, autoAddWaterMark: boolean, showSystemFolder: boolean }`

- `originPictureUpload`： 是否原图上传
- `autoAddWaterMark`： 是否自动添加水印
- `showSystemFolder`： 是否显示系统文件夹

### Events

| 事件名 | 说明 | 回调参数 |
| --- | --- | --- |
| owner-file-page-change | “我的文件”分页组件页码切换 | (page: string) |
| public-file-page-change | “图片库”分页组件页码切换 | (page: string) |
| trigger-upload | 触发文件上传 | - |
| add-folder | 触发新增文件夹 | - |
| edit-photo | 背景图片重复方式变化 | - |
| owner-file-search | “我的文件”搜索 | (keyword: string) |
| public-file-search | “图片库”搜索 | (keyword: string) |
| folder-close | 删除文件夹 | (data: object) |
| folder-choose | 选择文件夹 | (data: object) |
| file-close | 删除文件 | (data: object) |
| file-choose | 选择文件 | (data: object) |
| file-unchoose | 取消选择文件 | (data: object) |
| fodder-choose | 选择素材 | (data: object) |
| fodder-unchoose | 取消选择素材 | (data: object) |
| folder-drop | 文件被移入文件夹 | ({ file: object, folder: object }) |
| chosen-file-list-sort | 已选文件排序 | (list: `array<BasicUploadChosenFileData>`) |
| water-mark-setting | 点击设置水印 | - |
| setting-ok | 点击文件设置弹窗确定按钮 | (data: `BasicUploadSetting`) |
| folder-label-change | 文件夹名称修改 | ({ data: object, label: string ) |
| file-label-change | 文件名称修改 | ({ data: object, label: string ) |
| capacity-expand | 点击容量提示的扩容按钮 | - |
| topic-choose | 选择了素材主题 | (key: string/number) |
| category-choose | 选择了素材类型 | (key: string/number) |
| owner-file-svg-edit | “我的文件” svg 编辑 | (dataURI: string) |
| public-file-svg-edit | “图片库” svg 编辑 | (dataURI: string) |
| scroll-end | 内容区(BasicUploadFileViewList)滚动触底事件 | - |

### Slots

| 名称                           | 说明                           |
| ------------------------------ | ------------------------------ |
| popup-title                    | 弹窗标题                       |
| owner-file-toolbar-group-left  | “我的文件”工具栏左侧           |
| owner-file-toolbar-group-right | “我的文件”工具栏右侧           |
| toolbar-ui-upload-menu         | “我的文件”工具栏的上传按钮菜单 |
| loading                        | 加载占位图                     |
| owner-file-empty               | “我的文件”空数据占位图         |
| public-file-empty              | “图片库”空数据占位图           |
| select-suffixIcon              | 选择结尾插槽                   |
| file-slider-view               | 右边栏文件内容                 |
| file-slider-layer              | 右边栏文件编辑栏               |
| owner-file-view-ui-file-view   | “我的文件”文件内容             |
| owner-file-view-ui-file-layer  | “我的文件”文件编辑栏           |
| public-file-view-ui-file-view  | “图片库”文件内容               |
| public-file-view-ui-file-layer | “图片库”文件编辑栏             |
