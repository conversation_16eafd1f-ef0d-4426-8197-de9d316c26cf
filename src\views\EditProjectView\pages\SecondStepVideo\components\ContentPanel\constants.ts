/**
 * 内容面板相关常量
 */

/**
 * 内容面板操作类型
 */
export const CONTENT_PANEL_ACTION_TYPES = {
  /** 脚本相关操作 */
  SCRIPT: 'script',
  /** 背景音乐相关操作 */
  MUSIC: 'music',
  /** 配音相关操作 */
  VOICE: 'voice',
  /** 花字相关操作 */
  FONT_TAG: 'fontTag',
};

/**
 * 内容面板操作动作
 */
export const CONTENT_PANEL_ACTIONS = {
  /** 编辑 */
  EDIT: 'edit',
  /** 更换 */
  CHANGE: 'change',
  /** 添加 */
  ADD: 'add',
  /** 删除 */
  DELETE: 'delete',
};

/**
 * 内容面板配置
 */
export const CONTENT_PANEL_CONFIG = {
  /** 脚本内容最大高度 */
  SCRIPT_MAX_HEIGHT: 'auto',
  /** 文本最大长度（超出显示省略号） */
  TEXT_MAX_LENGTH: 20,
  /** 脚本文字最大高度 */
  SCRIPT_TEXT_MAX_HEIGHT: 260,
  /** 脚本标签页 */
  SCRIPT_TABS: [
    { id: 'intro', name: '开场介绍' },
    { id: 'management', name: '经营理念' },
    { id: 'product', name: '产品质量' },
    { id: 'customer', name: '顾客体验' },
    { id: 'ending', name: '情感化结尾' },
  ],
};
