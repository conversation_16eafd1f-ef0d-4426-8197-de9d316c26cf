import { defineConfig, loadEnv, TransformResult } from 'vite';
import UnoCSS from 'unocss/vite';
import vue2 from '@vitejs/plugin-vue2';
import path from 'path';
import { viteMockServe } from 'vite-plugin-mock';
import Components from 'unplugin-vue-components/vite';
import { ElementUiResolver } from 'unplugin-vue-components/resolvers';
import { endsWith } from 'lodash-es';
import { visualizer } from 'rollup-plugin-visualizer';
import { fileURLToPath } from 'url';

export default defineConfig(({ mode }) => {
  const env = loadEnv(mode, process.cwd());
  /** 是否启用 Mock */
  const useMock = env.VITE_USE_MOCK === 'true';
  /** 是否为分销 */
  const isOEM = env.VITE_IS_OEM === 'true';
  /** API接口域名 */
  const apiTargetMap = {
    test: isOEM ? env.VITE_TEST_OEM_API_DOMAIN : env.VITE_TEST_API_DOMAIN,
  };
  /** 开发模式下登录域名 */
  const devLoginDomain = isOEM
    ? env.VITE_TEST_OEM_LOGIN_DOMAIN
    : env.VITE_TEST_LOGIN_DOMAIN;
  const apiTarget = apiTargetMap[env.VITE_ENV];

  /** 修复antd图标引入问题 */
  function antDesignIconsFix() {
    return {
      name: '@ant-design-icons-fix',
      transform(code: string, id: string): TransformResult {
        if (id.includes('@ant-design/icons/lib/dist.js')) {
          return {
            code: code.replace(', dist as default', ''),
            map: null,
          };
        }
        return {
          code,
          map: null,
        };
      },
    };
  }

  return {
    plugins: [
      vue2(),
      UnoCSS(),
      Components({
        resolvers: [ElementUiResolver()],
      }),
      viteMockServe({
        mockPath: 'src/mock', // Mock 数据文件夹路径
        enable: useMock,
        watchFiles: true,
      }),
      visualizer({
        gzipSize: true,
        brotliSize: true,
        emitFile: false,
        filename: 'visualizer.html', //分析图生成的文件名
        open: false,
      }),
    ],
    build: {
      sourcemap: false,
      rollupOptions: {
        plugins: [antDesignIconsFix()],
        output: {
          manualChunks(id: string) {
            if (id.includes('node_modules')) {
              if (id.includes('faicomponent')) return 'vendor-fai';
              if (id.includes('fa-component-cus')) return 'vendor-fa-cus';
              if (id.includes('element-ui')) return 'vendor-element';
              if (id.includes('video.js')) return undefined;
              return 'vendor';
            }
            return undefined;
          },
        },
      },
      commonjsOptions: {
        transformMixedEsModules: true,
      },
      minify: 'terser',
      terserOptions: {
        format: {
          comments: false,
          ascii_only: true,
        },
        keep_classnames: false,
        keep_fnames: false,
        compress: {
          pure_funcs: [
            'console.log',
            'console.warn',
            'console.info',
            'console.debug',
          ], // 只保留 console.error
          drop_debugger: true, // 移除 debugger
        },
      },
    },
    resolve: {
      alias: {
        '@': path.resolve(__dirname, 'src'),
        'vue': fileURLToPath(
          new URL('./node_modules/vue/dist/vue.esm.js', import.meta.url),
        ),
        '@fk/faicomponent/lib': path.resolve(
          __dirname,
          'node_modules/@fk/faicomponent/lib',
        ),
        '@fk/faicomponent': path.resolve(
          __dirname,
          'node_modules/@fk/faicomponent/lib',
        ),
      },
    },
    css: {
      preprocessorOptions: {
        less: {
          javascriptEnabled: true,
        },
        scss: {
          // eslint-disable-next-line quotes
          additionalData: "@import '@/style/z-index.scss';",
          silenceDeprecations: ['legacy-js-api', 'import'],
        },
      },
    },
    server: {
      port: 4399,
      open: true,
      proxy: {
        '/api': {
          target: apiTarget,
          changeOrigin: true,
        },
        '/portal': {
          target: devLoginDomain,
          changeOrigin: true,
          rewrite: path => path.replace(/^\/portal/, ''),
        },
      },
    },
    experimental: {
      renderBuiltUrl(filename, { hostType }) {
        const dir = endsWith(filename, '.js')
          ? 'js/'
          : ['.svg', '.png', '.jpg', '.jpeg', '.webp', '.gif'].some(ext =>
              filename.endsWith(ext),
            )
          ? 'image/'
          : 'css/';
        if (filename.endsWith('.woff')) {
          return '/' + filename.replace(/(assets|fonts)\//, 'css/');
        }
        if (hostType === 'css') {
          return '/' + filename.replace('assets/', 'image/');
        }
        if (hostType === 'html') {
          return '<%=_resRoot%>/' + filename.replace('assets/', dir);
        }
        return {
          runtime: `window._resRoot + '/${filename.replace('assets/', dir)}'`,
        };
      },
    },
  };
});
