import { GET, POST_JSON } from '@/api/request';
import { PROJECT_STATUS } from '@/constants/project';
import { ProjectData } from '@/types/Project';

export interface ProjectQuery extends Record<string, unknown> {
  pageNow: number;
  limit: number;
  name?: string;
  status?: PROJECT_STATUS;
  sortKey?: string;
  desc?: boolean;
  viewMode?: number; // 作品列表获取项目列表下拉选择，传入viewMode=1
}

/**
 * 获取项目列表
 * @param params 项目列表参数
 * @param params.pageNow 页码
 * @param params.limit 每页数量, 不传默认20
 * @param params.status 项目状态
 * @param params.name 项目名称
 * @param params.sortKey 排序字段, 如：createTime
 * @param params.desc 排序方式，是否倒序
 * @returns
 */
export const getProjectList = (params: ProjectQuery) => {
  return POST_JSON<ProjectData[]>('/api/project/getList', params);
};

/**
 * 获取项目详情
 * @param id 项目id
 * @returns
 */
export const getProjectDetailInfo = (id: number) => {
  return GET<ProjectData>('/api/project/getInfo', {
    id,
  });
};

/**
 * 复制项目
 * @param id 项目id
 * @returns
 */
export const copyProject = (id: number) => {
  return GET<{ success: true }>('/api/project/copy', {
    id,
  });
};

/**
 * 删除项目
 * @param id 项目id
 * @returns
 */
export const deleteProject = (id: number | string) => {
  return GET<{ success: true }>(`/api/project/del`, {
    id,
  });
};

/**
 * 修改项目名称
 * @param params
 * @param params.id 项目id
 * @param params.name 项目名称
 * @returns
 */
export const updateProjectName = (params: {
  id: number | string;
  name: string;
}) => {
  return POST_JSON<{ success: true }>('/api/project/update', params);
};
