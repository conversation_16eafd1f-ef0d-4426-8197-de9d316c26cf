<template>
  <div
    class="image-zoom-container"
    ref="zoomContainerRef"
    :class="{
      'image-zoom-container--active': props.isActive && !isSingleImage,
      'cursor-all-scroll': !isSingleImage,
    }"
    @click="handleClick"
    :key="vdrKey"
  >
    <img
      v-if="props.puzzleImage.resId == ''"
      src="@/assets/common/imgEmpty.webp"
      alt="默认封面"
      class="w-full h-full object-cover"
    />
    <div v-else>
      <!-- 图片容器 -->
      <div
        class="image-zoom-container__image-wrapper"
        @mousedown="startDrag"
        @wheel="handleWheel"
      >
        <ScImg
          :key="`${props.puzzleImage.resId}-${selectedSettingIndex}`"
          :src="props.puzzleImage.resId"
          class="image-zoom-container__image"
          :style="{
            top: `${top}px`,
            left: `${left}px`,
            width: `${originSize.width * scale}px`,
            height: `${originSize.height * scale}px`,
          }"
          @load="handleImageLoad"
          @mousedown="startDrag"
          @wheel="handleWheel"
        />
      </div>

      <fa-tooltip placement="top">
        <template slot="title">
          <span>缩放</span>
        </template>
        <!-- 缩放条 -->
        <div
          v-show="props.isActive && !isSingleImage"
          class="image-zoom-container__zoom-slider"
          title="缩放"
        >
          <div
            class="image-zoom-container__zoom-slider-track"
            @mousedown.stop="startSliderDrag"
          >
            <div
              class="image-zoom-container__zoom-slider-track-left"
              :style="{ width: `${sliderPercentage}%` }"
            ></div>
            <div
              class="image-zoom-container__zoom-slider-thumb"
              :style="{ left: `${sliderPercentage}%` }"
            ></div>
          </div>
        </div>
      </fa-tooltip>
    </div>
  </div>
</template>

<script lang="ts" setup>
import {
  computed,
  onUnmounted,
  onMounted,
  ref,
  reactive,
  watch,
  nextTick,
} from 'vue';
import {
  currentSetting,
  setCurrentSpace,
  isFirstImgSetting,
  isSingleImage,
  currentSpace,
  selectedSettingIndex,
} from '../hook/useWorkInfo';
import ScImg from '@/components/comm/ScImg.vue';
import { eventBus, EVENT_NAMES } from '@/utils/eventBus';

const props = defineProps<{
  puzzleImage: {
    x?: number;
    y?: number;
    scale?: number;
    resId: string;
    type: number;
  };
  isActive: boolean;
  imageIndex: number;
  previewImageBoxSize: {
    width: string;
    height: string;
  };
  previewImageSize: {
    width: string;
    height: string;
  };
}>();

const emit = defineEmits<{
  (e: 'select', index: number): void;
  (e: 'updateHeight', height: number): void;
}>();

// 状态
const vdrKey = ref(0);
const scale = ref(1);
const baseScale = ref(1);
const top = ref(0);
const left = ref(0);
const zoomContainerRef = ref<HTMLElement | null>(null);
const isDragging = ref(false);
const isDraggingSlider = ref(false);
const dragStart = ref({ x: 0, y: 0 });
const dragStartPosition = ref({ top: 0, left: 0 });
const containerRect = ref<DOMRect | null>(null);
const imageRect = ref<DOMRect | null>(null);
const originSize = reactive({ width: 0, height: 0 });

// 添加事件监听器引用
const currentDragHandler = ref<((e: MouseEvent) => void) | null>(null);
const currentDragStopHandler = ref<(() => void) | null>(null);

// 计算属性
const sliderPercentage = computed(() => {
  // 设定最小和最大缩放倍数
  const minZoom = 1;
  const maxZoom = 3;
  // 当前缩放倍数
  const zoomRatio = scale.value / baseScale.value;
  // 计算百分比（0~100）
  const percent = ((zoomRatio - minZoom) / (maxZoom - minZoom)) * 100;
  return percent;
});

/**
 * 获取前后端画布宽度比例
 * @returns 前后端画布宽度比例
 */
const getCanvasScale = computed(() => {
  // 后端画布固定宽度
  const backendWidth = 1080;
  // 获取前端画布宽度
  const frontendWidth = currentSpace.value.renderW;
  // 计算前后端画布宽度比例
  return backendWidth / frontendWidth!;
});

/**
 * 用于设置图片位置
 * @param l left值
 * @param t top值
 */
const changeLeftAndTop = (l: number, t: number): void => {
  left.value = l;
  top.value = t;
};

/**
 * 用于设置缩放值
 * @param s scale值
 */
const changeScale = (s: number): void => {
  scale.value = s;
};
/**
 * 计算基础缩放比例
 * @param imageWidth 图片宽度
 * @param imageHeight 图片高度
 * @param containerWidth 容器宽度
 * @param containerHeight 容器高度
 * @returns 基础缩放比例
 */
const calculateBaseScale = (
  imageWidth: number,
  imageHeight: number,
  containerWidth: number,
  containerHeight: number,
): number => {
  const imageRatio = imageWidth / imageHeight;
  const containerRatio = containerWidth / containerHeight;

  if (isFirstImgSetting.value) {
    // 封面图 && 单图：以宽度为准缩放，高度按比例即可
    if (isSingleImage.value) {
      const MAX_WIDTH = 324;
      const MAX_HEIGHT = 432;
      const MIN_HEIGHT = 324;
      // 计算等比例缩放后的高度
      let scaledWidth = MAX_WIDTH;
      const scale = scaledWidth / imageWidth;
      let scaledHeight = imageHeight * scale;
      let emitHeight = scaledHeight;

      // 限制高度范围
      if (scaledHeight < MIN_HEIGHT) {
        // scaledHeight = MIN_HEIGHT;
        // scaledWidth = scaledHeight * imageRatio;
        emitHeight = MIN_HEIGHT;
      } else if (scaledHeight > MAX_HEIGHT) {
        scaledHeight = MAX_HEIGHT;
        scaledWidth = scaledHeight * imageRatio;
        emitHeight = MAX_HEIGHT;
      }
      // 高度特殊处理，并发送同步给父节点的容器高度
      emit('updateHeight', emitHeight);

      return scaledWidth / imageWidth;
    } else {
      // 封面图 && 拼图：以短边为基准进行等比例缩放
      if (imageRatio >= containerRatio) {
        return containerHeight / imageHeight;
      } else {
        return containerWidth / imageWidth;
      }
    }
  } else {
    if (isSingleImage.value) {
      // 次图 && 单图：计算基础缩放比例，使图片至少一条边填充满宫格
      if (imageRatio >= containerRatio) {
        // 宽图：以宽度为基准，确保宽度填充满宫格
        return containerWidth / imageWidth;
      } else {
        // 高图：以高度为基准，确保高度填充满宫格
        return containerHeight / imageHeight;
      }
    } else {
      // 次图 && 拼图：以短边为基准进行等比例缩放
      if (imageRatio >= containerRatio) {
        return containerHeight / imageHeight;
      } else {
        return containerWidth / imageWidth;
      }
    }
  }
};

/**
 * 设置图片位置
 * @param containerWidth 容器宽度
 * @param containerHeight 容器高度
 * @param scaledWidth 缩放后的图片宽度
 * @param scaledHeight 缩放后的图片高度
 */
const setImagePosition = (
  containerWidth: number,
  containerHeight: number,
  scaledWidth: number,
  scaledHeight: number,
) => {
  // 优先级最高：单图模式直接居中，不用 puzzleStyle 的 x/y
  if (isSingleImage.value) {
    changeLeftAndTop(
      (containerWidth - scaledWidth) / 2,
      (containerHeight - scaledHeight) / 2,
    );
    return;
  }

  // 设置初始位置（多图模式）
  const puzzleStyleList = currentSetting.value?.puzzleStyle;
  const puzzleStyle = puzzleStyleList?.[props.imageIndex];
  const puzzleType = currentSetting.value?.puzzleType;

  if (puzzleStyle && puzzleStyle.isNewCreate) {
    puzzleStyle.isNewCreate = false;
    // 计算居中逻辑
    const offsetX = (containerWidth - scaledWidth) / 2;
    const offsetY = (containerHeight - scaledHeight) / 2;
    changeLeftAndTop(offsetX, offsetY);
  } else if (puzzleStyle && puzzleType) {
    const editVal = initCoordinate(puzzleStyle?.x ?? 0, puzzleStyle?.y ?? 0);
    changeLeftAndTop(editVal.left, editVal.top);
  }
};

/**
 * 初始化坐标转换：将后端坐标转换为前端显示坐标
 * @param x 后端x坐标
 * @param y 后端y坐标
 * @param ratio 图片比例类型
 * @returns 转换后的前端坐标对象
 */
const initCoordinate = (
  x: number,
  y: number,
): { left: number; top: number } => {
  const scale = getCanvasScale.value;
  return {
    left: -x / scale,
    top: -y / scale,
  };
};

/**
 * 保存坐标转换：将前端坐标转换为后端保存坐标
 * @param x 前端x坐标
 * @param y 前端y坐标
 * @param ratio 图片比例类型
 * @returns 转换后的后端坐标对象
 */
const saveCoordinate = (x: number, y: number): { x: number; y: number } => {
  const scale = getCanvasScale.value;
  return {
    x: (x == 0 ? 0 : -x) * scale,
    y: (y == 0 ? 0 : -y) * scale,
  };
};

// 监听scale变化，更新workInfo
watch(
  [() => scale.value, () => left.value, () => top.value],
  async newScale => {
    // watch触发后，会触发布局变化，等待布局和响应式数据更新后再设置图片定位
    await nextTick();
    if (currentSetting.value?.puzzleStyle) {
      const zoom = newScale[0] / baseScale.value;
      if (currentSetting.value.puzzleStyle[props.imageIndex]) {
        currentSetting.value.puzzleStyle[props.imageIndex].scale = zoom;
        // 保存时转换坐标
        const savedCoord = saveCoordinate(newScale[1], newScale[2]);
        currentSetting.value.puzzleStyle[props.imageIndex].x = Math.round(
          savedCoord.x,
        );
        currentSetting.value.puzzleStyle[props.imageIndex].y = Math.round(
          savedCoord.y,
        );
      }
    }
  },
);

const handleChangeRatio = () => {
  vdrKey.value += 1;
};

// 处理图片加载
const handleImageLoad = async (event: Event) => {
  const img = event.target as HTMLImageElement;
  if (!img) return;

  // 获取容器尺寸
  const container = img.closest('.image-zoom-container') as HTMLElement;
  containerRect.value = container.getBoundingClientRect();
  imageRect.value = img.getBoundingClientRect();
  originSize.width = img.naturalWidth;
  originSize.height = img.naturalHeight;

  let containerWidth = containerRect.value.width;
  let containerHeight = containerRect.value.height;
  const imageWidth = originSize.width;
  const imageHeight = originSize.height;

  // 计算初始缩放值
  baseScale.value = calculateBaseScale(
    imageWidth,
    imageHeight,
    containerWidth,
    containerHeight,
  );
  const savedZoom =
    currentSetting.value?.puzzleStyle?.[props.imageIndex]?.scale;
  changeScale(savedZoom ? baseScale.value * savedZoom : baseScale.value);

  // baseScale改变后，会触发布局变化，等待布局和响应式数据更新后再设置图片定位
  await nextTick();

  // 单图加载好就记录留白距离(拼图在ImagePreviewWindow中计算完定位后处理)
  if (isSingleImage.value) {
    const spaceH = (324 - originSize.width * scale.value) / 2;
    const spaceV =
      (parseInt(String(props.previewImageBoxSize.height)) -
        originSize.height * scale.value) /
      2;
    const renderW = originSize.width * scale.value;
    const renderH = originSize.height * scale.value;
    const originW = originSize.width;
    setCurrentSpace(
      selectedSettingIndex.value,
      spaceH,
      spaceV,
      renderW,
      renderH,
      originW,
    );
  }

  // 更新containerRect，设置图片位置
  containerRect.value = container.getBoundingClientRect();
  containerWidth = containerRect.value.width;
  containerHeight = containerRect.value.height;
  const scaledWidth = originSize.width * scale.value;
  const scaledHeight = originSize.height * scale.value;
  setImagePosition(containerWidth, containerHeight, scaledWidth, scaledHeight);
};

// 获取事件点坐标
const getEventPoint = (
  event: MouseEvent | TouchEvent,
): { x: number; y: number } | null => {
  if (event instanceof MouseEvent) {
    return { x: event.clientX, y: event.clientY };
  } else if (event instanceof TouchEvent && event.touches.length > 0) {
    return { x: event.touches[0].clientX, y: event.touches[0].clientY };
  }
  return null;
};

// 开始拖拽
const startDrag = (event: MouseEvent) => {
  if (isSingleImage.value) return; // 单图禁止拖动
  if (event.target !== event.currentTarget) return;
  event.preventDefault();
  isDragging.value = true;
  dragStart.value = { x: event.clientX, y: event.clientY };
  dragStartPosition.value = { top: top.value, left: left.value };
  document.addEventListener('mousemove', handleDrag);
  document.addEventListener('mouseup', stopDrag);
};

// 处理拖拽
const handleDrag = (event: MouseEvent) => {
  if (!isDragging.value) return;

  // 获取鼠标/触摸位置
  const point = getEventPoint(event);
  if (!point) return;

  // 计算移动距离
  const deltaX = point.x - dragStart.value.x;
  const deltaY = point.y - dragStart.value.y;

  // 获取容器和图片元素
  const container = zoomContainerRef.value?.querySelector(
    '.image-zoom-container__image-wrapper',
  ) as HTMLElement;
  if (!container || !containerRect.value) return;

  const img = container.querySelector('img') as HTMLImageElement;
  if (!img) return;

  // 计算图片实际尺寸（考虑缩放）
  const scaledWidth = img.naturalWidth * scale.value;
  const scaledHeight = img.naturalHeight * scale.value;

  // 计算新位置
  const newLeft = dragStartPosition.value.left + deltaX;
  const newTop = dragStartPosition.value.top + deltaY;

  // 检查是否会出现留白
  const containerWidth = containerRect.value.width;
  const containerHeight = containerRect.value.height;

  // 计算图片边缘到容器边缘的距离
  const leftGap = newLeft; // 左边缘到容器左边缘的距离
  const rightGap = containerWidth - (newLeft + scaledWidth); // 右边缘到容器右边缘的距离
  const topGap = newTop; // 上边缘到容器上边缘的距离
  const bottomGap = containerHeight - (newTop + scaledHeight); // 下边缘到容器下边缘的距离

  // 计算最终位置
  let finalLeft = newLeft;
  let finalTop = newTop;

  // 如果出现留白，调整位置让图片贴边
  if (leftGap > 0) {
    finalLeft = 0; // 贴左边
  } else if (rightGap > 0) {
    finalLeft = containerWidth - scaledWidth; // 贴右边
  }

  if (topGap > 0) {
    finalTop = 0; // 贴上边
  } else if (bottomGap > 0) {
    finalTop = containerHeight - scaledHeight; // 贴下边
  }

  // 更新位置
  changeLeftAndTop(finalLeft, finalTop);

  // 阻止默认行为和事件冒泡
  event.preventDefault();
  event.stopPropagation();
};

// 停止拖拽
const stopDrag = () => {
  isDragging.value = false;
  document.removeEventListener('mousemove', handleDrag);
  document.removeEventListener('mouseup', stopDrag);
};

// 处理缩放，originPoint 为缩放原点
const handleZoom = (newScale: number, point: { x: number; y: number }) => {
  // 计算缩放比例变化
  const scaleChange = newScale / scale.value;

  // 从事件目标开始查找图片元素
  const container = zoomContainerRef.value?.querySelector(
    '.image-zoom-container__image-wrapper',
  ) as HTMLElement;
  if (!container || !containerRect.value) return;

  const img = container.querySelector('img') as HTMLImageElement;
  if (!img) return;

  // 计算图片实际尺寸（考虑缩放）
  const scaledWidth = img.naturalWidth * newScale;
  const scaledHeight = img.naturalHeight * newScale;

  // 计算鼠标所在位置跟宫格左上角的偏移量A
  const offsetA = {
    x: point.x - left.value,
    y: point.y - top.value,
  };

  // 计算缩放后的偏移量B = 最新的缩放量 * 偏移量A
  const offsetB = {
    x: offsetA.x * scaleChange,
    y: offsetA.y * scaleChange,
  };

  // 算出偏移量A和偏移量B之间的偏移量C
  const offsetC = {
    x: offsetB.x - offsetA.x,
    y: offsetB.y - offsetA.y,
  };

  // 把偏移量B的位置移动偏移量A初始所在的位置
  const newLeft = left.value - offsetC.x;
  const newTop = top.value - offsetC.y;

  // 更新缩放值
  changeScale(newScale);

  // 只在缩小的时候处理留白
  if (scaleChange < 1) {
    // 检查是否会出现留白
    const containerWidth = containerRect.value.width;
    const containerHeight = containerRect.value.height;

    // 计算图片边缘到容器边缘的距离
    const leftGap = newLeft; // 左边缘到容器左边缘的距离
    const rightGap = containerWidth - (newLeft + scaledWidth); // 右边缘到容器右边缘的距离
    const topGap = newTop; // 上边缘到容器上边缘的距离
    const bottomGap = containerHeight - (newTop + scaledHeight); // 下边缘到容器下边缘的距离

    // 计算最终位置
    let finalLeft = newLeft;
    let finalTop = newTop;

    // 如果出现留白，调整位置让图片贴边
    if (leftGap > 0) {
      finalLeft = 0; // 贴左边
    } else if (rightGap > 0) {
      finalLeft = containerWidth - scaledWidth; // 贴右边
    }

    if (topGap > 0) {
      finalTop = 0; // 贴上边
    } else if (bottomGap > 0) {
      finalTop = containerHeight - scaledHeight; // 贴下边
    }

    // 更新位置
    changeLeftAndTop(finalLeft, finalTop);
  } else {
    // 放大时保持原有逻辑
    changeLeftAndTop(newLeft, newTop);
  }
};

// 处理滚轮缩放
const handleWheel = (event: WheelEvent) => {
  // 只有在选中状态下才处理缩放
  if (!props.isActive || isSingleImage.value) return;

  event.preventDefault();

  // 更新缩放值，限制在基础缩放的 1-3 倍范围内
  const delta = event.deltaY > 0 ? 0.9 : 1.1;
  const newScale = Math.max(
    baseScale.value,
    Math.min(baseScale.value * 3, scale.value * delta),
  );

  // 获取鼠标在容器上的相对位置
  const mouseTarget = event.currentTarget as HTMLElement;
  const mouseEle = mouseTarget.closest('.image-zoom-container') as HTMLElement;
  const rect = mouseEle.getBoundingClientRect();
  const mouseX = event.clientX - rect.left;
  const mouseY = event.clientY - rect.top;
  // 以鼠标位置为原点进行缩放
  handleZoom(newScale, {
    x: mouseX,
    y: mouseY,
  });
};

// 处理滑块缩放
const handleSliderZoom = (percentage: number) => {
  if (isSingleImage.value) return; // 单图禁止缩放
  // 更新缩放值 (0-100 映射到 1-3 倍基础缩放)
  const newScale = baseScale.value * (1 + (percentage / 100) * 2);

  // 获取容器元素
  const container = zoomContainerRef.value?.querySelector(
    '.image-zoom-container__image-wrapper',
  );
  if (!container || !containerRect.value) return;

  // 计算宫格中心点
  const centerPoint = {
    x: containerRect.value.width / 2,
    y: containerRect.value.height / 2,
  };

  // 使用传入的事件对象
  handleZoom(newScale, centerPoint);
};

// 开始滑块拖拽
const startSliderDrag = (event: MouseEvent) => {
  if (isSingleImage.value) return; // 单图禁止缩放
  event.preventDefault();
  event.stopPropagation();

  // 清理之前的事件监听器
  if (currentDragHandler.value && currentDragStopHandler.value) {
    document.removeEventListener('mousemove', currentDragHandler.value);
    document.removeEventListener('mouseup', currentDragStopHandler.value);
  }

  isDraggingSlider.value = true;
  const track = event.currentTarget as HTMLElement;
  const rect = track.getBoundingClientRect();

  // 计算初始百分比，限制在 0-100 范围内
  const percentage = Math.max(
    0,
    Math.min(100, ((event.clientX - rect.left) / rect.width) * 100),
  );

  // 处理初始点击位置的缩放
  handleSliderZoom(percentage);

  const handleSliderDragWrapper = (e: MouseEvent) => {
    if (!isDraggingSlider.value) return;

    const rect = track.getBoundingClientRect();
    // 计算当前鼠标位置对应的百分比，限制在 0-100 范围内
    const percentage = Math.max(
      0,
      Math.min(100, ((e.clientX - rect.left) / rect.width) * 100),
    );

    // 处理滑块缩放，传入事件对象
    handleSliderZoom(percentage);
  };

  const stopSliderDragWrapper = () => {
    isDraggingSlider.value = false;
    if (currentDragHandler.value && currentDragStopHandler.value) {
      document.removeEventListener('mousemove', currentDragHandler.value);
      document.removeEventListener('mouseup', currentDragStopHandler.value);
      currentDragHandler.value = null;
      currentDragStopHandler.value = null;
    }
  };

  // 保存当前的事件处理函数引用
  currentDragHandler.value = handleSliderDragWrapper;
  currentDragStopHandler.value = stopSliderDragWrapper;

  document.addEventListener('mousemove', handleSliderDragWrapper);
  document.addEventListener('mouseup', stopSliderDragWrapper);
};

// 组件挂载时初始化
onMounted(() => {
  eventBus.on(EVENT_NAMES.CHANGE_RATIO, handleChangeRatio);
  const container = document.querySelector(
    '.image-zoom-container',
  ) as HTMLElement;
  if (container) {
    containerRect.value = container.getBoundingClientRect();
  }
});

// 组件卸载时清理
onUnmounted(() => {
  eventBus.off(EVENT_NAMES.CHANGE_RATIO, handleChangeRatio);
  // 清理拖拽事件
  document.removeEventListener('mousemove', handleDrag);
  document.removeEventListener('mouseup', stopDrag);

  // 清理滑块拖动事件
  if (currentDragHandler.value && currentDragStopHandler.value) {
    document.removeEventListener('mousemove', currentDragHandler.value);
    document.removeEventListener('mouseup', currentDragStopHandler.value);
    currentDragHandler.value = null;
    currentDragStopHandler.value = null;
  }
});

// 处理图片点击
const handleClick = () => {
  emit('select', props.imageIndex);
};
</script>

<style lang="scss" scoped>
.image-zoom-container {
  /* 布局相关 */
  @apply relative;
  /* 尺寸相关 */
  @apply w-full h-full;
  /* 外观相关 */
  @apply overflow-hidden;
}

.image-zoom-container__image {
  /* 布局相关 */
  @apply relative block;
  /* 尺寸相关 */
  @apply w-full h-full w-[324px];
}

.image-zoom-container--active {
  @apply relative z-10 box-border;
}

.image-zoom-container--active::after {
  /* 内容相关 */
  @apply content-[''];
  /* 布局相关 */
  @apply absolute inset-0;
  /* 外观相关 */
  @apply border border-dashed border-[#999999];
  /* 交互相关 */
  @apply pointer-events-none;
  /* 盒模型相关 */
  @apply box-border;
}

/* 位置信息显示样式 */
.image-zoom-container__position-info {
  /* 布局相关 */
  @apply absolute top-[10px] left-0 right-0 mx-auto;
  /* 尺寸相关 */
  @apply w-[180px] h-[24px] text-[12px];
  /* 外观相关 */
  @apply rounded-[6px] bg-[#000] bg-opacity-60 text-white;
  /* 布局方式 */
  @apply flex items-center justify-center;
  /* 层级 */
  @apply z-30;
  /* 确保内部元素平滑过渡 */
  will-change: transform;
  /* 阻止事件冒泡，防止点击信息标签时触发背景拖动 */
  @apply cursor-default;
  /* 禁止事件穿透 */
  pointer-events: auto;

  &.info2 {
    @apply top-[35px];
  }
}

/* 缩放滑动条样式 */
.image-zoom-container__zoom-slider {
  /* 布局相关 */
  @apply absolute bottom-[10px] left-0 right-0 mx-auto;
  /* 尺寸相关 */
  @apply w-[80%] h-[32px];
  /* 外观相关 */
  @apply rounded-[6px] bg-[#000] bg-opacity-60;
  /* 布局方式 */
  @apply flex items-center justify-center;
  /* 层级 */
  @apply z-30;
  /* 确保内部元素平滑过渡 */
  will-change: transform;
  /* 阻止事件冒泡，防止点击滑块时触发背景拖动 */
  @apply cursor-default;
  /* 禁止事件穿透 */
  pointer-events: auto;
}

.image-zoom-container__zoom-slider-track {
  /* 布局相关 */
  @apply relative;
  /* 尺寸相关 */
  @apply w-[85%] h-[4px];
  /* 外观相关 */
  @apply bg-[rgba(217,217,217,0.5)] rounded-[2px];
  /* 交互 */
  @apply cursor-pointer;
  /* 确保滑块在轨道上方 */
  @apply z-1;
  /* 优化渲染性能 */
  will-change: contents;
}

/* 滑块左侧轨道 */
.image-zoom-container__zoom-slider-track-left {
  /* 布局相关 */
  @apply absolute left-0 h-full;
  /* 外观相关 */
  @apply bg-[#fff] rounded-l-[2px];
  /* 居中定位，与轨道高度一致 */
  @apply top-[50%];
  transform: translateY(-50%);
  /* 设置高度与主轨道相同 */
  height: 4px;
  /* 避免鼠标事件干扰 */
  pointer-events: none;
  /* 优化渲染性能 */
  will-change: width;
  /* 防止出现锯齿 */
  backface-visibility: hidden;
}

.image-zoom-container__zoom-slider-thumb {
  /* 布局相关 */
  @apply absolute top-[50%];
  transform: translateY(-50%);
  /* 尺寸相关 */
  @apply w-[14px] h-[14px];
  /* 外观相关 */
  @apply bg-[#fff] border-[2px] border-solid border-[#3370ff] rounded-full;
  /* 交互 */
  @apply cursor-grab;
  /* 效果 */
  @apply transition-none;
  /* 确保滑块在最上层 */
  @apply z-10;
  /* 允许事件，但阻止冒泡 */
  pointer-events: auto;
  /* 优化渲染性能 */
  will-change: left;
  /* 防止出现锯齿 */
  backface-visibility: hidden;
}
</style>
