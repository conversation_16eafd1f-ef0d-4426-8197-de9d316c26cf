<template>
  <div class="video-player">
    <div
      ref="videoContainer"
      class="video-player__container"
      :class="containerClasses"
    >
      <div class="video-player__video-container">
        <!-- 加载状态 -->
        <LoadingState
          v-if="showLoadingState"
          :percent="currentProgress"
          :loadingText="stateLoadingText"
          :width="88"
        />

        <!-- 重新生成待选择提示 -->
        <RegeneratedState
          v-if="showRegeneratedState"
          :progress="currentProgress"
          @preview-video="handlePreviewVideo"
        />

        <!-- 生成失败状态 -->
        <FailedState
          v-if="showFailedState"
          :errTitle="failedTitle"
          :errMsg="failedErrorMessage"
          :showRetryButton="isRetryFailed"
          :workType="PROJECT_TYPE_VIDEO"
          :pinchPoint="workData?.pinchPoint"
          @retry="handleFailedStateRestore"
        />

        <!-- 错误状态 -->
        <ErrorState
          v-if="showErrorState"
          message="视频加载失败，请稍后重试"
          @retry="handleRetry"
        />

        <!-- 视频播放器 -->
        <div class="video-player__video-wrapper" v-show="showVideoPlayer">
          <video
            ref="videoPlayer"
            class="video-player__video video-js vjs-default-skin"
            preload="none"
            playsinline
            webkit-playsinline
            controlsList="nodownload"
            @contextmenu.prevent
          />

          <!-- 封面图层 - 在视频加载时保持显示 -->
          <div
            v-if="shouldShowPoster && posterUrl"
            class="video-player__poster-overlay"
            :style="{ backgroundImage: `url(${posterUrl})` }"
          >
            <!-- 加载指示器 -->
            <div
              v-if="shouldShowPoster && posterUrl && isVideoLoading && false"
              class="video-player__loading-indicator"
            >
              <fa-spin size="large" />
            </div>
          </div>
        </div>

        <!-- 全屏按钮 -->
        <FullScreenButton
          :is-fullscreen="isFullscreen"
          :is-disabled="isControlDisabled"
          @toggle="toggleFullScreen"
        />
      </div>
    </div>

    <!-- 控制栏 -->
    <VideoControls
      :is-playing="isPlaying"
      :is-disabled="isControlDisabled"
      :is-loading="isControlsLoading"
      :buffered-percent="bufferedPercent"
      :played-percent="playedPercent"
      :current-time="currentTime"
      :duration="duration || workData?.duration || 0"
      @toggle-play="togglePlay"
      @seek="setPlayerTime"
      @seeking="handleSeeking"
      @drag-start="handleDragStart"
      @drag-end="handleDragEnd"
    />
  </div>
</template>

<script lang="ts">
import {
  defineComponent,
  ref,
  computed,
  onMounted,
  onUnmounted,
  onBeforeUnmount,
  type Ref,
  type PropType,
} from 'vue';
import type Player from 'video.js/dist/types/player';
import 'video.js/dist/video-js.css';
import {
  WORK_STATUS,
  getWorkStatusInfo,
  isRetryFailedWork,
} from '@/constants/workStatus';
import {
  eventBus,
  EVENT_NAMES,
  type StopCurrentVideoData,
} from '@/utils/eventBus';
import FullScreenButton from '@/views/EditProjectView/pages/SecondStepVideo/components/VideoPlayer/components/FullScreenButton.vue';
import VideoControls from '@/views/EditProjectView/pages/SecondStepVideo/components/VideoPlayer/components/VideoControls.vue';

import { VideoPlayerEventParams } from './types';
import { usePlayerEvents } from './hooks/usePlayerEvents';
import { useFullscreen } from './hooks/useFullscreen';
import { usePlayer } from './hooks/usePlayer';
import { useVideoPlayerState } from './hooks/useVideoPlayerState';
import LoadingState from '@/components/WorkView/WorkModal/LoadingPreview.vue';
import RegeneratedState from './components/RegeneratedState.vue';
import FailedState from './components/FailedState.vue';
import ErrorState from './components/ErrorState.vue';
import { getMaterialFullUrl } from '@/components/MaterialBasicUpload/utils/index.ts';
import { FILE_TYPES } from '@/constants/fileType';
import { useWorkRestore } from '@/views/EditProjectView/composables/useWorkRestore';
import type { VideoWorkItem } from '@/types/Work';
import { PROJECT_TYPE_VIDEO } from '@/views/EditProjectView/constants';

/**
 * 视频播放器组件
 * @description 基于 VideoJS 的视频播放器组件，支持播放控制、全屏、进度控制等功能
 *
 * @features
 * - 视频播放/暂停控制
 * - 全屏模式切换
 * - 进度条拖拽控制
 * - 多种状态展示（加载中、失败、重新生成等）
 * - 失败提示关闭时自动更新作品状态
 */
export default defineComponent({
  name: 'VideoPlayer',
  components: {
    LoadingState,
    RegeneratedState,
    FailedState,
    ErrorState,
    VideoControls,
    FullScreenButton,
  },
  props: {
    videoUrl: {
      type: String,
      required: true,
    },
    progress: {
      type: Number,
      default: 0,
    },
    /** 封面图资源ID */
    coverImg: {
      type: String,
      default: '',
    },
    /** 封面图类型 */
    coverImgType: {
      type: Number,
      default: 0,
    },
    /** 当前作品数据 - 包含状态信息 */
    workData: {
      type: Object as PropType<VideoWorkItem | undefined>,
      default: undefined,
    },
  },
  emits: {
    'video-player:action': (_: VideoPlayerEventParams) => true,
  },
  setup(props, { emit }) {
    // DOM 引用
    const videoPlayer = ref<HTMLVideoElement | null>(null);
    const videoContainer = ref<HTMLDivElement | null>(null);

    // 计算属性
    const videoUrlValue = computed(() => props.videoUrl);
    const progressValue = computed(() => props.progress);

    // 失败状态错误信息
    const failedErrorMessage = computed(() => {
      return props.workData?.errMsg || '生成失败';
    });

    // 失败状态标题
    const failedTitle = computed(() => {
      if (!props.workData) return '作品生成失败';

      const statusInfo = getWorkStatusInfo(props.workData);
      const shouldShowPointsRefund = props.workData.pinchPoint === true;

      if (isRetryFailedWork(statusInfo)) {
        return shouldShowPointsRefund
          ? '重新生成作品失败，创作点数已自动返还'
          : '重新生成作品失败';
      } else {
        return shouldShowPointsRefund
          ? '作品生成失败，创作点数已自动返还'
          : '作品生成失败';
      }
    });

    // 是否为重新生成失败状态
    const isRetryFailed = computed(() => {
      if (!props.workData) return false;

      const statusInfo = getWorkStatusInfo(props.workData);
      return isRetryFailedWork(statusInfo);
    });

    // 封面图URL计算属性
    const posterUrl = computed(() => {
      if (!props.coverImg || !props.coverImgType) return '';

      // 使用getMaterialFullUrl获取完整的封面图URL
      return getMaterialFullUrl(
        props.coverImg,
        FILE_TYPES.WEBP || props.coverImgType,
        'user',
      );
    });

    // 播放器管理 hook
    const {
      player,
      isInitialized,
      isLoading,
      isError,
      initializePlayer,
      destroyPlayer,
      retryInitialization,
      // 视频加载状态
      isVideoLoading,
      isVideoReadyToPlay,
      shouldShowPoster,
      // 控制条加载状态
      isControlsLoading,
      // 播放状态
      isPlaying,
      currentTime,
      duration,
      bufferedPercent,
      playedPercent,
      isControlDisabled,
    } = usePlayer({
      videoElement: videoPlayer,
      videoUrl: videoUrlValue,
      workItem: computed(() => props.workData || null),
      posterUrl: posterUrl,
      onInitialized: () => {
        // 设置事件监听
        setupPlayerEvents();
      },
    });

    // 状态管理 hook
    const {
      showLoadingState,
      showRegeneratedState,
      showFailedState,
      showErrorState,
      showVideoPlayer,
      containerClasses,
      loadingText: stateLoadingText,
      currentProgress,
    } = useVideoPlayerState(
      progressValue,
      isLoading,
      isError,
      computed(() => props.workData),
    );

    // 使用全屏 hook
    const { isFullscreen, toggleFullscreen } = useFullscreen(
      videoPlayer,
      player as Ref<Player | null>,
    );

    // 拖动状态管理
    const isDragging = ref(false);

    // 使用播放器事件 hook
    const { setupPlayerEvents, cleanupPlayerEvents } = usePlayerEvents({
      emit: (actionType: VideoPlayerEventParams['actionType']) => {
        emit('video-player:action', { actionType });
      },
      player: player as unknown as Ref<Player | null>,
      isPlaying,
      currentTime,
      duration,
      isDragging, // 传递拖动状态
    });

    // 在组件挂载后初始化播放器并注册事件监听
    onMounted(async () => {
      if (videoPlayer.value && videoUrlValue.value) {
        await initializePlayer(cleanupPlayerEvents);
      }

      // 注册停止视频播放事件监听
      eventBus.on(EVENT_NAMES.STOP_CURRENT_VIDEO, handleStopCurrentVideo);
    });

    // 组件卸载前清理播放器资源（确保在 DOM 销毁前完成）
    onBeforeUnmount(async () => {
      console.log(`[MEMORY] VideoPlayer: 开始清理播放器资源`);

      // 清理 VideoJS 播放器实例和相关资源，包括事件监听器
      await destroyPlayer(cleanupPlayerEvents);
    });

    // 组件卸载时清理事件监听器
    onUnmounted(() => {
      // 清理 EventBus 事件监听器
      eventBus.off(EVENT_NAMES.STOP_CURRENT_VIDEO, handleStopCurrentVideo);

      console.log(`[MEMORY] VideoPlayer: 组件卸载，事件监听器清理完成`);
    });

    /**
     * 切换播放状态
     * @description 在播放和暂停状态之间切换，首次播放时会触发视频加载
     */
    const togglePlay = async () => {
      if (!player.value) return;

      if (isPlaying.value) {
        player.value.pause();
      } else {
        try {
          await player.value.play();
        } catch (error) {
          console.error('视频播放失败:', error);
        }
      }
    };

    /**
     * 设置播放时间
     * @description 跳转到指定时间点，确保时间值有效
     * @param time 目标时间（秒）
     */
    const setPlayerTime = (time: number) => {
      if (!player.value) return;
      player.value.currentTime(time);
    };

    /**
     * 处理视频预览请求
     * @description 当用户点击预览按钮时，通过eventBus触发统一的新旧选择弹窗
     */
    const handlePreviewVideo = () => {
      // 通过eventBus触发统一的新旧选择弹窗
      eventBus.emit(EVENT_NAMES.SHOW_NEW_OLD_CHOICE_MODAL, props.workData);
    };

    /**
     * 处理拖动进度条时的预览
     * @description 在拖动进度条时更新时间显示，但不实际改变播放位置
     * @param time 预览的时间点（秒）
     */
    const handleSeeking = (time: number) => {
      if (!player.value) return;

      // 只在拖动时更新时间显示，不影响实际播放位置
      // 这里的更新主要用于时间显示的同步
      currentTime.value = time;
    };

    /**
     * 处理拖动开始事件
     * @description 设置拖动状态，阻止timeupdate事件干扰
     */
    const handleDragStart = () => {
      isDragging.value = true;
    };

    /**
     * 处理拖动结束事件
     * @description 重置拖动状态，恢复timeupdate事件更新
     */
    const handleDragEnd = () => {
      isDragging.value = false;
    };

    /**
     * 处理重试操作
     * @description 重试初始化播放器
     */
    const handleRetry = () => {
      retryInitialization();
    };

    /**
     * 停止视频播放
     * @description 暂停视频播放，用于在打开编辑器前停止播放
     */
    const stopVideo = () => {
      if (!player.value) return;

      if (isPlaying.value) {
        player.value.pause();
      }
    };

    /**
     * 处理停止当前视频事件
     * @description 通过 eventBus 接收停止视频播放的事件，只有当前作品ID匹配时才执行停止操作
     * @param data 事件数据，包含需要停止的作品ID
     */
    const handleStopCurrentVideo = (data: unknown) => {
      // 类型检查和转换
      const eventData = data as StopCurrentVideoData;
      if (!eventData || typeof eventData.workId !== 'number') {
        console.warn(`[MEMORY] VideoPlayer: 收到无效的停止视频事件数据`, data);
        return;
      }

      // 只有当前作品ID匹配时才停止播放
      if (props.workData?.id === eventData.workId) {
        console.log(
          `🎬 VideoPlayer: 收到停止视频播放事件，作品ID: ${eventData.workId}`,
        );
        stopVideo();
      }
    };

    // 初始化恢复旧作品功能
    const { restoreWork } = useWorkRestore({
      showSuccessMessage: true,
      showErrorMessage: true,
    });

    /**
     * 处理失败状态恢复事件
     * @description 当用户点击"恢复旧作品"按钮时触发，用于选择旧作品并同步到服务端
     *
     * @async
     * @function handleFailedStateRestore
     * @returns {Promise<void>} 无返回值的异步操作
     */
    const handleFailedStateRestore = async (): Promise<void> => {
      // 确保关联作品ID存在且有效
      if (!props.workData?.relWorkId) {
        console.warn('[VideoPlayer] 无法获取有效的关联作品ID，无法恢复旧作品', {
          workData: props.workData,
          relWorkId: props.workData?.relWorkId,
        });
        return;
      }

      // 使用统一的恢复旧作品逻辑
      const [err] = await restoreWork({
        relWorkId: props.workData.relWorkId,
      });

      if (err) {
        console.error('[VideoPlayer] 恢复旧作品失败:', err);
        return;
      }

      // 恢复成功后的处理逻辑
      console.log('[VideoPlayer] 恢复旧作品成功');

      // 发送刷新作品列表事件
      emit('video-player:action', { actionType: 'refresh-full-list' });
    };

    return {
      // DOM引用
      videoContainer,
      videoPlayer,

      // 状态
      isLoading,
      isError,
      isPlaying,
      currentTime,
      duration,
      bufferedPercent,
      playedPercent,
      isFullscreen,
      isControlDisabled,
      playerInitialized: isInitialized,

      // 计算属性
      videoUrlValue,
      failedErrorMessage,
      failedTitle,
      isRetryFailed,
      posterUrl,

      // 视频加载状态
      isVideoLoading,
      isVideoReadyToPlay,
      shouldShowPoster,
      // 控制条加载状态
      isControlsLoading,

      // 状态管理hook的返回值
      showLoadingState,
      showRegeneratedState,
      showFailedState,
      showErrorState,
      showVideoPlayer,
      containerClasses,
      stateLoadingText,
      currentProgress,

      // 常量
      WORK_STATUS,
      PROJECT_TYPE_VIDEO,

      // 方法
      togglePlay,
      setPlayerTime,
      handleSeeking,
      handleDragStart,
      handleDragEnd,
      toggleFullScreen: toggleFullscreen,
      initializePlayer,
      handlePreviewVideo,
      handleRetry,
      stopVideo,
      handleFailedStateRestore,
    };
  },
});
</script>

<style lang="scss" scoped>
.video-player {
  /* 布局相关 */
  @apply relative flex flex-col;
  /* 尺寸相关 */
  @apply w-full h-full;
}

.video-player__container {
  /* 布局相关 */
  @apply relative overflow-hidden flex justify-center items-center flex-1;
  /* 尺寸相关 */
  @apply w-full py-0;
  /* 外观相关 */
  @apply bg-black rounded-lg;
  border-radius: 8px;
  background: #f3f3f5;
}

.video-player__video-container {
  /* 布局相关 */
  @apply flex justify-center items-center;
  /* 尺寸相关 */
  @apply w-full h-full;
  /* 外观相关 */
  @apply object-contain;
}

.video-player__container--generating {
  /* 文字相关 */
  @apply text-0;
}

.video-player__container--regenerating {
  /* 文字相关 */
  @apply text-0;
}

.video-player__video-wrapper {
  /* 布局相关 */
  @apply flex justify-center items-center relative;
  /* 尺寸相关 */
  @apply w-full h-full;
}

.video-player__video {
  /* 尺寸相关 */
  @apply w-full h-full;
  /* 外观相关 */
  @apply object-contain;
}

.video-player__poster-overlay {
  /* 布局相关 */
  @apply absolute top-0 left-0 flex justify-center items-center;
  /* 尺寸相关 */
  @apply w-full h-full;
  /* 外观相关 */
  @apply bg-center bg-contain bg-no-repeat;
  /* 层级相关 - 使用项目规范的video-controls层级 */
  @apply zi-video-controls;
  /* 背景颜色 - 透明 */
  background-color: transparent;
}

.video-player__loading-indicator {
  /* 布局相关 */
  @apply absolute top-1/2 left-1/2 flex justify-center items-center;
  /* 位置调整 - 垂直居中 */
  transform: translate(-50%, -50%);
  /* 层级相关 - 确保显示在封面图之上 */
  @apply zi-video-controls-1;
}

/* 视频样式覆盖 */
:deep(.video-js) {
  /* 尺寸相关 */
  @apply w-full h-full;
  /* 外观相关 */
  @apply overflow-hidden;
  background-color: transparent;
}

:deep(.vjs-tech) {
  /* 尺寸相关 */
  @apply w-full h-full;
  /* 外观相关 */
  @apply object-contain;
}

:deep(.vjs-poster) {
  /* 外观相关 */
  background-size: contain;
  img {
    cursor: default;
  }
}

:deep(.vjs-error-display) {
  /* 显示相关 */
  @apply hidden;
}

:deep(.loading-wrapper) {
  /* 尺寸相关 */
  @apply border-rd-0 h-full p-20px max-w-[unset] w-auto;
  /* 最小宽度限制 */
  min-width: 220px;
  /* 宽高比例 9:16，高度100%，宽度自动计算 */
  aspect-ratio: 9 / 16;
  /* 显示模式 contain 效果 */
  object-fit: contain;
}

:deep(.failed-state) {
  /* 尺寸相关 */
  @apply border-rd-0 h-full p-20px max-w-[unset] w-auto;
  /* 最小宽度限制 */
  min-width: 220px;
  /* 宽高比例 9:16，高度100%，宽度自动计算 */
  aspect-ratio: 9 / 16;
  /* 显示模式 contain 效果 */
  object-fit: contain;
}

.video-player__alert {
  /* 布局相关 */
  @apply absolute top-[8px] left-[8px] right-[8px] zi-notification color-[#333];
}

/* 旋转动画 */
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
</style>
