/**
 * @fileoverview 表单系统完整类型定义
 * @description 包含动态表单系统的所有类型定义，包括表单项、验证规则、表单接口、事件等
 * <AUTHOR>
 * @since 1.0.0
 */

// ============= 基础类型导入 =============

import type {
  ProjectType,
  FieldType,
  MediaType,
  FormLayout,
  ValidationTrigger,
  BaseValidationResult,
  FormItemOption,
  FileStatus,
} from './base';

import type { FileInfo } from './file';

// ============= 表单验证相关类型 =============

/**
 * @description 表单验证规则接口，定义表单字段的验证规则。
 * @interface FormRule
 * @template T 待验证值的类型。
 */
export interface FormRule<T = unknown> {
  /**
   * 是否为必填字段。
   * @type {boolean}
   * @memberof FormRule
   */
  required?: boolean;
  /**
   * 验证失败时显示的错误提示消息。
   * @type {string}
   * @memberof FormRule
   */
  message?: string;
  /**
   * 验证触发时机，可以是单个触发器或触发器数组。
   * @type {ValidationTrigger | ValidationTrigger[]}
   * @memberof FormRule
   */
  trigger?: ValidationTrigger | ValidationTrigger[];
  /**
   * 自定义验证器函数，可以实现复杂的验证逻辑。
   * @type {Function}
   * @memberof FormRule
   * @param {FormRule<T>} rule - 当前验证规则。
   * @param {T} value - 要验证的值。
   * @param {(error?: Error) => void} callback - 回调函数，传入Error表示验证失败。
   */
  validator?: (
    rule: FormRule<T>,
    value: T,
    callback: (error?: Error) => void,
  ) => void;
  /**
   * 字段最小长度限制，适用于字符串和数组。
   * @type {number}
   * @memberof FormRule
   */
  min?: number;
  /**
   * 字段最大长度限制，适用于字符串和数组。
   * @type {number}
   * @memberof FormRule
   */
  max?: number;
  /**
   * 正则表达式模式验证，用于字符串匹配。
   * @type {RegExp}
   * @memberof FormRule
   */
  pattern?: RegExp;
  /**
   * 字段类型验证，用于检查值的类型。
   * @type {'string' | 'number' | 'boolean' | 'array' | 'object' | 'email' | 'url' | 'date'}
   * @memberof FormRule
   */
  type?:
    | 'string'
    | 'number'
    | 'boolean'
    | 'array'
    | 'object'
    | 'email'
    | 'url'
    | 'date';
  /**
   * 数值范围验证：最小值。
   * @type {number}
   * @memberof FormRule
   */
  minValue?: number;
  /**
   * 数值范围验证：最大值。
   * @type {number}
   * @memberof FormRule
   */
  maxValue?: number;
  /**
   * 白名单值验证：允许的值列表。
   * @type {T[]}
   * @memberof FormRule
   */
  whitelist?: T[];
  /**
   * 黑名单值验证：禁止的值列表。
   * @type {T[]}
   * @memberof FormRule
   */
  blacklist?: T[];
  /**
   * 自动触发标记，用于控制验证的自动触发行为。
   * @type {boolean}
   * @memberof FormRule
   */
  triggerAuto?: boolean;
  /**
   * 其他扩展验证规则属性，用于支持自定义验证规则。
   * @type {unknown}
   * @memberof FormRule
   */
  [key: string]: unknown;
}

/**
 * @description 表单验证结果接口，扩展基础验证结果并添加消息。
 * @interface ValidationResult
 * @extends BaseValidationResult
 */
export interface ValidationResult extends BaseValidationResult {
  /**
   * 验证消息，可用于显示给用户的提示。
   * @type {string}
   * @memberof ValidationResult
   */
  message?: string;
}

/**
 * @description 详细验证结果接口，提供更详细的验证信息。
 * @interface DetailedValidationResult
 * @extends BaseValidationResult
 */
export interface DetailedValidationResult extends BaseValidationResult {
  /**
   * 验证错误信息，键为字段名，值为错误消息数组。
   * @type {Record<string, string[]>}
   * @memberof DetailedValidationResult
   */
  errors: Record<string, string[]>;
  /**
   * 验证通过的字段列表。
   * @type {string[]}
   * @memberof DetailedValidationResult
   */
  validFields: string[];
  /**
   * 验证失败的字段列表。
   * @type {string[]}
   * @memberof DetailedValidationResult
   */
  invalidFields: string[];
}

/**
 * @description 基于字段类型的条件验证规则，根据字段类型提供不同的验证规则。
 * @template T 字段值类型。
 * @typedef {object} ConditionalFormRule
 */
export type ConditionalFormRule<T = unknown> = T extends string
  ? FormRule<string> & { minLength?: number; maxLength?: number }
  : T extends number
  ? FormRule<number> & { minValue?: number; maxValue?: number }
  : T extends unknown[]
  ? FormRule<unknown[]> & { minLength?: number; maxLength?: number }
  : FormRule<T>;

/**
 * @description 验证器函数类型，用于验证表单值。
 * @template T 验证值的类型。
 * @typedef {Function} ValidatorFunction
 * @returns {boolean | string | Promise<boolean | string>} 返回布尔值表示验证结果，字符串表示错误消息。
 */
export type ValidatorFunction<T = unknown> = (
  value: T,
  rule?: FormRule<T>,
) => boolean | string | Promise<boolean | string>;

/**
 * @description 异步验证器函数类型，专用于异步验证。
 * @template T 验证值的类型。
 * @typedef {Function} AsyncValidatorFunction
 * @returns {Promise<boolean | string>} 返回Promise，解析为布尔值表示验证结果，字符串表示错误消息。
 */
export type AsyncValidatorFunction<T = unknown> = (
  value: T,
  rule?: FormRule<T>,
) => Promise<boolean | string>;

/**
 * @description 验证器工厂函数类型，用于创建验证器函数。
 * @template T 验证值的类型。
 * @typedef {Function} ValidatorFactory
 * @returns {ValidatorFunction<T>} 返回一个验证器函数。
 */
export type ValidatorFactory<T = unknown> = (
  options?: Record<string, unknown>,
) => ValidatorFunction<T>;

/**
 * @description 常用验证器集合接口，提供常见的验证器函数。
 * @interface CommonValidators
 */
export interface CommonValidators {
  /**
   * 必填验证器，检查值是否存在且不为空。
   * @type {ValidatorFunction<unknown>}
   * @memberof CommonValidators
   */
  required: ValidatorFunction<unknown>;
  /**
   * 邮箱验证器，检查字符串是否为有效的邮箱格式。
   * @type {ValidatorFunction<string>}
   * @memberof CommonValidators
   */
  email: ValidatorFunction<string>;
  /**
   * 手机号验证器，检查字符串是否为有效的手机号格式。
   * @type {ValidatorFunction<string>}
   * @memberof CommonValidators
   */
  phone: ValidatorFunction<string>;
  /**
   * URL验证器，检查字符串是否为有效的URL格式。
   * @type {ValidatorFunction<string>}
   * @memberof CommonValidators
   */
  url: ValidatorFunction<string>;
  /**
   * 身份证验证器，检查字符串是否为有效的身份证号码。
   * @type {ValidatorFunction<string>}
   * @memberof CommonValidators
   */
  idCard: ValidatorFunction<string>;
  /**
   * 数字验证器，检查值是否为有效的数字。
   * @type {ValidatorFunction<string | number>}
   * @memberof CommonValidators
   */
  number: ValidatorFunction<string | number>;
  /**
   * 整数验证器，检查值是否为有效的整数。
   * @type {ValidatorFunction<string | number>}
   * @memberof CommonValidators
   */
  integer: ValidatorFunction<string | number>;
  /**
   * 正数验证器，检查值是否为正数。
   * @type {ValidatorFunction<string | number>}
   * @memberof CommonValidators
   */
  positive: ValidatorFunction<string | number>;
}

// ============= 表单项相关类型 =============

/**
 * @description 表单项附加属性接口，定义传递给底层UI组件的属性。
 * @interface FormItemAttrs
 */
export interface FormItemAttrs {
  /**
   * 字段最大长度限制，用于输入框和文本域。
   * @type {number}
   * @memberof FormItemAttrs
   */
  maxLength?: number;
  /**
   * 是否显示字符计数，用于输入框和文本域。
   * @type {boolean}
   * @memberof FormItemAttrs
   */
  showCount?: boolean;
  /**
   * 输入框行数，用于文本域。
   * @type {number}
   * @memberof FormItemAttrs
   */
  rows?: number;
  /**
   * 是否自动调整高度，用于文本域。
   * @type {boolean}
   * @memberof FormItemAttrs
   */
  autoSize?: boolean;
  /**
   * 是否允许清空，用于输入框和选择器。
   * @type {boolean}
   * @memberof FormItemAttrs
   */
  allowClear?: boolean;
  /**
   * 其他扩展属性，用于支持更多UI组件属性。
   * @type {unknown}
   * @memberof FormItemAttrs
   */
  [key: string]: unknown;
}

/**
 * @description 表单项基础接口，定义表单项的通用属性。
 * @interface BaseFormItem
 */
export interface BaseFormItem {
  /**
   * 表单控件类型标识，决定使用哪种UI控件渲染。
   * @type {string | number}
   * @memberof BaseFormItem
   */
  type: string | number;
  /**
   * 字段属性名，表单提交时的键名，用于标识表单数据。
   * @type {string}
   * @memberof BaseFormItem
   */
  prop: string;
  /**
   * 表单项显示标签文本，显示在表单控件前面。
   * @type {string}
   * @memberof BaseFormItem
   */
  label: string;
  /**
   * 字段验证规则数组，用于验证表单值。
   * @type {FormRule[]}
   * @memberof BaseFormItem
   */
  rules?: FormRule[];
  /**
   * 是否为必填字段。
   * @type {boolean}
   * @memberof BaseFormItem
   */
  required?: boolean;
  /**
   * 扩展属性，用于存储其他自定义数据。
   * @type {unknown}
   * @memberof BaseFormItem
   */
  [key: string]: unknown;
}

/**
 * @description 内部表单项接口（完整配置），用于组件内部的表单项配置。
 * @interface FormItemInternal
 * @extends BaseFormItem
 */
export interface FormItemInternal extends BaseFormItem {
  /**
   * 表单控件类型，决定使用哪种UI控件渲染。
   * @type {string | number}
   * @memberof FormItemInternal
   */
  type: string | number;
  /**
   * 表单项标签文本，显示在表单控件前面。
   * @type {string}
   * @memberof FormItemInternal
   */
  label: string;
  /**
   * 字段属性名，表单提交时的键名。
   * @type {string}
   * @memberof FormItemInternal
   */
  prop: string;
  /**
   * 是否为必填字段，true表示必填。
   * @type {boolean}
   * @memberof FormItemInternal
   */
  required?: boolean;
  /**
   * 输入提示文本，显示在输入框内的提示信息。
   * @type {string}
   * @memberof FormItemInternal
   */
  placeholder?: string;
  /**
   * 验证规则数组，用于验证表单值。
   * @type {FormRule[]}
   * @memberof FormItemInternal
   */
  rules: FormRule[];
  /**
   * 控件的附加属性配置，用于传递给底层UI组件，例如原生HTML属性或特定组件库的属性。
   * @type {FormItemAttrs}
   * @memberof FormItemInternal
   */
  attrs: FormItemAttrs;
  /**
   * 选择类控件的选项列表，用于下拉框、单选框等。
   * @type {FormItemOption[]}
   * @memberof FormItemInternal
   */
  options?: FormItemOption[];
  /**
   * 是否启用AI推荐功能，用于智能表单填充。
   * @type {boolean}
   * @memberof FormItemInternal
   */
  openAiTip?: boolean;
  /**
   * AI推荐的依赖字段列表，用于计算推荐内容。
   * @type {string[]}
   * @memberof FormItemInternal
   */
  prompt?: string[];
  /**
   * 媒体文件类型限制，用于上传控件。
   * @type {MediaType}
   * @memberof FormItemInternal
   */
  mediaType?: MediaType;
  /**
   * 字段长度限制，用于文本输入和文件上传数量。
   * @type {number}
   * @memberof FormItemInternal
   */
  maxLength?: number;
  /**
   * 字段描述文本，显示在表单项下方的辅助说明。
   * @type {string}
   * @memberof FormItemInternal
   */
  description?: string;
  /**
   * 是否支持AI改嘴型功能（视频类型专用）。
   * @type {boolean}
   * @memberof FormItemInternal
   */
  hasModifyMouth?: boolean;
  /**
   * AI改嘴型开关的当前状态。
   * @type {boolean}
   * @memberof FormItemInternal
   */
  openModifyMouth?: boolean;
  /**
   * 插槽名称，当type为'slot'时使用，用于自定义渲染。
   * @type {string}
   * @memberof FormItemInternal
   */
  slotName?: string;
  /**
   * 字段默认值，初始化表单时使用。
   * @type {unknown}
   * @memberof FormItemInternal
   */
  defaultValue?: unknown;
  /**
   * 是否禁用该表单项，禁用后不可编辑。
   * @type {boolean}
   * @memberof FormItemInternal
   */
  disabled?: boolean;
  /**
   * 标签选择器最大标签数量。
   * @type {number}
   * @memberof FormItemInternal
   */
  maxTagCount?: number;
  /**
   * 标签选择器单个标签最大文本长度。
   * @type {number}
   * @memberof FormItemInternal
   */
  maxTagTextLength?: number;
  /**
   * 是否启用AI功能，通用AI辅助标记。
   * @type {boolean}
   * @memberof FormItemInternal
   */
  enableAI?: boolean;
  /**
   * 资源ID，用于获取示例资源。
   * @type {string}
   * @memberof FormItemInternal
   */
  resId?: string;
  /**
   * 资源类型，对应后端类型ID。
   * @type {number}
   * @memberof FormItemInternal
   */
  resType?: number;
  /**
   * 封面图ID，用于获取示例资源封面。
   * @type {string}
   * @memberof FormItemInternal
   */
  coverId?: string;
  /**
   * 封面图类型，对应后端类型ID。
   * @type {number}
   * @memberof FormItemInternal
   */
  coverType?: number;
  /**
   * 默认填写内容
   * @type {string}
   * @memberof FormItemInternal
   */
  defaultContent?: string;
}

/**
 * @description 基础信息表单项（API格式），用于与后端API交互的表单项格式。
 * @interface InputFormItem
 */
export interface InputFormItem {
  /**
   * 字段变量名，表单提交时的键名。
   * @type {string}
   * @memberof InputFormItem
   */
  variable: string;
  /**
   * 字段显示标签文本。
   * @type {string}
   * @memberof InputFormItem
   */
  label: string;
  /**
   * 字段最小长度限制。
   * @type {number}
   * @memberof InputFormItem
   */
  minLength?: number;
  /**
   * 字段最大长度限制。
   * @type {number}
   * @memberof InputFormItem
   */
  maxLength?: number;
  /**
   * 是否为必填字段。
   * @type {boolean}
   * @memberof InputFormItem
   */
  required: boolean;
  /**
   * 字段类型标识，对应 {@link FieldType} 枚举。
   * @type {FieldType}
   * @memberof InputFormItem
   * @note 后端API字段名为 filedType（拼写错误），为保持兼容性暂时保留
   */
  filedType: FieldType;
  /**
   * 输入提示文本。
   * @type {string}
   * @memberof InputFormItem
   */
  placeholder?: string;
  /**
   * 选择类型字段的选项列表。
   * @type {FormItemOption[]}
   * @memberof InputFormItem
   */
  options?: FormItemOption[];
  /**
   * 是否启用AI推荐功能。
   * @type {boolean}
   * @memberof InputFormItem
   */
  openAiTip?: boolean;
  /**
   * AI推荐的提示文本列表。
   * @type {string[]}
   * @memberof InputFormItem
   */
  prompt?: string[];
  /**
   * 文件上传类型的媒体类型限制。
   * @type {string}
   * @memberof InputFormItem
   */
  mediaType?: string;
  /**
   * 字段描述文本。
   * @type {string}
   * @memberof InputFormItem
   */
  description?: string;
  /**
   * 字段当前值，可以是文本、数字、字符串数组，或为空。
   * @type {string | number | string[] | null | undefined}
   * @memberof InputFormItem
   */
  value: string | number | string[] | null | undefined;
  /**
   * 默认填写内容
   * @type {string}
   * @memberof InputFormItem
   */
  defaultContent?: string;
}

/**
 * @description 资源表单项（API格式），用于处理文件资源相关的表单项。
 * @interface ResFormItem
 */
export interface ResFormItem {
  /**
   * 表单项唯一标识ID。
   * @type {number}
   * @memberof ResFormItem
   */
  id: number;
  /**
   * 资源类型标识，区分视频/图片，对应 {@link ProjectType}。
   * @type {ProjectType}
   * @memberof ResFormItem
   */
  type: ProjectType;
  /**
   * 字段变量名。
   * @type {string}
   * @memberof ResFormItem
   */
  variable: string;
  /**
   * 字段显示标签文本。
   * @type {string}
   * @memberof ResFormItem
   */
  label: string;
  /**
   * 是否为必填字段。
   * @type {boolean}
   * @memberof ResFormItem
   */
  required: boolean;
  /**
   * 字段值，为文件对象数组或null。每个文件对象包含URL、名称、时长、状态等信息。
   * @type {Array<{url: string, name?: string, duration?: number, status?: FileStatus, [key: string]: unknown}> | null}
   * @memberof ResFormItem
   */
  value: Array<{
    /** 文件URL地址 */
    url: string;
    /** 文件名称 */
    name?: string;
    /** 视频时长（秒） */
    duration?: number;
    /** 文件状态，对应 {@link FileStatus} */
    status?: FileStatus;
    /** 其他扩展属性 */
    [key: string]: unknown;
  }> | null;
  /**
   * 最小文件数量限制。
   * @type {number}
   * @memberof ResFormItem
   */
  minLength?: number;
  /**
   * 最大文件数量限制。
   * @type {number}
   * @memberof ResFormItem
   */
  maxLength?: number;
  /**
   * 字段描述文本。
   * @type {string}
   * @memberof ResFormItem
   */
  description?: string;
  /**
   * 是否启用AI改嘴型功能（视频专用）。
   * @type {boolean}
   * @memberof ResFormItem
   */
  hasModifyMouth?: boolean;
  /**
   * AI改嘴型开关的当前状态。
   * @type {boolean}
   * @memberof ResFormItem
   */
  openModifyMouth?: boolean;
  /**
   * 资源ID，用于获取示例资源。
   * @type {string}
   * @memberof ResFormItem
   */
  resId?: string;
  /**
   * 资源类型，对应后端类型ID。
   * @type {number}
   * @memberof ResFormItem
   */
  resType?: number;
  /**
   * 封面图ID，用于获取示例资源封面。
   * @type {string}
   * @memberof ResFormItem
   */
  coverId?: string;
  /**
   * 封面图类型，对应后端类型ID。
   * @type {number}
   * @memberof ResFormItem
   */
  coverType?: number;
}

// ============= 特化表单项类型 =============

/**
 * @description 文件表单项类型，专用于文件上传表单项。
 * @typedef {FormItemInternal & {mediaType: MediaType, maxLength: number}} FileFormItem
 * @extends FormItemInternal
 */
export type FileFormItem = FormItemInternal & {
  /**
   * 媒体文件类型限制，如image、video等。
   * @type {MediaType}
   * @memberof FileFormItem
   */
  mediaType: MediaType;
  /**
   * 最大文件数量限制。
   * @type {number}
   * @memberof FileFormItem
   */
  maxLength: number;
};

/**
 * @description AI推荐表单项类型，专用于启用AI辅助的表单项。
 * @typedef {FormItemInternal & {openAiTip: true, prompt: string[]}} AiTipFormItem
 * @extends FormItemInternal
 */
export type AiTipFormItem = FormItemInternal & {
  /**
   * 是否启用AI推荐功能，始终为true。
   * @type {true}
   * @memberof AiTipFormItem
   */
  openAiTip: true;
  /**
   * AI推荐的依赖字段列表，用于计算推荐内容。
   * @type {string[]}
   * @memberof AiTipFormItem
   */
  prompt: string[];
};

/**
 * @description 必填表单项类型，强制要求为必填的表单项。
 * @typedef {FormItemInternal & {required: true}} RequiredFormItem
 * @extends FormItemInternal
 */
export type RequiredFormItem = FormItemInternal & {
  /**
   * 是否为必填字段，始终为true。
   * @type {true}
   * @memberof RequiredFormItem
   */
  required: true;
};

/**
 * @description 可选表单项类型，明确标记为可选的表单项。
 * @typedef {FormItemInternal & {required?: false}} OptionalFormItem
 * @extends FormItemInternal
 */
export type OptionalFormItem = FormItemInternal & {
  /**
   * 是否为必填字段，始终为false或undefined。
   * @type {false|undefined}
   * @memberof OptionalFormItem
   */
  required?: false;
};

// ============= 表单接口相关类型 =============

/**
 * @description 基础表单接口，提供获取表单数据的能力。
 * @interface BaseFormRef
 * @template T 表单数据类型，默认为Record<string, unknown>。
 */
export interface BaseFormRef<T = Record<string, unknown>> {
  /**
   * 获取表单当前数据。
   * @returns {T} 当前表单数据。
   * @memberof BaseFormRef
   */
  getFormData: () => T;
}

/**
 * @description 可验证表单接口，扩展基础表单接口，提供验证表单的能力。
 * @interface ValidatableFormRef
 * @extends BaseFormRef
 * @template T 表单数据类型，默认为Record<string, unknown>。
 */
export interface ValidatableFormRef<T = Record<string, unknown>>
  extends BaseFormRef<T> {
  /**
   * 验证整个表单。
   * @returns {Promise<boolean>} 验证结果，true表示验证通过，false表示验证失败。
   * @memberof ValidatableFormRef
   */
  validate: () => Promise<boolean>;
}

/**
 * @description 可重置表单接口，扩展基础表单接口，提供重置表单的能力。
 * @interface ResettableFormRef
 * @extends BaseFormRef
 * @template T 表单数据类型，默认为Record<string, unknown>。
 */
export interface ResettableFormRef<T = Record<string, unknown>>
  extends BaseFormRef<T> {
  /**
   * 重置表单到初始状态。
   * @returns {void}
   * @memberof ResettableFormRef
   */
  handleReset: () => void;
}

/**
 * @description 完整动态表单实例接口，组合了可验证表单和可重置表单的能力。
 * @interface DynamicFormInstance
 * @extends ValidatableFormRef
 * @extends ResettableFormRef
 * @template T 表单数据类型，默认为Record<string, unknown>。
 */
export interface DynamicFormInstance<T = Record<string, unknown>>
  extends ValidatableFormRef<T>,
    ResettableFormRef<T> {
  /**
   * 设置表单数据。
   * @param {T} data 要设置的表单数据。
   * @param {boolean} [override] 是否覆盖现有数据，true表示完全覆盖，false表示合并。
   * @returns {void}
   * @memberof DynamicFormInstance
   */
  setFormData?: (data: T, override?: boolean) => void;
}

/**
 * @description 文件表单接口，专用于处理文件上传表单。
 * @interface FileFormRef
 */
export interface FileFormRef {
  /**
   * 设置文件表单数据。
   * @param {Record<string, FileInfo[]>} data 文件数据对象，键为字段名，值为文件列表。
   * @returns {void}
   * @memberof FileFormRef
   */
  setFormData: (data: Record<string, FileInfo[]>) => void;
  /**
   * 获取文件表单数据。
   * @returns {Record<string, FileInfo[]>} 文件数据对象，键为字段名，值为文件列表。
   * @memberof FileFormRef
   */
  getFormData: () => Record<string, FileInfo[]>;
  /**
   * 验证指定字段。
   * @param {string} prop 字段名。
   * @returns {void}
   * @memberof DynamicFormMethods
   */
  validateField: (prop: string) => void;
}

/**
 * @description 动态文件表单实例接口，扩展文件表单接口，增加验证和重置能力。
 * @interface DynamicFileFormInstance
 * @extends FileFormRef
 */
export interface DynamicFileFormInstance extends FileFormRef {
  /**
   * 验证文件表单。
   * @returns {Promise<boolean>} 验证结果，true表示验证通过，false表示验证失败。
   * @memberof DynamicFileFormInstance
   */
  validate: () => Promise<boolean>;
  /**
   * 重置文件表单。
   * @returns {void}
   * @memberof DynamicFileFormInstance
   */
  handleReset: () => void;
  /**
   * 清除验证错误信息。
   * @param {(string | string[])} [props] 字段名或字段名数组，不传则清除所有字段。
   * @returns {void}
   * @memberof DynamicFileFormInstance
   */
  clearValidate: (props?: string | string[]) => void;
  /**
   * 检查字段是否已经被验证过。
   * @param {string} prop 字段名。
   * @returns {boolean} 是否已验证过。
   * @memberof DynamicFileFormInstance
   */
  hasFieldBeenValidated: (prop: string) => boolean;
}

/**
 * @description 表单配置接口，定义表单的全局配置项。
 * @interface FormConfig
 */
export interface FormConfig {
  /**
   * 表单布局方式。
   * @type {FormLayout}
   * @memberof FormConfig
   */
  layout?: FormLayout;
  /**
   * 标签列配置，用于控制标签的布局。
   * @type {{ span?: number; offset?: number; [key: string]: unknown; }}
   * @memberof FormConfig
   */
  labelCol?: {
    /** 栅格占据的列数 */
    span?: number;
    /** 栅格左侧的间隔格数 */
    offset?: number;
    /** 其他标签列配置 */
    [key: string]: unknown;
  };
  /**
   * 包装器列配置，用于控制表单控件的布局。
   * @type {{ span?: number; offset?: number; [key: string]: unknown; }}
   * @memberof FormConfig
   */
  wrapperCol?: {
    /** 栅格占据的列数 */
    span?: number;
    /** 栅格左侧的间隔格数 */
    offset?: number;
    /** 其他包装器列配置 */
    [key: string]: unknown;
  };
  /**
   * 文件上传的默认媒体类型。
   * @type {string}
   * @memberof FormConfig
   */
  defaultMediaType?: string;
  /**
   * 表单验证失败时是否滚动到错误字段。
   * @type {boolean}
   * @memberof FormConfig
   */
  scrollToError?: boolean;
  /**
   * 验证失败时的滚动偏移量。
   * @type {number}
   * @memberof FormConfig
   */
  scrollOffset?: number;
  /**
   * 是否在控件改变时立即触发验证。
   * @type {boolean}
   * @memberof FormConfig
   */
  validateOnChange?: boolean;
  /**
   * 是否在控件失焦时触发验证。
   * @type {boolean}
   * @memberof FormConfig
   */
  validateOnBlur?: boolean;
  /**
   * 表单大小。
   * @type {'small' | 'default' | 'large'}
   * @memberof FormConfig
   */
  size?: 'small' | 'default' | 'large';
  /**
   * 其他表单配置选项。
   * @type {unknown}
   * @memberof FormConfig
   */
  [key: string]: unknown;
}

/**
 * @description 动态表单方法接口，定义动态表单组件的所有方法。
 * @interface DynamicFormMethods
 */
export interface DynamicFormMethods {
  /**
   * 获取当前表单数据。
   * @returns {Record<string, unknown>} 当前表单数据。
   * @memberof DynamicFormMethods
   */
  getFormData: () => Record<string, unknown>;
  /**
   * 设置表单数据。
   * @param {Record<string, unknown>} data 要设置的表单数据。
   * @param {boolean} [override] 是否覆盖现有数据。
   * @returns {void}
   * @memberof DynamicFormMethods
   */
  setFormData: (data: Record<string, unknown>, override?: boolean) => void;
  /**
   * 验证整个表单。
   * @returns {Promise<boolean>} 验证结果。
   * @memberof DynamicFormMethods
   */
  validate: () => Promise<boolean>;
  /**
   * 验证指定字段。
   * @param {string} prop 字段名。
   * @returns {void}
   * @memberof DynamicFormMethods
   */
  validateField: (prop: string) => void;
  /**
   * 重置表单到初始状态。
   * @returns {void}
   * @memberof DynamicFormMethods
   */
  handleReset: () => void;
  /**
   * 清除验证错误信息。
   * @param {(string | string[])} [props] 字段名或字段名数组，不传则清除所有字段。
   * @returns {void}
   * @memberof DynamicFormMethods
   */
  clearValidate: (props?: string | string[]) => void;
  /**
   * 重置指定字段。
   * @param {string} prop 字段名。
   * @returns {void}
   * @memberof DynamicFormMethods
   */
  resetField: (prop: string) => void;
  /**
   * 检查字段是否已经被验证过。
   * @param {string} prop 字段名。
   * @returns {boolean} 是否已验证过。
   * @memberof DynamicFormMethods
   */
  hasFieldBeenValidated: (prop: string) => boolean;
}

/**
 * @description 动态表单属性接口，定义动态表单组件的所有属性。
 * @interface DynamicFormProps
 */
export interface DynamicFormProps {
  /**
   * 表单全局配置对象。
   * @type {FormConfig}
   * @memberof DynamicFormProps
   */
  formConfig?: FormConfig;
  /**
   * 表单项配置数组。
   * @type {FormItemInternal[]}
   * @memberof DynamicFormProps
   */
  formItems?: FormItemInternal[];
  /**
   * 表单字段初始值映射。
   * @type {Record<string, unknown>}
   * @memberof DynamicFormProps
   */
  initialValues?: Record<string, unknown>;
  /**
   * 是否显示表单底部的操作按钮区域。
   * @type {boolean}
   * @memberof DynamicFormProps
   */
  showButtons?: boolean;
  /**
   * 是否在操作按钮区域显示重置按钮。
   * @type {boolean}
   * @memberof DynamicFormProps
   */
  showResetBtn?: boolean;
  /**
   * 提交按钮的显示文字。
   * @type {string}
   * @memberof DynamicFormProps
   */
  submitBtnText?: string;
  /**
   * 重置按钮的显示文字。
   * @type {string}
   * @memberof DynamicFormProps
   */
  resetBtnText?: string;
}

/**
 * @description 动态表单引用类型别名，简化可验证表单引用的使用。
 * @typedef {ValidatableFormRef} DynamicFormRef
 */
export type DynamicFormRef = ValidatableFormRef;

// ============= 表单事件相关类型 =============

/**
 * @description 表单事件参数集合，定义所有表单事件的参数类型。
 * @interface FormEventParams
 */
export interface FormEventParams {
  /**
   * @description 文件上传点击事件参数。
   * @property {string} prop 触发事件的字段名。
   * @property {MediaType} mediaType 请求上传的媒体类型。
   * @property {FormItemInternal} [formItem] 触发事件的表单项配置（可选）。
   * @memberof FormEventParams
   */
  upload: {
    /** 触发事件的字段名 */
    prop: string;
    /** 请求上传的媒体类型 */
    mediaType: MediaType;
    /** 触发事件的表单项配置（可选） */
    formItem?: FormItemInternal;
  };

  /**
   * @description 文件删除事件参数。
   * @property {number} index 要删除的文件在列表中的索引。
   * @property {string} prop 文件所属的字段名。
   * @property {MediaType} mediaType 文件的媒体类型 (虽然删除时主要靠索引，但保留它可能对某些上层逻辑有用)。
   * @property {FormItemInternal} [formItem] 触发事件的表单项配置（可选）。
   * @memberof FormEventParams
   */
  fileDelete: {
    /** 要删除的文件在列表中的索引 */
    index: number;
    /** 文件所属的字段名 */
    prop: string;
    /** 文件的媒体类型 */
    mediaType: MediaType;
    /** 触发事件的表单项配置（可选） */
    formItem?: FormItemInternal;
  };

  /**
   * @description 文件替换事件参数。
   * @property {number} index 要替换的文件在列表中的索引。
   * @property {string} prop 文件所属的字段名。
   * @property {MediaType} mediaType 希望替换成的新文件的媒体类型。
   * @property {FormItemInternal} [formItem] 触发事件的表单项配置（可选）。
   * @memberof FormEventParams
   */
  replace: {
    /** 要替换的文件在列表中的索引 */
    index: number;
    /** 文件所属的字段名 */
    prop: string;
    /** 希望替换成的新文件的媒体类型 */
    mediaType: MediaType;
    /** 触发事件的表单项配置（可选） */
    formItem?: FormItemInternal;
  };
}

/**
 * @description 动态表单事件接口，定义动态表单组件可以触发的所有事件。
 * @interface DynamicFormEmits
 */
export interface DynamicFormEmits {
  /**
   * @description 表单提交事件。
   * @event submit
   * @param {Record<string, unknown>} formData 表单数据。
   * @param {(success?: boolean, message?: string) => void} callback 回调函数，用于通知表单提交结果。
   * @returns {void}
   * @memberof DynamicFormEmits
   */
  'submit': (
    formData: Record<string, unknown>,
    callback: (success?: boolean, message?: string) => void,
  ) => void;
  /**
   * @description 表单重置事件。
   * @event reset
   * @returns {void}
   * @memberof DynamicFormEmits
   */
  'reset': () => void;
  /**
   * @description 文件上传点击事件。
   * @event upload-click
   * @param {FormEventParams['upload']} data 上传事件参数。
   * @returns {void}
   * @memberof DynamicFormEmits
   */
  'upload-click': (data: FormEventParams['upload']) => void;
  /**
   * @description 文件删除事件。
   * @event file-delete
   * @param {FormEventParams['fileDelete']} data 删除事件参数。
   * @returns {void}
   * @memberof DynamicFormEmits
   */
  'file-delete': (data: FormEventParams['fileDelete']) => void;
  /**
   * @description 文件替换事件。
   * @event replace
   * @param {FormEventParams['replace']} data 替换事件参数。
   * @returns {void}
   * @memberof DynamicFormEmits
   */
  'replace': (data: FormEventParams['replace']) => void;
}

/**
 * @description 动态表单项事件接口，定义动态表单项组件可以触发的所有事件。
 * @interface DynamicFormItemEmits
 */
export interface DynamicFormItemEmits {
  /**
   * @description 文件上传点击事件。
   * @event upload-click
   * @param {FormEventParams['upload']} data 上传事件参数。
   * @returns {void}
   * @memberof DynamicFormItemEmits
   */
  'upload-click': (data: FormEventParams['upload']) => void;
  /**
   * @description 文件删除事件。
   * @event file-delete
   * @param {FormEventParams['fileDelete']} data 删除事件参数。
   * @returns {void}
   * @memberof DynamicFormItemEmits
   */
  'file-delete': (data: FormEventParams['fileDelete']) => void;
  /**
   * @description 文件替换事件。
   * @event replace
   * @param {FormEventParams['replace']} data 替换事件参数。
   * @returns {void}
   * @memberof DynamicFormItemEmits
   */
  'replace': (data: FormEventParams['replace']) => void;
}

/**
 * @description 自定义上传组件事件接口，定义上传组件可以触发的所有事件。
 * @interface CustomUploadEmits
 */
export interface CustomUploadEmits {
  /**
   * @description 上传按钮点击事件。
   * @event upload-click
   * @param {{mediaType: MediaType}} data 上传事件参数，包含媒体类型。
   * @returns {void}
   * @memberof CustomUploadEmits
   */
  'upload-click': (data: { mediaType: MediaType }) => void;
  /**
   * @description 文件删除事件。
   * @event delete
   * @param {FileInfo} file 要删除的文件信息。
   * @returns {void}
   * @memberof CustomUploadEmits
   */
  'delete': (file: FileInfo) => void;
  /**
   * @description 文件替换事件。
   * @event replace
   * @param {{file: FileInfo; mediaType: MediaType}} data 替换事件参数，包含要替换的文件和新媒体类型。
   * @returns {void}
   * @memberof CustomUploadEmits
   */
  'replace': (data: { file: FileInfo; mediaType: MediaType }) => void;
}

// ============= 表单组件相关类型 =============

/**
 * @description DynamicFormItem组件属性接口，定义表单项组件的属性。
 * @interface DynamicFormItemProps
 */
export interface DynamicFormItemProps {
  /**
   * 当前表单项的完整配置对象。
   * @type {FormItemInternal}
   * @memberof DynamicFormItemProps
   */
  formItem: FormItemInternal;
  /**
   * 整个表单的数据对象。
   * @type {Record<string, unknown>}
   * @memberof DynamicFormItemProps
   */
  formValues: Record<string, unknown>;
  /**
   * 所有表单项的配置数组。
   * @type {FormItemInternal[]}
   * @memberof DynamicFormItemProps
   */
  formItems?: FormItemInternal[];
  /**
   * 表单全局配置。
   * @type {FormConfig}
   * @memberof DynamicFormItemProps
   */
  formConfig?: DynamicFormProps['formConfig'];
  /**
   * 当前表单项的验证规则数组。
   * @type {FormRule[]}
   * @memberof DynamicFormItemProps
   */
  rules?: FormRule[];
}

/**
 * @description DynamicFormItem组件数据接口，定义表单项组件的内部数据。
 * @interface DynamicFormItemData
 */
export interface DynamicFormItemData {
  /**
   * AI改嘴型开关的本地状态。
   * @type {boolean}
   * @memberof DynamicFormItemData
   */
  openModifyMouthLocal: boolean;
  /**
   * AI改嘴型开关的显示文本常量。
   * @type {string}
   * @memberof DynamicFormItemData
   */
  MOUTH_SHAPE_TEXT: string;
  /**
   * AI改嘴型功能的提示文本常量。
   * @type {string}
   * @memberof DynamicFormItemData
   */
  MOUTH_SHAPE_TIP: string;
}

/**
 * @description 自定义上传组件属性接口，定义上传组件的属性。
 * @interface CustomUploadProps
 */
export interface CustomUploadProps {
  /**
   * 当前已上传的文件列表。
   * @type {FileInfo[]}
   * @memberof CustomUploadProps
   */
  fileList?: FileInfo[];
  /**
   * 是否禁用组件。
   * @type {boolean}
   * @memberof CustomUploadProps
   */
  disabled?: boolean;
  /**
   * 允许上传的最大文件数量。
   * @type {number}
   * @memberof CustomUploadProps
   */
  maxLength?: number;
  /**
   * 默认上传媒体类型。
   * @type {MediaType}
   * @memberof CustomUploadProps
   */
  defaultMediaType?: MediaType;
  /**
   * 示例图片URL，用于预览示例。
   * @type {string}
   * @memberof CustomUploadProps
   */
  exampleImageUrl?: string;
  /**
   * 示例视频URL，用于预览示例。
   * @type {string}
   * @memberof CustomUploadProps
   */
  exampleVideoUrl?: string;
  /**
   * 示例视频的缩略图URL。
   * @type {string}
   * @memberof CustomUploadProps
   */
  exampleVideoThumbnail?: string;
  /**
   * 默认图片占位符URL。
   * @type {string}
   * @memberof CustomUploadProps
   */
  defaultImagePlaceholder?: string;
  /**
   * 接受的文件类型。
   * @type {string[]}
   * @memberof CustomUploadProps
   */
  accept?: string[];
  /**
   * 单个文件大小限制。
   * @type {number}
   * @memberof CustomUploadProps
   */
  maxFileSize?: number;
  /**
   * 是否支持多选文件。
   * @type {boolean}
   * @memberof CustomUploadProps
   */
  multiple?: boolean;
  /**
   * 是否支持拖拽上传。
   * @type {boolean}
   * @memberof CustomUploadProps
   */
  dragUpload?: boolean;
  /**
   * 是否启用点击预览功能，控制示例素材和用户上传素材的点击预览，视频popover预览除外。
   * @type {boolean}
   * @memberof CustomUploadProps
   */
  enablePreview?: boolean;
}

/**
 * @description 自定义上传组件数据接口，定义上传组件的内部数据。
 * @interface CustomUploadData
 */
export interface CustomUploadData {
  /**
   * 示例视频预览弹窗的显示状态。
   * @type {boolean}
   * @memberof CustomUploadData
   */
  videoPreviewVisible: boolean;
}

/**
 * @description OpenAI提示按钮属性接口，定义AI提示按钮的属性。
 * @interface OpenAiTipButtonProps
 */
export interface OpenAiTipButtonProps {
  /**
   * 当前字段名称。
   * @type {string}
   * @memberof OpenAiTipButtonProps
   */
  fieldName: string;
  /**
   * 上下文数据对象，用于AI推荐计算。
   * @type {Record<string, unknown>}
   * @memberof OpenAiTipButtonProps
   */
  contextData?: Record<string, unknown>;
  /**
   * 依赖字段数组，AI推荐计算需要的其他字段。
   * @type {string[]}
   * @memberof OpenAiTipButtonProps
   */
  dependencyFields?: string[];
  /**
   * 字段配置对象。
   * @type {FormItemInternal}
   * @memberof OpenAiTipButtonProps
   */
  fieldsConfig?: FormItemInternal;
  /**
   * 是否禁用组件。
   * @type {boolean}
   * @memberof OpenAiTipButtonProps
   */
  disabled?: boolean;
}

/**
 * @description OpenAI提示按钮数据接口，定义AI提示按钮的内部数据。
 * @interface OpenAiTipButtonData
 */
export interface OpenAiTipButtonData {
  /**
   * AI推荐请求的加载状态。
   * @type {boolean}
   * @memberof OpenAiTipButtonData
   */
  loading: boolean;
  /**
   * AI推荐建议列表。
   * @type {Array<string | { value: string; [key: string]: unknown }>}
   * @memberof OpenAiTipButtonData
   */
  suggestions: Array<string | { value: string; [key: string]: unknown }>;
  /**
   * 依赖字段值缓存。
   * @type {Record<string, unknown>}
   * @memberof OpenAiTipButtonData
   */
  dependencyValuesCache: Record<string, unknown>;
}

// ============= 工具类型 =============

/**
 * @description 表单组件实例联合类型，用于泛型约束。
 * @typedef {Object} DynamicFormComponentInstance
 */
export type DynamicFormComponentInstance =
  | { $props: DynamicFormProps }
  | { $props: DynamicFormItemProps }
  | { $props: CustomUploadProps }
  | { $props: OpenAiTipButtonProps };

/**
 * @description 媒体预览处理函数类型，用于处理媒体文件预览。
 * @typedef {(file: FileInfo) => void} MediaPreviewHandler
 * @param {FileInfo} file - 要预览的文件信息。
 * @returns {void}
 */
export type MediaPreviewHandler = (file: FileInfo) => void;

/**
 * @description 时长格式化函数类型，用于格式化媒体文件时长。
 * @typedef {(seconds: number) => string} DurationFormatter
 * @param {number} seconds - 秒数。
 * @returns {string} 格式化后的时长字符串，如"01:30"。
 */
export type DurationFormatter = (seconds: number) => string;

/**
 * @description 表单提交回调函数类型，用于处理表单提交结果。
 * @typedef {(success?: boolean, message?: string) => void} FormSubmitCallback
 * @param {boolean} [success] - 是否成功。
 * @param {string} [message] - 提示消息。
 * @returns {void}
 */
export type FormSubmitCallback = (success?: boolean, message?: string) => void;

/**
 * @description 文件上传成功回调函数类型。
 * @typedef {(files: FileInfo[]) => void} FileUploadSuccessCallback
 * @param {FileInfo[]} files - 上传成功的文件列表。
 * @returns {void}
 */
export type FileUploadSuccessCallback = (files: FileInfo[]) => void;

/**
 * @description 文件上传错误回调函数类型。
 * @typedef {(error: Error) => void} FileUploadErrorCallback
 * @param {Error} error - 上传错误信息。
 * @returns {void}
 */
export type FileUploadErrorCallback = (error: Error) => void;

/**
 * @description AI提示选择回调函数类型。
 * @typedef {(value: string) => void} AiTipSelectCallback
 * @param {string} value - 选中的AI提示内容。
 * @returns {void}
 */
export type AiTipSelectCallback = (value: string) => void;

/**
 * @description 提取表单字段名类型，从表单项配置数组中提取所有字段名。
 * @template T 表单项配置数组类型，继承自 {@link FormItemInternal}。
 * @typedef {T[number]['prop']} ExtractFieldNames
 */
export type ExtractFieldNames<T extends readonly FormItemInternal[]> =
  T[number]['prop'];

/**
 * @description 表单值类型映射，根据表单项配置生成类型安全的表单值类型。
 * @template T 表单项配置数组类型，继承自 {@link FormItemInternal}。
 * @typedef {{ [K in ExtractFieldNames<T>]: ExtractFieldValue<T, K> }} FormValues
 */
export type FormValues<T extends readonly FormItemInternal[]> = {
  [K in ExtractFieldNames<T>]: ExtractFieldValue<T, K>;
};

/**
 * @description 提取表单字段值类型，获取特定字段的值类型。
 * @template T 表单项配置数组类型，继承自 {@link FormItemInternal}。
 * @template K 字段名类型，为T中提取的字段名。
 * @typedef {Extract<T[number], { prop: K }>['defaultValue']} ExtractFieldValue
 */
export type ExtractFieldValue<
  T extends readonly FormItemInternal[],
  K extends ExtractFieldNames<T>,
> = Extract<T[number], { prop: K }>['defaultValue'];

/**
 * @description 过滤表单项类型，基于条件筛选表单项。
 * @template T 表单项类型，继承自 {@link FormItemInternal}。
 * @template Condition 过滤条件类型。
 * @typedef {T extends Condition ? T : never} FilterFormItems
 */
export type FilterFormItems<
  T extends FormItemInternal,
  Condition,
> = T extends Condition ? T : never;

/**
 * @description 必填字段类型，提取所有必填的表单项。
 * @template T 表单项配置数组类型，继承自 {@link FormItemInternal}。
 * @typedef {FilterFormItems<T[number], { required: true }>} RequiredFields
 */
export type RequiredFields<T extends readonly FormItemInternal[]> =
  FilterFormItems<T[number], { required: true }>;

/**
 * @description 可选字段类型，提取所有可选的表单项。
 * @template T 表单项配置数组类型，继承自 {@link FormItemInternal}。
 * @typedef {FilterFormItems<T[number], { required?: false }>} OptionalFields
 */
export type OptionalFields<T extends readonly FormItemInternal[]> =
  FilterFormItems<T[number], { required?: false }>;

/**
 * @description 提取表单值类型，获取表单项的默认值类型。
 * @template T 表单项类型，继承自 {@link FormItemInternal}。
 * @typedef {T['defaultValue']} ExtractFormValue
 */
export type ExtractFormValue<T extends FormItemInternal> = T['defaultValue'];

/**
 * @description 表单数据类型映射，根据表单项配置生成完整的表单数据类型。
 * @template T 表单项配置数组类型，继承自 {@link FormItemInternal}。
 * @typedef {{ [K in T[number]['prop']]: ExtractFormValue<Extract<T[number], { prop: K }>> }} FormDataType
 */
export type FormDataType<T extends readonly FormItemInternal[]> = {
  [K in T[number]['prop']]: ExtractFormValue<Extract<T[number], { prop: K }>>;
};

// ============= 常量重新导出 =============
// 重新导出表单系统中常用的枚举和常量

export {
  FORM_LAYOUT,
  VALIDATION_TRIGGER,
  FORM_STATE,
  MEDIA_TYPES,
} from './base';
