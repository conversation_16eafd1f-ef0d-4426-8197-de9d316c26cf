<template>
  <FaFileManagerToolBar>
    <template #left>
      <fa-popover
        v-model="switchUploadFileGroup"
        placement="bottom"
        trigger="click"
        overlay-class-name="uploadFilePopover"
      >
        <template #content>
          <div>
            <div @click="handleClickLocal" class="btnItem">从本地上传</div>
            <div @click="handleClickMobile" class="btnItem">从手机上传</div>
          </div>
        </template>
        <fa-button v-show="!fileUploading" type="primary" size="small">
          <FaDesignerIcon type="upload" />
          上传文件
        </fa-button>
      </fa-popover>

      <fa-button v-show="fileUploading" type="primary" size="small" disabled>
        <FaDesignerIcon type="upload" />
        上传文件
      </fa-button>

      <fa-button
        :style="{ 'margin-left': '12px' }"
        size="small"
        :disabled="fileUploading"
        @click="handleFolderAdd"
      >
        <FaDesignerIcon type="folder_add" />
        新建文件夹
      </fa-button>

      <FaQrUpload
        v-model="visibleQrUpload"
        :loading="loadingQrUpload"
        :access-key="accessKey"
        :media-type="mediaType"
        :env="env"
        bss="hd"
        @refresh="handleClickMobile"
      />
    </template>

    <template #right>
      <fa-select
        class="file-manage_select_type"
        :dropdownMatchSelectWidth="true"
        :value="selectType"
        @change="handleSelectType"
      >
        <fa-select-option
          v-for="item in selectOptions"
          :key="item.type"
          :value="item.type"
        >
          {{ item.name }}
        </fa-select-option>
      </fa-select>

      <FaFileManagerSeach
        :history-list="historyList"
        :value="searchValue"
        placeholder="搜索当前文件夹"
        @search="handleSearch"
        @history-clear="handleHistoryClear"
        @history-search="handleSearch"
      />

      <FileManagerIcon class="notCancelSelect" @click="handleChangeMode">
        <FaDesignerIcon :type="isViewListMode ? 'viewList' : 'viewTable'" />
      </FileManagerIcon>

      <FaFileManagerSort
        :sort-by="sortBy"
        :sort-mode="sortMode"
        :sort-list="sortList"
        @sort-change="handleSortChange"
      />

      <!-- <FileManagerIcon>
        <FaDesignerIcon type="upload_setting" />
      </FileManagerIcon> -->

      <FaFileManagerCapacity
        :class="{
          'file-manager-exceeding-capacity':
            capacityUsedSize >= capacityTotalSize,
        }"
        :progress="capacityProgress"
        @expand="handleExpand"
      >
        {{ capacityUse }}/{{ capacityTotal }}
      </FaFileManagerCapacity>
    </template>
  </FaFileManagerToolBar>
</template>

<script>
import {
  FileManager as FaFileManager,
  DesignerIcon as FaDesignerIcon,
} from '@fk/fa-component-cus';
import {
  Icon as FaIcon,
  Button as FaButton,
  Tooltip as FaTooltip,
  message as FaMessage,
} from '@fk/faicomponent';
import FileManagerIcon from '@/components/MaterialManager/layout/FileManagerToolBar/components/FileManagerIcon.vue';
import { FileType } from '@/components/MaterialManager/types/index.ts';
import {
  SORT_BY_KEY,
  STATUS_DEF,
} from '@/components/MaterialManager/constants/index.ts';
import {
  folderAdd,
  loadCurrFmList,
  calcSize,
  updateView,
} from '@/components/MaterialManager/utils/index.ts';
import store from '@/store';
import { getExternalDynamicUrl } from '@/constants/system';
import { getAccessKey } from '@/api/Material/index';

export default {
  name: 'FileManagerToolBar',
  components: {
    FaIcon,
    FaButton,
    FaTooltip,
    FaDesignerIcon,
    FileManagerIcon,
    FaFileManagerSort: FaFileManager.FileManagerSort,
    FaFileManagerSeach: FaFileManager.FileManagerSeach,
    FaFileManagerToolBar: FaFileManager.FileManagerToolBar,
    FaFileManagerCapacity: FaFileManager.FileManagerCapacity,
  },

  data() {
    return {
      switchUploadFileGroup: false,
      visibleQrUpload: false,
      loadingQrUpload: false,
      accessKey: '',
      sortList: [
        {
          name: '文件名称',
          key: SORT_BY_KEY.NAME,
        },
        {
          name: '文件大小',
          key: SORT_BY_KEY.FILE_SIZE,
        },
        {
          name: '上传时间',
          key: SORT_BY_KEY.CREATE_TIME,
        },
      ],
      mediaType: [
        {
          type: 'image',
          subtype: ['jpg', 'jpeg', 'png'],
        },
        { type: 'video', subtype: ['mp4', 'mov'] },
      ],
    };
  },

  computed: {
    // 获取环境
    env() {
      return window.location.host.includes('fff.com')
        ? 0
        : window.location.host.includes('fkw.com.faidev.cc')
        ? 1
        : 2;
    },
    currentFolder() {
      return store.state.meta.id;
    },
    selectOptions() {
      return [
        {
          type: FileType.ALL,
          name: '全部',
        },
        {
          type: FileType.IMAGE,
          name: '图片',
        },
        {
          type: FileType.VIDEO,
          name: '视频',
        },
        {
          type: FileType.FOLDER,
          name: '文件夹',
        },
      ];
    },
    searchValue() {
      return store.state.meta.searchValue;
    },
    selectType() {
      return store.state.meta.selectType;
    },
    fileUploading() {
      return store.state.meta.fileUploading;
    },
    historyList() {
      return store.state.meta.historyList;
    },
    capacityUsedSize() {
      return store.state.materialUpload.capacityUsedSize;
    },
    capacityTotalSize() {
      return store.state.materialUpload.capacityTotalSize;
    },
    capacityProgress() {
      return (this.capacityUsedSize / this.capacityTotalSize) * 100;
    },
    capacityUse() {
      const capacityUse = calcSize(this.capacityUsedSize, true);
      return capacityUse === '-' ? '0KB' : capacityUse;
    },
    capacityTotal() {
      const capacityTotal = calcSize(this.capacityTotalSize, true);
      return capacityTotal === '-' ? '0KB' : capacityTotal;
    },
    isViewListMode() {
      return store.getters['materialUpload/isListView'];
    },
    sortBy() {
      return store.state.meta.sortBy;
    },
    sortMode() {
      return store.state.meta.sortMode;
    },
  },

  watch: {
    visibleQrUpload(newVal) {
      !newVal && updateView();
    },
  },

  methods: {
    handleSelectType(selectType) {
      store.commit('setPage', 1);
      store.commit('setSelectType', selectType);
      store.commit('setStatus', STATUS_DEF.SEARCHED);
      store.dispatch('loadDirSearchedFmList');
    },

    handleClickLocal() {
      this.switchUploadFileGroup = false;
      store.commit('handleUpload');
    },
    async handleClickMobile() {
      this.switchUploadFileGroup = false;
      this.loadingQrUpload = true;
      const preset = { folderId: this.currentFolder };
      const [err, res] = await getAccessKey(preset);
      if (err) {
        this.loadingQrUpload = false;
        const error = { message: err.message || '获取上传凭证失败' };
        FaMessage.error(error.message);
        throw new Error(error.message);
      }

      const accessKey = res?.data?.[0];
      if (!accessKey) {
        this.loadingQrUpload = false;
        throw new Error('获取上传凭证失败：凭证为空');
      }
      this.loadingQrUpload = false;
      this.visibleQrUpload = true;
      this.accessKey = accessKey;
    },
    setSearchValueWithHistory(val) {
      store.commit('pushHistoryList', val);
    },
    searchCurrDirFmList() {
      store.commit('setPage', 1);
      store.dispatch('loadDirSearchedFmList');
    },

    handleSearch(val) {
      this.setSearchValueWithHistory(val);
      if (val?.trim() === '') {
        store.commit('setStatus', STATUS_DEF.INIT);
        loadCurrFmList();
      } else {
        store.commit('setStatus', STATUS_DEF.SEARCHED);
        this.searchCurrDirFmList();
      }
    },

    handleHistoryClear() {
      store.commit('setHistoryList', []);
      store.commit('setStatus', STATUS_DEF.INIT);
    },

    // 扩容点击事件
    handleExpand() {
      const versionBuyUrl = getExternalDynamicUrl().VERSION_BUY;
      window.open(versionBuyUrl, '_blank');
    },

    handleChangeMode() {
      store.dispatch('materialUpload/toggleViewType');
    },

    handleSortChange(info) {
      store.commit('setSort', info);
    },

    handleFolderAdd() {
      store.commit('setSelectType', FileType.ALL);
      folderAdd(store.state.meta.id);
    },
  },
};
</script>

<style>
.fa-dropdown {
  z-index: 999;
}
.fa-file-manager--container {
  width: 100%;
}
.fa-file-manager--container .fa-file-manager-tool-bar-left .fa-btn .anticon {
  position: relative;
  top: -3px;
}
.fa-file-manager--container .file-manage_select_type {
  width: 130px;
  height: 32px;
  margin-right: 12px;
}
.fa-file-manager-search--input {
  width: 184px;
  color: #333;
}
.fa-file-manager-sort--menu .fa-file-manager-sort--menu-check.anticon,
.fa-file-manager .pointerText,
.file-manager-demo--table-control .file-manager-demo--table-btn,
.fa-file-manager-search--overlay .fa-file-manager-search--history-clear {
  color: var(--color-primary);
}
.file-manager-demo-tool-bar-tooltip .fa-tooltip-inner {
  color: #333;
}
.file-manager-demo-tool-bar-tooltip-icon {
  pointer-events: auto !important;
}
.fa-file-manager-input .blur.edit,
.file-manager-demo--move-modal-input .fa-file-manager-input > div {
  filter: none;
}
.file-manager-exceeding-capacity .fa-file-manager-capacity--progress > div {
  background-color: #f5222d;
}
.uploadFilePopover .fa-popover-inner-content {
  width: 104px;
  border-radius: 4px;
  background: #fff;
  padding: 4px 0;
}
.uploadFilePopover .fa-popover-inner-content .btnItem {
  padding: 0 12px;
  height: 32px;
  line-height: 32px;
  font-weight: 400;
  font-size: 14px;
  text-align: left;
  color: #333;
  cursor: pointer;
}
.uploadFilePopover .fa-popover-inner-content .btnItem:hover {
  background: #f0f7ff;
}
</style>
