<!-- AI推荐标签项组件 -->
<template>
  <!-- 文本长度超过20才显示tooltip -->
  <fa-tooltip v-if="displayText.length > 20" :title="displayText">
    <fa-tag
      class="ai-suggestion__tag"
      :class="{ clicked: isClicked }"
      :style="animationStyle"
      @click="handleClick"
    >
      {{ truncatedText }}
    </fa-tag>
  </fa-tooltip>
  <fa-tag
    v-else
    class="ai-suggestion__tag"
    :class="{ clicked: isClicked }"
    :style="animationStyle"
    @click="handleClick"
  >
    {{ displayText }}
  </fa-tag>
</template>

<script lang="ts">
import { defineComponent, computed, type PropType } from 'vue';

/**
 * 推荐项数据类型
 */
type SuggestionData = { value: string } | string;

/**
 * 动画配置接口
 */
interface AnimationConfig {
  /** 基础延迟时间（毫秒） */
  baseDelay: number;
  /** 每项递增延迟时间（毫秒） */
  incrementDelay: number;
  /** 最大文本长度 */
  maxTextLength: number;
}

/**
 * AI推荐标签项组件
 * @description 单个推荐标签的渲染组件，支持tooltip、点击动画和自定义样式
 */
export default defineComponent({
  name: 'SuggestionItem',
  props: {
    /**
     * 推荐项数据
     * @description 推荐内容，可以是字符串或包含value属性的对象
     */
    suggestion: {
      type: [String, Object] as PropType<SuggestionData>,
      required: true,
    },
    /**
     * 在列表中的索引
     * @description 用于计算动画延迟时间
     */
    index: {
      type: Number,
      required: true,
    },
    /**
     * 是否处于点击状态
     * @description 控制点击动画效果的显示
     */
    isClicked: {
      type: Boolean,
      default: false,
    },
    /**
     * 动画配置
     * @description 控制动画效果的参数配置
     */
    animationConfig: {
      type: Object as PropType<AnimationConfig>,
      required: true,
    },
  },
  emits: {
    /**
     * 选择推荐项事件
     * @description 用户点击推荐标签时触发
     * @param suggestion 被选择的推荐项
     */
    select: (_suggestion: SuggestionData) => true,
  },
  setup(props, { emit }) {
    /**
     * 获取推荐项的显示文本
     * @description 从推荐项中提取用于显示的文本内容
     */
    const displayText = computed<string>(() => {
      if (!props.suggestion) return '';
      return typeof props.suggestion === 'object'
        ? props.suggestion.value
        : props.suggestion;
    });

    /**
     * 截断后的显示文本
     * @description 根据配置的最大长度截断文本并添加省略号
     */
    const truncatedText = computed<string>(() => {
      const maxLength = props.animationConfig.maxTextLength;
      const text = displayText.value;
      return text.length > maxLength ? text.slice(0, maxLength) + '...' : text;
    });

    /**
     * 动画样式
     * @description 计算当前项的动画延迟样式
     */
    const animationStyle = computed(() => ({
      '--enter-delay': `${
        props.animationConfig.baseDelay +
        props.index * props.animationConfig.incrementDelay
      }ms`,
    }));

    /**
     * 处理点击事件
     * @description 向父组件发送选择事件
     */
    const handleClick = (): void => {
      emit('select', props.suggestion);
    };

    return {
      displayText,
      truncatedText,
      animationStyle,
      handleClick,
    };
  },
});
</script>

<style lang="scss" scoped>
/* 推荐标签项样式 */

// 元素：推荐标签
.ai-suggestion__tag {
  @apply cursor-pointer transition-all duration-300 ease-out;
  @apply flex flex-col p-[1.5px_8px] rounded-[4px] mr-0;
  @apply font-normal text-[14px] text-[#333];
  @apply will-change-[transform,box-shadow,opacity]; // 优化动画性能
  @apply bg-[#fafafa] border border-solid border-[#e8e8e8];

  &:hover {
    @apply transform translate-y-[-2px];
    @apply shadow-[0_2px_6px_rgba(0,0,0,0.12)];
    @apply text-[#333] border-[#e8e8e8];
  }

  &:active {
    @apply transform scale-95 translate-y-[0px];
    @apply shadow-[0_1px_2px_rgba(0,0,0,0.1)];
    @apply transition-all duration-100;
    @apply bg-[#e8e8e8];
  }

  &.clicked {
    animation: tag-pulse 0.3s ease-in-out;
  }
}

// 标签点击动画
@keyframes tag-pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(0.95);
  }
  100% {
    transform: scale(1);
  }
}
</style>
