import { GET, POST, POST_JSON } from '@/api/request';
import { MaterialData, FolderData } from '@/types';

/**
 * 容量查询(已使用大小)
 * @returns
 */
export const getSpaceUsage = () =>
  GET<{
    byte: number;
    mb: number;
    kb: number;
    gb: number;
    tb: number;
    total: number;
    totalFormat: string;
    usage: number;
    usageFormat: string;
  }>('/api/resource/getSpaceUsage');

/**
 * 获取文件夹列表
 * @param params
 * @returns 文件夹列表
 */
export const getFolderList = () => GET<FolderData[]>('/api/folder/list');

/**
 * 获取文件夹内容列表(已支持搜索)
 * @param params
 * @param params.parentId 上级文件夹id
 * @param params.limit 分页数
 * @param params.page 当前页
 * @param params.type 类型 1: 图片 2: 视频
 * @param params.name 搜索关键字
 * @returns
 */
export const getFolderContent = (params: {
  parentId: number;
  limit: number;
  page: number;
  type: number;
  name?: string;
  typeFilter?: boolean;
}) => GET<MaterialData[]>('/api/folder/getContent', params);

/**
 * 添加文件夹
 * @param params.parentId 上级文件夹id
 * @param params.name 文件夹名字
 * @returns
 */
export const addFolder = (params: { parentId: number; name: string }) =>
  POST<FolderData>('/api/folder/add', params);

/**
 * 删除文件夹
 * @param params.id 文件夹id
 * @returns
 */
export const deleteFolder = (params: { id: number }) =>
  POST<{ success: boolean }>('/api/folder/delete', params);

/**
 * 修改文件夹
 * @param params.id 文件夹id
 * @param params.name 文件夹名字
 * @returns
 */
export const updateFolder = (params: { id: number; name: string }) =>
  POST<{ success: boolean }>('/api/folder/update', params);

/**
 * 移动文件夹
 * @param params.id 文件夹id
 * @param params.parentId 上级文件夹id
 * @returns
 */
export const moveFolder = (params: { ids: number[]; parentId: number }) =>
  POST_JSON<{ success: boolean }>('/api/folder/move', params);

/**
 * 删除资源
 * @param params
 * @param params.resIds 资源id数组
 * @returns
 */
export const deleteResource = (params: { resIds: string[] }) =>
  POST_JSON<{ success: boolean }>('/api/resource/delete', params);

/**
 * 修改资源名称
 * @param params
 * @param params.resId 资源id
 * @param params.name 资源名字
 * @returns
 */
export const updateResource = (params: { resId: string; name: string }) =>
  POST_JSON<{ success: boolean }>('/api/resource/update', params);

/**
 * 移动资源
 * @param params
 * @param params.parentId 上级文件夹id
 * @param params.resIds 资源id数组
 * @returns
 */
export const moveResource = (params: { parentId: number; resIds: string[] }) =>
  POST_JSON<{ success: boolean }>('/api/resource/move', params);

/**
 * 获取文件上传的访问密钥
 * @param params
 * @param params.number 非必须，数量，默认为1
 * @param params.folderId 非必须，文件夹id，默认为0
 * @param params.isTmp 非必须，是否临时文件，默认为否
 * @returns
 */
export const getAccessKey = (params?: {
  number?: number;
  folderId?: number;
  isTmp?: boolean;
}) => GET<string[]>('/api/upload/getAccessKey', params);
