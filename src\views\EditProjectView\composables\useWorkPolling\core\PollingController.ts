/**
 * @fileoverview 智能轮询控制器
 * @description 负责轮询的启动、停止和超时管理
 */

import { ref, Ref } from 'vue';
import { logger } from '@/utils/logger';
import { POLLING_TIMEOUT } from '../config';
import { POLLING_TRIGGER_TYPE } from '../constants';
import { createPollingContext } from '../utils';

/**
 * 轮询控制器类
 * 负责轮询的启动、停止和超时管理
 */
export class PollingController {
  // 轮询状态
  public readonly isPolling = ref(false);
  public readonly isPollingTimedOut = ref(false);
  public readonly pollingStartTime = ref<number | null>(null);

  // 私有状态
  private pollingTimer: Ref<number | null> = ref(null);
  private pollingTimeoutTimer: Ref<number | null> = ref(null);

  constructor(
    private projectId: number,
    private projectType: number,
    private getPollingWorksCount: () => number,
  ) {}

  /**
   * 启动轮询
   * @param triggerType 触发类型
   * @param pollingFunction 轮询执行函数
   * @param intervalFunction 间隔计算函数
   * @param needsPollingFunction 是否需要轮询判断函数
   */
  startPolling(
    triggerType: string = POLLING_TRIGGER_TYPE.AUTO,
    pollingFunction: () => Promise<void>,
    intervalFunction: () => number,
    needsPollingFunction: () => boolean,
  ): void {
    if (this.isPolling.value || !needsPollingFunction()) {
      return;
    }

    this.isPolling.value = true;
    const initialInterval = intervalFunction();

    // 启动超时计时器
    this.startPollingTimeout();

    // 记录启动日志
    this.logPollingStart(triggerType, initialInterval);

    // 开始递归轮询
    this.scheduleNextPoll(pollingFunction, intervalFunction, needsPollingFunction);
  }

  /**
   * 停止轮询
   */
  stopPolling(): void {
    if (this.pollingTimer.value) {
      clearTimeout(this.pollingTimer.value);
      this.pollingTimer.value = null;
    }

    // 清理超时计时器
    this.clearPollingTimeout();

    const wasPolling = this.isPolling.value;
    this.isPolling.value = false;

    if (wasPolling) {
      this.logPollingStop();
    }
  }

  /**
   * 清理资源
   */
  cleanup(): void {
    this.stopPolling();
    this.clearPollingTimeout();
  }

  /**
   * 启动超时计时器
   * @private
   */
  private startPollingTimeout(): void {
    // 清除之前的超时计时器
    if (this.pollingTimeoutTimer.value) {
      clearTimeout(this.pollingTimeoutTimer.value);
      this.pollingTimeoutTimer.value = null;
    }

    // 重置超时状态
    this.isPollingTimedOut.value = false;
    this.pollingStartTime.value = Date.now();

    // 启动新的超时计时器
    this.pollingTimeoutTimer.value = window.setTimeout(() => {
      const duration = Date.now() - (this.pollingStartTime.value || 0);
      const durationMinutes = Math.round(duration / (60 * 1000));

      logger.debug('⏰ 轮询超时自动停止', {
        超时时长: `${durationMinutes}分钟`,
        项目ID: this.projectId,
        轮询作品数量: this.getPollingWorksCount(),
        停止时间: new Date().toLocaleTimeString(),
      });

      // 标记为超时并停止轮询
      this.isPollingTimedOut.value = true;
      this.stopPolling();
    }, POLLING_TIMEOUT.DURATION);
  }

  /**
   * 清除超时计时器
   * @private
   */
  private clearPollingTimeout(): void {
    if (this.pollingTimeoutTimer.value) {
      clearTimeout(this.pollingTimeoutTimer.value);
      this.pollingTimeoutTimer.value = null;
    }
    this.pollingStartTime.value = null;
  }

  /**
   * 递归调度下次轮询
   * @private
   */
  private scheduleNextPoll(
    pollingFunction: () => Promise<void>,
    intervalFunction: () => number,
    needsPollingFunction: () => boolean,
  ): void {
    if (!this.isPolling.value) {
      return;
    }

    const interval = intervalFunction();
    this.pollingTimer.value = window.setTimeout(async () => {
      await pollingFunction();

      // 检查是否还需要继续轮询
      if (needsPollingFunction() && this.isPolling.value) {
        this.scheduleNextPoll(pollingFunction, intervalFunction, needsPollingFunction);
      } else {
        this.stopPolling();
      }
    }, interval);
  }

  /**
   * 记录轮询启动日志
   * @private
   */
  private logPollingStart(triggerType: string, initialInterval: number): void {
    const context = createPollingContext(
      triggerType,
      this.projectId,
      this.projectType,
      this.getPollingWorksCount(),
      true, // 启动时页面必然可见
    );

    logger.debug('🚀 智能轮询启动', {
      ...context,
      初始轮询间隔: `${initialInterval}ms`,
      超时时长: `${POLLING_TIMEOUT.DURATION / (60 * 1000)}分钟`,
    });
  }

  /**
   * 记录轮询停止日志
   * @private
   */
  private logPollingStop(): void {
    const stopReason = this.isPollingTimedOut.value
      ? '轮询超时'
      : '手动停止或无需轮询作品';

    const context = createPollingContext(
      '停止',
      this.projectId,
      this.projectType,
      this.getPollingWorksCount(),
      true,
    );

    logger.debug('⏹️ 智能轮询停止', {
      ...context,
      停止原因: stopReason,
      是否超时: this.isPollingTimedOut.value,
    });
  }
}
