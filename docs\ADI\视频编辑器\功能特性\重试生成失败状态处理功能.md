# 视频编辑器重试生成失败状态处理

## 概述

为视频编辑器预览页面添加对 `WORK_STATUS.FAILED_AGAIN`（重试生成失败）状态的完整处理逻辑。该状态与普通生成失败状态基本相同，但在某些细节上有特殊处理。

## 组件改动详情

### 作品列表组件

跟生成失败一样的结构，有如下特殊改动：

- **标签文案**：重试生成失败
- **操作按钮**：允许删除，不允许保存

### 视频播放器

跟生成失败一样的结构，有如下特殊改动：

- **顶部新增一个**：`<fa-alert message="重新生成作品失败（作品ID：10000），创作点数已自动返还。失败原因：配音额度不足，请咨询客服" banner />`

### 右侧编辑区域

- **不允许进行编辑**

## 具体实现改动

### 1. VideoPlayer 组件状态处理逻辑修改

#### 文件路径：`src/views/EditProjectView/pages/SecondStepVideo/components/VideoPlayer/hooks/useVideoPlayerState.ts`

**修改位置：**

- 第 31-33 行：`showFailedState` 计算属性
- 第 94-102 行：`statusText` 状态映射

```typescript
/**
 * 是否显示失败状态
 */
const showFailedState = computed(() => {
  return (
    videoStatus.value === WORK_STATUS.FAILED ||
    videoStatus.value === WORK_STATUS.FAILED_AGAIN
  );
});

/**
 * 状态描述文本
 */
const statusText = computed(() => {
  const statusMap: Record<number, string> = {
    [WORK_STATUS.GENERATING]: '生成中',
    [WORK_STATUS.REGENERATING]: '重新生成中',
    [WORK_STATUS.RECOMPLETED]: '重新生成完成',
    [WORK_STATUS.COMPLETED]: '已生成',
    [WORK_STATUS.HIDDEN]: '隐藏',
    [WORK_STATUS.EDIT]: '编辑中',
    [WORK_STATUS.FAILED]: '生成失败',
    [WORK_STATUS.FAILED_AGAIN]: '重试生成失败',
  };

  return statusMap[videoStatus.value] || '未知状态';
});
```

### 2. WorkListBarItem 组件状态处理修改

#### 文件路径：`src/components/WorkListBar/WorkListBarItem.vue`

**修改位置：**

- 第 278-281 行：`isFailed` 计算属性
- 第 330-348 行：`statusClass` 计算属性
- 第 350-365 行：`statusText` 计算属性

```typescript
// 计算是否为生成失败状态（状态为6或7表示生成失败）
const isFailed = computed(
  () =>
    props.workItem.status === WORK_STATUS.FAILED ||
    props.workItem.status === WORK_STATUS.FAILED_AGAIN,
);

// 计算状态类名
const statusClass = computed(() => {
  const { status } = props.workItem;
  if (status === WORK_STATUS.FAILED || status === WORK_STATUS.FAILED_AGAIN)
    return 'media-file-item__status-tag--failed';
  // ... 其他状态处理
});

// 计算状态文本
const statusText = computed(() => {
  const { status } = props.workItem;

  if (status === WORK_STATUS.FAILED) return '生成失败';
  if (status === WORK_STATUS.FAILED_AGAIN) return '重试生成失败';
  // ... 其他状态处理
});
```

### 3. ContentPanel 组件编辑权限修改

#### 文件路径：`src/views/EditProjectView/pages/SecondStepVideo/components/ContentPanel/index.vue`

**修改位置：**

- 第 281-294 行：`isEditDisabled` 计算属性

```typescript
/**
 * 是否禁用编辑功能
 * 只有已生成(COMPLETED)和重新生成完成(RECOMPLETED)状态的作品可以进行编辑操作
 * 生成失败和重试生成失败状态都不允许编辑
 */
isEditDisabled() {
  if (
    this.workData?.status === undefined ||
    this.workData?.status === null
  ) {
    return true; // 没有状态信息时禁用编辑
  }

  // 只有已完成状态和重新生成完成状态的作品可以编辑
  return !(
    this.workData.status === this.WORK_STATUS.COMPLETED ||
    this.workData.status === this.WORK_STATUS.RECOMPLETED
  );
},
```

### 4. VideoPlayer 组件顶部 Alert 添加

#### 文件路径：`src/views/EditProjectView/pages/SecondStepVideo/components/VideoPlayer/index.vue`

**需要添加：** 在 VideoPlayer 组件顶部添加 Alert 组件，当状态为 `WORK_STATUS.FAILED_AGAIN` 时显示：

```vue
<template>
  <div class="video-player">
    <!-- 重试生成失败状态的Alert提示 -->
    <fa-alert
      v-if="videoStatus === WORK_STATUS.FAILED_AGAIN"
      :message="`重新生成作品失败（作品ID：${workData?.id}），创作点数已自动返还。失败原因：${failedErrorMessage}`"
      banner
      class="video-player__alert"
    />

    <!-- 原有的视频容器 -->
    <div ref="videoContainer" class="video-player__container">
      <!-- ... 原有内容 -->
    </div>
  </div>
</template>
```

### 5. 轮询和状态管理相关修改

#### 文件路径：`src/hook/useWorkList.ts`

**修改位置：**

- 第 246-249 行：`hadFailWork` 状态判断

```typescript
hadFailWork:
  item.status === WORK_STATUS.FAILED ||
  item.status === WORK_STATUS.FAILED_AGAIN, // 是否有失败的作品
```

## 用户交互行为

### 编辑功能

- **禁用状态**：重试生成失败状态下，所有编辑相关按钮都被禁用
- **视觉反馈**：按钮呈现禁用状态的视觉样式

### 操作按钮

- **保存功能**：失败状态下不显示保存按钮
- **删除功能**：允许删除操作
- **状态显示**：侧边栏和预览区域正确显示"重试生成失败"状态

### 错误信息提示

- **Tooltip 提示**：通过 tooltip 显示具体的失败原因
- **Alert 横幅**：在视频播放器顶部显示详细的失败信息

## 样式处理

### CSS 类名

- 重试生成失败状态与普通生成失败状态使用相同的样式类：`media-file-item__status-tag--failed`
- 确保失败状态的视觉样式保持一致

### 响应式处理

- Alert 组件在不同屏幕尺寸下的适配
- 确保失败状态信息在移动端也能正确显示

## 注意事项

1. **状态一致性**：确保所有组件对 `WORK_STATUS.FAILED_AGAIN` 状态的处理保持一致
2. **用户体验**：失败状态的交互体验应与普通生成失败保持一致
3. **错误信息**：确保错误信息的准确性和用户友好性
4. **性能考虑**：失败状态不需要轮询，避免不必要的 API 请求

## 完成的任务清单

[x] VideoPlayer 组件状态处理修改

- [x] 修改 useVideoPlayerState.ts 中的 showFailedState 计算属性
- [x] 修改 useVideoPlayerState.ts 中的 statusText 状态映射
- [x] 检查 usePlayer.ts 中的 canShowVideo 计算属性（已正确）

[x] WorkListBarItem 组件状态处理修改

- [x] 修改 isFailed 计算属性支持 FAILED_AGAIN 状态
- [x] 修改 statusClass 计算属性支持 FAILED_AGAIN 状态
- [x] 修改 statusText 计算属性添加"重试生成失败"文案

[x] ContentPanel 组件编辑权限修改

- [x] 更新 isEditDisabled 计算属性注释说明（逻辑已正确）

[x] VideoPlayer 组件顶部 Alert 添加

- [x] 在 VideoPlayer 组件模板中添加 fa-alert 组件
- [x] 添加 FAILED_AGAIN 状态判断逻辑
- [x] 配置 Alert 消息内容和样式

[x] 轮询和状态管理相关修改

- [x] 确认 useWorkList.ts 中的 hadFailWork 状态判断（已正确）
- [x] 修改 ImagePreview 组件的 showFailedState 计算属性
- [x] 修改 useWorkPolling.ts 中的失败状态检测逻辑

[x] 测试验证和总结

- [x] 验证所有修改文件无语法错误
- [x] 确认状态处理逻辑的一致性
- [x] 完成功能实现总结

## 测试要点

1. **状态显示**：验证"重试生成失败"状态在各个组件中的正确显示
2. **交互禁用**：确认编辑功能在失败状态下被正确禁用
3. **错误信息**：验证 Alert 和 Tooltip 中的错误信息显示正确
4. **操作权限**：确认删除功能可用，保存功能被禁用
