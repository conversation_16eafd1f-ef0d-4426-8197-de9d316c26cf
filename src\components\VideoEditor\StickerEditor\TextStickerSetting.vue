<template>
  <div
    class="text-sticker-setting"
    ref="settingRoot"
    :class="{ 'b-primary!': settingFocus }"
  >
    <!-- 顶部栏 -->
    <div
      class="relative rounded-t-[8px] h-[53px] bg-[#FAFAFA] flex items-center justify-between px-[12px]"
      ref="toolbar"
    >
      <div class="flex items-center">
        <!-- 对齐方式 -->
        <fa-tooltip placement="top">
          <template slot="title">
            <span>左对齐</span>
          </template>
          <Icon
            type="juzuoduiqi"
            class="align-icon"
            :class="{ 'text-text!': textInfo.align === 'left' }"
            @click="textInfo.align = 'left'"
          />
        </fa-tooltip>
        <fa-tooltip placement="top">
          <template slot="title">
            <span>居中</span>
          </template>
          <Icon
            class="align-icon"
            :class="{ 'text-text!': textInfo.align === 'center' }"
            type="juzhongduiqi"
            @click="textInfo.align = 'center'"
          />
        </fa-tooltip>
        <fa-tooltip placement="top">
          <template slot="title">
            <span>右对齐</span>
          </template>
          <Icon
            class="align-icon"
            :class="{ 'text-text!': textInfo.align === 'right' }"
            type="juyouduiqi"
            @click="textInfo.align = 'right'"
          />
        </fa-tooltip>
        <!-- 字体 -->
        <fa-dropdown
          :getPopupContainer="getToolbarEl"
          :trigger="['click']"
          v-model="isShowFontList"
        >
          <!-- 按钮 -->
          <fa-button
            class="flex items-center w-[135px] mr-[12px] rounded-[6px]"
            size="small"
          >
            <div
              class="w-[90px] text-ellipsis overflow-hidden text-left mr-[16px] text-text line-height-[19px]"
            >
              {{ currFont?.name }}
            </div>
            <fa-icon
              type="down"
              class="text-disabledText transition-transform! duration-[200]!"
              :class="{ 'rotate-[180]!': isShowFontList }"
            />
          </fa-button>
          <!-- 下拉框 -->
          <div
            slot="overlay"
            class="bg-white rounded-[8px] shadow-[0_0_12px_#00000029]"
          >
            <!-- 搜索框 -->
            <fa-input v-model="searchText" class="w-[228px] m-[16px]">
              <fa-icon type="search" slot="suffix"></fa-icon>
            </fa-input>
            <!-- 语言切换 -->
            <fa-tabs v-model="currLang">
              <fa-tab-pane
                v-for="item in langList"
                :tab="item.name"
                :key="item.id"
              ></fa-tab-pane>
            </fa-tabs>
            <!-- 字体列表 -->
            <fa-collapse
              ref="fontCollapse"
              :activeKey="fontDropList.map(item => item.name)"
              v-show="freeList.length || paidList.length"
              class="h-[309px] min-h-[309px] max-h-[300px] overflow-auto rounded-b-[8px]"
            >
              <template v-slot:expandIcon="props">
                <fa-icon
                  class="text-[10px]!"
                  type="caret-right"
                  :rotate="props.isActive ? 90 : 0"
                />
              </template>
              <fa-collapse-panel
                v-for="fontCate in fontDropList"
                :key="fontCate.name"
              >
                <div slot="header" class="flex items-center">
                  {{ fontCate.name
                  }}<fa-tooltip placement="top" :arrowPointAtCenter="true">
                    <template slot="title">
                      <span>{{ fontCate.desc }}</span>
                    </template>
                    <Icon
                      type="wenxiao-biaoshan"
                      class="w-[14px] h-[14px] text-[#BFBFBF] hover:text-primary ml-[4px]"
                    />
                  </fa-tooltip>
                </div>
                <fa-menu mode="inline">
                  <template v-for="(item, index) in fontCate.list">
                    <template v-if="item.items.length > 1">
                      <fa-sub-menu
                        :key="item.category"
                        triggerSubMenuAction="click"
                      >
                        <template #title>
                          <ScImg
                            :lazy="index >= 10"
                            class="h-full"
                            :src="`${publicDomain}/fontIcons/${item.category}/${item.category}_default.svg`"
                          >
                            <div slot="error" class="text-red">
                              {{ item.category }}
                            </div>
                          </ScImg>
                        </template>
                        <fa-menu-item
                          v-for="font in item.items"
                          :key="font.fontFamily"
                          @click="handleClickFont(font)"
                        >
                          <ScImg
                            :lazy="index >= 10"
                            class="h-full"
                            :src="`${publicDomain}/fontIcons/${item.category}/${font.fontFamily}.svg`"
                          >
                            <div slot="error" class="text-red">
                              {{ font.name }}
                            </div>
                          </ScImg>
                        </fa-menu-item>
                      </fa-sub-menu>
                    </template>
                    <template v-else>
                      <fa-menu-item
                        v-for="font in item.items"
                        :key="font.fontFamily"
                        @click="handleClickFont(font)"
                      >
                        <ScImg
                          :lazy="index >= 10"
                          class="h-full"
                          :src="`${publicDomain}/fontIcons/${
                            font.styleCategory ? font.styleCategory + '/' : ''
                          }${font.fontFamily}.svg`"
                        >
                          <div slot="error" class="text-red">
                            {{ font.name }}
                          </div>
                        </ScImg>
                      </fa-menu-item>
                    </template>
                  </template>
                </fa-menu>
              </fa-collapse-panel>
            </fa-collapse>
            <!-- 空状态 -->
            <div
              class="flex flex-col justify-center items-center h-[355px]"
              v-show="!freeList.length && !paidList.length"
            >
              <img
                class="w-[120px] h-auto"
                src="@/assets/common/searchEmpty.webp"
                alt="空状态"
              />
              <div class="text-assist text-[15px] mt-[16px]">
                未找到相关字体
              </div>
            </div>
          </div>
        </fa-dropdown>
        <!-- 字体大小 -->
        <fa-select
          v-model="fontSizePx"
          :getPopupContainer="getToolbarEl"
          class="w-[80px] mr-[12px] text-text"
        >
          <fa-select-option
            v-for="size in Array.from(
              { length: currentEditorType == 'image' ? 96 : 89 },
              (_, i) => (currentEditorType == 'image' ? 5 : 12) + i,
            )"
            :key="size + 'px'"
            :value="size"
          >
            {{ size }}px
          </fa-select-option>
        </fa-select>
        <!-- 字体预设 -->
        <fa-dropdown :getPopupContainer="getToolbarEl" :trigger="['click']">
          <div
            class="w-[32px] h-[32px] rounded-[6px] cursor-pointer mr-[12px] bg-[#f5f5f5] border-[#d9d9d9] border hover:border-primary transition-colors duration-200"
          >
            <img
              :src="
                presetImages[`/src/assets/FontPreset/${textInfo.styleId}.webp`]
                  ?.default || ''
              "
              class="w-full h-full rounded-[6px]"
            />
          </div>
          <div
            slot="overlay"
            class="grid grid-cols-6 gap-[8px] p-[16px] rounded-[6px] bg-white shadow-[0_3px_20px_#0000001a]"
          >
            <div
              class="font-preset-item"
              :class="{ selected: textInfo.styleId === preset.id }"
              v-for="preset in fontPresetList"
              :key="preset.id"
              @click="handleClickPreset(preset)"
            >
              <img
                :src="
                  presetImages[`/src/assets/FontPreset/${preset.id}.webp`]
                    ?.default || ''
                "
              />
            </div>
          </div>
        </fa-dropdown>
      </div>
      <!-- 应用到全部 -->
      <fa-popconfirm
        :getPopupContainer="getToolbarEl"
        placement="topRight"
        :title="`确定将当前样式应用到${
          currentEditorType == 'image' ? '图片' : '视频'
        }中的全部花字中吗？`"
        @confirm="applyToAll"
      >
        <div class="text-[13px] text-text hover:text-primary cursor-pointer">
          样式应用到全部
        </div>
      </fa-popconfirm>
    </div>
    <!-- 输入区-->
    <div class="relative input-area-wrapper">
      <fa-textarea
        class="w-full scrollbar-small"
        showCount
        :maxLength="200"
        :rows="4"
        v-model="textInfo.text"
      />
      <Icon type="shanshu" class="delIcon" @click="handleDeleteText" />
    </div>
  </div>
</template>

<script lang="ts" setup>
import { Font, FontLanguage, FontPreset, TextSticker } from '@/types';
import Vue, {
  computed,
  nextTick,
  onBeforeUnmount,
  onMounted,
  ref,
  watch,
} from 'vue';
import { loadFont } from '@/utils/loadFont';
import { useFontInfo } from './hook/useFontInfo';
import { useFontPreset } from './hook/useFontPreset';
import { formatToTree, pxToSize, sizeToPx } from './utils';
import store from '@/store';
import { message } from '@fk/faicomponent';
import { currentEditorType } from '@/constants/project';
import { throttle } from 'lodash-es';

/********辅助fa组件弹窗层挂载 star***********/
const toolbar = ref<HTMLElement | null>(null);
const getToolbarEl = (): HTMLElement => {
  if (!toolbar.value) {
    console.warn(
      '[TextStickerSetting] toolbar ref not found, using document.body as fallback',
    );
    return document.body;
  }

  return toolbar.value;
};
/********辅助fa组件弹窗层挂载 end***********/

/********基础属性 star***********/
const props = defineProps<{
  /** 当前花字 */
  textInfo: TextSticker;
}>();
const emit = defineEmits<{
  (e: 'applyToAll'): void;
  (e: 'deleteText'): void;
}>();
/********基础属性 end***********/

/********监听点击区域是否在组件内，外框变蓝 star***********/
const settingFocus = ref(false);
const settingRoot = ref<HTMLElement>();
/** 监听全局点击，判断是否在组件内 */
const handleClickOutside = (event: MouseEvent) => {
  // 如果rootRef不存在，直接返回
  if (!settingRoot.value) return;
  // 判断点击目标是否在组件内
  if (
    settingRoot.value.contains(event.target as Node) ||
    (event.target instanceof HTMLElement &&
      event.target.closest('.fa-popover') &&
      !event.target.closest('.dev-mode__popover'))
  ) {
    // 在组件内，设置为true
    settingFocus.value = true;
  } else {
    // 不在组件内，设置为false
    settingFocus.value = false;
  }
};
// 组件挂载时添加事件监听，卸载时移除
onMounted(() => {
  document.addEventListener('mousedown', handleClickOutside);
});
onBeforeUnmount(() => {
  document.removeEventListener('mousedown', handleClickOutside);
});
/********监听点击区域是否在组件内，外框变蓝 end***********/

const isDev = import.meta.env.MODE === 'development';
const publicDomain = isDev
  ? window.location.origin
  : store.state.system.scPortalResRoot + '/css';

/********字体选择 star***********/
const { fontList } = useFontInfo();
const isShowFontList = ref(false);
const currFont = computed(() => {
  const font = fontList.value.find(
    item => item.fontFamily === props.textInfo.fileName.split('.')[0],
  );
  return font;
});
const langList = [
  { name: '中文', id: FontLanguage.ZH },
  { name: '英文', id: FontLanguage.EN },
];
const currLang = ref(langList[0].id);
const searchText = ref('');
const wordForSearch = ref('');
const updateWordForSearch = throttle(() => {
  wordForSearch.value = searchText.value.trim().toLowerCase();
}, 300);
watch(searchText, updateWordForSearch);
const fontCollapse = ref<Vue>();

const freeList = computed(() => {
  return formatToTree(
    fontList.value.filter(item => {
      if (!item.name.toLowerCase().includes(wordForSearch.value)) return false;
      return (
        item.languageCategory === currLang.value &&
        item.pricingCategory === 'free'
      );
    }),
  );
});
const paidList = computed(() => {
  return formatToTree(
    fontList.value.filter(item => {
      if (!item.name.toLowerCase().includes(wordForSearch.value)) return false;
      return (
        item.languageCategory === currLang.value &&
        item.pricingCategory === 'paid'
      );
    }),
  );
});
const fontDropList = computed(() => {
  const list = [];
  if (freeList.value.length > 0) {
    list.push({
      name: '免费字体',
      desc: '字体厂商或个人设计师向全社会开放授权，可免费商用',
      list: freeList.value,
    });
  }
  if (paidList.value.length > 0) {
    list.push({
      name: '凡科网臻享字体',
      desc: '凡科网已向字体厂商购买授权，提供给用户在凡科网平台使用的正版字体',
      list: paidList.value,
    });
  }
  return list;
});
watch(
  () => currLang.value,
  () => {
    nextTick(() => {
      if (fontCollapse.value) {
        fontCollapse.value.$el.scrollTo({ behavior: 'smooth', top: 0 });
      }
    });
  },
);
const handleClickFont = async (font: Font) => {
  try {
    isShowFontList.value = false;
    await loadFont(font);
    nextTick(() => {
      Vue.set(props.textInfo, 'fontName', font.fontName);
      Vue.set(props.textInfo, 'fileName', font.fileName);
    });
  } catch (error) {
    message.error('加载字体失败: ' + error);
  }
};
/********字体选择 end***********/

/********预设 star***********/
const { fontPresetList } = useFontPreset();
/** 点击预设 */
const handleClickPreset = (preset: FontPreset) => {
  const DEFAULT_COLOR = '00000000';
  const DEFAULT_POSITION = 0;
  props.textInfo.styleId = Number(preset.id);
  Vue.set(props.textInfo, 'color', preset.color || DEFAULT_COLOR);
  Vue.set(props.textInfo, 'boxColor', preset.boxColor || DEFAULT_COLOR);
  Vue.set(props.textInfo, 'strokeColor', preset.strokeColor || DEFAULT_COLOR);
  Vue.set(
    props.textInfo,
    'strokeWidth',
    preset.strokeWidth || DEFAULT_POSITION,
  );
  Vue.set(props.textInfo, 'shadowColor', preset.shadowColor || DEFAULT_COLOR);
  Vue.set(props.textInfo, 'shadowX', preset.shadowX || DEFAULT_POSITION);
  Vue.set(props.textInfo, 'shadowY', preset.shadowY || DEFAULT_POSITION);
  Vue.set(
    props.textInfo,
    'shadowStrokeColor',
    preset.shadowStrokeColor || DEFAULT_COLOR,
  );
  Vue.set(
    props.textInfo,
    'shadowStrokeWidth',
    preset.shadowStrokeWidth || DEFAULT_POSITION,
  );
};
/** 预设图标 */
const presetImages: Record<string, { default: string }> = import.meta.glob(
  '@/assets/FontPreset/*.webp',
  { eager: true },
);
/********预设 end***********/

const fontSizePx = computed({
  get: () => sizeToPx(props.textInfo.fontSize),
  set: (val: number) => {
    props.textInfo.fontSize = pxToSize(val);
  },
});

const applyToAll = () => {
  emit('applyToAll');
};
const handleDeleteText = () => {
  emit('deleteText');
};
</script>

<style lang="scss" scoped>
.text-sticker-setting {
  @apply b-edge b-1 rounded-[8px] select-none;
  .font-preset-item {
    @apply w-[32px] h-[32px] rounded-[6px] cursor-pointer bg-[#f5f5f5];
    img {
      @apply w-full h-full rounded-[6px];
    }
    &:hover,
    &.selected {
      @apply b-1 b-solid b-primary;
    }
  }
  .input-area-wrapper {
    @apply relative;
    .delIcon {
      @apply hidden w-[18px] h-[18px] absolute cursor-pointer top-[50%] right-[13px] transform -translate-y-1/2 text-assist;
    }
    &:hover {
      .delIcon {
        @apply block;
      }
    }
  }
  .align-icon {
    @apply outline-none w-[20px] h-[20px] mr-[12px] text-disabledText hover:text-subText cursor-pointer;
  }
  ::v-deep {
    .fa-tabs-nav {
      @apply ml-[0px];
      .fa-tabs-tab {
        @apply line-height-17px text-[13px] pb-13px;
      }
    }
    .fa-tabs-tab {
      @apply px-[11px]! mx-[41px]! pt-0!;
    }
    .fa-collapse {
      @apply b-0 b-transparent bg-transparent;
    }
    .fa-collapse-item {
      @apply b-0;
      &:not(:last-child) {
        &::after {
          content: '';
          display: block;
          box-sizing: border-box;
          margin: 4px auto 0;
          width: calc(100% - 32px);
          height: 1px;
          background-color: #e8e8e8;
        }
      }
      .fa-collapse-header {
        @apply text-assist! pl-[27px] text-[13px] pt-[12px] pb-[8px];
        .fa-collapse-arrow {
          @apply text-assist! left-[14px] transform-translate-y-[-30%];
        }
      }
    }
    .fa-collapse-content {
      @apply b-0;
      .fa-collapse-content-box {
        @apply p-[0px] bg-transparent;
        .fa-menu-submenu-title {
          @apply m-0 h-[32px] line-height-[32px] pl-[16px]! pr-[20px]!;
        }
      }
    }
    .fa-menu-inline {
      @apply b-r-0;
    }
    .fa-menu-item,
    .fa-menu-sub.fa-menu-inline > .fa-menu-item {
      @apply h-[32px] line-height-[32px] pl-[16px]! pr-[20px]! m-0!;
      @apply hover:bg-#f5f5f5;
    }
    .fa-menu-item-selected {
      &::after {
        @apply hidden;
      }
    }
    .fa-menu-submenu-arrow {
      @apply rotate-0!;
    }
    textarea.fa-input {
      @apply b-0 rounded-b-[8px] pr-[24px];
    }
    .fa-select-selection {
      @apply rounded-[6px];
    }
  }
}
::v-deep {
  textarea.fa-input {
    @apply b-0 shadow-none! b-transparent!;
  }
  .fa-select-dropdown {
    @apply scrollbar-small;
  }
}
</style>
