/**
 * @description useFirstStep composable 的单元测试
 */
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { useFirstStep } from '../useFirstStep';
import { PROJECT_TYPE_VIDEO, PROJECT_TYPE_IMAGE } from '../../constants';
import type { UseFirstStepOptions } from '../useFirstStep';

// Mock API 调用
vi.mock('@/api/EditProjectView', () => ({
  getConsumePoint: vi.fn().mockResolvedValue([null, { data: 100 }]),
  generatePreview: vi.fn().mockResolvedValue([null, { data: {} }]),
  saveProjectData: vi.fn().mockResolvedValue([null, { data: {} }]),
}));

// Mock 工具函数
vi.mock('../../utils/index', () => ({
  formatFirstStepDataForSave: vi.fn().mockReturnValue({
    name: 'test project',
    inputForm: [],
    resForm: [],
    bgMusic: null,
    voice: null,
    script: null,
  }),
  createInputFormItem: vi.fn().mockReturnValue({}),
}));

// Mock 消息提示
vi.mock('@fk/faicomponent', () => ({
  message: {
    success: vi.fn(),
    error: vi.fn(),
  },
}));

describe('useFirstStep', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('generateButtonConfig 配置', () => {
    it('应该为视频类型项目设置正确的默认值和限制', () => {
      const options: UseFirstStepOptions = {
        templateId: 1,
        projectType: PROJECT_TYPE_VIDEO,
      };

      const { generateButtonConfig } = useFirstStep(options);

      expect(generateButtonConfig.value).toEqual({
        num: 5, // 视频类型默认生成数量
        maxCount: 10, // 视频类型最大生成数量
        minCount: 1, // 最小生成数量
        integral: 1,
        showIntegral: false,
      });
    });

    it('应该为图文类型项目设置正确的默认值和限制', () => {
      const options: UseFirstStepOptions = {
        templateId: 1,
        projectType: PROJECT_TYPE_IMAGE,
      };

      const { generateButtonConfig } = useFirstStep(options);

      expect(generateButtonConfig.value).toEqual({
        num: 10, // 图文类型默认生成数量
        maxCount: 99, // 图文类型最大生成数量
        minCount: 1, // 最小生成数量
        integral: 1,
        showIntegral: false,
      });
    });

    it('应该为未知项目类型设置默认值（视频类型）', () => {
      const options: UseFirstStepOptions = {
        templateId: 1,
        projectType: 999 as any, // 未知类型
      };

      const { generateButtonConfig } = useFirstStep(options);

      expect(generateButtonConfig.value).toEqual({
        num: 5, // 默认为视频类型的值
        maxCount: 10, // 默认为视频类型的值
        minCount: 1,
        integral: 1,
        showIntegral: false,
      });
    });
  });

  describe('生成数量限制验证', () => {
    it('视频类型项目的生成数量限制应该正确', () => {
      const options: UseFirstStepOptions = {
        templateId: 1,
        projectType: PROJECT_TYPE_VIDEO,
      };

      const { generateButtonConfig } = useFirstStep(options);

      // 验证视频类型的限制
      expect(generateButtonConfig.value.minCount).toBe(1);
      expect(generateButtonConfig.value.maxCount).toBe(10);
      expect(generateButtonConfig.value.num).toBe(5);
      expect(generateButtonConfig.value.num).toBeGreaterThanOrEqual(
        generateButtonConfig.value.minCount,
      );
      expect(generateButtonConfig.value.num).toBeLessThanOrEqual(
        generateButtonConfig.value.maxCount,
      );
    });

    it('图文类型项目的生成数量限制应该正确', () => {
      const options: UseFirstStepOptions = {
        templateId: 1,
        projectType: PROJECT_TYPE_IMAGE,
      };

      const { generateButtonConfig } = useFirstStep(options);

      // 验证图文类型的限制
      expect(generateButtonConfig.value.minCount).toBe(1);
      expect(generateButtonConfig.value.maxCount).toBe(99);
      expect(generateButtonConfig.value.num).toBe(10);
      expect(generateButtonConfig.value.num).toBeGreaterThanOrEqual(
        generateButtonConfig.value.minCount,
      );
      expect(generateButtonConfig.value.num).toBeLessThanOrEqual(
        generateButtonConfig.value.maxCount,
      );
    });
  });

  describe('配置对象结构验证', () => {
    it('generateButtonConfig 应该包含所有必需的属性', () => {
      const options: UseFirstStepOptions = {
        templateId: 1,
        projectType: PROJECT_TYPE_VIDEO,
      };

      const { generateButtonConfig } = useFirstStep(options);

      // 验证配置对象包含所有必需的属性
      expect(generateButtonConfig.value).toHaveProperty('num');
      expect(generateButtonConfig.value).toHaveProperty('maxCount');
      expect(generateButtonConfig.value).toHaveProperty('minCount');
      expect(generateButtonConfig.value).toHaveProperty('integral');
      expect(generateButtonConfig.value).toHaveProperty('showIntegral');

      // 验证属性类型
      expect(typeof generateButtonConfig.value.num).toBe('number');
      expect(typeof generateButtonConfig.value.maxCount).toBe('number');
      expect(typeof generateButtonConfig.value.minCount).toBe('number');
      expect(typeof generateButtonConfig.value.integral).toBe('number');
      expect(typeof generateButtonConfig.value.showIntegral).toBe('boolean');
    });
  });
});
