import { ref, onMounted, onUnmounted, type Ref } from 'vue';
import type { WorkItem } from '@/types/Work';
import { eventBus, EVENT_NAMES } from '@/utils/eventBus';
import { message } from '@fk/faicomponent';

/**
 * 新旧选择弹窗的参数接口
 */
export interface UseNewOldChoiceModalParams {
  /** 刷新作品列表的方法 */
  refreshWorkList: () => Promise<[Error | null, void | null]>;
}

/**
 * 新旧选择弹窗的返回值接口
 */
export interface UseNewOldChoiceModalReturn {
  /** 弹窗显示状态 */
  showNewOldChoiceModal: Ref<boolean>;
  /** 当前重新生成的作品数据 */
  currentRegeneratedWork: Ref<WorkItem | null>;
  /** 处理从弹窗保存新作品 */
  handleSaveNewFromModal: () => Promise<void>;
  /** 处理从弹窗保存新旧作品 */
  handleSaveAllFromModal: () => Promise<void>;
}

/**
 * 新旧选择弹窗 Composables
 * @description 封装新旧选择弹窗的状态管理和事件处理逻辑
 * @param params 参数对象
 * @returns 弹窗相关的状态和方法
 */
export function useNewOldChoiceModal(
  params: UseNewOldChoiceModalParams,
): UseNewOldChoiceModalReturn {
  const { refreshWorkList } = params;

  // 弹窗状态
  const showNewOldChoiceModal = ref<boolean>(false);
  const currentRegeneratedWork = ref<WorkItem | null>(null);

  /**
   * 处理新旧选择弹窗的关闭
   */
  const handleNewOldChoiceModalClose = (): void => {
    showNewOldChoiceModal.value = false;
    currentRegeneratedWork.value = null;
  };

  /**
   * 处理从弹窗保存新作品
   * @description WorkPreviewModal内部已处理保存逻辑，这里只需要关闭弹窗并刷新作品列表
   */
  const handleSaveNewFromModal = async (): Promise<void> => {
    handleNewOldChoiceModalClose();

    // 刷新作品列表
    const [err] = await refreshWorkList();
    if (err) {
      console.error('保存新作品后刷新列表失败:', err.message);
      message.error(err.message || '保存失败！');
      return;
    }
  };

  /**
   * 处理从弹窗保存新旧作品
   * @description WorkPreviewModal内部已处理保存逻辑，这里只需要关闭弹窗并刷新作品列表
   */
  const handleSaveAllFromModal = async (): Promise<void> => {
    handleNewOldChoiceModalClose();

    // 刷新作品列表
    const [err] = await refreshWorkList();
    if (err) {
      console.error('保存新旧作品后刷新列表失败:', err.message);
      message.error(err.message || '保存失败！');
      return;
    }
  };

  /**
   * 处理显示新旧选择弹窗
   * @param args 事件参数
   */
  const handleShowNewOldChoiceModal = (...args: unknown[]): void => {
    const workData = args[0] as WorkItem;
    currentRegeneratedWork.value = workData;
    showNewOldChoiceModal.value = true;
  };

  // 监听 eventBus 事件
  onMounted(() => {
    eventBus.on(
      EVENT_NAMES.SHOW_NEW_OLD_CHOICE_MODAL,
      handleShowNewOldChoiceModal,
    );
  });

  onUnmounted(() => {
    eventBus.off(
      EVENT_NAMES.SHOW_NEW_OLD_CHOICE_MODAL,
      handleShowNewOldChoiceModal,
    );
  });

  return {
    showNewOldChoiceModal,
    currentRegeneratedWork,
    handleSaveNewFromModal,
    handleSaveAllFromModal,
  };
}
