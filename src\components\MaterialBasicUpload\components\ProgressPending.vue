<template>
  <div class="upload-progress">
    <div class="upload-progress--mask"></div>
    <fa-progress :percent="percent" size="small" :show-info="false" />
    <div class="upload-progress--text">{{ text }}</div>
  </div>
</template>

<script lang="ts" setup>
import { defineProps, computed } from 'vue';

const props = defineProps({
  percent: {
    type: Number,
    default: 0,
  },
});

const text = computed(() => {
  return props.percent > 0 ? '文件上传中' : '等待中';
});
</script>

<style lang="scss" scoped>
.upload-progress {
  @apply absolute top-[0] left-[0] pl-[5px] pr-[5px];
  @apply w-full h-full border border-edge rounded-[8px];
  &--mask {
    @apply absolute left-0 right-0 top-0 h-full rounded-[8px] bg-[#fafafa];
  }
  :deep(.fa-progress) {
    margin-top: calc(50% - 7px);
    .fa-progress-inner {
      @apply bg-divider;
    }
    .fa-progress-bg {
      @apply bg-primary;
    }
  }
  &--text {
    @apply relative text-[14px] text-assist text-center;
  }
}
</style>
