/**
 * @fileoverview useProjectData 项目状态检查功能单元测试
 */

import { describe, it, expect, beforeEach, afterEach, vi, Mock } from 'vitest';
import { nextTick, ref } from 'vue';
import { useProjectData } from '../useProjectData';
import { getEditProjectViewData } from '@/api/EditProjectView';
import router from '@/router';

// Mock API 模块
vi.mock('@/api/EditProjectView', () => ({
  getEditProjectViewData: vi.fn(),
}));

// Mock router
vi.mock('@/router', () => ({
  default: {
    currentRoute: {
      path: '/video-project',
      query: { projectId: '123', templateId: '456', step: '0' },
    },
    replace: vi.fn(),
    go: vi.fn(),
  },
}));

// Mock message
vi.mock('@fk/faicomponent', () => ({
  message: {
    error: vi.fn(),
  },
}));

// Mock document.cookie
Object.defineProperty(document, 'cookie', {
  writable: true,
  value: '',
});

const mockGetEditProjectViewData = getEditProjectViewData as Mock;
const mockRouter = router as any;

describe('useProjectData - 项目状态检查功能', () => {
  beforeEach(() => {
    // 清除所有模拟函数的调用记录
    vi.clearAllMocks();

    // 重置路由状态
    mockRouter.currentRoute = {
      path: '/video-project',
      query: { projectId: '123', templateId: '456', step: '0' },
    };

    // 重置 cookie 状态
    document.cookie = '';
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('项目状态检查', () => {
    it('草稿状态（status=0）应该继续正常流程', async () => {
      // 设置普通账号 cookie
      document.cookie = 'user=normal; token=1234567';

      // 模拟获取项目数据API返回草稿状态
      mockGetEditProjectViewData.mockResolvedValue([
        null,
        {
          projectId: 123,
          templateId: 456,
          type: 0,
          inputForm: [],
          resForm: [],
        },
      ]);

      const { fetchFormData } = useProjectData({
        initialStep: ref(0),
        templateId: 456,
      });

      // 调用fetchFormData，应该正常执行
      await fetchFormData();

      // 验证获取项目数据API被调用，并且启用了状态检查
      expect(mockGetEditProjectViewData).toHaveBeenCalledWith(456, 123, {
        checkProjectStatus: true,
      });

      // 验证没有进行路由重定向
      expect(mockRouter.replace).not.toHaveBeenCalled();
      expect(mockRouter.go).not.toHaveBeenCalled();
    });

    it('生成中状态（status=1）应该重定向到step=1', async () => {
      // 设置普通账号 cookie
      document.cookie = 'user=normal; token=1234567';

      // 模拟获取项目数据API返回状态检查失败错误
      mockGetEditProjectViewData.mockResolvedValue([
        new Error(
          '项目状态检查失败：当前项目状态为 1(生成中)，只有非生成中状态才允许访问第一步，需要重定向到正确步骤',
        ),
        null,
      ]);

      const { fetchFormData } = useProjectData({
        initialStep: ref(0),
        templateId: 456,
      });

      // 调用fetchFormData
      await fetchFormData();

      // 等待nextTick执行
      await nextTick();
      await nextTick();

      // 验证获取项目数据API被调用，并且启用了状态检查
      expect(mockGetEditProjectViewData).toHaveBeenCalledWith(456, 123, {
        checkProjectStatus: true,
      });

      // 验证进行了路由重定向
      expect(mockRouter.replace).toHaveBeenCalledWith({
        path: '/video-project',
        query: {
          projectId: '123',
          templateId: '456',
          step: '1', // 重定向到step=1
        },
      });

      // 注意：已移除router.go(0)强制刷新，现在使用响应式更新
      expect(mockRouter.go).not.toHaveBeenCalled();
    });

    it('待保存状态（status=2）应该继续正常流程', async () => {
      // 设置普通账号 cookie
      document.cookie = 'user=normal; token=1234567';

      // 模拟获取项目数据API返回待保存状态，现在应该正常继续（使用统一的 shouldHidePreviousStep 逻辑）
      mockGetEditProjectViewData.mockResolvedValue([
        null,
        {
          projectId: 123,
          templateId: 456,
          type: 0,
          inputForm: [],
          resForm: [],
        },
      ]);

      const { fetchFormData } = useProjectData({
        initialStep: ref(0),
        templateId: 456,
      });

      // 调用fetchFormData，应该正常执行
      await fetchFormData();

      // 验证获取项目数据API被调用，并且启用了状态检查
      expect(mockGetEditProjectViewData).toHaveBeenCalledWith(456, 123, {
        checkProjectStatus: true,
      });

      // 验证没有进行路由重定向（待保存状态现在允许继续）
      expect(mockRouter.replace).not.toHaveBeenCalled();
      expect(mockRouter.go).not.toHaveBeenCalled();
    });

    it('已完成状态（status=3）应该继续正常流程', async () => {
      // 设置普通账号 cookie
      document.cookie = 'user=normal; token=1234567';

      // 模拟获取项目数据API返回已完成状态，应该正常继续
      mockGetEditProjectViewData.mockResolvedValue([
        null,
        {
          projectId: 123,
          templateId: 456,
          type: 0,
          inputForm: [],
          resForm: [],
        },
      ]);

      const { fetchFormData } = useProjectData({
        initialStep: ref(0),
        templateId: 456,
      });

      // 调用fetchFormData，应该正常执行
      await fetchFormData();

      // 验证获取项目数据API被调用，并且启用了状态检查
      expect(mockGetEditProjectViewData).toHaveBeenCalledWith(456, 123, {
        checkProjectStatus: true,
      });

      // 验证没有进行路由重定向（已完成状态允许继续）
      expect(mockRouter.replace).not.toHaveBeenCalled();
      expect(mockRouter.go).not.toHaveBeenCalled();
    });

    it('重新生成完成状态（status=5）应该继续正常流程', async () => {
      // 设置普通账号 cookie
      document.cookie = 'user=normal; token=1234567';

      // 模拟获取项目数据API返回重新生成完成状态，现在应该正常继续（使用统一的 shouldHidePreviousStep 逻辑）
      mockGetEditProjectViewData.mockResolvedValue([
        null,
        {
          projectId: 123,
          templateId: 456,
          type: 0,
          inputForm: [],
          resForm: [],
        },
      ]);

      const { fetchFormData } = useProjectData({
        initialStep: ref(0),
        templateId: 456,
      });

      // 调用fetchFormData
      await fetchFormData();

      // 等待nextTick执行
      await nextTick();
      await nextTick();

      // 验证获取项目数据API被调用，并且启用了状态检查
      expect(mockGetEditProjectViewData).toHaveBeenCalledWith(456, 123, {
        checkProjectStatus: true,
      });

      // 验证没有进行路由重定向（重新生成完成状态现在允许继续）
      expect(mockRouter.replace).not.toHaveBeenCalled();
      expect(mockRouter.go).not.toHaveBeenCalled();
    });
  });

  describe('开发者账号特殊处理', () => {
    it('开发者账号（cookie包含9875837）应该跳过状态检查', async () => {
      // 设置开发者账号 cookie
      document.cookie = 'user=developer; token=9875837; other=value';

      // 模拟获取项目数据API返回正常数据
      mockGetEditProjectViewData.mockResolvedValue([
        null,
        {
          projectId: 123,
          templateId: 456,
          type: 0,
          inputForm: [],
          resForm: [],
        },
      ]);

      const { fetchFormData } = useProjectData({
        initialStep: ref(0),
        templateId: 456,
      });

      // 调用fetchFormData
      await fetchFormData();

      // 验证获取项目数据API被调用，但没有启用状态检查
      expect(mockGetEditProjectViewData).toHaveBeenCalledWith(456, 123, {
        checkProjectStatus: false, // 开发者账号应该跳过状态检查
      });

      // 验证没有进行路由重定向
      expect(mockRouter.replace).not.toHaveBeenCalled();
      expect(mockRouter.go).not.toHaveBeenCalled();
    });

    it('普通账号（cookie不包含9875837）应该正常进行状态检查', async () => {
      // 设置普通账号 cookie
      document.cookie = 'user=normal; token=1234567; other=value';

      // 模拟获取项目数据API返回正常数据
      mockGetEditProjectViewData.mockResolvedValue([
        null,
        {
          projectId: 123,
          templateId: 456,
          type: 0,
          inputForm: [],
          resForm: [],
        },
      ]);

      const { fetchFormData } = useProjectData({
        initialStep: ref(0),
        templateId: 456,
      });

      // 调用fetchFormData
      await fetchFormData();

      // 验证获取项目数据API被调用，并且启用了状态检查
      expect(mockGetEditProjectViewData).toHaveBeenCalledWith(456, 123, {
        checkProjectStatus: true, // 普通账号应该启用状态检查
      });

      // 验证没有进行路由重定向
      expect(mockRouter.replace).not.toHaveBeenCalled();
      expect(mockRouter.go).not.toHaveBeenCalled();
    });

    it('开发者账号在生成中状态也不会被重定向', async () => {
      // 设置开发者账号 cookie
      document.cookie = 'user=developer; token=9875837; other=value';

      // 模拟获取项目数据API返回正常数据（因为跳过了状态检查）
      mockGetEditProjectViewData.mockResolvedValue([
        null,
        {
          projectId: 123,
          templateId: 456,
          type: 0,
          inputForm: [],
          resForm: [],
        },
      ]);

      const { fetchFormData } = useProjectData({
        initialStep: ref(0),
        templateId: 456,
      });

      // 调用fetchFormData
      await fetchFormData();

      // 验证获取项目数据API被调用，但没有启用状态检查
      expect(mockGetEditProjectViewData).toHaveBeenCalledWith(456, 123, {
        checkProjectStatus: false, // 开发者账号跳过状态检查
      });

      // 验证没有进行路由重定向（即使项目可能处于生成中状态）
      expect(mockRouter.replace).not.toHaveBeenCalled();
      expect(mockRouter.go).not.toHaveBeenCalled();
    });
  });

  describe('错误处理', () => {
    it('获取项目数据失败时应该显示错误信息', async () => {
      // 设置普通账号 cookie
      document.cookie = 'user=normal; token=1234567';

      // 模拟获取项目数据API调用失败（非状态检查错误）
      mockGetEditProjectViewData.mockResolvedValue([
        new Error('网络连接失败'),
        null,
      ]);

      const { fetchFormData } = useProjectData({
        initialStep: ref(0),
        templateId: 456,
      });

      // 调用fetchFormData
      await fetchFormData();

      // 验证获取项目数据API被调用，并且启用了状态检查
      expect(mockGetEditProjectViewData).toHaveBeenCalledWith(456, 123, {
        checkProjectStatus: true,
      });

      // 验证没有进行路由重定向（因为不是状态检查错误）
      expect(mockRouter.replace).not.toHaveBeenCalled();
      expect(mockRouter.go).not.toHaveBeenCalled();
    });

    it('新建模式下不应该进行状态检查', async () => {
      // 模拟获取项目数据API
      mockGetEditProjectViewData.mockResolvedValue([
        null,
        {
          projectId: 123,
          templateId: 456,
          type: 0,
          inputForm: [],
          resForm: [],
        },
      ]);

      // 设置路由为新建模式（没有projectId）
      mockRouter.currentRoute.query = { templateId: '456', step: '0' };

      const { fetchFormData } = useProjectData({
        initialStep: ref(0),
        templateId: 456,
      });

      // 调用fetchFormData
      await fetchFormData();

      // 验证调用了获取项目数据API，但没有启用状态检查
      expect(mockGetEditProjectViewData).toHaveBeenCalledWith(456, undefined, {
        checkProjectStatus: false,
      });

      // 验证新建模式下会调用updateRouteWithProjectId来更新路由
      expect(mockRouter.replace).toHaveBeenCalledWith({
        path: '/video-project',
        query: {
          templateId: '456',
          step: '0',
          projectId: '123',
        },
      });

      // 验证没有进行状态检查相关的重定向（go方法）
      expect(mockRouter.go).not.toHaveBeenCalled();
    });
  });
});
