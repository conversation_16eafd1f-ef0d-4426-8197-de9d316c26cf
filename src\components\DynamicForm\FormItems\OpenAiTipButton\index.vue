<!-- AI推荐按钮组件 -->
<template>
  <div v-show="!shouldHideButton" class="ai-suggestion">
    <div v-show="shouldShowSuggestions" class="ai-suggestion__container">
      <div class="ai-suggestion__content">
        <transition-group
          name="suggestion-item"
          tag="span"
          class="ai-suggestion__tags-container"
          :key="animationKey"
          appear
          :duration="animationDuration"
          @before-enter="onBeforeEnter"
          @enter="onEnter"
          @leave="onLeave"
        >
          <!-- 推荐标签项组件 -->
          <SuggestionItem
            v-for="(suggestion, index) in filteredSuggestions"
            :key="getSuggestionValue(suggestion)"
            :suggestion="suggestion"
            :index="index"
            :is-clicked="isTagClicked(suggestion)"
            :animation-config="suggestionAnimationConfig"
            @select="handleSelect"
          />
          <!-- 刷新按钮 -->
          <fa-button
            v-show="canRefresh"
            key="refresh-button"
            type="link"
            ghost
            size="small"
            class="ai-suggestion__refresh no-transition"
            :disabled="!canRefresh"
            @click="handleRefresh"
          >
            <Icon
              v-show="!loading"
              type="huanyihuan"
              class="ai-suggestion__refresh-icon"
            />
            <span v-show="!loading">换一换</span>
            <span v-show="loading" class="ai-suggestion__refresh-loading">
              <fa-spin
                size="small"
                type="huanyihuan"
                class="ai-suggestion__refresh-loading__icon"
              />
              <span class="ai-suggestion__refresh-loading__text"
                >AI助手生成中...</span
              >
            </span>
          </fa-button>
        </transition-group>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import {
  defineComponent,
  ref,
  computed,
  watch,
  onMounted,
  toRef,
  type PropType,
} from 'vue';
import { debounce } from 'lodash-es';
import { getOpenAiTip } from '@/api/EditProjectView/openAiTip';
import router from '@/router';
import type { FormItemInternal } from '@/views/EditProjectView/types/index';
import SuggestionItem from './SuggestionItem.vue';
import { useSuggestionFilter } from './composables/useSuggestionFilter.ts';
import {
  OPENAI_TIP_CONFIG,
  getAnimationConfig,
  getAnimationDuration,
} from './config/index';
import {
  extractFieldNamesFromTemplate,
  isTemplateFormat,
  isFieldValueValid,
  deduplicateSuggestions,
  getSuggestionText,
  truncateText,
  getSelectedSuggestions,
  detectDependencyFieldsChange,
  updateDependencyCache,
  calculateTagDelay,
} from './utils/index';

/**
 * AI推荐按钮组件
 * @description 基于上下文数据获取AI推荐内容，支持依赖字段检查、防抖请求、长度限制验证等功能，适用于文本输入和标签选择等场景
 * @example
 * ```vue
 * <OpenAiTipButton
 *   field-name="description"
 *   :context-data="formData"
 *   :dependency-fields="['title', 'category']"
 *   :fields-config="fieldConfig"
 *   :disabled="false"
 *   @select="handleSelectSuggestion"
 * />
 * ```

 * @since 1.0.0
 */
export default defineComponent({
  name: 'OpenAiTipButton',
  components: {
    SuggestionItem,
  },
  props: {
    /**
     * 当前字段名称
     * @description 需要获取AI推荐的字段名，用于API请求和事件传递
     * @required
     */
    fieldName: {
      type: String,
      required: true,
    },
    /**
     * 上下文数据对象
     * @description 包含表单所有字段数据的对象，用于提供AI推荐的上下文信息
     * @default {}
     */
    contextData: {
      type: Object as PropType<Record<string, unknown>>,
      default: () => ({}),
    },
    /**
     * 依赖字段数组
     * @description 需要先填写的依赖字段名列表，只有这些字段都有值时才能进行AI推荐
     * @default []
     */
    dependencyFields: {
      type: Array as PropType<string[]>,
      default: () => [],
    },
    /**
     * 字段配置对象
     * @description 当前字段的完整配置信息，包含类型、属性、最大长度等设置
     * @default {}
     */
    fieldsConfig: {
      type: Object as PropType<FormItemInternal>,
      default: () => ({}),
    },
    /**
     * 是否禁用组件
     * @description 控制AI推荐组件是否处于禁用状态，禁用时不显示推荐内容和刷新按钮
     * @default false
     */
    disabled: {
      type: Boolean,
      default: false,
    },
    /**
     * 表单项是否获得焦点
     * @description 控制AI推荐是否应该显示，只有在表单项获得焦点时才显示推荐
     * @default false
     */
    isFocused: {
      type: Boolean,
      default: false,
    },
  },
  emits: {
    /**
     * 选择推荐内容事件
     * @description 用户点击推荐标签时触发，传递选中的推荐值给父组件
     * @param value 选中的推荐值字符串
     */
    select: (_value: string) => true,
  },
  setup(props, { emit }) {
    // ============= 响应式数据 =============

    /**
     * AI推荐加载状态
     * @description 控制推荐内容获取过程中的加载状态显示
     */
    const loading = ref<boolean>(false);

    /**
     * 当前显示的推荐内容列表
     * @description 存储当前显示的推荐内容数组，每次显示3个
     */
    const suggestions = ref<Array<{ value: string } | string>>([]);

    /**
     * 是否曾经成功加载过数据
     * @description 用于控制AI推荐区域的初始显示状态，避免初始加载时显示loading状态
     */
    const hasEverLoaded = ref<boolean>(false);

    /**
     * 推荐词缓存池
     * @description 缓存从API获取的30个推荐词，去重后存储
     */
    const cachedSuggestions = ref<Array<{ value: string } | string>>([]);

    /**
     * 当前显示索引
     * @description 用于控制当前显示的推荐词在缓存池中的起始位置
     */
    const currentDisplayIndex = ref<number>(0);

    // ============= 常量配置 =============

    /**
     * 组件配置常量（从配置文件导入）
     */
    const CONFIG = OPENAI_TIP_CONFIG;

    /**
     * 已选择的推荐词集合
     * @description 记录用户已经选择过的推荐词，用于过滤显示列表
     */
    const selectedSuggestions = ref<Set<string>>(new Set());

    /**
     * 当前组件是否曾经获得过焦点
     * @description 一旦获得焦点并加载数据后，就保持显示状态
     */
    const hasEverBeenFocused = ref<boolean>(false);

    /**
     * 动画触发计数器
     * @description 用于强制重新渲染transition-group，只有在推荐词从隐藏变为显示时才触发动画
     */
    const animationTriggerCount = ref<number>(0);

    /**
     * 上一次 shouldShowSuggestions 的状态
     * @description 用于检测推荐词显示状态的变化，只有从隐藏变为显示时才播放动画
     */
    const previousShouldShowSuggestions = ref<boolean>(false);

    // ============= 推荐内容过滤 Composable =============

    /**
     * 使用推荐内容过滤 composable
     * @description 提供推荐内容的过滤、验证和处理功能
     */
    const {
      filteredSuggestions: baseSuggestions,
      shouldHideButton,
      getSuggestionValue,
    } = useSuggestionFilter(
      suggestions,
      props.fieldName,
      toRef(props, 'contextData'),
      props.fieldsConfig,
    );

    /**
     * 过滤后的推荐列表
     * @description 使用基础过滤的推荐列表，已选择推荐词的过滤逻辑已移至 updateDisplayedSuggestions 函数中
     */
    const filteredSuggestions = computed(() => {
      return baseSuggestions.value;
    });

    /**
     * 依赖字段值缓存
     * @description 缓存依赖字段的值，用于检测依赖字段是否发生变化
     */
    const dependencyValuesCache = ref<Record<string, unknown>>({});

    /**
     * 上一次的依赖满足状态
     * @description 用于检测依赖条件是否从"不满足"变为"满足"
     */
    const previousDependenciesFulfilled = ref<boolean>(false);

    /**
     * 依赖字段是否已变化标记
     * @description 用于标记依赖字段是否发生变化，需要重新获取推荐词
     */
    const dependencyFieldsChanged = ref<boolean>(false);

    /**
     * 防抖的获取推荐函数
     * @description 防抖处理的推荐获取函数，避免频繁的API请求
     */
    let debouncedFetchSuggestions: ReturnType<typeof debounce>;

    /**
     * 当前点击的标签值
     * @description 记录当前被点击的标签值，用于点击动画效果显示
     */
    const clickedTag = ref<string>('');

    // ============= 动画配置 =============

    /**
     * 动画持续时间配置
     * @description 控制transition-group的动画时长
     */
    const animationDuration = computed(() => getAnimationDuration());

    /**
     * 推荐项动画配置
     * @description 传递给SuggestionItem组件的动画参数
     */
    const suggestionAnimationConfig = computed(() => getAnimationConfig());

    // ============= 动画钩子函数 =============

    /**
     * 元素进入前的钩子
     * @description 设置元素进入动画前的初始状态
     * @param el 动画元素
     */
    const onBeforeEnter = (el: Element): void => {
      const htmlEl = el as HTMLElement;
      htmlEl.style.opacity = '0';
      htmlEl.style.transform = 'translateY(-10px) scale(0.8)';
    };

    /**
     * 元素进入时的钩子
     * @description 执行元素进入动画
     * @param el 动画元素
     * @param done 动画完成回调
     */
    const onEnter = (el: Element, done: () => void): void => {
      const htmlEl = el as HTMLElement;
      const delay = htmlEl.style.getPropertyValue('--enter-delay') || '0ms';

      setTimeout(() => {
        htmlEl.style.transition = 'all 0.4s cubic-bezier(0.25, 1, 0.5, 1)';
        htmlEl.style.opacity = '1';
        htmlEl.style.transform = 'translateY(0) scale(1)';

        setTimeout(done, 400);
      }, parseInt(delay));
    };

    /**
     * 元素离开时的钩子
     * @description 执行元素离开动画
     * @param el 动画元素
     * @param done 动画完成回调
     */
    const onLeave = (el: Element, done: () => void): void => {
      const htmlEl = el as HTMLElement;
      htmlEl.style.transition = 'all 0.35s cubic-bezier(0.25, 1, 0.5, 1)';
      htmlEl.style.opacity = '0';
      htmlEl.style.transform = 'translateY(10px) scale(0.8)';

      setTimeout(done, 350);
    };

    // ============= 计算属性 =============

    // ============= 工具函数（从工具模块导入） =============

    // ============= 计算属性 =============

    /**
     * 是否存在依赖字段
     * @description 检查提示模板中是否包含除当前字段外的依赖字段
     * @returns 是否存在依赖字段
     */
    const hasDependencies = computed<boolean>(() => {
      if (!props.dependencyFields?.length) return false;

      if (isTemplateFormat(props.dependencyFields)) {
        const template = props.dependencyFields[0] as string;
        const fieldNames = extractFieldNamesFromTemplate(template);
        const otherDependencies = fieldNames.filter(
          field => field !== props.fieldName,
        );
        return otherDependencies.length > 0;
      }

      // 兼容旧版本：检查字段名数组是否非空
      return props.dependencyFields.length > 0;
    });

    /**
     * 依赖字段名列表
     * @description 从提示模板中提取依赖字段名
     * @returns 依赖字段名数组
     */
    const dependencyFieldNames = computed<string[]>(() => {
      if (!props.dependencyFields?.length) return [];

      if (isTemplateFormat(props.dependencyFields)) {
        const template = props.dependencyFields[0] as string;
        return extractFieldNamesFromTemplate(template);
      }

      // 兼容旧版本：直接返回字段名数组
      return props.dependencyFields as string[];
    });

    /**
     * 其他依赖字段名列表
     * @description 过滤掉当前字段名，只返回其他依赖字段
     * @returns 其他依赖字段名数组
     */
    const otherDependencyFields = computed<string[]>(() => {
      return dependencyFieldNames.value.filter(
        field => field !== props.fieldName,
      );
    });

    /**
     * 依赖字段是否已满足
     * @description 检查提示模板中除当前字段外的所有依赖字段是否都已填写
     * @returns 依赖条件是否满足
     */
    const dependenciesFulfilled = computed<boolean>(() => {
      if (!hasDependencies.value) return true;
      if (otherDependencyFields.value.length === 0) return true;

      // 检查所有其他依赖字段是否都有值
      return otherDependencyFields.value.every(field => {
        return isFieldValueValid(props.contextData[field]);
      });
    });

    /**
     * 是否可以刷新推荐
     * @description 检查是否可以执行刷新操作（非禁用状态、依赖条件满足，且有可用的推荐内容可显示或需要重新获取）
     * @returns 是否可以刷新推荐
     */
    const canRefresh = computed<boolean>(() => {
      if (!(!props.disabled && dependenciesFulfilled.value)) {
        return false;
      }

      // 如果依赖字段已变化，需要显示刷新按钮以获取新推荐词
      if (dependencyFieldsChanged.value) {
        return true;
      }

      // 如果当前有显示的推荐词，可以刷新
      if (suggestions.value.length > 0) {
        return true;
      }

      // 如果缓存池为空，不能刷新
      if (cachedSuggestions.value.length === 0) {
        return false;
      }

      // 检查缓存池中是否还有未选择的推荐词
      const selectedSet = selectedSuggestions.value;
      const availableSuggestions = cachedSuggestions.value.filter(
        suggestion => {
          const value = getSuggestionValue(suggestion);
          return !selectedSet.has(value);
        },
      );

      return availableSuggestions.length > 0;
    });

    /**
     * 是否应该显示推荐内容
     * @description 检查是否应该显示推荐区域（非禁用状态、曾经获得过焦点并加载过数据，并且有推荐内容或正在加载）
     * @returns 是否显示推荐内容
     */
    const shouldShowSuggestions = computed<boolean>(() => {
      return (
        !props.disabled &&
        hasEverBeenFocused.value &&
        hasEverLoaded.value &&
        (loading.value || suggestions.value.length > 0)
      );
    });

    /**
     * 动画键值
     * @description 通过改变key值强制重新渲染transition-group，确保每次聚焦时都能播放动画
     * @returns 动画键值
     */
    const animationKey = computed<string>(() => {
      // 当应该显示推荐时，使用触发计数器作为key
      if (shouldShowSuggestions.value) {
        return `animation-${animationTriggerCount.value}`;
      }
      // 不显示时使用固定key
      return 'hidden';
    });

    // ============= 方法定义 =============

    /**
     * 更新已选择推荐词集合（包装工具函数）
     * @description 根据当前表单字段的值，更新已选择的推荐词集合，用于过滤显示
     * @private
     */
    const updateSelectedSuggestions = (): void => {
      const currentValue = props.contextData[props.fieldName];
      const newSelectedSet = getSelectedSuggestions(
        currentValue,
        props.fieldsConfig.type as string,
        cachedSuggestions.value,
        getSuggestionValue,
      );
      selectedSuggestions.value = newSelectedSet;
    };

    /**
     * 更新依赖字段值缓存（包装工具函数）
     * @description 将当前依赖字段的值保存到缓存中，用于后续变化检测
     * @private
     */
    const updateDependencyCacheWrapper = (): void => {
      if (!hasDependencies.value) return;
      updateDependencyCache(
        otherDependencyFields.value,
        props.contextData,
        dependencyValuesCache.value,
      );
    };

    /**
     * 检测依赖字段是否发生变化（包装工具函数）
     * @description 比较当前依赖字段值与缓存值，检测是否有变化
     * @returns 是否有依赖字段发生变化
     * @private
     */
    const detectDependencyFieldsChangeWrapper = (): boolean => {
      return detectDependencyFieldsChange(
        otherDependencyFields.value,
        props.contextData,
        dependencyValuesCache.value,
      );
    };

    /**
     * 处理依赖字段变化的核心逻辑
     * @description 根据是否已获得焦点分别处理依赖字段变化
     * @private
     */
    const handleDependencyFieldsChange = (): void => {
      if (!hasEverBeenFocused.value) {
        // 情况1：还没有显示（未获取焦点）- 清空缓存并获取新数据
        cachedSuggestions.value = [];
        currentDisplayIndex.value = 0;
        debouncedFetchSuggestions();
      } else {
        // 情况2：已经显示（已获取过焦点）- 标记依赖字段已变化，保持当前显示
        dependencyFieldsChanged.value = true;
        currentDisplayIndex.value = 0;
      }
      // 更新依赖字段缓存
      updateDependencyCacheWrapper();
    };

    /**
     * 更新当前显示的推荐词
     * @description 从缓存池中获取当前应该显示的推荐词，考虑已选择推荐词的过滤，确保最终显示指定数量，不足时从头补充
     * @private
     */
    const updateDisplayedSuggestions = (): void => {
      if (cachedSuggestions.value.length === 0) {
        suggestions.value = [];
        return;
      }

      // 获取已选择的推荐词集合
      const selectedSet = selectedSuggestions.value;

      // 从缓存中过滤掉已选择的推荐词
      const availableSuggestions = cachedSuggestions.value.filter(
        suggestion => {
          const value = getSuggestionValue(suggestion);
          return !selectedSet.has(value);
        },
      );

      // 如果没有可用的推荐词，清空显示
      if (availableSuggestions.length === 0) {
        suggestions.value = [];
        return;
      }

      // 如果当前索引超出了可用推荐词的范围，重置索引
      if (currentDisplayIndex.value >= availableSuggestions.length) {
        currentDisplayIndex.value = 0;
      }

      const startIndex = currentDisplayIndex.value;
      const targetCount = CONFIG.DISPLAY_COUNT;

      // 计算从当前索引开始能获取多少个推荐词
      const remainingFromIndex = availableSuggestions.length - startIndex;

      if (remainingFromIndex >= targetCount) {
        // 如果剩余推荐词足够，直接切片
        suggestions.value = availableSuggestions.slice(
          startIndex,
          startIndex + targetCount,
        );
      } else {
        // 如果剩余推荐词不足，从头补充
        const firstPart = availableSuggestions.slice(startIndex); // 从当前索引到末尾
        const needMore = targetCount - firstPart.length; // 还需要多少个
        const secondPart = availableSuggestions.slice(0, needMore); // 从头开始补充

        // 更新索引到补充部分的末尾，这样下次"换一换"就会从补充部分后面开始显示
        currentDisplayIndex.value = -needMore;

        suggestions.value = [...firstPart, ...secondPart];
      }
    };

    /**
     * 获取AI推荐内容
     * @description 调用API获取AI推荐内容，实现批量获取、去重和分批显示
     * @private
     */
    const fetchSuggestions = async (): Promise<void> => {
      // 避免重复请求
      if (loading.value) {
        return;
      }

      if (!dependenciesFulfilled.value) {
        suggestions.value = [];
        return;
      }

      loading.value = true;

      // 构建promptMap，包含依赖字段的值
      const promptMap: Record<string, unknown> = {};

      // 如果是提示模板字符串（新格式）
      if (
        props.dependencyFields.length === 1 &&
        typeof props.dependencyFields[0] === 'string'
      ) {
        const promptTemplate = props.dependencyFields[0];

        // 如果包含字段占位符，提取字段名并添加到promptMap
        if (promptTemplate.includes('#')) {
          const fieldMatches = promptTemplate.match(/#([^#]+)#/g) || [];
          const dependencyFieldNames = fieldMatches.map(match =>
            match.replace(/#/g, ''),
          );

          // 添加其他依赖字段到promptMap
          dependencyFieldNames.forEach(fieldName => {
            if (fieldName !== props.fieldName) {
              const value = props.contextData[fieldName];
              const processedValue = Array.isArray(value)
                ? value.join(',')
                : value;
              promptMap[fieldName] = processedValue || '';
            }
          });
        }
      }
      const templateId = router?.currentRoute?.query?.templateId || 0;

      // 调用API获取推荐
      const [err, res] = await getOpenAiTip({
        // 模板ID，后续可从配置中获取
        id: Number(templateId),
        // 当前字段名
        variable: props.fieldName,
        // 依赖字段键值对
        promptMap,
      });

      loading.value = false;

      // 处理响应
      if (err) {
        suggestions.value = [];
        return;
      }

      // 检查是否有新版API的返回格式
      const responseData = res.data;
      // responseData.answerList = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10].map(i =>
      //   i.toString(),
      // );
      if (responseData?.answerList && Array.isArray(responseData.answerList)) {
        // 对获取的推荐词进行去重处理
        const deduplicatedSuggestions = deduplicateSuggestions(
          responseData.answerList,
          getSuggestionValue,
        );

        // 更新缓存池
        cachedSuggestions.value = deduplicatedSuggestions;

        // 重置显示索引
        currentDisplayIndex.value = 0;

        // 更新当前显示的推荐词
        updateDisplayedSuggestions();

        // 成功获取到数据后，标记为已加载过，允许显示推荐区域
        hasEverLoaded.value = true;

        // 重置依赖字段变化标记
        dependencyFieldsChanged.value = false;
      } else {
        suggestions.value = [];
      }
    };

    /**
     * 检查是否需要重新获取推荐词
     * @description 检查依赖字段是否发生变化，需要重新获取推荐词
     * @private
     */
    const shouldRefetchSuggestions = (): boolean => {
      // 如果缓存池为空，需要获取
      if (cachedSuggestions.value.length === 0) return true;

      // 如果依赖字段已变化，需要重新获取
      if (dependencyFieldsChanged.value) return true;

      // 如果显示索引为0且当前没有显示推荐词，说明依赖字段可能发生了变化
      if (currentDisplayIndex.value === 0 && suggestions.value.length === 0) {
        return true;
      }

      return false;
    };

    /**
     * 处理刷新推荐操作
     * @description 处理用户点击"换一换"按钮，循环显示推荐词或重新获取，考虑已选择推荐词的过滤
     * @public
     */
    const handleRefresh = (): void => {
      if (!canRefresh.value || loading.value) return;

      // 检查是否需要重新获取推荐词
      if (shouldRefetchSuggestions()) {
        fetchSuggestions();
        return;
      }

      // 获取已选择的推荐词集合
      const selectedSet = selectedSuggestions.value;

      // 从缓存中过滤掉已选择的推荐词
      const availableSuggestions = cachedSuggestions.value.filter(
        suggestion => {
          const value = getSuggestionValue(suggestion);
          return !selectedSet.has(value);
        },
      );

      // 如果缓存池中还有可用的推荐词，则循环显示
      if (availableSuggestions.length > 0) {
        const nextIndex = currentDisplayIndex.value + CONFIG.DISPLAY_COUNT;

        // 检查下一批推荐词是否在可用范围内
        if (nextIndex < availableSuggestions.length) {
          // 还有未显示的推荐词，更新显示索引
          currentDisplayIndex.value = nextIndex;
          updateDisplayedSuggestions();
        } else {
          // 当前批次已全部显示完毕，循环显示已有推荐词，节省token
          currentDisplayIndex.value = 0;
          updateDisplayedSuggestions();
        }
      } else {
        // 没有可用的推荐词，重新获取推荐词
        fetchSuggestions();
      }
    };

    /**
     * 处理推荐内容选择
     * @description 处理用户点击推荐标签，进行长度验证后发送选择事件
     * @param suggestion 选择的推荐项（字符串或包含value属性的对象）
     * @public
     */
    const handleSelect = (suggestion: { value: string } | string): void => {
      if (!suggestion) return;

      const value = getSuggestionValue(suggestion);

      // 设置点击状态，用于动画效果
      clickedTag.value = value;
      setTimeout(() => {
        clickedTag.value = '';
      }, 300);

      emit('select', value);
    };

    /**
     * 获取标签延迟时间（包装工具函数）
     * @description 计算标签动画的延迟时间，用于错开动画效果
     * @param index 标签在列表中的索引
     * @returns 延迟时间（毫秒）
     * @public
     */
    const getTagDelay = (index: number): number => {
      return calculateTagDelay(index, CONFIG.ANIMATION_DELAY_INCREMENT);
    };

    /**
     * 检查标签是否被点击
     * @description 检查指定标签是否处于点击状态，用于显示点击动画
     * @param suggestion 推荐项
     * @returns 是否被点击
     * @public
     */
    const isTagClicked = (suggestion: { value: string } | string): boolean => {
      return getSuggestionValue(suggestion) === clickedTag.value;
    };

    // ============= 生命周期钩子 =============

    /**
     * 组件挂载后的初始化
     * @description 设置防抖函数、监听依赖字段变化，可以预先获取推荐内容但不显示
     */
    onMounted(() => {
      // 初始化防抖函数
      debouncedFetchSuggestions = debounce(
        fetchSuggestions,
        CONFIG.DEBOUNCE_DELAY,
      );

      // 初始化依赖字段缓存
      updateDependencyCacheWrapper();

      // 初始化已选择推荐词集合
      updateSelectedSuggestions();

      // 初始化上一次的依赖满足状态
      previousDependenciesFulfilled.value = dependenciesFulfilled.value;

      // 可以预先获取推荐内容，但只有在获得焦点时才显示
      if (dependenciesFulfilled.value) {
        debouncedFetchSuggestions();
      }
    });

    // ============= 监听器 =============

    /**
     * 监听依赖字段变化
     * @description 当依赖字段的值发生变化时，检查是否需要自动获取AI推荐内容
     * 优化：只监听实际的依赖字段，而不是整个 contextData
     */
    watch(
      () => {
        if (!hasDependencies.value) return {};

        // 只监听实际的其他依赖字段
        const relevantData: Record<string, unknown> = {};
        otherDependencyFields.value.forEach(fieldName => {
          relevantData[fieldName] = props.contextData[fieldName];
        });

        return relevantData;
      },
      () => {
        if (!hasDependencies.value) return;

        // 使用之前保存的依赖满足状态
        const wasDependenciesFulfilled = previousDependenciesFulfilled.value;

        // 检查依赖字段是否发生变化
        const hasChanged = detectDependencyFieldsChangeWrapper();

        // 检查依赖条件是否从"不满足"变为"满足"
        const currentDependenciesFulfilled = dependenciesFulfilled.value;
        const shouldAutoFetch =
          !wasDependenciesFulfilled &&
          currentDependenciesFulfilled &&
          !hasEverLoaded.value;

        // 如果依赖字段发生变化，调用统一的处理函数
        if (hasChanged) {
          handleDependencyFieldsChange();
        }

        // 如果依赖条件从"不满足"变为"满足"，且从未加载过数据，则自动获取推荐词
        if (shouldAutoFetch) {
          debouncedFetchSuggestions();
        }

        // 更新上一次的依赖满足状态，用于下次比较
        previousDependenciesFulfilled.value = currentDependenciesFulfilled;
      },
      { deep: true },
    );

    /**
     * 监听焦点状态变化
     * @description 当表单项获得焦点时，标记为曾经获得过焦点，并在需要时获取AI推荐内容
     */
    watch(
      () => props.isFocused,
      newFocused => {
        if (newFocused) {
          // 标记当前组件曾经获得过焦点
          hasEverBeenFocused.value = true;

          // 如果依赖条件满足且尚未加载过数据，则获取AI推荐内容
          if (dependenciesFulfilled.value && !hasEverLoaded.value) {
            debouncedFetchSuggestions();
          }
        }
      },
    );

    /**
     * 监听推荐词显示状态变化
     * @description 只有当推荐词从隐藏状态变为显示状态时才触发动画
     */
    watch(shouldShowSuggestions, (newValue, oldValue) => {
      // 只有当从 false 变为 true 时才增加动画计数器
      if (newValue && !oldValue) {
        animationTriggerCount.value++;
      }
      // 更新上一次的状态
      previousShouldShowSuggestions.value = newValue;
    });

    /**
     * 监听当前字段值变化
     * @description 当当前字段的值发生变化时，更新已选择的推荐词集合
     */
    watch(
      () => props.contextData[props.fieldName],
      () => {
        updateSelectedSuggestions();
      },
      { deep: true },
    );

    // ============= 返回模板所需数据 =============

    return {
      // 响应式数据
      loading,
      suggestions,
      clickedTag,
      hasEverLoaded,
      hasEverBeenFocused,
      cachedSuggestions,
      currentDisplayIndex,
      selectedSuggestions,
      previousDependenciesFulfilled,
      dependencyFieldsChanged,
      animationTriggerCount,
      previousShouldShowSuggestions,

      // 计算属性
      hasDependencies,
      dependenciesFulfilled,
      canRefresh,
      shouldShowSuggestions,
      animationKey,
      shouldHideButton,
      filteredSuggestions,

      // 动画配置
      animationDuration,
      suggestionAnimationConfig,

      // 动画钩子函数
      onBeforeEnter,
      onEnter,
      onLeave,

      // 工具函数
      extractFieldNamesFromTemplate,
      isTemplateFormat,
      detectDependencyFieldsChange,
      handleDependencyFieldsChange,

      // 方法
      isFieldValueValid,
      deduplicateSuggestions,
      updateDisplayedSuggestions,
      shouldRefetchSuggestions,
      updateDependencyCache,
      updateSelectedSuggestions,
      fetchSuggestions,
      handleRefresh,
      handleSelect,
      getSuggestionText,
      getSuggestionValue,
      getTagDelay,
      truncateText,
      isTagClicked,
    };
  },
});
</script>

<style lang="scss" scoped>
/* AI推荐按钮组件样式 */

// 主块：AI推荐组件
.ai-suggestion {
  @apply w-full;
}

// 元素：AI推荐内容容器
.ai-suggestion__container {
  @apply mt-2 transition-all duration-300 ease-in-out;
}

// 元素：推荐内容列表
.ai-suggestion__content {
  @apply flex flex-wrap gap-2 items-center min-h-[30px];
}

// 元素：标签容器
.ai-suggestion__tags-container {
  @apply inline-flex flex-wrap gap-[8px] items-center;
  @apply w-auto; // 使其自适应内容宽度，不占据整行
  @apply relative; // 为了支持绝对定位的leaving元素
}

// 元素：刷新按钮
.ai-suggestion__refresh.fa-btn {
  @apply flex items-center text-[#999];
  @apply align-middle; // 垂直居中对齐
  @apply h-[24px] p-0; // 设置高度与标签一致
  @apply transition-all duration-300 ease-out;

  /* 刷新按钮图标通用样式 */
  .ai-suggestion__refresh-icon,
  .ai-suggestion__refresh-loading__icon {
    /* 尺寸相关 */
    @apply mr-[2px] w-20px h-20px;
    /* 动画相关 */
    @apply transition-all duration-300 ease-out;
  }

  /* 换一换图标 */
  .ai-suggestion__refresh-icon {
    /* 动画相关 */
    @apply transition-all;
  }

  /* 加载状态容器 */
  .ai-suggestion__refresh-loading {
    /* 布局相关 */
    @apply flex items-center;
    /* 动画相关 */
    @apply transition-all duration-300 ease-in-out;
    /* 容器淡入动画 */
    animation: fadeInContainer 0.3s ease-in-out;

    /* 加载状态图标 */
    .ai-suggestion__refresh-loading__icon {
      /* 布局相关 */
      @apply flex justify-center items-center;
    }

    /* 加载状态文本 */
    .ai-suggestion__refresh-loading__text {
      /* 文字相关 */
      @apply font-400 text-14px text-left text-[#3261fd];
      /* 动画相关 */
      @apply transition-all duration-300 ease-in-out;
      /* 动画效果：淡入 + 轻微位移 */
      animation: fadeInSlide 0.3s ease-in-out;
    }
  }

  &:hover,
  &:focus {
    @apply text-[#666];
  }
}

// 标签淡入淡出效果（重构后的动画）
.suggestion-item-enter-active {
  transition: all 0.4s cubic-bezier(0.25, 1, 0.5, 1);
  transition-delay: var(--enter-delay, 0ms);
}

.suggestion-item-leave-active {
  transition: all 0.4s cubic-bezier(0.25, 1, 0.5, 1);
  position: absolute;
}

.suggestion-item-enter-from {
  opacity: 0;
  transform: translateY(-5px) scale(0.8);
}

.suggestion-item-leave-to {
  opacity: 0;
  transform: translateY(5px) scale(0.8);
}

.suggestion-item-move {
  transition: transform 0.4s cubic-bezier(0.16, 1, 0.3, 1);
}

// 禁用过渡效果的元素
.no-transition {
  transition: none !important;
  transform: none !important;
  opacity: 1 !important;
}

// 图标旋转动画
@keyframes rotate-icon {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

// 容器淡入动画
@keyframes fadeInContainer {
  0% {
    opacity: 0;
    transform: scale(0.95);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

// 文本淡入滑动动画
@keyframes fadeInSlide {
  0% {
    opacity: 0;
    transform: translateX(-8px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}
</style>
