<template>
  <div
    class="file-manager-demo-icon"
    @mousedown.prevent
    @click="$emit('click')"
  >
    <slot />
  </div>
</template>

<script>
export default {
  name: 'FileManagerIcon',
};
</script>

<style lang="scss" scoped>
.file-manager-demo-icon {
  display: inline-block;
  margin-left: 14px;
  font-size: 0;
  color: #919191;
  transition: 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);

  > * {
    font-size: 16px;
  }

  &:hover {
    color: #3a84fe;
  }

  &:active {
    color: #096dd9;
  }
}
</style>
