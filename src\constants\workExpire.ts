/**
 * 作品过期时间相关常量
 * 统一的作品过期时间配置，用于整个应用程序
 */

/**
 * 作品默认有效期（天数）
 * 从作品创建时间开始计算的有效期
 */
export const WORK_EXPIRE_DAYS = 7;

/**
 * 即将过期警告阈值（小时）
 * 当剩余时间少于等于此值时，显示"即将失效"
 */
export const EXPIRE_WARNING_HOURS = 24;

/**
 * 将天数转换为毫秒
 * @param days 天数
 * @returns 毫秒数
 */
export const daysToMilliseconds = (days: number): number => {
  return days * 24 * 60 * 60 * 1000;
};

/**
 * 将小时转换为毫秒
 * @param hours 小时数
 * @returns 毫秒数
 */
export const hoursToMilliseconds = (hours: number): number => {
  return hours * 60 * 60 * 1000;
};

/**
 * 计算作品过期时间
 * @param createTime 创建时间（时间戳或日期字符串）
 * @returns 过期时间的Date对象
 */
export const calculateExpireTime = (createTime: string | number): Date => {
  const createTimeMs = typeof createTime === 'string' 
    ? Number(createTime) 
    : createTime;
  return new Date(createTimeMs + daysToMilliseconds(WORK_EXPIRE_DAYS));
};

/**
 * 计算剩余有效时间（小时）
 * @param createTime 创建时间（时间戳或日期字符串）
 * @returns 剩余有效时间（小时），如果已过期则返回0
 */
export const calculateRemainingHours = (createTime: string | number): number => {
  const now = new Date().getTime();
  const expireTime = calculateExpireTime(createTime).getTime();
  const diffHours = Math.floor((expireTime - now) / (1000 * 60 * 60));
  
  return Math.max(diffHours, 0);
};

/**
 * 计算剩余有效天数
 * @param createTime 创建时间（时间戳或日期字符串）
 * @returns 剩余有效天数，如果已过期则返回0
 */
export const calculateRemainingDays = (createTime: string | number): number => {
  const now = new Date().getTime();
  const expireTime = calculateExpireTime(createTime).getTime();
  const diffDays = Math.ceil((expireTime - now) / (1000 * 60 * 60 * 24));
  
  return Math.max(diffDays, 0);
};
