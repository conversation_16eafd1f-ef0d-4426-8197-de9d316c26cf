# 通用版本校验工具使用指南

## 概述

通用版本校验工具是一套统一的版本控制解决方案，旨在简化版本校验功能的接入和维护。该工具支持三种使用模式，能够满足不同场景下的版本校验需求。

## 核心优势

- **易用性优先** - 新功能只需几行代码即可接入版本校验
- **统一维护** - 版本配置、校验逻辑、UI 展示等集中管理
- **性能优化** - 接口级降级避免运行时检查，无 UI 闪烁
- **类型安全** - 完整的 TypeScript 类型支持
- **可扩展性** - 支持自定义降级逻辑和提示消息

## 架构设计

```
版本校验配置中心 (config.ts)
    ↓
通用校验工具 (VersionController)
    ↓
┌─────────────────┬─────────────────┬─────────────────┐
│  UI交互式校验    │  消息提示式校验  │  接口级自动降级  │
│  VersionSwitch  │  showUpgradeMsg │  applyDowngrade │
└─────────────────┴─────────────────┴─────────────────┘
```

## 快速开始

### 1. 添加功能配置

在 `src/utils/versionControl/config.ts` 中添加新功能配置：

```typescript
export const VERSION_FEATURE_CONFIG = {
  // 现有配置...

  NEW_FEATURE: {
    featureKey: 'NEW_FEATURE',
    requiredVersion: VERSION.PRO,
    featureName: '新功能',
    supportDowngrade: true,
    downgradeHandler: data => {
      // 自定义降级逻辑
      data.enabled = false;
      return data;
    },
  },
};
```

### 2. 选择使用模式

根据功能特点选择合适的校验模式：

- **开关类功能** → UI 交互式校验
- **操作类功能** → 消息提示式校验
- **数据驱动功能** → 接口级自动降级

## 使用模式详解

### 模式 1：UI 交互式校验

**适用场景：** 开关类功能，如 AI 改嘴型、自动配音等

**特点：**

- 自动显示版本图标和提示
- 权限不足时自动阻止操作
- 版本变化时自动降级

**使用示例：**

```vue
<template>
  <div class="feature-controls">
    <VersionSwitch
      feature-key="AI_MOUTH_SHAPE"
      v-model="mouthShapeEnabled"
      label="AI改嘴型"
      @permission-denied="handlePermissionDenied"
    />
  </div>
</template>

<script setup>
import { ref } from 'vue';
import { VersionSwitch } from '@/components/version';

const mouthShapeEnabled = ref(false);

const handlePermissionDenied = (featureKey: string) => {
  console.log(`权限不足，无法开启功能: ${featureKey}`);
  // 可以添加埋点统计等逻辑
};
</script>
```

### 模式 2：消息提示式校验

**适用场景：** 操作类功能，如文件上传、成员管理等

**特点：**

- 操作时进行权限检查
- 显示版本限制提示消息
- 包含升级引导链接

**使用示例：**

```typescript
import VersionController from '@/utils/versionControl';

// 方式1：使用checkAndExecute（推荐）
function handleFileUpload() {
  const executed = VersionController.checkAndExecute('FILE_UPLOAD_SIZE', () => {
    // 执行文件上传逻辑
    performUpload();
  });

  if (!executed) {
    console.log('文件上传被阻止：版本权限不足');
  }
}

// 方式2：手动检查
function handleMemberSetting() {
  if (!VersionController.hasPermission('MEMBER_PERMISSION')) {
    VersionController.showUpgradeMessage('MEMBER_PERMISSION');
    return;
  }

  // 执行成员设置逻辑
  performMemberSetting();
}
```

### 模式 3：接口级自动降级

**适用场景：** 数据驱动功能，需要根据版本自动调整数据结构

**特点：**

- 数据获取时一次性处理
- 无 UI 闪烁，用户体验佳
- 性能最优，避免运行时检查

**使用示例：**

```typescript
import VersionController from '@/utils/versionControl';

// 单个数据处理
function processFormData(rawData: FormData): FormData {
  return VersionController.applyDowngrade(rawData, 'AI_MOUTH_SHAPE');
}

// 批量数据处理
function processFormDataList(rawDataList: FormData[]): FormData[] {
  return VersionController.applyDowngradeToList(rawDataList, 'AI_MOUTH_SHAPE');
}

// 在数据处理层集成
export function mergeProjectData(templateData, projectData) {
  const transformedData = transformApiData(templateData, projectData);

  // 应用版本校验降级处理
  transformedData.resForm = VersionController.applyDowngradeToList(
    transformedData.resForm,
    'AI_MOUTH_SHAPE',
  );

  return transformedData;
}
```

## API 参考

### VersionController 类

#### 静态方法

##### hasPermission(featureKey: string): boolean

检查功能权限

```typescript
const hasPermission = VersionController.hasPermission('AI_MOUTH_SHAPE');
```

##### getFeatureConfig(featureKey: string): VersionFeatureConfig | null

获取功能配置

```typescript
const config = VersionController.getFeatureConfig('AI_MOUTH_SHAPE');
```

##### showUpgradeMessage(featureKey: string): void

显示版本限制提示

```typescript
VersionController.showUpgradeMessage('FILE_UPLOAD_SIZE');
```

##### applyDowngrade<T>(data: T, featureKey: string): T

应用降级处理

```typescript
const processedData = VersionController.applyDowngrade(
  rawData,
  'AI_MOUTH_SHAPE',
);
```

##### checkAndExecute(featureKey: string, operation: () => void): boolean

检查并执行操作

```typescript
const executed = VersionController.checkAndExecute('FEATURE_KEY', () => {
  // 执行操作
});
```

### VersionSwitch 组件

#### Props

| 属性       | 类型    | 必需 | 默认值 | 说明             |
| ---------- | ------- | ---- | ------ | ---------------- |
| featureKey | string  | 是   | -      | 功能标识         |
| modelValue | boolean | 否   | false  | 开关状态         |
| label      | string  | 否   | ''     | 开关显示文本     |
| showIcon   | boolean | 否   | true   | 是否显示版本图标 |

#### Events

| 事件              | 参数               | 说明         |
| ----------------- | ------------------ | ------------ |
| update:modelValue | value: boolean     | 开关状态变化 |
| permission-denied | featureKey: string | 权限拒绝事件 |

## 配置说明

### VersionFeatureConfig 接口

```typescript
interface VersionFeatureConfig {
  featureKey: string; // 功能标识
  requiredVersion: VERSION; // 要求的最低版本
  featureName: string; // 功能名称
  customMessage?: string; // 自定义提示消息
  supportDowngrade?: boolean; // 是否支持自动降级
  downgradeHandler?: <T extends Record<string, unknown>>(data: T) => T; // 降级处理函数
}
```

### 配置示例

```typescript
AI_MOUTH_SHAPE: {
  featureKey: 'AI_MOUTH_SHAPE',
  requiredVersion: VERSION.BASIC,
  featureName: 'AI改嘴型',
  supportDowngrade: true,
  downgradeHandler: (data) => {
    data.hasModifyMouth = false;
    data.openModifyMouth = false;
    return data;
  },
},
```

## 最佳实践

### 1. 功能配置命名规范

- **使用大写字母和下划线**：`AI_MOUTH_SHAPE`、`FILE_UPLOAD_SIZE`
- **功能名称简洁明了**：`AI改嘴型`、`大文件上传`
- **提供有意义的降级处理函数**：确保降级后功能仍可正常使用

### 2. 选择合适的使用模式

| 功能类型     | 推荐模式       | 原因                               |
| ------------ | -------------- | ---------------------------------- |
| 开关类功能   | UI 交互式校验  | 用户可直观看到版本要求，交互体验好 |
| 操作类功能   | 消息提示式校验 | 在用户操作时及时提示，避免无效操作 |
| 数据驱动功能 | 接口级自动降级 | 性能最优，用户无感知，体验最佳     |

### 3. 错误处理

```typescript
// 安全的版本校验调用
function safeVersionCheck(featureKey: string): boolean {
  try {
    return VersionController.hasPermission(featureKey);
  } catch (error) {
    console.error(`版本校验失败 (${featureKey}):`, error);
    return true; // 出错时默认允许访问
  }
}

// 安全的降级处理
function safeDowngrade<T>(data: T, featureKey: string): T {
  try {
    return VersionController.applyDowngrade(data, featureKey);
  } catch (error) {
    console.error(`降级处理失败 (${featureKey}):`, error);
    return data; // 出错时返回原数据
  }
}
```

### 4. 性能优化建议

- **优先使用接口级降级**：避免运行时检查，提升性能
- **合理使用缓存**：对于频繁调用的权限检查，考虑添加缓存
- **避免过度检查**：不要在每次渲染时都进行权限检查

## 迁移指南

### 从现有实现迁移

#### 步骤 1：识别现有版本校验代码

查找项目中使用以下模式的代码：

- `import { checkVersionPermission } from '@/utils/versionPermission'`
- `import { VersionDowngradeCallback } from '@/utils/types/versionPermission'`
- `checkVersionPermission(VERSION.XXX)`
- `showVersionMsg(message, version)`
- 手动的版本监听和降级处理

#### 步骤 2：更新导入语句

```typescript
// 旧导入
import { checkVersionPermission } from '@/utils/versionPermission';
import { VersionDowngradeCallback } from '@/utils/types/versionPermission';

// 新导入
import { checkVersionPermission } from '@/utils/versionControl';
import { VersionDowngradeCallback } from '@/utils/versionControl/types';
// 或者使用统一导入
import {
  VersionController,
  checkVersionPermission,
} from '@/utils/versionControl';
```

#### 步骤 3：添加功能配置

在 `VERSION_FEATURE_CONFIG` 中添加对应的功能配置。

#### 步骤 4：替换为新的 API 调用

```typescript
// 旧代码
const hasPermission = checkVersionPermission(VERSION.BASIC);
if (!hasPermission) {
  showVersionMsg('需要升级版本', store.state.user.version);
  return;
}

// 新代码
if (!VersionController.hasPermission('FEATURE_KEY')) {
  VersionController.showUpgradeMessage('FEATURE_KEY');
  return;
}
```

#### 步骤 4：测试功能正常性

确保迁移后功能表现与之前一致。

### AI 改嘴型功能迁移示例

```vue
<!-- 旧代码 -->
<fa-switch
  v-model="openModifyMouthLocal"
  :disabled="!hasMouthShapePermission"
  @change="handleMouthShapeChange"
/>
<VersionIcon :required-version="VERSION.BASIC" type="medium" />

<!-- 新代码 -->
<VersionSwitch
  feature-key="AI_MOUTH_SHAPE"
  v-model="openModifyMouthLocal"
  label="AI改嘴型"
  @update:modelValue="handleMouthShapeChange"
/>
```

## 常见问题

### Q: 如何添加新的版本校验功能？

A: 在 `VERSION_FEATURE_CONFIG` 中添加配置，然后选择合适的使用模式即可。

### Q: 如何自定义降级逻辑？

A: 在功能配置中提供 `downgradeHandler` 函数，实现自定义的降级处理逻辑。

### Q: 如何处理复杂的版本校验场景？

A: 可以组合使用多种模式，或者在 `downgradeHandler` 中实现复杂的业务逻辑。

### Q: 版本校验失败时如何处理？

A: 建议添加 try-catch 错误处理，出错时采用安全的默认行为。

### Q: 如何进行性能优化？

A: 优先使用接口级降级，避免运行时检查；对频繁调用的权限检查考虑添加缓存。

### Q: VersionSwitch 组件状态不更新怎么办？

A: 确保正确使用 `v-model` 或 `:model-value` + `@update:modelValue` 的组合。注意事件名称必须是 `update:modelValue`（驼峰命名），而不是 `update:model-value`。

### Q: 如何调试版本校验问题？

A: 可以在开发环境中临时添加 console.log 来跟踪事件流：

```typescript
// 在 handleChange 中添加调试日志
console.log('权限检查:', hasPermission.value);
console.log('发射事件:', value);
```

## 技术支持

如有问题或建议，请联系开发团队或在项目中提交 Issue。

## 更新日志

- **v1.0.0** - 初始版本，支持三种校验模式
- **v1.1.0** - 添加批量降级处理功能
- **v1.2.0** - 优化错误处理和类型定义
