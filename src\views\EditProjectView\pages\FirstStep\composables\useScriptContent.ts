/**
 * @description 脚本内容相关的composable
 */
import { ref, type Ref } from 'vue';
import type {
  ScriptData,
  ProjectType,
} from '@/views/EditProjectView/types/index';
import { PROJECT_TYPE_IMAGE } from '@/views/EditProjectView/constants';

/**
 * 脚本内容处理Hook的返回值类型
 * @interface UseScriptContentReturn
 */
interface UseScriptContentReturn {
  /** 示例脚本内容 */
  exampleScriptContent: Ref<string>;
  /** 是否有示例脚本 */
  hasExampleScript: Ref<boolean>;
  /** 处理示例脚本内容 */
  processScriptContent: (script?: ScriptData, type?: ProjectType) => void;
}

/**
 * 脚本内容处理Hook
 * @returns 脚本内容相关状态和方法
 */
export function useScriptContent(): UseScriptContentReturn {
  // 示例脚本内容（初始为空，将通过API获取）
  const exampleScriptContent = ref<string>('');
  // 是否有示例脚本
  const hasExampleScript = ref<boolean>(false);

  /**
   * 处理示例脚本内容
   * @param script 脚本数据
   * @param type 项目类型
   */
  const processScriptContent = (
    script?: ScriptData,
    type?: ProjectType,
  ): void => {
    // 图文类型项目不需要示例脚本
    if (type === PROJECT_TYPE_IMAGE) {
      hasExampleScript.value = false;
      exampleScriptContent.value = '';
      return;
    }

    if (script?.content) {
      exampleScriptContent.value = script.content;
      hasExampleScript.value = true;
    } else {
      hasExampleScript.value = false;
      exampleScriptContent.value = '';
    }
  };

  return {
    exampleScriptContent,
    hasExampleScript,
    processScriptContent,
  };
}
