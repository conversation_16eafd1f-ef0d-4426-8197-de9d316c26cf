import { Module } from 'vuex';
import { RootState } from '@/store';
import { getIndustryAndScene } from '@/api/TemplateView';
import { getDomainAndToken, SystemInfo } from '@/api/System/System';
import Cookies from 'js-cookie';

export interface SystemState {
  /** 全局前置信息是否加载完成 */
  isPreload: boolean;
  /** 系统信息是否加载完成 */
  isSystemDone: boolean;
  /** 是否为分销 */
  isOem: boolean;
  /** 公司home域名 */
  homeDomain: string;
  /** 公司首页URL */
  homeUrl: string;
  /** 公司登录页 */
  portal: string;
  /** 速创资源URL */
  scPortalResRoot: string;
  /** 用户资源URL */
  scUsrResRoot: string;
  /** 用户临时资源URL */
  scUsrTmpResRoot: string;
  /** 行业 Map */
  industryMap: Record<number, string>;
  /** 场景 Map */
  sceneMap: Record<number, string>;
  /** CDN鉴权域名，用于素材库 */
  scUsrResAuthRoot: string;
  /** O系统资源域名，用于获取O系统的静态资源，如图片、视频等 */
  scUsrResOssRoot: string;
}

export const state: SystemState = {
  isPreload: false,
  isSystemDone: false,
  isOem: false,
  homeDomain: '',
  homeUrl: '',
  portal: '',
  scPortalResRoot: '',
  scUsrResRoot: '',
  scUsrTmpResRoot: '',
  scUsrResAuthRoot: '',
  scUsrResOssRoot: '',
  industryMap: {},
  sceneMap: {},
};

const mutations = {
  setIsOem(state: SystemState, isOem: boolean) {
    state.isOem = isOem;
  },
  setSystemData(state: SystemState, systemData: SystemInfo) {
    state.homeDomain = systemData.homeDomain;
    state.homeUrl = systemData.homeRoot;
    state.portal = systemData.portal;
    state.scPortalResRoot = systemData.scPortalResRoot;
    state.scUsrResRoot = systemData.scUsrResRoot;
    state.scUsrTmpResRoot = systemData.scUsrTmpResRoot;
    state.scUsrResAuthRoot = systemData.scUsrResAuthRoot;
    state.scUsrResOssRoot = systemData.scUsrResOssRoot;
    state.isSystemDone = true;
  },
  setIndustryMap(state: SystemState, industryMap: Record<number, string>) {
    state.industryMap = industryMap;
  },
  setSceneMap(state: SystemState, sceneMap: Record<number, string>) {
    state.sceneMap = sceneMap;
  },
  setPreloadDone(state: SystemState, isDone: boolean) {
    state.isPreload = isDone;
  },
  setPortal(state: SystemState, portal: string) {
    state.portal = portal;
  },
};

const actions = {
  /** 获取行业M和场景 */
  async getIndustryAndScene({
    commit,
  }: {
    commit: (mutation: string, payload: Record<number, string> | null) => void;
  }) {
    const [err, res] = await getIndustryAndScene();
    if (err) {
      console.log(err);
      return;
    }
    const { industry, scene } = res.data;
    const industryMap = industry.reduce(
      (acc: Record<number, string>, item: { id: number; name: string }) => {
        acc[item.id] = item.name;
        return acc;
      },
      {},
    );
    const sceneMap = scene.reduce(
      (acc: Record<number, string>, item: { id: number; name: string }) => {
        acc[item.id] = item.name;
        return acc;
      },
      {},
    );
    commit('setIndustryMap', industryMap);
    commit('setSceneMap', sceneMap);
  },
  /** 获取资源域名、直分销、token等 */
  async getSystem({
    commit,
  }: {
    commit: (mutation: string, payload: SystemInfo | string) => void;
  }) {
    const [err, res] = await getDomainAndToken();
    if (err) {
      console.error(err);
      return;
    }
    commit('setSystemData', res.data);
    Cookies.set('_TOKEN', res.data.token, { expires: 1 });
  },
};

const system: Module<SystemState, RootState> = {
  state,
  mutations,
  actions,
};
export default system;
