import { ref, type Ref } from 'vue';
import type { BgMusicSetting } from '@/views/EditProjectView/types/index';
import type { Music } from '@/types';
import { getMusicInfo } from '@/api/VideoEditor/music';
import type { EditProjectViewData } from '@/views/EditProjectView/types/index';

/**
 * 背景音乐管理Hook的返回值类型
 * @interface UseBgMusicReturn
 */
interface UseBgMusicReturn {
  /** 背景音乐设置 */
  bgMusicSettings: Ref<BgMusicSetting>;
  /** 背景音乐信息 */
  bgMusicInfo: Ref<Music[]>;
  /** 是否显示背景音乐选择器 */
  isShowBgMusicSelector: Ref<boolean>;
  /** 显示背景音乐选择器 */
  showBgMusicSelector: () => void;
  /** 处理音乐选择变更 */
  handleChangeMusicInfo: (
    selectedMusics: Music[],
    useAiRecommend: boolean,
    maxSelectCount?: number,
  ) => Promise<void>;
  /** 初始化背景音乐信息 */
  initBgMusicInfo: () => Promise<void>;
  /** 处理背景音乐开关变更 */
  handleBgMusicSwitchChange: (value: boolean) => void;
  /** 设置数据变更回调函数 */
  setOnDataChange: (callback: (hasChanges: boolean) => void) => void;
}

/**
 * @description 背景音乐管理的composable
 * @param initialData 初始背景音乐数据
 * @returns 背景音乐相关状态和方法
 */
export function useBgMusic(
  initialData: EditProjectViewData | null = null,
): UseBgMusicReturn {
  // 背景音乐设置
  const bgMusicSettings = ref<BgMusicSetting>({
    open: initialData?.bgMusic?.open || false,
    useAiRecommend: initialData?.bgMusic?.useAiRecommend || false,
    resIds: initialData?.bgMusic?.resIds || [],
    name: initialData?.bgMusic?.name,
  });

  // 背景音乐信息
  const bgMusicInfo = ref<Music[]>([]);

  // 是否显示背景音乐选择器
  const isShowBgMusicSelector = ref(false);

  // 数据变更回调函数
  let onDataChangeCallback: ((hasChanges: boolean) => void) | null = null;

  // 显示背景音乐选择器
  const showBgMusicSelector = () => {
    isShowBgMusicSelector.value = true;
  };

  // 处理音乐选择变更
  const handleChangeMusicInfo = async (
    selectedMusics: Music[],
    useAiRecommend: boolean,
    maxSelectCount?: number,
  ) => {
    bgMusicSettings.value.useAiRecommend = useAiRecommend;

    if (selectedMusics.length > 0 && !useAiRecommend) {
      // 保存所有选中的音乐
      bgMusicInfo.value = selectedMusics;
      bgMusicSettings.value.resIds = selectedMusics.map(music => music.resId);
      // 设置第一首音乐的名称
      if (selectedMusics[0]) {
        bgMusicSettings.value.name = selectedMusics[0].name;
      }
    } else if (useAiRecommend) {
      // 如果使用智能推荐，则清空具体音乐选择
      bgMusicInfo.value = [];
      bgMusicSettings.value.resIds = [];
    } else {
      // 如果未开启智能推荐且没有选择音乐
      // 检查是否为多选模式下的取消操作
      const isMultiSelectMode = maxSelectCount && maxSelectCount > 1;

      if (isMultiSelectMode) {
        // 多选模式下的取消操作：只关闭音乐开关，保持原有配置不变
        bgMusicSettings.value.open = false;
        // 不清空 resIds 和 bgMusicInfo，保持用户原来的背景音乐设置
      } else {
        // 单选模式下：清空音乐选择
        bgMusicInfo.value = [];
        bgMusicSettings.value.resIds = [];
      }
    }

    // 触发数据变更回调
    if (onDataChangeCallback) {
      onDataChangeCallback(true);
    }
  };

  // 初始化背景音乐信息
  const initBgMusicInfo = async () => {
    if (initialData?.bgMusic) {
      const bgMusic = initialData.bgMusic;
      // 确保resIds始终有初始值
      const resIds = bgMusic.resIds || [];

      bgMusicSettings.value = {
        open: bgMusic.open,
        useAiRecommend: bgMusic.useAiRecommend || false,
        resIds, // 使用已初始化的resIds
        name: bgMusic.name, // 保持原有的name属性
      };

      // 获取音乐详情
      if (resIds.length > 0 && !bgMusic.useAiRecommend) {
        // 清空之前的数据
        bgMusicInfo.value = [];

        // 获取每个音乐的详情
        for (const resId of resIds) {
          const [err, res] = await getMusicInfo(resId);
          if (!err && res?.data) {
            bgMusicInfo.value.push(res.data);
            bgMusicSettings.value.name = res.data.name;
          }
        }
      }
    }
  };

  // 处理背景音乐开关变更
  const handleBgMusicSwitchChange = (value: boolean) => {
    // 触发数据变更回调
    if (onDataChangeCallback) {
      onDataChangeCallback(true);
    }

    if (value) {
      showBgMusicSelector();
    }
  };

  // 设置数据变更回调函数
  const setOnDataChange = (callback: (hasChanges: boolean) => void) => {
    onDataChangeCallback = callback;
  };

  return {
    bgMusicSettings,
    bgMusicInfo,
    isShowBgMusicSelector,
    showBgMusicSelector,
    handleChangeMusicInfo,
    initBgMusicInfo,
    handleBgMusicSwitchChange,
    setOnDataChange,
  };
}
