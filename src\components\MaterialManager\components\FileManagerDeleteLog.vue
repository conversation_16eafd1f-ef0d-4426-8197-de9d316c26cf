<template>
  <fa-modal
    v-model="visible"
    :width="1000"
    :footer="null"
    class="file-manager-demo-delete-log"
    title="回收站日志"
  >
    <FaTable
      class="file-manager-demo-delete-log-table"
      skin-type="icon-base"
      :pagination="false"
      :columns="columns"
      :data-source="dataSource"
    ></FaTable>
    <fa-pagination class="pagination" :default-current="1" :total="100" />
  </fa-modal>
</template>

<script>
import {
  Modal as FaModal,
  Table as FaTable,
  Pagination as FaPagination,
} from '@fk/faicomponent';

const dataSource = [];
for (let i = 1; i <= 10; i++) {
  dataSource.push({
    key: i,
    log: '这个是日志这个是日志这个是日志',
    people: '我是操作人',
    time: 'xxxx-xx-xx xx:xx',
  });
}

export default {
  name: 'FileManagerDeleteLog',
  components: {
    FaModal,
    FaTable,
    FaPagination,
  },

  props: {
    value: {
      type: Boolean,
      default: false,
    },
  },

  data() {
    return {
      columns: [
        {
          title: '操作日志',
          width: 518,
          dataIndex: 'log',
        },
        {
          title: '操作人',
          width: 200,
          dataIndex: 'people',
        },
        {
          title: '操作时间',
          width: 200,
          dataIndex: 'time',
        },
      ],

      dataSource,
    };
  },

  computed: {
    visible: {
      get() {
        return this.value;
      },
      set(newVal) {
        this.$emit('input', newVal);
      },
    },
  },
};
</script>

<style lang="scss" scoped>
.file-manager-demo-delete-log {
  ::v-deep {
    .fa-modal-body {
      padding: 24px 40px 0;
    }

    .fa-table {
      border: 1px solid #d9d9d9;
      border-radius: 4px;
    }

    .fa-pagination-item,
    .fa-pagination-prev,
    .fa-pagination-next,
    .fa-pagination-jump-prev,
    .fa-pagination-jump-next {
      min-width: 24px;
      height: 24px;
      line-height: 24px;
    }

    .fa-table-tbody > tr:nth-last-of-type(1) > td {
      border-bottom: 0;
    }
  }

  .pagination {
    padding: 16px 0 24px 0;
    text-align: center;
  }
}
</style>
