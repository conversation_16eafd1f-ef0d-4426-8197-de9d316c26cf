# 数据更新逻辑架构文档

## 概述

本文档详细梳理了 EditProjectView 模块中业务层和组件层的数据更新逻辑，包括核心架构、业务场景、组件使用情况和数据流向。

## 🏗️ 核心架构层

### 1. 统一数据更新入口 - `updateWorkItems`

**位置**：`src/views/EditProjectView/composables/useInfiniteWorkList.ts`

```typescript
const updateWorkItems = async (
  workIds: number[],
  options: {
    clearDetailCache?: boolean;    // 是否清除详情缓存
    refreshFullList?: boolean;     // 是否完整刷新列表
    context?: string;              // 操作上下文（用于日志）
    refreshCurrentDetail?: boolean; // 是否重新获取当前作品详情
  } = {},
): Promise<void>
```

**功能说明**：

- 所有作品数据更新的统一管理入口
- `refreshFullList: true` → 调用 `refreshWorkList()` 完整刷新
- `clearDetailCache: true` → 清除指定作品详情缓存
- `refreshCurrentDetail: true` → 重新获取当前作品详情

### 2. 数据操作类型枚举

```typescript
enum WorkListOperation {
  REPLACE = 'replace', // 替换整个列表（初始加载、分页切换）
  MERGE = 'merge', // 合并更新（轮询、保存、编辑后更新）
  REMOVE = 'remove', // 删除操作
}
```

### 3. 核心数据更新方法

```typescript
const updateWorkList = (
  data: T[] | number,
  operation: WorkListOperation,
): void => {
  switch (operation) {
    case WorkListOperation.REPLACE:
      handleReplaceOperation(data as T[]);
      break;
    case WorkListOperation.MERGE:
      handleMergeOperation(data as T[]);
      break;
    case WorkListOperation.REMOVE:
      handleRemoveOperation(data as number);
      break;
  }
};
```

## 📋 业务场景专用方法

### 1. 保存场景 - `handleSaveUpdate`

```typescript
const handleSaveUpdate = async (
  workIds: number[],
  isBatchSave = false,
): Promise<void> => {
  await updateWorkItems(workIds, {
    refreshFullList: isBatchSave,
    clearDetailCache: isBatchSave,
    context: isBatchSave ? '批量保存作品' : '保存作品',
  });
};
```

#### 1.1 单个保存 (`isBatchSave = false`)

**执行流程**：

1. **获取更新数据**：调用 API 获取指定作品的最新状态
2. **合并更新**：使用 `WorkListOperation.MERGE` 将新数据合并到现有列表
3. **局部缓存清理**：仅清除指定作品的详情缓存
4. **保持浏览状态**：不改变当前页码、选中状态、当前作品

**适用场景**：

- 用户点击单个作品的"保存"按钮
- 作品列表项的快速保存操作
- 需要保持用户当前浏览位置的场景

**调用示例**：

```typescript
// 保存单个作品，workId = 123
await handleSaveUpdate([123], false);
```

#### 1.2 批量保存 (`isBatchSave = true`)

**执行流程**：

1. **完整刷新列表**：调用 `refreshWorkList()` 方法
2. **清空所有作品详情缓存**：调用 `clearAllWorkDetailCache()`
3. **重置选中状态**：`selectedWorkIds.value = []`
4. **重置当前作品**：`currentWorkId.value = -1` 或 `null`（使用无效值确保重新选择）
5. **回到第一页**：调用 `onPageChange(1)`
6. **重新获取列表数据**：从服务器获取最新的作品列表
7. **自动选择第一个作品**：如果列表不为空，自动选择第一个作品

**适用场景**：

- 用户使用"保存到我的作品"功能保存多个作品
- 全选状态下的批量保存操作
- 需要确保数据完全同步的场景

**调用示例**：

```typescript
// 批量保存多个作品
await handleSaveUpdate([123, 456, 789], true);

// 全选状态下的批量保存（传入空数组表示全选）
await handleSaveUpdate([], true);
```

**调用位置**：

- `src/views/EditProjectView/composables/useSecondStep.ts` - 保存到我的作品功能
  - ⚠️ **特别注意**：这里是通过**组件引用**调用 `currentCompRef.handleSaveUpdate()`，不是通过 inject
  - 调用的是第二步组件（SecondStepVideo/SecondStepImage）暴露的方法
- `src/components/WorkListBar/WorkListBarItem.vue` - 单个作品保存
  - 通过 `eventBus.emit(EVENT_NAMES.WORK_SAVE_REQUEST)` 发送事件请求

### 2. 编辑场景 - `handleEditUpdate`

```typescript
const handleEditUpdate = async (workId: number): Promise<void> => {
  await updateWorkItems([workId], {
    clearDetailCache: true,
    switchToFirst: true,
    context: '编辑作品',
  });
};
```

**执行流程**：

1. **获取更新数据**：调用 API 获取编辑后作品的最新状态
2. **合并更新列表**：使用 `WorkListOperation.MERGE` 更新作品列表中的对应项
3. **清除详情缓存**：删除该作品的详情缓存 `clearWorkDetailCacheEntry(workId)`
4. **重新获取详情**：如果当前选中的是该作品且 `refreshCurrentDetail: true`，调用 `switchToWork(workId)` 重新获取详情
5. **状态同步**：确保列表状态和详情状态的一致性

**业务特点**：

- **状态流转**：编辑后作品状态从"已完成"变为"重新生成中"
- **数据一致性**：编辑操作会改变作品的核心数据，必须清除缓存
- **用户体验**：保持用户在当前作品的浏览状态，但显示最新数据

**适用场景**：

- 视频编辑器中修改脚本、背景音乐、配音等后保存
- 图文编辑器中修改文案、字体、布局等后保存
- 任何会导致作品重新生成的编辑操作

**调用位置**：

- `src/views/EditProjectView/pages/SecondStepVideo/index.vue` - 视频编辑器保存回调
- `src/views/EditProjectView/pages/SecondStepImage/index.vue` - 图文编辑器保存回调

### 3. 生成完成场景 - `handleGenerateUpdate`

```typescript
const handleGenerateUpdate = async (workId: number): Promise<void> => {
  await updateWorkItems([workId], {
    refreshFullList: true,
    // clearDetailCache 在 refreshFullList: true 时是多余的，
    // 因为 refreshWorkList 已经清空了所有缓存
    context: '生成完成作品',
  });
};
```

**执行流程**：

1. **完整刷新列表**：调用 `refreshWorkList()` 方法（内部已包含清空所有缓存的逻辑）
2. **重置选中状态**：`selectedWorkIds.value = []`
3. **重置当前作品**：`currentWorkId.value = -1`（使用无效值确保重新选择）
4. **回到第一页**：调用 `onPageChange(1)`
5. **重新获取列表数据**：从服务器获取最新的作品列表
6. **自动选择第一个作品**：如果列表不为空，自动选择第一个作品

**业务特点**：

- **数据完整性**：生成完成可能影响作品排序、状态、数量等
- **用户体验**：回到第一页让用户看到最新生成的作品（通常排在前面）
- **缓存策略**：清空所有缓存确保显示最新数据

**适用场景**：

- 视频编辑器中点击"生成"按钮完成后
- 图文编辑器中点击"生成"按钮完成后
- 任何会产生新作品的生成操作完成后

**调用位置**：

- `src/views/EditProjectView/pages/SecondStepVideo/index.vue` - 视频编辑器生成回调
- `src/views/EditProjectView/pages/SecondStepImage/index.vue` - 图文编辑器生成回调

### 4. 完整刷新 - `refreshWorkList`

```typescript
const refreshWorkList = async (): Promise<void> => {
  console.log('刷新作品列表，清空所有作品详情缓存并重新刷新作品列表');

  // 1. 清空所有作品的详情缓存
  clearAllWorkDetailCache();

  // 2. 强制清空选中状态（确保无论当前页码是什么都会重新选择第一个作品）
  selectedWorkIds.value = [];
  // 同时清空当前作品ID，确保 fetchWorkList 会重新选择第一个作品
  currentWorkId.value = -1;

  // 3. 回到第一页并重新获取作品列表数据
  const [err] = await onPageChange(1);

  if (err) {
    console.error('刷新作品列表失败:', err.message);
    return;
  }

  console.log('刷新作品列表完成，所有作品详情缓存已清空');
};
```

**执行流程详解**：

1. **清空详情缓存**：

   - 调用 `clearAllWorkDetailCache()` 清空 `workDetailCache` Map
   - 确保后续获取详情时都是最新数据

2. **重置选中状态**：

   - `selectedWorkIds.value = []` 清空已选择的作品 ID 列表
   - `currentWorkId.value = -1` 重置当前作品 ID（使用无效值确保重新选择）

3. **页面重置**：

   - 调用 `onPageChange(1)` 回到第一页
   - 触发 `fetchWorkList` 重新获取作品列表数据

4. **自动选择**：
   - 如果获取到的列表不为空，自动选择第一个作品
   - 调用 `fetchWorkDetail` 获取第一个作品的详情

**使用场景**：

- **批量保存后**：确保所有保存的作品状态正确显示
- **生成完成后**：显示最新生成的作品和更新的状态
- **新旧选择弹窗保存后**：同步弹窗中的保存操作结果
- **EventBus 触发**：通过 `EVENT_NAMES.REFRESH_WORK_LIST` 事件触发

**调用位置**：

- `src/views/EditProjectView/composables/useNewOldChoiceModal.ts` - 新旧选择弹窗保存后
- `src/views/EditProjectView/index.vue` - EventBus 事件处理
- 作为 `updateWorkItems` 的内部调用（当 `refreshFullList: true` 时）

## 🔄 轮询更新机制

### 1. 智能轮询 - `useWorkPolling`

**位置**：`src/views/EditProjectView/composables/useWorkPolling.ts`

**功能**：

- **触发条件**：检测到"生成中"状态的作品
- **更新方式**：使用 `WorkListOperation.MERGE` 合并更新
- **完成回调**：`handleWorkCompleted` 清除详情缓存并重新获取

### 2. 轮询适配器

```typescript
const updateWorkListAdapter = (data: T[], _type: string): void => {
  // 轮询更新统一使用合并操作
  updateWorkList(data, WorkListOperation.MERGE);
};
```

### 3. 作品完成回调

```typescript
const handleWorkCompleted = (workId: number): void => {
  // 清除该作品的详情缓存
  clearWorkDetailCacheEntry(workId);

  // 如果当前选中的就是这个作品，重新获取详情
  if (currentWorkId.value === workId) {
    switchToWork(workId);
  }
};
```

## 🎯 组件层使用情况

### ⚠️ 重要说明：三种不同的调用方式

在当前架构中，`handleSaveUpdate`、`handleEditUpdate`、`handleGenerateUpdate` 这三个方法有**三种不同的调用方式**，请务必注意区分：

#### 1. 通过 Composable 返回值直接调用

- **位置**：`SecondStepVideo/index.vue`、`SecondStepImage/index.vue`
- **方式**：从 `useInfiniteWorkList` 的返回值中直接获取
- **示例**：`const { handleEditUpdate } = useInfiniteWorkList(projectId)`

#### 2. 通过组件引用调用

- **位置**：`useSecondStep.ts`
- **方式**：通过组件实例的引用调用暴露的方法
- **示例**：`currentCompRef.handleSaveUpdate(workIds, isBatchSave)`
- **说明**：这里的 `currentCompRef` 指向第二步组件（SecondStepVideo/SecondStepImage）

#### 3. 通过 EventBus 事件总线调用

- **位置**：`WorkListBarItem.vue`
- **方式**：通过 `eventBus.emit()` 发送事件请求
- **示例**：`eventBus.emit(EVENT_NAMES.WORK_SAVE_REQUEST, { workIds, isBatchSave })`

**⚠️ 删除风险提醒**：

- 不能仅仅因为某个方法在 `workListActions` 中存在就认为只有 inject 方式在使用
- 这三个方法同时被多种方式调用，删除任何一个都会导致功能异常

### 1. EventBus 事件总线模式

**事件定义**：`src/utils/eventBus.ts`

```typescript
// 作品操作相关事件
EVENT_NAMES.WORK_SAVE_REQUEST; // 保存作品请求
EVENT_NAMES.WORK_REMOVE_REQUEST; // 删除作品请求
EVENT_NAMES.WORK_REMOVE_SUCCESS; // 删除成功通知
EVENT_NAMES.WORK_REMOVE_ERROR; // 删除失败通知
EVENT_NAMES.REFRESH_WORK_LIST; // 刷新作品列表
EVENT_NAMES.SHOW_NEW_OLD_CHOICE_MODAL; // 显示新旧选择弹窗
```

**事件发送方**：

- `src/components/WorkListBar/WorkListBarItem.vue` - 发送保存和删除事件
- 其他需要触发作品操作的组件

**事件监听方**：

- `src/views/EditProjectView/index.vue` - 监听并处理作品操作事件
- `src/views/EditProjectView/composables/useInfiniteWorkList.ts` - 处理具体的业务逻辑

### 2. 组件层调用点

#### a) 第二步页面组件

- **SecondStepVideo/index.vue**：视频编辑器关闭回调处理
  - ⚠️ **特别注意**：这里是**直接调用**从 `useInfiniteWorkList` 返回的方法
  - 不是通过 inject，而是通过 composable 的返回值
- **SecondStepImage/index.vue**：图文编辑器关闭回调处理
  - 同样是**直接调用**从 `useInfiniteWorkList` 返回的方法

```typescript
// 视频编辑器关闭回调示例（SecondStepVideo/index.vue）
// 这里的 handleEditUpdate 和 handleGenerateUpdate 来自 useInfiniteWorkList 的返回值
switch (closeStatus) {
  case VIDEO_EDITOR_CLOSE_STATUS.SAVED:
    await handleEditUpdate(currentWork.value.id); // 直接调用，不是 inject
    break;
  case VIDEO_EDITOR_CLOSE_STATUS.GENERATED:
    await handleGenerateUpdate(currentWork.value.id); // 直接调用，不是 inject
    break;
}
```

#### b) 内容面板组件

- **ContentPanel/index.vue**：通过 props 接收回调函数
- 支持编辑和生成完成的回调处理

#### c) 作品列表项组件

- **WorkListBarItem.vue**：通过 eventBus 发送作品操作事件

```typescript
// 导入 eventBus 和事件名称
import { eventBus, EVENT_NAMES } from '@/utils/eventBus';

// 保存作品 - 发送事件请求
const handleSave = async () => {
  // ... 保存逻辑 ...

  // 通过 eventBus 发送保存请求
  eventBus.emit(EVENT_NAMES.WORK_SAVE_REQUEST, {
    workIds: [props.workItem.id],
    isBatchSave: false,
  });
};

// 删除作品 - 发送事件请求（统一使用普通删除流程）
const handleRemove = async () => {
  // 所有作品删除（包括重新生成完成状态）统一使用普通删除流程
  eventBus.emit(EVENT_NAMES.WORK_REMOVE_REQUEST, {
    workId: props.workItem.id,
  });

  // 监听删除结果
  eventBus.once(EVENT_NAMES.WORK_REMOVE_SUCCESS, handleRemoveSuccess);
  eventBus.once(EVENT_NAMES.WORK_REMOVE_ERROR, handleRemoveError);
};
```

### 3. EventBus 事件处理机制

**事件监听设置**：

```typescript
// 在 EditProjectView/index.vue 中监听事件
onMounted(() => {
  // 监听保存请求
  eventBus.on(EVENT_NAMES.WORK_SAVE_REQUEST, handleWorkSaveRequest);

  // 监听删除请求
  eventBus.on(EVENT_NAMES.WORK_REMOVE_REQUEST, handleWorkRemoveRequest);

  // 监听刷新列表请求
  eventBus.on(EVENT_NAMES.REFRESH_WORK_LIST, handleRefreshWorkList);

  // 监听新旧选择弹窗请求
  eventBus.on(
    EVENT_NAMES.SHOW_NEW_OLD_CHOICE_MODAL,
    handleShowNewOldChoiceModal,
  );
});

// 组件卸载时清理事件监听
onUnmounted(() => {
  eventBus.off(EVENT_NAMES.WORK_SAVE_REQUEST, handleWorkSaveRequest);
  eventBus.off(EVENT_NAMES.WORK_REMOVE_REQUEST, handleWorkRemoveRequest);
  eventBus.off(EVENT_NAMES.REFRESH_WORK_LIST, handleRefreshWorkList);
  eventBus.off(
    EVENT_NAMES.SHOW_NEW_OLD_CHOICE_MODAL,
    handleShowNewOldChoiceModal,
  );
});
```

**事件处理函数**：

```typescript
// 处理保存请求
const handleWorkSaveRequest = async (data: {
  workIds: number[];
  isBatchSave: boolean;
}) => {
  await handleSaveUpdate(data.workIds, data.isBatchSave);
};

// 处理删除请求
const handleWorkRemoveRequest = async (data: { workId: number }) => {
  const success = await removeWork(data.workId);
  if (success) {
    eventBus.emit(EVENT_NAMES.WORK_REMOVE_SUCCESS);
  } else {
    eventBus.emit(EVENT_NAMES.WORK_REMOVE_ERROR);
  }
};
```

## 📊 数据流向图

```mermaid
graph TD
    A[用户操作] --> B{操作来源}

    B -->|WorkListBarItem| C[EventBus事件]
    B -->|SecondStep组件| D[直接调用]
    B -->|组件引用| E[Ref调用]
    B -->|轮询检测| F[updateWorkListAdapter]

    C -->|WORK_SAVE_REQUEST| G[handleWorkSaveRequest]
    C -->|WORK_REMOVE_REQUEST| H[handleWorkRemoveRequest]
    C -->|REFRESH_WORK_LIST| I[handleRefreshWorkList]

    D --> J[handleEditUpdate/handleGenerateUpdate]
    E --> K[handleSaveUpdate]

    G --> L[handleSaveUpdate]
    H --> M[removeWork]
    I --> N[refreshWorkList]
    J --> O[updateWorkItems]
    K --> O
    L --> O
    F --> P[updateWorkList MERGE]

    O --> Q{refreshFullList?}
    Q -->|true| N
    Q -->|false| R[局部更新]

    N --> S[清空所有缓存]
    N --> T[回到第一页]
    N --> U[重新获取列表]

    R --> V[清除指定缓存]
    R --> W[合并更新数据]
    R --> X[重新获取详情]
```

## 🎯 设计优势

### 1. 统一管理

- 所有更新操作都通过 `updateWorkItems` 统一处理
- 避免了分散的更新逻辑和重复代码

### 2. 参数化配置

- 通过 options 参数灵活控制更新行为
- 支持不同业务场景的个性化需求

### 3. 职责分离

- 业务场景方法专注于参数配置
- 核心逻辑集中在统一入口管理

### 4. 事件驱动架构

- 使用 EventBus 事件总线实现组件间解耦
- 避免了深层次的 provide/inject 依赖关系
- 提高了代码的可维护性和可测试性

### 5. 缓存策略

- 智能的缓存清理和更新机制
- 避免不必要的 API 调用和数据重复加载

### 6. 错误处理

- 统一的错误处理和日志记录
- 便于问题排查和系统监控

### 7. 向后兼容

- 保留专用方法，便于组件层调用
- 渐进式重构，不影响现有功能

### 8. 灵活的通信方式

- 支持多种调用方式：直接调用、组件引用、事件总线
- 根据不同场景选择最适合的通信模式

## 📝 最佳实践

### 1. 选择合适的更新方法

- **单个作品更新**：使用 `handleEditUpdate` 或 `handleSaveUpdate`
- **批量操作**：使用 `handleSaveUpdate(ids, true)` 或 `refreshWorkList`
- **生成完成**：使用 `handleGenerateUpdate`

### 2. 缓存管理

- 编辑操作后必须清除详情缓存
- 批量操作建议完整刷新
- 轮询更新使用合并策略

### 3. 错误处理

- 所有更新操作都应该处理错误情况
- 使用 await-to 范式处理异步错误
- 提供用户友好的错误提示

### 4. 性能优化

- 避免不必要的完整刷新
- 合理使用缓存机制
- 批量操作时使用防抖策略

## 📊 业务场景使用对比表

### 🎯 核心操作维度对比表

| 业务场景 | 列表数据更新方式 | 详情缓存处理 | 当前作品处理 | 页面状态变化 | 选中状态处理 |
| --- | --- | --- | --- | --- | --- |
| **编辑单个作品** | 🔄 合并更新指定作品 | 🗑️ 清除指定作品缓存 | 🔄 重新获取当前作品详情 | 📍 保持当前页面和位置 | ✅ 保持现有选中状态 |
| **保存单个作品** | 🔄 合并更新指定作品 | 💾 保留所有缓存 | ❌ 不更新详情 | 📍 保持当前页面和位置 | ✅ 保持现有选中状态 |
| **批量保存作品** | 🔄 完整重新获取列表 | 🗑️ 清空所有缓存 | 🎯 自动选择第一个作品 | 🔄 回到第一页 | 🔄 重置为空，重新选择 |
| **生成完成** | 🔄 完整重新获取列表 | 🗑️ 清空所有缓存 | 🎯 自动选择第一个作品 | 🔄 回到第一页 | 🔄 重置为空，重新选择 |
| **轮询更新** | 🔄 合并更新指定作品 | 💾 保留所有缓存 | ❌ 不更新详情 | 📍 保持当前页面和位置 | ✅ 保持现有选中状态 |
| **作品完成回调** | 🔄 合并更新指定作品 | 🗑️ 清除指定作品缓存 | 🔄 重新获取当前作品详情 | 📍 保持当前页面和位置 | ✅ 保持现有选中状态 |
| **删除作品** | ❌ 从列表中移除作品 | 🗑️ 清除指定作品缓存 | 🔄 如果是当前作品则切换 | 📍 保持当前页面 | 🔄 从选中列表中移除 |
| **新旧选择保存** | 🔄 完整重新获取列表 | 🗑️ 清空所有缓存 | 🎯 自动选择第一个作品 | 🔄 回到第一页 | 🔄 重置为空，重新选择 |

### 🔧 技术参数配置对比表

| 业务场景 | workIds | refreshFullList | clearDetailCache | refreshCurrentDetail | allowEmptyIds | 调用的核心方法 |
| --- | --- | --- | --- | --- | --- | --- |
| **编辑单个作品** | `[workId]` | `false` | `true` | `true` | `false` | `updateWorkItems` |
| **保存单个作品** | `[workId]` | `false` | `false` | `false` | `false` | `updateWorkItems` |
| **批量保存作品** | `[]` | `true` | `true` | `false` | `true` | `updateWorkItems` → `refreshWorkList` |
| **生成完成** | `[workId]` | `true` | `true` | `false` | `true` | `updateWorkItems` → `refreshWorkList` |
| **轮询更新** | `[...workIds]` | `false` | `false` | `false` | `false` | `updateWorkListAdapter` → `updateWorkList` |
| **作品完成回调** | `[workId]` | `false` | `true` | `true` | `false` | `handleWorkCompleted` |
| **删除作品** | `[workId]` | `false` | `true` | `false` | `false` | `removeWork` → `updateWorkList` |
| **新旧选择保存** | `[]` | `true` | `true` | `false` | `true` | `refreshWorkList` |

### 📋 数据操作详细分解表

#### 🔄 列表数据更新方式详解

| 更新方式 | 操作类型 | 数据来源 | 影响范围 | 性能特点 | 使用场景 |
| --- | --- | --- | --- | --- | --- |
| **合并更新指定作品** | `MERGE` | API 获取指定作品最新数据 | 仅影响指定作品项 | ⚡ 高性能，局部更新 | 编辑、保存、轮询单个作品 |
| **完整重新获取列表** | `REPLACE` | API 重新获取整个列表 | 影响整个列表数据 | 🐌 较低性能，全量更新 | 批量操作、生成完成 |
| **从列表中移除作品** | `REMOVE` | 本地删除操作 | 仅移除指定作品 | ⚡ 高性能，本地操作 | 删除作品 |

#### 🗑️ 详情缓存处理详解

| 处理方式 | 缓存操作 | 影响范围 | 内存影响 | 后续加载 | 使用原因 |
| --- | --- | --- | --- | --- | --- |
| **清除指定作品缓存** | `clearWorkDetailCacheEntry(workId)` | 仅指定作品 | 🟢 释放单个缓存 | 该作品需重新加载 | 作品内容已变化 |
| **清空所有缓存** | `clearAllWorkDetailCache()` | 所有作品 | 🟡 释放所有缓存 | 所有作品需重新加载 | 确保数据完全同步 |
| **保留所有缓存** | 无操作 | 无影响 | 🟢 保持现状 | 使用现有缓存 | 作品内容未变化 |

#### 🎯 当前作品处理详解

| 处理方式 | 操作内容 | 触发条件 | 用户体验 | API 调用 | 适用场景 |
| --- | --- | --- | --- | --- | --- |
| **重新获取当前作品详情** | `switchToWork(currentWorkId)` | 当前作品=操作作品 | 🔄 显示最新内容 | 调用详情 API | 作品内容已变化 |
| **自动选择第一个作品** | `switchToWork(firstWorkId)` | 列表重新加载后 | 🎯 聚焦最新作品 | 调用详情 API | 完整刷新场景 |
| **如果是当前作品则切换** | `switchToWork(nextWorkId)` | 当前作品被删除 | 🔄 自动切换到下一个 | 调用详情 API | 删除当前作品 |
| **不更新详情** | 无操作 | 作品内容未变化 | 📍 保持现有显示 | 无 API 调用 | 仅状态更新 |

#### 📍 页面状态变化详解

| 变化方式 | 页码变化 | 滚动位置 | 筛选条件 | 排序状态 | 用户感知 | 使用场景 |
| --- | --- | --- | --- | --- | --- | --- |
| **保持当前页面和位置** | 不变 | 保持 | 保持 | 保持 | 📍 无感知更新 | 单个作品操作 |
| **回到第一页** | 强制第 1 页 | 重置到顶部 | 保持 | 保持 | 🔄 明显的页面跳转 | 批量操作、生成完成 |

#### 🔄 选中状态处理详解

| 处理方式 | selectedWorkIds 变化 | currentWorkId 变化 | 全选状态 | 用户操作影响 | 适用场景 |
| --- | --- | --- | --- | --- | --- |
| **保持现有选中状态** | 不变 | 不变（除非被删除） | 保持 | 📍 用户选择不受影响 | 单个作品操作、轮询 |
| **重置为空，重新选择** | `[]` | `-1` → 自动选择第一个 | 重置为 false | 🔄 清空用户选择 | 完整刷新场景 |
| **从选中列表中移除** | 移除被删除的 ID | 如果是当前则切换 | 可能影响 | 🗑️ 移除无效选择 | 删除作品 |

### 详细场景说明

#### 1. **编辑单个作品**

```typescript
await updateWorkItems([workId], {
  clearDetailCache: true, // 清除该作品的详情缓存
  refreshCurrentDetail: true, // 重新获取当前作品详情
  context: '编辑作品',
});
```

**处理逻辑**：

- 📝 **作品列表**：使用 MERGE 操作更新指定作品状态（通常变为"重新生成中"）
- 🗑️ **详情缓存**：清除该作品的详情缓存，确保显示最新数据
- 🔄 **当前详情**：如果编辑的是当前选中作品，重新获取详情
- 📍 **页面状态**：保持用户当前浏览位置和选中状态

#### 2. **保存单个作品**

```typescript
await updateWorkItems([workId], {
  clearDetailCache: false, // 保留详情缓存
  refreshCurrentDetail: false, // 不重新获取详情
  context: '保存作品',
});
```

**处理逻辑**：

- 📝 **作品列表**：使用 MERGE 操作更新作品的保存状态和时间
- 💾 **详情缓存**：保留现有缓存，避免不必要的重新加载
- 📍 **页面状态**：完全保持用户当前浏览状态

#### 3. **批量保存作品**

```typescript
await updateWorkItems([], {
  refreshFullList: true, // 完整刷新列表
  allowEmptyIds: true, // 允许空ID列表
  context: '批量保存作品',
});
```

**处理逻辑**：

- 📝 **作品列表**：完整重新获取作品列表数据
- 🗑️ **详情缓存**：清空所有作品的详情缓存
- 🔄 **当前详情**：自动选择第一个作品并获取详情
- 📍 **页面状态**：回到第一页，重置选中状态

#### 4. **生成完成**

```typescript
await updateWorkItems([workId], {
  refreshFullList: true, // 完整刷新列表
  allowEmptyIds: true, // 允许空ID列表
  context: '生成完成作品',
});
```

**处理逻辑**：

- 📝 **作品列表**：完整重新获取，新生成的作品通常排在前面
- 🗑️ **详情缓存**：清空所有缓存，确保显示最新生成的作品
- 🔄 **当前详情**：自动选择第一个作品（通常是新生成的）
- 📍 **页面状态**：回到第一页，让用户看到最新作品

#### 5. **轮询更新**

```typescript
// 通过 updateWorkListAdapter 调用
updateWorkList(updatedWorks, WorkListOperation.MERGE);
```

**处理逻辑**：

- 📝 **作品列表**：使用 MERGE 操作更新作品状态（如从"生成中"变为"已完成"）
- 💾 **详情缓存**：保留所有缓存，避免影响用户体验
- 📍 **页面状态**：完全保持用户当前状态

#### 6. **作品完成回调**

```typescript
// 在 handleWorkCompleted 中处理
clearWorkDetailCacheEntry(workId);
if (currentWorkId.value === workId) {
  switchToWork(workId);
}
```

**处理逻辑**：

- 📝 **作品列表**：通过轮询已更新状态
- 🗑️ **详情缓存**：仅清除完成作品的详情缓存
- 🔄 **当前详情**：如果是当前作品，重新获取详情显示最新状态
- 📍 **页面状态**：保持用户当前浏览位置

#### 7. **删除作品**

```typescript
// 通过 removeWork 方法处理
updateWorkList(workId, WorkListOperation.REMOVE);
clearWorkDetailCacheEntry(workId);
```

**处理逻辑**：

- 📝 **作品列表**：使用 REMOVE 操作从列表中移除作品
- 🗑️ **详情缓存**：清除被删除作品的详情缓存
- 🔄 **当前详情**：如果删除的是当前作品，自动切换到下一个作品
- 📍 **页面状态**：保持当前页面，但可能需要调整选中状态

#### 8. **新旧选择保存**

```typescript
// 在 useNewOldChoiceModal 中调用
refreshWorkList();
```

**处理逻辑**：

- 📝 **作品列表**：完整重新获取，同步弹窗中的保存结果
- 🗑️ **详情缓存**：清空所有缓存，确保数据一致性
- 🔄 **当前详情**：自动选择第一个作品
- 📍 **页面状态**：回到第一页，重置所有状态

### 缓存策略说明

#### 🗑️ **清除详情缓存的场景**

- **编辑操作**：作品内容发生变化，必须清除缓存
- **批量操作**：可能影响多个作品，清空所有缓存确保一致性
- **生成完成**：新作品生成，清空缓存显示最新内容
- **删除操作**：移除无效缓存，避免内存泄漏

#### 💾 **保留详情缓存的场景**

- **单个保存**：仅更新保存状态，内容未变化
- **轮询更新**：后台状态同步，不影响用户体验
- **状态查看**：纯查看操作，无需清除缓存

### 页面状态管理

#### 📍 **保持当前页面的场景**

- **编辑、保存、删除单个作品**：用户期望停留在当前位置
- **轮询更新**：后台操作，不干扰用户浏览

#### 🔄 **回到第一页的场景**

- **批量操作**：用户明确执行了批量功能
- **生成完成**：让用户看到最新生成的作品
- **完整刷新**：确保数据完全同步

### 🎯 业务场景决策流程图

```mermaid
graph TD
    A[用户操作] --> B{操作类型判断}

    B -->|单个作品编辑| C[编辑单个作品]
    B -->|单个作品保存| D[保存单个作品]
    B -->|批量保存| E[批量保存作品]
    B -->|生成完成| F[生成完成]
    B -->|后台轮询| G[轮询更新]
    B -->|删除作品| H[删除作品]

    C --> C1[🔄 合并更新指定作品]
    C --> C2[🗑️ 清除指定作品缓存]
    C --> C3[🔄 重新获取当前作品详情]
    C --> C4[📍 保持当前页面]

    D --> D1[🔄 合并更新指定作品]
    D --> D2[💾 保留所有缓存]
    D --> D3[❌ 不更新详情]
    D --> D4[📍 保持当前页面]

    E --> E1[🔄 完整重新获取列表]
    E --> E2[🗑️ 清空所有缓存]
    E --> E3[🎯 自动选择第一个作品]
    E --> E4[🔄 回到第一页]

    F --> F1[🔄 完整重新获取列表]
    F --> F2[🗑️ 清空所有缓存]
    F --> F3[🎯 自动选择第一个作品]
    F --> F4[🔄 回到第一页]

    G --> G1[🔄 合并更新指定作品]
    G --> G2[💾 保留所有缓存]
    G --> G3[❌ 不更新详情]
    G --> G4[📍 保持当前页面]

    H --> H1[❌ 从列表中移除作品]
    H --> H2[🗑️ 清除指定作品缓存]
    H --> H3[🔄 如果是当前作品则切换]
    H --> H4[📍 保持当前页面]
```

### 🚀 场景使用建议与最佳实践

#### 1. **选择合适的更新模式**

| 操作特征 | 推荐模式 | 原因 | 示例场景 |
| --- | --- | --- | --- |
| **单个作品操作** | 🔄 局部更新 | 性能好，用户体验佳 | 编辑脚本、保存作品、单个删除 |
| **批量操作** | 🔄 完整刷新 | 确保数据一致性 | 批量保存、批量删除、全选操作 |
| **后台同步** | 🔄 轮询更新 | 静默更新，不干扰用户 | 生成状态同步、自动保存 |
| **重要状态变更** | 🔄 完整刷新 | 确保显示最新状态 | 生成完成、发布完成 |

#### 2. **缓存管理原则**

| 数据变化类型 | 缓存策略 | 处理方式 | 业务场景 |
| --- | --- | --- | --- |
| **内容变化** | 🗑️ 清除缓存 | `clearWorkDetailCacheEntry(workId)` | 编辑脚本、修改配音、更换背景 |
| **状态变化** | 💾 保留缓存 | 无操作 | 保存状态更新、生成进度更新 |
| **批量操作** | 🗑️ 清空所有缓存 | `clearAllWorkDetailCache()` | 批量保存、完整刷新 |
| **结构变化** | 🗑️ 清空所有缓存 | `clearAllWorkDetailCache()` | 新增作品、删除多个作品 |

#### 3. **用户体验考虑**

| 用户期望 | 实现策略 | 技术手段 | 适用场景 |
| --- | --- | --- | --- |
| **保持浏览位置** | 📍 局部更新 | 保持页码、滚动位置、选中状态 | 单个作品编辑、保存 |
| **显示最新内容** | 🔄 完整刷新 | 回到第一页，重新获取数据 | 生成完成、批量操作 |
| **后台同步** | 🔄 静默更新 | 合并更新，不改变用户状态 | 轮询检测、自动保存 |
| **快速响应** | ⚡ 本地操作 | 先更新 UI，后同步服务器 | 删除操作、状态切换 |

#### 4. **性能优化建议**

| 优化目标 | 实现方法 | 技术细节 | 预期效果 |
| --- | --- | --- | --- |
| **减少 API 调用** | 💾 合理使用缓存 | 保留未变化作品的详情缓存 | 🚀 提升加载速度 |
| **避免频繁刷新** | 🔄 局部更新优先 | 单个作品操作使用 MERGE 模式 | 🚀 减少服务器压力 |
| **批量操作优化** | ⏱️ 防抖策略 | 短时间内多次操作合并处理 | 🚀 避免重复请求 |
| **内存管理** | 🗑️ 及时清理缓存 | 删除无效缓存，避免内存泄漏 | 🚀 保持应用性能 |

#### 5. **错误处理策略**

| 错误类型 | 处理策略 | 用户反馈 | 恢复方案 |
| --- | --- | --- | --- |
| **API 调用失败** | 🔄 重试机制 | 显示错误提示，提供重试按钮 | 用户手动重试或自动重试 |
| **数据不一致** | 🔄 完整刷新 | 提示数据已更新，自动刷新 | 强制重新获取最新数据 |
| **缓存异常** | 🗑️ 清空缓存 | 静默处理，重新加载 | 清空所有缓存，重新获取 |
| **网络异常** | 💾 保留本地状态 | 显示离线提示 | 网络恢复后自动同步 |

## 📚 相关文件

### 核心文件

- `src/views/EditProjectView/composables/useInfiniteWorkList.ts` - 主要逻辑
- `src/views/EditProjectView/composables/useWorkList.ts` - 基础作品列表
- `src/views/EditProjectView/composables/useWorkDetail.ts` - 作品详情管理
- `src/views/EditProjectView/composables/useWorkPolling.ts` - 轮询机制

### 业务组件

- `src/views/EditProjectView/pages/SecondStepVideo/index.vue` - 视频步骤页面
- `src/views/EditProjectView/pages/SecondStepImage/index.vue` - 图文步骤页面
- `src/views/EditProjectView/composables/useSecondStep.ts` - 第二步逻辑

### UI 组件

- `src/components/WorkListBar/WorkListBarItem.vue` - 作品列表项
- `src/views/EditProjectView/pages/SecondStepVideo/components/ContentPanel/index.vue` - 内容面板

---

_文档版本：v3.1_ _最后更新：2025-06-23_ _更新内容：更新 WorkListBarItem.vue 组件的技术实现描述，从 inject 模式改为 EventBus 事件总线机制，确保文档与代码实现保持一致_
