# CustomUpload 组件预览功能说明

## 功能概述

CustomUpload 组件现在支持通过 `enablePreview` prop 控制点击预览功能的开启和关闭。

## 新增功能

### enablePreview 属性

- **类型**: `boolean`
- **默认值**: `false`
- **描述**: 控制示例素材和用户上传素材的点击预览功能，视频 popover 预览除外

### 预览功能控制范围

#### 受控制的预览功能：

1. **示例图片点击预览** - 点击示例图片时打开图片预览器
2. **示例视频点击预览** - 点击示例视频时打开视频预览器
3. **用户上传图片点击预览** - 点击已上传的图片时打开图片预览器
4. **用户上传视频点击预览** - 点击已上传的视频时打开视频预览器

#### 不受控制的预览功能：

1. **视频 popover 预览** - 示例视频的悬停预览功能始终保持开启，hover 区域覆盖整个 `.custom-upload__example-video` 区域

## 使用方法

### 基本用法

```vue
<template>
  <!-- 关闭预览功能（默认） -->
  <CustomUpload
    :file-list="fileList"
    :enable-preview="false"
    @upload-click="handleUploadClick"
  />

  <!-- 开启预览功能 -->
  <CustomUpload
    :file-list="fileList"
    :enable-preview="true"
    @upload-click="handleUploadClick"
  />
</template>
```

### 完整示例

```vue
<template>
  <CustomUpload
    :file-list="fileList"
    :disabled="false"
    :max-length="10"
    default-media-type="image"
    :example-image-url="exampleImageUrl"
    :example-video-url="exampleVideoUrl"
    :example-video-thumbnail="exampleVideoThumbnail"
    :enable-preview="true"
    @upload-click="handleUploadClick"
    @delete="handleFileDelete"
    @replace="handleFileReplace"
  />
</template>

<script setup lang="ts">
import { ref } from 'vue';
import type { FileInfo, MediaType } from '@/views/EditProjectView/types/index';

const fileList = ref<FileInfo[]>([]);
const exampleImageUrl = 'https://example.com/image.jpg';
const exampleVideoUrl = 'https://example.com/video.mp4';
const exampleVideoThumbnail = 'https://example.com/thumbnail.jpg';

const handleUploadClick = (payload: { mediaType: MediaType }) => {
  console.log('上传点击:', payload);
};

const handleFileDelete = (payload: { index: number }) => {
  fileList.value.splice(payload.index, 1);
};

const handleFileReplace = (payload: {
  index: number;
  mediaType: MediaType;
}) => {
  console.log('替换文件:', payload);
};
</script>
```

## 行为说明

### enablePreview = false（默认）

- 点击示例素材和用户上传素材不会触发预览
- 鼠标悬停时显示默认光标
- 视频 popover 预览仍然可用，hover 整个示例视频区域即可触发

### enablePreview = true

- 点击示例素材和用户上传素材会触发相应的预览器
- 鼠标悬停时显示指针光标
- 视频 popover 预览正常工作，hover 整个示例视频区域即可触发

## 技术实现

### 模板层面

- 使用条件表达式控制点击事件绑定：`@click="enablePreview ? previewMethod : undefined"`
- 使用动态样式控制光标显示：`:style="{ cursor: enablePreview ? 'pointer' : 'default' }"`
- 视频 popover 预览：将 `fa-popover` 组件包裹整个 `.custom-upload__example-video` 区域，扩大 hover 触发范围

### 方法层面

- 在预览方法开头添加 `enablePreview` 检查
- 如果 `enablePreview` 为 `false`，直接返回，不执行预览逻辑

## 兼容性

此更新完全向后兼容，现有代码无需修改即可正常工作，因为 `enablePreview` 的默认值为 `false`，保持了原有的行为。

## 测试

可以使用 `CustomUpload.test.vue` 文件进行功能测试，该文件包含了两个测试场景：

1. 预览功能关闭的情况
2. 预览功能开启的情况
