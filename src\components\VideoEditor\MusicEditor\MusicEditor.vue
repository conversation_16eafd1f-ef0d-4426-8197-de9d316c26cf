<template>
  <div class="music-editor h-full overflow-y-auto pt-[16px]">
    <div class="px-[40px] pt-[8px] pb-[16px]">
      <!-- 背景音乐 -->
      <div class="flex">
        <div class="title">背景音乐</div>
        <div>
          <fa-radio-group
            v-model="workInfo?.setting.bgMusic.open"
            @change="handleOpenBgMusic"
          >
            <fa-radio :value="true">开启</fa-radio>
            <fa-radio :value="false">关闭</fa-radio>
          </fa-radio-group>
          <div v-show="workInfo?.setting.bgMusic.open" class="box-content">
            <div class="flex items-center">
              <img
                :src="
                  bgMusicInfo.cover.resId && !isBgmInvalid
                    ? getMaterialFullUrl(
                        bgMusicInfo.cover.resId,
                        FILE_TYPES.WEBP,
                        'oss',
                        48,
                      )
                    : defaultBgMusicImg
                "
                class="rounded-img"
                alt="背景音乐封面"
              />
              <div class="ml-[16px] w-[255px]">
                <div
                  class="text-text"
                  :class="{ 'text-disabledText': isBgmInvalid }"
                >
                  {{
                    isBgmInvalid
                      ? '背景音乐已失效，请重新更换'
                      : bgMusicInfo.name
                  }}
                </div>
                <div class="text-disabledText">
                  {{ isBgmInvalid ? '' : bgMusicInfo.duration }}
                </div>
              </div>
              <fa-button
                size="small"
                class="w-[80px]"
                @click="showBgMusicSelector"
                >更换音乐</fa-button
              >
            </div>
            <div class="flex mt-[16px] items-center">
              <div class="text-title">音乐音量</div>
              <fa-slider
                class="w-[225px] ml-[16px]"
                v-model="workInfo?.setting.bgMusic.vol"
                :min="0"
                :max="200"
                :step="1"
                :disabled="isBgmInvalid"
              />
              <fa-input-number
                class="w-[80px] rounded-[6px] ml-[16px]"
                :min="0"
                :max="200"
                v-model="workInfo?.setting.bgMusic.vol"
                @blur="handleVolBlur"
                :disabled="isBgmInvalid"
              />
            </div>
          </div>
        </div>
      </div>
      <!-- 配音 -->
      <div class="flex mt-[24px]">
        <div class="title pt-[8px]">配音</div>
        <div>
          <div class="box-content !mt-0">
            <div class="flex items-center">
              <img
                :src="
                  dubbingInfo.cover.resId && !isDubbingInvalid
                    ? getMaterialFullUrl(
                        dubbingInfo.cover.resId,
                        FILE_TYPES.WEBP,
                        'oss',
                        48,
                      )
                    : defaultVoiceImg
                "
                class="rounded-img"
                alt="配音封面"
              />
              <div class="ml-[16px] w-[255px]">
                <div
                  class="text-text"
                  :class="{ 'text-disabledText': isDubbingInvalid }"
                >
                  {{
                    isDubbingInvalid
                      ? '配音已失效，请重新更换'
                      : dubbingInfo.name
                  }}
                </div>
                <div class="text-disabledText">
                  {{ isDubbingInvalid ? '' : dubbingInfo.categoryName || '' }}
                </div>
              </div>
              <fa-button
                size="small"
                class="w-[80px]"
                @click="handleChangeDubbing"
                >更换配音</fa-button
              >
            </div>
            <div class="flex mt-[16px] items-center">
              <div class="text-text">配音语速</div>
              <fa-slider
                class="w-[225px] ml-[16px]"
                v-model="workInfo?.setting.voice.speed"
                :min="MIN_SPEED"
                :max="1.500000000001"
                :step="0.1"
                :disabled="isDubbingInvalid"
              />
              <fa-input-number
                class="w-[80px] rounded-[6px] ml-[16px] text-text"
                :min="MIN_SPEED"
                :max="MAX_SPEED"
                :step="0.1"
                v-model="workInfo?.setting.voice.speed"
                @blur="handleSpeedBlur"
                :disabled="isDubbingInvalid"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- 音乐资源弹窗 -->
    <BgMusicSelector
      v-model="isShowBgMusicSelector"
      :resIds="bgMusicInfo && [bgMusicInfo.resId]"
      @changeMusicInfo="handleChangeMusicInfo"
      @input="handleCloseBgMusicSelector"
    />
    <!-- 配音弹窗 -->
    <DubbingSelector
      v-model="isShowDubbingSelector"
      :voiceId="dubbingInfo.voiceId"
      @changeDubbingInfo="handleChangeDubbingInfo"
    />
  </div>
</template>

<script lang="ts" setup>
import {
  isBgmInvalid,
  isDubbingInvalid,
  loading,
  updateConsumePoint,
  workInfo,
} from '../hook/useWorkInfo';
import { nextTick, ref, watch } from 'vue';
import BgMusicSelector from '@/components/VideoEditor/MusicEditor/BgMusicSelector.vue';
import DubbingSelector from '@/components/VideoEditor/MusicEditor/DubbingSelector.vue';
import { getDubbingInfo, getMusicInfo } from '@/api/VideoEditor/music';
import { Dubbing, Music } from '@/types';
import { message } from '@fk/faicomponent';
import { FILE_TYPES } from '@/constants/fileType';
import { getMaterialFullUrl } from '@/components/MaterialBasicUpload/utils/index';
import defaultBgMusicImg from '@/assets/EditProject/bgMusicImg.webp';
import defaultVoiceImg from '@/assets/EditProject/voiceImg.webp';

/** 背景音乐信息 */
const bgMusicInfo = ref<Music>({
  resId: '',
  name: '未选择音乐',
  duration: '-',
  cover: {
    resId: '',
    resType: FILE_TYPES.WEBP,
  },
  link: '',
});
/** 是否显示音乐资源弹窗 */
const isShowBgMusicSelector = ref(false);
/** 点击更换音乐, 显示音乐资源弹窗 */
const showBgMusicSelector = () => {
  isShowBgMusicSelector.value = true;
};
/** 处理更换音乐 */
const handleChangeMusicInfo = (musicList: Music[]) => {
  if (musicList.length > 0) {
    const music = musicList[0];
    workInfo.value && (workInfo.value.setting.bgMusic.resId = music.resId);
    bgMusicInfo.value = music;
    isBgmInvalid.value = false;
  }
};
/** 处理开启音乐 */
const handleOpenBgMusic = (open: boolean) => {
  if (workInfo.value) {
    const isMusicEmpty = !workInfo.value.setting.bgMusic.resId;
    if (open && isMusicEmpty) {
      showBgMusicSelector();
    }
  }
};
/** 处理关闭音乐资源弹窗 */
const handleCloseBgMusicSelector = (visible: boolean) => {
  if (visible) return;
  nextTick(() => {
    if (workInfo.value) {
      const isMusicEmpty = !workInfo.value.setting.bgMusic.resId;
      const isMusicOpen = workInfo.value.setting.bgMusic.open;
      if (isMusicEmpty && isMusicOpen) {
        workInfo.value.setting.bgMusic.open = false;
      }
    }
  });
};

/** 是否显示配音资源弹窗 */
const isShowDubbingSelector = ref(false);
/** 配音信息 */
const dubbingInfo = ref<Dubbing>({
  voiceId: workInfo.value?.setting.voice.voiceId || '',
  typeName: '',
  categoryName: '',
  cover: {
    resId: '',
    resType: FILE_TYPES.WEBP,
  },
  name: '',
  link: '',
  extraType: workInfo.value?.setting.voice.extraType,
});
/** 语速最小值 */
const MIN_SPEED = 0.8;
const MAX_SPEED = 1.5;
/** 点击更换配音, 显示配音资源弹窗 */
const handleChangeDubbing = () => {
  isShowDubbingSelector.value = true;
};
/** 处理更换配音 */
const handleChangeDubbingInfo = (dubbing: Dubbing | null) => {
  if (dubbing) {
    dubbingInfo.value = dubbing;
    if (workInfo.value) {
      workInfo.value.setting.voice.voiceId = dubbing.voiceId;
      workInfo.value.setting.voice.extraType = dubbing.extraType;
      updateConsumePoint();
    }
    isDubbingInvalid.value = false;
  }
};

/** 处理数值校验的通用函数 */
const validateAndClamp = (value: number, min: number, max: number): number => {
  return Math.max(min, Math.min(max, value));
};

/** 处理背景音乐音量失焦校验 */
const handleVolBlur = () => {
  if (workInfo.value?.setting.bgMusic.vol !== undefined) {
    workInfo.value.setting.bgMusic.vol = validateAndClamp(
      workInfo.value.setting.bgMusic.vol,
      0,
      200,
    );
  }
};

/** 处理配音语速失焦校验 */
const handleSpeedBlur = () => {
  if (workInfo.value?.setting.voice.speed !== undefined) {
    workInfo.value.setting.voice.speed = validateAndClamp(
      workInfo.value.setting.voice.speed,
      MIN_SPEED,
      MAX_SPEED,
    );
  }
};
watch(
  () => workInfo.value?.setting.voice.speed,
  _newVal => {
    updateConsumePoint();
  },
);

/**********初始化 star***********/
/** 初始化背景音乐信息，使用 async/await */
const initBgMusic = async () => {
  // 如果有背景音乐资源ID，则获取音乐信息
  if (workInfo.value?.setting.bgMusic.resId) {
    const [err, res] = await getMusicInfo(
      workInfo.value?.setting.bgMusic.resId,
    );
    if (err) {
      if (!(err instanceof Error) && err.rt === -3) {
        isBgmInvalid.value = true;
        return;
      }
      message.error('获取背景音乐失败', err.message);
      throw err;
    }
    bgMusicInfo.value = res.data;
  }
};

/** 初始化配音信息，使用 async/await */
const initDubbing = async () => {
  // 如果有配音ID，则获取配音信息
  if (workInfo.value?.setting.voice.voiceId) {
    const [err, res] = await getDubbingInfo(
      workInfo.value?.setting.voice.voiceId,
    );
    if (err) {
      if (!(err instanceof Error) && err.rt === -3) {
        isDubbingInvalid.value = true;
        return;
      }
      console.error(err);
      return;
    }
    dubbingInfo.value = res.data;
  }
};
let isInited = false;
watch(
  () => loading.value,
  async newVal => {
    if (!isInited && !newVal) {
      isInited = true;
      await initBgMusic();
      await initDubbing();
    }
  },
  { immediate: true },
);
/**********初始化 end***********/
</script>

<style lang="scss" scoped>
.title {
  @apply w-[80px] text-text text-[14px] line-height-[19px];
}
.box-content {
  @apply mt-[16px] p-[16px] b-edge b-1 rounded-[8px];
  .rounded-img {
    @apply w-[48px] h-[48px] rounded-full;
  }
}
::v-deep {
  .fa-radio-wrapper {
    @apply mr-[12px] text-text;
  }
}
</style>
