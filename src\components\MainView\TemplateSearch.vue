<template>
  <div class="template-search">
    <fa-skeleton :loading="isShowSkeleton" :paragraph="false" active>
      <fa-input
        v-model="searchValue"
        size="large"
        placeholder="在海量模板中搜索"
        class="search-inputer"
        :max-length="50"
        @keyup.enter="handleClickSearch"
      >
        <Icon
          slot="prefix"
          type="xiaosuo"
          class="w-[20px] h-[20px] text-assist"
        />
        <template slot="suffix">
          <fa-icon
            v-show="isShowClearInput"
            type="fa-delete"
            theme="filled"
            :style="{ fontSize: '24px' }"
            class="mr-[16px] cursor-pointer text-disabledText"
            @click="handleClickClearInput"
          />
          <fa-button
            size="small"
            class="special-button"
            @click="handleClickSearch"
            >搜索</fa-button
          >
        </template>
      </fa-input>
    </fa-skeleton>
  </div>
</template>

<script lang="ts" setup>
import { computed } from 'vue';
import { useSkeleton } from '@/hook/useSkeleton';

/** 搜索文案 */
import { searchTemplate, searchValue } from '@/hook/useTemplateSearch';
/** 搜索回调 */
const handleClickSearch = () => {
  searchTemplate();
};
const { isShowSkeleton } = useSkeleton();
/** 是否显示清除输入框的图标 */
const isShowClearInput = computed(() => searchValue.value.length > 0);
function handleClickClearInput() {
  searchValue.value = '';
}
</script>

<style lang="scss" scoped>
.template-search {
  ::v-deep {
    .fa-skeleton-content {
      .fa-skeleton-title {
        height: 40px;
        width: 600px;
        border-radius: 20px;
      }
    }
    .fa-input-affix-wrapper {
      .fa-input-prefix {
        @apply left-[16px];
      }
      .fa-input-suffix {
        @apply right-[4px];
      }
      .fa-input:not(:first-child) {
        @apply pl-[44px];
      }
      &:hover {
        .fa-input:not(.fa-input-disabled) {
          border-color: rgba(0, 0, 0, 0);
          box-shadow: 0 0 0 2px rgba(50, 97, 253, 0.1);
        }
      }
    }
  }
  .search-inputer {
    ::v-deep {
      .fa-input {
        @apply rounded-full w-[600px] border-[#e8e8e8] text-text text-[14px] pr-[128px];
        @media (max-width: 1439px) {
          width: 480px !important;
        }
        &:focus {
          border-color: rgba(0, 0, 0, 0);
          box-shadow: 0 0 0 2px rgba(50, 97, 253, 0.1);
        }
      }
    }
    .special-button {
      @apply btn-special;
      @apply text-white rounded-full px-[22px];
    }
  }
}
</style>
