import { GET, POST_JSON } from '@/api/request';
import { transformProjectDataToEditProjectApiFormat } from '@/api/EditProjectView/utils/outputDataTransform';
import {
  fetchTemplateData,
  fetchProjectData,
} from '@/api/EditProjectView/utils/dataFetcher';
import { validateProjectAccess } from '@/api/EditProjectView/utils/dataValidator';
import {
  filterTemplateData,
  mergeProjectData,
} from '@/api/EditProjectView/utils/dataProcessor';
import store from '@/store';
import { PROJECT_STATUS } from '@/constants/project';
import type {
  EditProjectViewData,
  ResourceInfo,
  ResourceType,
} from '@/views/EditProjectView/types/index';

import type {
  UpdateProjectResponse,
  GeneratePreviewResult,
  GetConsumePointResponse,
  GetUniqueIdResponse,
} from './types/response';

/**
 * @description 获取项目数据
 * @param templateId 模板ID，必传
 * @param projectId 项目ID，为空时表示新建项目
 * @param options 可选配置
 * @param options.checkProjectStatus 是否检查项目状态，默认false
 * @returns Promise<[Error | null, EditProjectViewData | null]> 错误优先的元组格式
 */
export const getEditProjectViewData = async (
  templateId: number,
  projectId?: number,
  options?: { checkProjectStatus?: boolean },
): Promise<[Error | null, EditProjectViewData | null]> => {
  // 1. 获取模板数据
  const [templateErr, templateData] = await fetchTemplateData(templateId);
  if (templateErr) {
    return [templateErr, null];
  }

  // 2. 获取项目数据（可选）
  const [_projectErr, projectData] = await fetchProjectData(
    templateId,
    projectId,
  );
  // 项目数据获取失败不影响整体流程，只是使用默认配置

  // 3. 验证项目访问权限
  const accessError = validateProjectAccess(
    projectData || undefined,
    projectId,
    options?.checkProjectStatus,
  );
  if (accessError) {
    return [accessError, null];
  }

  // 4. 过滤和处理数据
  const filteredTemplateData = filterTemplateData(templateData!);
  const mergedData = mergeProjectData(
    filteredTemplateData,
    projectData || undefined,
  );

  // 5. 设置项目状态到 Vuex（使用合并后的完整数据）
  const finalProjectId = projectId || mergedData?.projectId;
  const projectStatus = mergedData?.status ?? PROJECT_STATUS.DRAFT;

  if (finalProjectId) {
    store.dispatch('project/setProjectStatus', {
      projectId: String(finalProjectId),
      status: projectStatus as PROJECT_STATUS,
    });

    console.log('已设置项目状态到 Vuex:', {
      项目ID: finalProjectId,
      项目状态: projectStatus,
      状态名称:
        projectStatus === PROJECT_STATUS.DRAFT
          ? '草稿'
          : projectStatus === PROJECT_STATUS.GENERATING
          ? '生成中'
          : projectStatus === PROJECT_STATUS.TO_BE_SAVED
          ? '待保存'
          : projectStatus === PROJECT_STATUS.COMPLETED
          ? '已完成'
          : '未知',
      模式: projectId ? '编辑模式' : '新建模式',
    });
  }

  return [null, mergedData];
};

/**
 * @description 保存项目数据
 * @param data 项目数据
 * @returns Promise<RequestResult<UpdateProjectResponse>> 保存结果
 */
export const saveProjectData = async (data: EditProjectViewData) => {
  // 获取唯一ID用于幂等性控制
  const [uniqueIdErr, uniqueIdRes] = await getUniqueId();
  if (uniqueIdErr) {
    console.warn('获取唯一ID失败，将不使用幂等性控制:', uniqueIdErr);
  }

  // 转换数据为API请求格式
  const apiData = transformProjectDataToEditProjectApiFormat(
    data,
    uniqueIdRes?.data?.uniqueId,
  );

  return POST_JSON<UpdateProjectResponse>('/api/project/update', apiData);
};

/**
 * @description 生成项目预览
 * @param data 项目数据
 * @returns Promise<RequestResult<GeneratePreviewResult>> 生成预览结果
 */
export const generatePreview = async (data: EditProjectViewData) => {
  // 获取唯一ID用于幂等性控制
  const [uniqueIdErr, uniqueIdRes] = await getUniqueId();
  if (uniqueIdErr) {
    console.warn('获取唯一ID失败，将不使用幂等性控制:', uniqueIdErr);
  }

  // 转换数据为API请求格式
  const apiData = transformProjectDataToEditProjectApiFormat(
    data,
    uniqueIdRes?.data?.uniqueId,
  );

  return POST_JSON<GeneratePreviewResult>('/api/project/submit', apiData);
};

/**
 * @description 获取消耗点数
 * @param typeList 消费点类型列表（逗号分隔），如："1,3"
 * @param createNum 创建数量
 * @returns Promise<RequestResult<GetConsumePointResponse>> 消耗点数
 */
export const getConsumePoint = (typeList: string, createNum: number) => {
  return GET<GetConsumePointResponse>('/api/scProf/getConsumePoint', {
    typeList,
    createNum,
  });
};

/**
 * @description 获取唯一ID，用于幂等性控制
 * @returns Promise<RequestResult<GetUniqueIdResponse>> 唯一ID
 */
export const getUniqueId = () => {
  return GET<GetUniqueIdResponse>('/api/project/getUniqueId');
};

/**
 * 待废弃
 * @description 获取资源信息
 * @param resId 资源ID
 * @param type 资源类型(可选)：'image' | 'video' | 'audio'，不传则根据resId前缀判断
 */
export const getResourceInfo = (resId: string, type?: ResourceType) => {
  return GET<ResourceInfo>('/api/resource/getInfo', { resId, type });
};
