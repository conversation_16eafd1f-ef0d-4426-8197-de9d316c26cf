import { Font, FontPreset } from '@/types/Sticker';
import { GET } from '../request';

export interface FontApi {
  // 字体接口定义
  feeGroup: 'free' | 'paid'; // 费用分组
  fileName: string; // 字体文件名
  fontName: string; // 字体名称
  fontFamily: string; // 字体家族
  langGroup: 'zh' | 'en'; // 语言分组
  styleGroup: string; // 风格分组
  name: string; // 显示名称
}

/** 获取字体列表 */
export const getFontList = async () => {
  const [err, res] = await GET<FontApi[]>(
    '/api/constant/getConf?key=3c966508c597a71c22550a2e99c33313',
  );
  if (err) {
    return [err, null] as const;
  }
  const data: Font[] = res.data
    .map(
      (item): Font => ({
        fileName: item.fileName,
        fontFamily: item.fontFamily,
        fontName: item.fontName,
        languageCategory: item.langGroup,
        styleCategory: item.styleGroup,
        pricingCategory: item.feeGroup,
        name: item.name,
      }),
    )
    .sort((a, b) => {
      // 按首字母 a-z 排序（忽略大小写）
      const aCaptal = a.fileName[0].toLowerCase();
      const bCaptal = b.fileName[0].toLowerCase();
      return aCaptal.localeCompare(bCaptal, 'en', { sensitivity: 'base' });
    });
  return [null, { ...res, data }] as const;
};

/**
 * 获取字体预设
 * description: 配置中心-scFontStyle
 * @returns 字体预设列表
 */
export const getFontPreset = () => {
  return GET<FontPreset[]>(
    '/api/constant/getConf?key=4804fdb92bc51ad8b1304c123e39d537',
  );
};
