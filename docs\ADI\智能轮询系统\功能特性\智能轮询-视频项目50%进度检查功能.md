# 视频项目 50%进度检查功能

## 功能概述

在智能轮询功能中，针对视频项目类型，新增了特殊的进度检查逻辑：当检测到作品进度达到 50%时，会触发一次作品详情数据的更新操作。

## 功能特点

1. **仅对视频项目生效**：只有当项目类型为 `PROJECT_TYPE_VIDEO`（0）时才会执行此逻辑，图文项目不受影响
2. **进度阈值检测**：监控作品进度从小于 50%变为大于等于 50%的变化
3. **避免重复触发**：同一作品在 50%进度点只触发一次详情更新
4. **不影响正常轮询**：此功能不会干扰正常的轮询流程和其他回调
5. **自动清理状态**：轮询停止或组件卸载时会自动清理 50%进度跟踪状态

## 技术实现

### 1. 接口扩展

在 `UseWorkPollingOptions` 接口中新增了可选的回调函数：

```typescript
export interface UseWorkPollingOptions<T extends WorkItem> {
  // ... 其他属性
  /** 视频作品50%进度时的回调函数，用于更新作品详情 */
  onVideoWork50PercentProgress?: (workId: number) => void;
}
```

### 2. 状态跟踪

使用 `Set` 数据结构记录已经触发过 50%进度更新的作品 ID：

```typescript
// 50%进度检查状态跟踪（仅用于视频项目）
// 记录已经触发过50%进度更新的作品ID，避免重复触发
const triggered50PercentWorks = ref<Set<number>>(new Set());
```

### 3. 进度检查逻辑

在 `performPollingUpdate` 函数中，更新作品列表数据后执行 50%进度检查：

```typescript
// 检查视频项目的50%进度触发点
if (projectType === PROJECT_TYPE_VIDEO && onVideoWork50PercentProgress) {
  updatedWorks.forEach(work => {
    const previousProgress = currentProgress.get(work.id) || 0;
    const currentWorkProgress = work.progress || 0;

    // 检查是否从小于50%变为大于等于50%，且未曾触发过
    if (
      previousProgress < 50 &&
      currentWorkProgress >= 50 &&
      !triggered50PercentWorks.value.has(work.id)
    ) {
      // 标记为已触发，避免重复触发
      triggered50PercentWorks.value.add(work.id);

      // 触发50%进度详情更新
      handleVideoWork50PercentProgress(work.id);
    }
  });
}
```

### 4. 回调触发处理

内部实现了 `handleVideoWork50PercentProgress` 函数来处理回调触发：

```typescript
/**
 * 处理视频作品50%进度时的回调触发
 * @param workId 作品ID
 */
const handleVideoWork50PercentProgress = (workId: number): void => {
  if (!onVideoWork50PercentProgress) {
    return;
  }

  // 直接调用外部回调函数，不需要重新获取作品详情
  // 因为轮询已经获取了最新的作品数据
  onVideoWork50PercentProgress(workId);

  logger.debug('✅ 视频作品50%进度回调触发', {
    作品ID: workId,
    项目ID: projectId,
    说明: '基于轮询获取的最新数据触发',
  });
};
```

**设计优化说明**：

- 不再重复调用 `getWorkInfoWithTransform` API，避免不必要的网络请求
- 直接基于轮询获取的最新数据触发回调，提高性能
- 轮询过程中已经获取了最新的作品状态和进度，无需重复获取

## 使用方式

### 在 useInfiniteWorkList 中的集成

```typescript
// 集成智能轮询功能，传入作品完成、失败和50%进度回调
const polling = useWorkPolling({
  workList: cachedItems,
  handlePollingUpdate: handlePollingDataUpdate,
  projectId,
  projectType: convertMediaTypeToProjectType(type),
  onWorkCompleted: handleWorkCompleted,
  onWorkFailed: handleWorkFailed,
  onVideoWork50PercentProgress: handleVideoWork50PercentProgress,
});
```

### 回调函数实现

```typescript
/**
 * 轮询更新-视频作品50%进度时的回调函数
 * 当视频作品进度达到50%时，更新作品详情数据以获取最新的元数据和状态信息
 * @param workId 达到50%进度的作品ID
 */
const handleVideoWork50PercentProgress = (workId: number): void => {
  logger.debug('🎯 视频作品50%进度，更新详情数据', {
    作品ID: workId,
    是否为当前选中作品: currentWorkId.value === workId,
    操作: '清除详情缓存并重新获取',
    上下文: '轮询更新-50%进度',
  });

  // 使用统一的更新方法处理50%进度更新
  updateWorkItems([workId], {
    clearDetailCache: true,
    refreshCurrentDetail: currentWorkId.value === workId,
    context: '轮询更新-50%进度',
  });
};
```

## 日志记录

功能运行时会产生详细的调试日志：

- **进度触发日志**：记录触发 50%进度检查的作品信息
- **详情更新成功日志**：记录成功获取作品详情的信息
- **详情更新失败日志**：记录获取作品详情失败的错误信息

## 测试覆盖

新增了完整的单元测试覆盖：

1. **视频项目 50%进度触发测试**：验证视频项目中 50%进度检查正常工作
2. **图文项目不触发测试**：验证图文项目不会触发 50%进度回调
3. **重复触发避免测试**：验证同一作品不会重复触发 50%进度回调

## 注意事项

1. **项目类型限制**：只有视频项目（`PROJECT_TYPE_VIDEO = 0`）才会执行 50%进度检查
2. **进度阈值**：检查的是从小于 50%变为大于等于 50%的变化，而不是精确的 50%
3. **状态清理**：轮询停止时会自动清理 50%进度跟踪状态，避免内存泄漏
4. **错误处理**：详情更新失败不会影响正常的轮询流程
5. **性能影响**：50%进度检查逻辑对轮询性能影响极小，只在必要时执行 API 调用

## 业务价值

此功能的主要目的是在视频生成的关键节点（50%进度）获取最新的作品元数据和状态信息，提升用户体验，确保用户能够及时看到作品的最新状态和相关信息。
