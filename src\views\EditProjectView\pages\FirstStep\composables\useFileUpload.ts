/**
 * @description 文件上传相关的composable
 */
import { Ref } from 'vue';
import { message } from '@fk/faicomponent';
import type {
  DynamicFileFormInstance,
  FormEventParams,
  FormItemInternal,
  FileInfo,
  MediaType,
} from '@/views/EditProjectView/types/index';
import { FILE_STATUS, MEDIA_TYPES } from '@/views/EditProjectView/types/index';
import { FILE_TYPES } from '@/constants/fileType';
import type { MaterialUploadFile } from '@/components/MaterialBasicUpload/types';

import { showMaterialBasicUpload } from '@/components/MaterialBasicUpload/index';
import { getMaterialFullUrl } from '@/components/MaterialBasicUpload/utils/index.ts';

/**
 * 文件上传处理Hook的返回值类型
 * @interface UseFileUploadReturn
 */
interface UseFileUploadReturn {
  /** 处理文件上传点击 */
  handleUploadClick: (
    params: FormEventParams['upload'],
    dynamicFormFiles: DynamicFileFormInstance,
  ) => void;
  /**
   * @description 处理文件删除（基于索引）。
   * @param params - 包含 `prop`（字段名）和 `index`（文件索引）的对象。
   * @param dynamicFormFiles - 文件表单的引用。
   */
  handleFileDelete: (
    params: FormEventParams['fileDelete'],
    dynamicFormFiles: DynamicFileFormInstance,
  ) => void;
  /**
   * @description 处理文件替换（基于索引）。
   * @param params - 包含 `prop`（字段名）、`index`（文件索引）和 `mediaType`（新媒体类型）的对象。
   * @param dynamicFormFiles - 文件表单的引用。
   */
  handleReplace: (
    params: FormEventParams['replace'],
    dynamicFormFiles: DynamicFileFormInstance,
  ) => void;
}

/**
 * @description 文件上传相关的组合式函数。
 * @param fileFormItems - Ref<FormItemInternal[]> 表单项配置的响应式引用。
 * @returns {UseFileUploadReturn} 包含文件上传、删除、替换处理逻辑的对象。
 */
export function useFileUpload(
  fileFormItems: Ref<FormItemInternal[]>,
): UseFileUploadReturn {
  /**
   * @description 将 MaterialUploadFile 转换为 FileInfo
   * @param materialFile - 素材文件对象
   * @param mediaType - 媒体类型
   * @returns 转换后的 FileInfo 对象
   */
  const convertMaterialFileToFileInfo = (
    materialFile: MaterialUploadFile,
    mediaType: MediaType,
  ): FileInfo => {
    const { data } = materialFile;
    const baseFileInfo: FileInfo = {
      name: data.name,
      url: data.url,
      status: FILE_STATUS.NORMAL,
      mediaType,
      resId: data.resId,
      resType: data.fileType,
    };

    if (mediaType === MEDIA_TYPES.VIDEO) {
      return {
        ...baseFileInfo,
        thumbUrl: getMaterialFullUrl(
          data.extra?.cover || '',
          FILE_TYPES.WEBP || data.extra?.coverType,
        ),
        duration: data.extra?.duration || 0,
        coverId: data.extra?.cover,
        coverType: data.extra?.coverType,
      };
    }
    return baseFileInfo;
  };
  /**
   * @description 处理文件上传按钮点击事件。
   * @param params - FormEventParams['upload'] 上传事件参数。
   * @param dynamicFormFiles - DynamicFileFormInstance 动态表单文件部分的引用。
   */
  const handleUploadClick = (
    params: FormEventParams['upload'],
    dynamicFormFiles: DynamicFileFormInstance,
  ) => {
    const { prop, mediaType, formItem: paramsFormItem } = params;
    const currentFiles: FileInfo[] =
      (dynamicFormFiles.getFormData()[prop] as FileInfo[]) || [];

    const formItemConfig = fileFormItems.value.find(item => item.prop === prop);
    const maxLength =
      paramsFormItem?.maxLength || formItemConfig?.maxLength || 1; // Prioritize formItem from event if available

    if (currentFiles.length >= maxLength) {
      message.warning(`已达到最大上传数量限制 (${maxLength})`);
      return;
    }

    showMaterialBasicUpload({
      title: mediaType === MEDIA_TYPES.VIDEO ? '添加视频' : '添加图片',
      maxChosenFileCount: maxLength - currentFiles.length,
      isVideo: mediaType === MEDIA_TYPES.VIDEO,
      onConfirm: (confirmedFiles?: MaterialUploadFile[]) => {
        if (!confirmedFiles?.length) {
          return;
        }
        const newMaterialFiles = confirmedFiles;
        // 额外安全检查：确保不超过最大数量限制
        const remainingSlots = maxLength - currentFiles.length;
        const filesToAdd = newMaterialFiles.slice(0, remainingSlots);

        if (filesToAdd.length < newMaterialFiles.length) {
          message.warning(
            `只能添加 ${filesToAdd.length} 个文件，已达到最大数量限制`,
          );
        }

        const newFilesInfo: FileInfo[] = filesToAdd.map(
          (fileUpload: MaterialUploadFile) =>
            convertMaterialFileToFileInfo(fileUpload, mediaType),
        );
        dynamicFormFiles.setFormData({
          [prop]: [...newFilesInfo, ...currentFiles],
        });
        // 如果字段已经被验证过（用户之前点击过生成按钮），则重新验证以清除错误状态
        // 如果字段未被验证过，则不触发验证，避免过早添加triggerAuto
        if (dynamicFormFiles?.hasFieldBeenValidated?.(prop)) {
          dynamicFormFiles?.validateField?.(prop);
        }
        message.success(
          mediaType === MEDIA_TYPES.VIDEO ? '视频上传成功' : '图片上传成功',
        );
      },
    });
  };

  /**
   * @description 处理文件删除事件（基于索引）。
   * @param params - FormEventParams['fileDelete'] 文件删除事件参数，包含 prop 和 index。
   * @param dynamicFormFiles - DynamicFileFormInstance 动态表单文件部分的引用。
   */
  const handleFileDelete = (
    params: FormEventParams['fileDelete'],
    dynamicFormFiles: DynamicFileFormInstance,
  ) => {
    const { index, prop } = params;
    const currentFiles: FileInfo[] =
      (dynamicFormFiles.getFormData()[prop] as FileInfo[]) || [];

    if (index >= 0 && index < currentFiles.length) {
      currentFiles.splice(index, 1);
      dynamicFormFiles.setFormData({
        [prop]: [...currentFiles],
      });
      // 如果字段已经被验证过（用户之前点击过生成按钮），则重新验证以清除错误状态
      // 如果字段未被验证过，则不触发验证，避免过早添加triggerAuto
      if (dynamicFormFiles?.hasFieldBeenValidated?.(prop)) {
        dynamicFormFiles?.validateField?.(prop);
      }
      // message.success('文件已删除');
    } else {
      message.warning('尝试删除的文件索引无效');
    }
  };

  /**
   * @description 处理素材替换事件（基于索引）。
   * @param params - FormEventParams['replace'] 文件替换事件参数，包含 prop, index, 和 mediaType。
   * @param dynamicFormFiles - DynamicFileFormInstance 动态表单文件部分的引用。
   */
  const handleReplace = (
    params: FormEventParams['replace'],
    dynamicFormFiles: DynamicFileFormInstance,
  ) => {
    const { index, prop, mediaType } = params;
    const currentFiles: FileInfo[] =
      (dynamicFormFiles.getFormData()[prop] as FileInfo[]) || [];

    if (index >= 0 && index < currentFiles.length) {
      showMaterialBasicUpload({
        title: mediaType === MEDIA_TYPES.VIDEO ? '添加视频' : '添加图片',
        maxChosenFileCount: 1, // 替换操作只能选择一个文件
        isVideo: mediaType === MEDIA_TYPES.VIDEO,
        onConfirm: (confirmedFiles?: MaterialUploadFile[]) => {
          if (!confirmedFiles?.length) {
            // 用户取消选择，不做任何操作，保持原文件
            return;
          }

          // 用户选择了新文件，进行替换
          const newMaterialFile = confirmedFiles[0];
          const convertedFile = convertMaterialFileToFileInfo(
            newMaterialFile,
            mediaType,
          );

          // 替换指定位置的文件
          const updatedFiles = [...currentFiles];
          updatedFiles[index] = convertedFile;

          dynamicFormFiles.setFormData({
            [prop]: updatedFiles,
          });

          // 如果字段已经被验证过（用户之前点击过生成按钮），则重新验证以清除错误状态
          // 如果字段未被验证过，则不触发验证，避免过早添加triggerAuto
          if (dynamicFormFiles?.hasFieldBeenValidated?.(prop)) {
            dynamicFormFiles?.validateField?.(prop);
          }
          message.success(
            mediaType === MEDIA_TYPES.VIDEO ? '视频替换成功' : '图片替换成功',
          );
        },
      });
    } else {
      message.error('未找到需要替换的素材或索引无效');
    }
  };

  return {
    handleUploadClick,
    handleFileDelete,
    handleReplace,
  };
}
