import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { mount } from '@vue/test-utils';
import ProjectHeader from '../index.vue';
import { FIRST_STEP_INDEX } from '@/views/EditProjectView/constants';
import { eventBus, EVENT_NAMES } from '@/utils/eventBus';

// Mock 导航常量
vi.mock('@/constants/navigation', () => ({
  PAGE_SOURCE: {
    TEMPLATE: 'template',
    PROJECT: 'project',
  },
  PAGE_SOURCE_RETURN_PATH: {
    template: '/template',
    project: '/project',
  },
}));

// Mock Vue Router
const mockPush = vi.fn();
const mockRouter = {
  push: mockPush,
};

const mockRoute = {
  query: {
    from: 'template',
  },
};

// Mock getCurrentInstance
vi.mock('vue', async () => {
  const actual = await vi.importActual('vue');
  return {
    ...actual,
    getCurrentInstance: () => ({
      proxy: {
        $router: mockRouter,
        $route: mockRoute,
      },
    }),
  };
});

// Mock window.confirm
const mockConfirm = vi.fn();
Object.defineProperty(window, 'confirm', {
  value: mockConfirm,
  writable: true,
});

describe('ProjectHeader 数据保护功能 (EventBus)', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    eventBus.clear(); // 清除所有事件监听器
  });

  afterEach(() => {
    vi.restoreAllMocks();
    eventBus.clear();
  });

  it('应该通过 eventBus 正确检查未保存状态', async () => {
    // 模拟 FirstStep 组件的响应
    eventBus.on(EVENT_NAMES.CHECK_UNSAVED_CHANGES, (data: any) => {
      if (data.currentStep === FIRST_STEP_INDEX) {
        eventBus.emit(EVENT_NAMES.UNSAVED_CHANGES_RESPONSE, {
          requestId: data.requestId,
          hasUnsavedChanges: true,
        });
      }
    });

    const wrapper = mount(ProjectHeader as any, {
      propsData: {
        currentStep: FIRST_STEP_INDEX,
        saveLoading: false,
      },
      stubs: {
        'fa-button': true,
        'Icon': true,
        'NavProject': true,
      },
    });

    // 获取组件实例
    const vm = wrapper.vm as any;

    // 测试异步的 checkUnsavedChanges 方法
    const result = await vm.checkUnsavedChanges();
    expect(result).toBe(true);
  });

  it('应该在第一步且有未保存更改时显示确认对话框', async () => {
    // 模拟 FirstStep 组件响应有未保存更改
    eventBus.on(EVENT_NAMES.CHECK_UNSAVED_CHANGES, (data: any) => {
      if (data.currentStep === FIRST_STEP_INDEX) {
        eventBus.emit(EVENT_NAMES.UNSAVED_CHANGES_RESPONSE, {
          requestId: data.requestId,
          hasUnsavedChanges: true,
        });
      }
    });

    mockConfirm.mockReturnValue(false); // 用户选择取消

    const wrapper = mount(ProjectHeader as any, {
      propsData: {
        currentStep: FIRST_STEP_INDEX,
        saveLoading: false,
      },
      stubs: {
        'fa-button': true,
        'Icon': true,
        'NavProject': true,
      },
    });

    // 获取组件实例并直接调用 goHome 方法
    const vm = wrapper.vm as any;
    await vm.goHome();

    // 验证确认对话框被调用
    expect(mockConfirm).toHaveBeenCalledWith(
      '您有未保存的更改，确定要离开吗？离开后将丢失未保存的内容。',
    );

    // 验证路由跳转没有被调用（用户取消了）
    expect(mockPush).not.toHaveBeenCalled();
  });

  it('应该在用户确认后执行路由跳转', async () => {
    // 模拟 FirstStep 组件响应有未保存更改
    eventBus.on(EVENT_NAMES.CHECK_UNSAVED_CHANGES, (data: any) => {
      if (data.currentStep === FIRST_STEP_INDEX) {
        eventBus.emit(EVENT_NAMES.UNSAVED_CHANGES_RESPONSE, {
          requestId: data.requestId,
          hasUnsavedChanges: true,
        });
      }
    });

    mockConfirm.mockReturnValue(true); // 用户选择确认

    const wrapper = mount(ProjectHeader as any, {
      propsData: {
        currentStep: FIRST_STEP_INDEX,
        saveLoading: false,
      },
      stubs: {
        'fa-button': true,
        'Icon': true,
        'NavProject': true,
      },
    });

    // 获取组件实例并直接调用 goHome 方法
    const vm = wrapper.vm as any;
    await vm.goHome();

    // 验证确认对话框被调用
    expect(mockConfirm).toHaveBeenCalledWith(
      '您有未保存的更改，确定要离开吗？离开后将丢失未保存的内容。',
    );

    // 验证路由跳转被调用
    expect(mockPush).toHaveBeenCalledWith('/template');
  });

  it('应该在没有未保存更改时直接执行路由跳转', async () => {
    // 模拟 FirstStep 组件响应没有未保存更改
    eventBus.on(EVENT_NAMES.CHECK_UNSAVED_CHANGES, (data: any) => {
      if (data.currentStep === FIRST_STEP_INDEX) {
        eventBus.emit(EVENT_NAMES.UNSAVED_CHANGES_RESPONSE, {
          requestId: data.requestId,
          hasUnsavedChanges: false,
        });
      }
    });

    const wrapper = mount(ProjectHeader as any, {
      propsData: {
        currentStep: FIRST_STEP_INDEX,
        saveLoading: false,
      },
      stubs: {
        'fa-button': true,
        'Icon': true,
        'NavProject': true,
      },
    });

    // 获取组件实例并直接调用 goHome 方法
    const vm = wrapper.vm as any;
    await vm.goHome();

    // 验证确认对话框没有被调用
    expect(mockConfirm).not.toHaveBeenCalled();

    // 验证路由跳转被调用
    expect(mockPush).toHaveBeenCalledWith('/template');
  });

  it('应该在非第一步时直接执行路由跳转', async () => {
    // 不需要模拟 eventBus 响应，因为非第一步不会发送检查请求

    const wrapper = mount(ProjectHeader as any, {
      propsData: {
        currentStep: 1, // 第二步
        saveLoading: false,
      },
      stubs: {
        'fa-button': true,
        'Icon': true,
        'NavProject': true,
      },
    });

    // 获取组件实例并直接调用 goHome 方法
    const vm = wrapper.vm as any;
    await vm.goHome();

    // 验证确认对话框没有被调用（因为不是第一步）
    expect(mockConfirm).not.toHaveBeenCalled();

    // 验证路由跳转被调用
    expect(mockPush).toHaveBeenCalledWith('/template');
  });

  it('应该在没有 eventBus 响应时超时并直接执行路由跳转', async () => {
    // 不模拟任何 eventBus 响应，测试超时机制

    const wrapper = mount(ProjectHeader as any, {
      propsData: {
        currentStep: FIRST_STEP_INDEX,
        saveLoading: false,
      },
      stubs: {
        'fa-button': true,
        'Icon': true,
        'NavProject': true,
      },
    });

    // 获取组件实例并直接调用 goHome 方法
    const vm = wrapper.vm as any;
    await vm.goHome();

    // 验证确认对话框没有被调用（因为超时默认为无未保存更改）
    expect(mockConfirm).not.toHaveBeenCalled();

    // 验证路由跳转被调用
    expect(mockPush).toHaveBeenCalledWith('/template');
  });
});
