## 使用接口 mock

**如何开启/关闭**

1. 打开项目根目录下的 `.env.development.local` 文件。
2. 找到 `VITE_USE_MOCK` 配置项：

- 设置为 `true`：开启接口 mock 功能。
- 设置为 `false`：关闭接口 mock 功能。

3. 保存文件后，vite 会自动重启项目并应用新的配置。

**如何新增 mock 接口**

1. 在 `src/mock` 目录下找到或创建对应的模块文件，例如 `user.ts`。
2. 在模块文件中，按照以下格式新增接口：

```typescript
import { MockMethod } from 'vite-plugin-mock';

const mocks: MockMethod[] = [
  {
    url: '/api/user/login',
    method: 'post',
    response: ({ body }) => {
      const { username, password } = body;
      if (username === 'admin' && password === '123456') {
        return {
          code: 0,
          message: '登录成功',
          data: { token: 'mock-token' },
        };
      } else {
        return {
          code: 1,
          message: '用户名或密码错误',
        };
      }
    },
  },
];

export default mocks;
```
