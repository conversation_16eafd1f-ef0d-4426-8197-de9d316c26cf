---
description: Front-end Code Review Guidelines and Key Concerns
globs: src/**/*
alwaysApply: false
---

```yaml
rules:
  review_process:
    output_format:
      severity_indicators:
        - prefix: "🚨 严重问题" # 安全漏洞、硬编码等
        - prefix: "⚠️ 业务风险" # 业务逻辑问题
        - prefix: "🔍 边界场景" # 边界条件处理
        - prefix: "⚡ 性能问题" # 性能相关
        - prefix: "📝 代码规范" # 代码风格问题

      status_markers:
        pass: "✅ 通过"
        fail: "❌ 不通过"
        warning: "⚠️ 警告"

    review_priorities:
      1_security_critical:
        xss_prevention:
          - HTML内容净化
          - 用户输入验证
          - innerHTML安全使用
          - 脚本注入防护
          - URL净化处理
          - 文件上传验证

        csrf_protection:
          - CSRF令牌实现
          - 同源策略检查
          - Cookie安全配置
          - 跨域请求处理

        hard_coding_check:
          - API端点硬编码
          - 敏感凭证
          - 配置值
          - 业务常量

      2_business_risks:
        user_interaction:
          - 防抖/节流实现
          - 重复点击防护
          - 表单重复提交防护
          - 异步操作状态管理

        data_flow:
          - 数据转换准确性
          - 状态管理一致性
          - API响应处理
          - 错误状态管理

      3_edge_cases:
        input_validation:
          - 空值处理
          - 特殊字符处理
          - 极值处理
          - 非法格式处理

        error_scenarios:
          - 网络故障处理
          - 超时处理
          - API错误处理
          - 服务响应异常
          - 浏览器兼容性
          - 设备兼容性
          - 离线模式处理

        state_management:
          - 组件卸载处理
          - 页面导航处理
          - 浏览器刷新处理
          - 会话过期处理
          - 缓存失效处理

      4_performance:
        optimization:
          - 内存使用优化
          - DOM操作优化
          - 重渲染防护
          - 包体积影响
          - 资源加载优化
          - 缓存利用

        resource_management:
          - 内存泄漏检查
          - 事件监听器清理
          - 连接池管理
          - 资源释放

      5_code_quality:
        maintainability:
          - 代码重复检查
          - 函数复杂度
          - 组件组成
          - 类型安全

        readability:
          - 命名规范
          - 文档完整性
          - 代码组织
          - 组件结构

    workflow:
      pre_review:
        - 对照清单自审
        - 自动化测试通过
        - 代码规范检查

      peer_review:
        - 按优先级检查
        - 安全重点关注
        - 业务逻辑验证

      issue_tracking:
        - 问题严重度分类
        - 时间节点分配
        - 定期跟进

    template: |
      ## 代码审查报告

      ### 1. 安全与严重问题
      🚨 {security_findings}

      ### 2. 业务风险
      ⚠️ {business_findings}

      ### 3. 边界场景
      🔍 {edge_case_findings}

      ### 4. 性能问题
      ⚡ {performance_findings}

      ### 5. 代码质量
      📝 {quality_findings}

      ### 结论
      状态: {status_indicator}
      优先级: {priority_level}
      必要操作: {required_actions}
```
