<template>
  <fa-spin :spinning="spinning" wrapperClassName="spin100vh">
    <div class="acct-info-page">
      <div class="acct-info-page__header">
        <p class="acct-info-page__header-name">
          {{ acctInfo.aAcct }}
        </p>
        <VersionIcon :version="acctInfo.version" type="medium" />
      </div>

      <div class="acct-info-page__item">
        <p class="acct-info-page__item-label">到期时间</p>
        <p class="acct-info-page__item-value">{{ formatExpireTime }}</p>
        <fa-button
          type="link"
          class="acct-info-page__link-button"
          @click="handleRenew"
          >{{ upgradeText }}</fa-button
        >
      </div>

      <div class="acct-info-page__item">
        <p class="acct-info-page__item-label">剩余创作点数</p>
        <div>
          <div class="flex">
            <p class="acct-info-page__item-value">{{ acctInfo.point }}</p>
            <fa-button
              type="link"
              class="acct-info-page__link-button"
              @click="showRecharge"
              >去充值</fa-button
            >
            <fa-button
              type="link"
              class="acct-info-page__link-button"
              @click="showDetail"
              >流水明细</fa-button
            >
          </div>
          <RemainPointDetail />
        </div>
      </div>
    </div>
  </fa-spin>
</template>

<script lang="ts" setup>
import { ref, computed } from 'vue';
import store from '@/store';
import { IS_FREE_VERSION } from '@/constants/version.ts';
import { getExternalDynamicUrl } from '@/constants/system';
import VersionIcon from '@/components/comm/ScVersionIcon.vue';
import RemainPointDetail from './RemainPointDetail.vue';

const acctInfo = computed(() => store.state.user);

/** 是否显示loading */
const spinning = ref(false);

const emit = defineEmits<{
  (e: 'showDetail'): void;
}>();

/**
 * @description 格式化账户到期时间
 * @returns {string} 格式化后的日期字符串，如果到期时间不存在则返回 '-'
 */
const formatExpireTime = computed(() => {
  return acctInfo.value?.expireTime
    ? new Date(acctInfo.value.expireTime).toLocaleDateString()
    : '无';
});

/**
 * @description 根据账户版本判断升级或续费文本
 * @returns {string} '升级' 或 '续费'
 */
const upgradeText = computed(() => {
  return IS_FREE_VERSION(acctInfo.value?.version) ? '升级' : '续费';
});

/**
 * @description 处理续费操作
 */
const handleRenew = () => {
  window.open(getExternalDynamicUrl().VERSION_BUY, '_blank');
};

/**
 * @description 显示充值弹窗
 */
const showRecharge = () => {
  window.open(getExternalDynamicUrl().VERSION_BUY, '_blank');
};

/**
 * @description 显示流水明细
 */
const showDetail = () => {
  emit('showDetail');
};

/**
 * @description 更新点数
 */
const updatePoint = () => {
  store.dispatch('updatePoints');
};

updatePoint();
</script>

<style lang="scss" scoped>
.acct-info-page {
  /* 布局相关 */
  @apply mt-24px;
  /* 外观相关 */
  @apply bg-[#fafafa] rounded-8px;
  /* 尺寸相关 */
  @apply p-32px;
}

.acct-info-page__header {
  /* 布局相关 */
  @apply flex items-center;
}

.acct-info-page__header-name {
  /* 文字相关 */
  @apply text-16px font-bold text-#333333;
  /* 尺寸相关 */
  @apply mr-11px;
}

.acct-info-page__item {
  /* 布局相关 */
  @apply flex;
  /* 尺寸相关 */
  @apply mt-24px;
}

.acct-info-page__item + .acct-info-page__item {
  /* 布局相关 */
  @apply mt-16px;
}

.acct-info-page__item-label {
  /* 尺寸相关 */
  @apply w-84px mr-24px;
  /* 文字相关 */
  @apply text-14px text-#999999;
}

.acct-info-page__item-value {
  /* 文字相关 */
  @apply text-14px text-#333333;
}

.acct-info-page__link-button {
  /* 尺寸相关 */
  @apply p-0px h-19px;
  /* 文字相关 */
  @apply text-primary;
  /* 布局相关 */
  @apply ml-24px;
  &:hover {
    @apply text-secondary;
  }
}

::v-deep {
  .fa-table-skin__specification--form-page .fa-table {
    border-radius: 8px;
    overflow: hidden;
  }
}
</style>
