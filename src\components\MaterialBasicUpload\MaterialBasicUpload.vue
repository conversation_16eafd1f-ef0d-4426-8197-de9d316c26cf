<template>
  <div>
    <fa-basic-upload
      class="FaBasicUpload"
      ref="FaBasicUploadRef"
      v-model="visible"
      :modalOptions="modalOptions"
      :title="title"
      :sub-title="`(只能添加${acceptList.join(',')})`"
      :default-owner-file-view-type="ownerFileViewType"
      :use-owner-file-view-type-switch="true"
      :file-preview-key="filePreviewKey"
      :file-key="INFO_KEYS_TMP.ID"
      :folder-key="INFO_KEYS_TMP.ID"
      :file-type-key="fileTypeNameKey"
      :file-label-key="INFO_KEYS_TMP.NAME"
      :file-size-key="INFO_KEYS_TMP.FILE_SIZE"
      :file-time-key="INFO_KEYS_TMP.CREATE_TIME"
      :folder-label-key="INFO_KEYS_TMP.NAME"
      :folder-size-key="folderSizeKey"
      :folder-time-key="INFO_KEYS_TMP.CREATE_TIME"
      :file-folder-key="INFO_KEYS_TMP.FOLDER_ID"
      :folder-parent-key="INFO_KEYS_TMP.PARENT_ID"
      :folder-list="showFolderList"
      :root-folder="rootFolder"
      :file-list="showFileList"
      :chosen-file-list="chosenFileList"
      :tab-props="tabProps"
      :tab-pane-list="tabPaneList"
      :show-file-desc="showFileDesc"
      :loading="loading"
      :sort-by="sortBy"
      :sort-mode="sortMode"
      :setting="setting"
      :max-chosen-file-count="maxChosenFileCount"
      :file-percent-key="INFO_KEYS_TMP.FILE_PERCENT"
      :current-folder="currentFolder"
      owner-file-search-placeholder="搜索当前文件夹"
      :owner-file-search-keywords="ownerFileSearchKeywords"
      :capacity-used-size="capacityUsedSize"
      :capacity-total-size="capacityTotalSize"
      :show-capacity="showCapacity"
      :owner-file-page-size="ownerFilePageSize"
      :owner-file-page-current="ownerFilePageCurrent"
      :owner-file-page-total="ownerFilePageTotal"
      :owner-file-force-show-pagination="false"
      :public-file-force-show-pagination="false"
      :dropdown-props="dropdownProps"
      :popover-props="popoverProps"
      :selectable="true"
      @file-label-change="onFileLabelChange"
      @folder-label-change="onFolderLabelChange"
      @trigger-upload="onTriggerUpload"
      @add-folder="onAddFolder"
      @owner-file-search="onOwnerFileSearch"
      @folder-close="onFolderClose"
      @folder-choose="onFolderChoose"
      @file-close="onFileClose"
      @file-choose="onFileChoose"
      @file-unchoose="onFileUnchoose"
      @select-change="onSelectChange"
      @folder-drop="onFolderDrop"
      @chosen-file-list-sort="onChoosedFileListSort"
      @popup-ok="onPopupOk"
      @popup-cancel="onPopupCancel"
      @popup-close="onPopupCancel"
      @popup-delete="onPopupDelete"
      @sort-by-change="onSortByChange"
      @sort-mode-change="onSortModeChange"
      @setting-ok="onSettingOk"
      @capacity-expand="onCapacityExpand"
      @owner-file-page-change="onOwnerFilePageChange"
      @scroll-end="onScrollEnd"
    >
      <!-- 顶部按钮 -->
      <template #owner-file-toolbar-group-left>
        <BasicUploadToolbarUiUpload
          :disabled="!!ownerFileSearchKeywords || fileUploading"
          @click="onTriggerUpload"
        ></BasicUploadToolbarUiUpload>
        <BasicUploadToolbarUiFolder
          :disabled="!!ownerFileSearchKeywords || fileUploading"
          @click="onAddFolder"
        ></BasicUploadToolbarUiFolder>
      </template>
      <!-- 自定义"我的文件"文件编辑栏 -->
      <template #owner-file-view-ui-file-layer="{ data }">
        <template v-if="checkFileUploading(data)">
          <ProgressPending :percent="data[INFO_KEYS_TMP.FILE_PERCENT]" />
        </template>
        <fa-basic-upload-view-ui-file-layer
          :active="checkFileChosen(data)"
          class="demo-basic-upload--owner-file-view-ui-file-layer"
          @click.native.stop="onFileChoose(data)"
        >
          <fa-basic-upload-view-ui-file-layer-item-close
            @click.native.stop="onFileClose(data)"
          ></fa-basic-upload-view-ui-file-layer-item-close>
          <fa-basic-upload-view-ui-file-layer-item-tick
            v-if="checkFileChosen(data)"
            class="demo-basic-upload--owner-file-view-ui-file-layer-item-tick"
          ></fa-basic-upload-view-ui-file-layer-item-tick>
          <fa-basic-upload-view-ui-file-toolbar
            v-if="checkOwnerFileImgType(data) && !checkFileUploading(data)"
          >
            <fa-basic-upload-view-ui-file-toolbar-item
              @click="onFilePreview(data)"
            >
              <fa-designer-icon type="preview"></fa-designer-icon>
            </fa-basic-upload-view-ui-file-toolbar-item>
          </fa-basic-upload-view-ui-file-toolbar>
          <!-- 视频播放按钮 -->
          <VideoPlayToolbar
            v-if="checkVideoType(data)"
            :file="data"
            :btnSize="16"
          />
        </fa-basic-upload-view-ui-file-layer>
      </template>

      <!-- "我的文件"文件内容 -->
      <template #owner-file-view-ui-file-view="{ data }">
        <!-- 自定义视频的封面 -->
        <CoverVideo
          v-if="checkVideoCover(data)"
          :file="data"
          :maxWidth="72"
          :imgStyle="{ width: '72px', height: '72px' }"
        />
      </template>

      <!-- 右边栏文件内容 -->
      <template #file-slider-view="{ data }">
        <CoverVideo
          v-if="checkVideoCover(data)"
          :file="data"
          :maxWidth="72"
          :imgStyle="{ width: '72px', height: '72px' }"
        />
      </template>

      <!-- 自定义"我的图片"无数据 -->
      <template slot="owner-file-empty">
        <div class="basic-upload--empty">
          <fa-empty
            :image="{
              PRESENTED_IMAGE_SIMPLE: true,
            }"
          />
        </div>
      </template>
    </fa-basic-upload>
  </div>
</template>

<script lang="ts" setup>
import { ref, defineProps, defineEmits, defineExpose } from 'vue';
import type { PropType } from 'vue';
import VideoPlayToolbar from './components/VideoPlayToolbar.vue';
import CoverVideo from './components/CoverVideo.vue';
import { useMaterialBasicUpload } from './hook/useMaterialBasicUpload';
import { INFO_KEYS } from '@/constants/material';
import type { MaterialUploadFile } from './types';
import { FaBasicUploadInstance } from './hook/useMaterialBasicUploadUtils';
import BasicUploadToolbarUiFolder from './components/BasicUploadToolbarUiFolder.vue';
import BasicUploadToolbarUiUpload from './components/BasicUploadToolbarUiUpload.vue';
import ProgressPending from './components/ProgressPending.vue';
import {
  checkFileUploading,
  checkVideoType,
  checkVideoCover,
} from './utils/index.ts';

/**
 * MaterialBasicUpload 组件 Props
 * @property visible 是否显示弹窗
 * @property maxChosenFileCount 最大可选文件数
 * @property title 弹窗标题
 * @property isVideo 是否为视频上传
 * @property onConfirm 确认回调
 */
const props = defineProps({
  /** 是否显示弹窗 */
  visible: {
    type: Boolean,
    default: false,
  },
  /** 最大可选文件数 */
  maxChosenFileCount: {
    type: Number,
    default: 1,
  },
  /** 弹窗标题 */
  title: {
    type: String,
    default: '',
  },
  /** 是否为视频上传 */
  isVideo: {
    type: Boolean,
    default: false,
  },
  /** 确认回调 */
  onConfirm: {
    type: Function as unknown as PropType<
      (files: MaterialUploadFile[]) => void
    >,
    default: undefined,
  },
});

/**
 * MaterialBasicUpload 组件 Emits
 * @event update:visible 更新弹窗可见性
 * @event close 关闭弹窗
 * @event file-choose-over 文件选择完成
 */
const emit = defineEmits(['update:visible', 'close', 'file-choose-over']);

// 静态值
const INFO_KEYS_TMP = ref<typeof INFO_KEYS>(INFO_KEYS); // 临时变量，用于文件列表的key
const folderSizeKey = ref<string>('folderSize'); // 后端没返回文件夹大小，先用临时字段代替，文件夹大小不进行排序
const filePreviewKey = ref<string>('url'); // 文件预览key
const fileTypeNameKey = ref<string>('suffix'); // 文件类型名称key

// 添加 FaBasicUploadRef
const FaBasicUploadRef = ref<FaBasicUploadInstance | null>(null);

// 使用 composable 函数
const composableResult = useMaterialBasicUpload(props, emit, FaBasicUploadRef);

// 解构出所有需要的变量，使模板中的变量可用
const {
  rootFolder,
  chosenFileList,
  tabProps,
  ownerFileViewType,
  tabPaneList,
  showFileDesc,
  loading,
  sortBy,
  sortMode,
  setting,
  currentFolder,
  ownerFileSearchKeywords,
  capacityUsedSize,
  capacityTotalSize,
  ownerFilePageSize,
  ownerFilePageCurrent,
  ownerFilePageTotal,
  dropdownProps,
  popoverProps,
  fileUploading,
  acceptList,
  modalOptions,
  showFileList,
  showFolderList,
  showCapacity,
  onScrollEnd,
  onTriggerUpload,
  onAddFolder,
  onOwnerFileSearch,
  onFolderClose,
  onFolderChoose,
  onFileClose,
  onFileChoose,
  onFileUnchoose,
  onFilePreview,
  onSelectChange,
  onFolderDrop,
  onChoosedFileListSort,
  onPopupOk,
  onPopupCancel,
  onPopupDelete,
  onSortByChange,
  onSortModeChange,
  onSettingOk,
  onFileLabelChange,
  onFolderLabelChange,
  checkOwnerFileImgType,
  onCapacityExpand,
  onOwnerFilePageChange,
  checkFileChosen,
} = composableResult;

// 暴露变量给模板使用
defineExpose(composableResult);
</script>

<style lang="scss" scoped>
// 重置数据为空时
.basic-upload--empty {
  @apply h-full flex flex-col items-center justify-center;
}
:deep(.upload-progress--text) {
  @apply text-[13px];
}
</style>
<style lang="scss">
// 重置弹窗样式
.FaBasicUpload {
  // 修改圆角
  .fa-popup {
    @apply rounded-[16px];
  }
  // 结合下面ml使用保证内容居中
  .fa-tabs .fa-tabs-left-content {
    @apply p-[0];
  }
  // 内容保持居中，同时保证最右边素材的删除按钮不会被裁剪
  .fa-basic-upload-owner-file--content {
    @apply ml-[40px];
  }
  // 右侧已选择列表的文字保证完整展示
  .fa-basic-upload-slider-file-list-item--desc,
  .fa-basic-upload-file-view-list-item--desc {
    @apply h-[20px];
  }
  // 修改圆角
  .fa-basic-upload-view-ui-upload,
  .fa-basic-upload-view-ui-file,
  .fa-basic-upload-view-ui-file-layer--content,
  .fa-basic-upload-view-ui-file-img {
    @apply rounded-[8px] border-[#d9d9d9];
  }
  // 修改圆角
  .fa-basic-upload-toolbar-ui-search {
    .fa-input {
      @apply rounded-[6px];
    }
  }
  // 重置按钮样式
  .fa-basic-upload-popup {
    .fa-btn {
      @apply h-[32px] flex items-center justify-center rounded-[6px];
      &:nth-child(2) {
        @apply hover:(text-title border-assist);
      }
    }
    .fa-basic-upload-view-ui-folder {
      .anticon {
        @apply vertical-top;
      }
    }
    .fa-spin-spinning {
      @apply absolute;
    }
  }
  // 隐藏设置按钮、隐藏页码
  .fa-basic-upload-toolbar-ui-setting,
  .fa-basic-upload-file-view-table--footer {
    @apply hidden;
  }
  .fa-table-header-column {
    .fa-table-selection {
      @apply hidden;
    }
  }
  .fa-basic-upload-toolbar-ui-upload .fa-btn[disabled]:hover .anticon,
  .fa-basic-upload-toolbar-ui-folder .fa-btn[disabled]:hover .anticon,
  .fa-basic-upload-toolbar-ui-upload .fa-btn[disabled]:active .anticon,
  .fa-basic-upload-toolbar-ui-folder .fa-btn[disabled]:active .anticon {
    @apply text-assist;
  }
  .fa-basic-upload-view-ui-file-toolbar:hover {
    .fa-basic-upload-view-ui-file-toolbar--mask {
      opacity: 1;
    }
    .anticon {
      @apply color-[#fff];
    }
  }
}
</style>
