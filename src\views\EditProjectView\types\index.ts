/**
 * @fileoverview 动态表单系统类型定义统一导出
 * @description 提供所有表单相关类型的统一导出入口，简化类型导入
 * <AUTHOR>
 * @since 1.0.0
 */

// ============= 核心类型导出 =============

/**
 * @description 导出基础类型和枚举，包括项目类型、字段类型、媒体类型、文件状态、表单布局等基础定义
 * @see ./base.ts 基础类型定义文件
 */
export * from './base';

/**
 * @description 导出表单系统完整类型，包括表单验证规则、表单项定义、表单实例接口、表单事件等
 * @see ./form.ts 表单系统类型定义文件
 */
export * from './form';

/**
 * @description 导出文件处理相关类型，包括文件信息、文件上传配置、文件处理器等
 * @see ./file.ts 文件处理相关类型定义文件
 */
export * from './file';

/**
 * @description 导出高级工具类型，包括类型过滤、条件类型、类型断言等实用工具类型
 * @see ./utils.ts 高级工具类型定义文件
 */
export * from './utils';

/**
 * @description 导出路由相关类型，包括路由查询参数、路由实例、路由信息等
 * @see ./router.ts 路由相关类型定义文件
 */
export * from './router';

// ============= 分类后的业务类型导出 =============

/**
 * @description 导出媒体相关类型，包括脚本数据、背景音乐设置、配音设置、资源信息等
 * @see ./media.ts 媒体相关类型定义文件
 */
export * from './media';

/**
 * @description 导出项目数据相关类型，包括项目数据接口、预览参数等
 * @see ./project.ts 项目数据相关类型定义文件
 */
export * from './project';

/**
 * @description 导出组件相关类型，包括组件引用、组件形状、组合式API返回值类型等
 * @see ./components.ts 组件相关类型定义文件
 */
export * from './components';
