<template>
  <fa-spin :spinning="spinning" wrapperClassName="spin100vh">
    <div class="works-list-container">
      <!-- 筛选区域 -->
      <div class="work-list-header mb-[24px]">
        <div class="flex items-center">
          <SearchInput
            placeholder="请输入作品名称"
            class="w-[200px] mr-[12px]"
            @search="handleSearchText"
          />

          <fa-select
            :value="selectedProject"
            placeholder="全部创作项目"
            showSearch
            size="large"
            :filterOption="false"
            @select="handleSelectProject"
            @search="handleSearchSelectText"
            @dropdownVisibleChange="handleSelectDropdownVisibleChange"
            @popupScroll="handleSelectPopupScroll"
          >
            <fa-select-option
              v-for="item in projectOptions"
              :key="item.id"
              :value="item.id"
              >{{ item.name }}</fa-select-option
            >
          </fa-select>

          <fa-range-picker
            class="date-picker"
            :showTime="false"
            :placeholder="['开始日期', '结束日期']"
            size="large"
            @change="handleDateChange"
          />
        </div>
        <RefreshBtn @refresh="fetchTableData" />
      </div>

      <!-- 表格区域 -->
      <fa-table
        :columns="columns"
        :data-source="tableData"
        skin-type="icon-base"
        row-key="id"
        :pagination="pagination.total > 0 ? pagination : false"
        :customRow="handleRowEvents"
        class="work-list-table"
        @change="handleTableChange"
      >
        <template #name="record">
          <div class="relative flex items-center pr-[28px]">
            <div
              class="work-pic-wrap"
              :class="{ 'work-generating': record.isGenerating }"
            >
              <!-- 生成失败 -->
              <img
                v-if="record.hadFailWork"
                class="work-pic"
                src="@/assets/ProjectView/fail.webp"
                alt="重新生成失败"
              />
              <!-- 生成成功 -->
              <ScImg
                v-else
                class="work-pic"
                fit="cover"
                :src="record.coverImg"
                :maxWidth="64"
              >
                <template slot="error">
                  <img
                    src="@/assets/common/imgEmpty.webp"
                    alt="默认封面"
                    class="w-full h-full object-cover"
                  />
                </template>
              </ScImg>

              <div v-if="record.isGenerating" class="generating-status">
                重新生成中
              </div>
            </div>
            <div class="flex-1">
              <span>{{ record.name }}</span>
              <img
                v-if="isShowNewGeneratedTag(record)"
                src="@/assets/WorkView/newGenerated.svg"
                alt="新生成"
                class="inline-block w-[42px] ml-[6px]"
              />
              <fa-tooltip
                v-if="record.hadFailWork"
                :overlayStyle="{ maxWidth: '440px' }"
              >
                <template slot="title">
                  <div class="text-[#fff] text-[14px] text-center">
                    <div>
                      重新生成作品失败（作品ID：{{ record.id }}）{{
                        PROJECT_TYPE.VIDEO === record.type
                          ? '，创作点数已自动返还'
                          : ''
                      }}。失败原因：{{ record.data.errMsg }}，请咨询客服<span
                        v-if="isVideoWork(record)"
                        class="ml-[8px] underline underline-offset-2px cursor-pointer hover:font-bold"
                        @click="handleFailedStateRestore(record)"
                        >恢复旧作品</span
                      >
                    </div>
                  </div>
                </template>
                <img
                  src="@/assets/EditProject/xinxishizhi.svg"
                  alt="信息提示"
                  class="inline-block w-[16px] h-[16px] mt-[-1px] ml-[8px] cursor-pointer"
                />
              </fa-tooltip>
            </div>

            <EditNamePopconfirm
              v-show="
                hoveredRowKey === record.id || editConfirmVisible[record.id]
              "
              inputLabel="作品名称"
              class="absolute right-[5px]"
              :itemId="record.id"
              :initialValue="record.name"
              :visible.sync="editConfirmVisible[record.id]"
              @confirm="handleUpdateWorkName"
            />
          </div>
        </template>

        <!-- 所属项目列 -->
        <template #project="record">
          <div
            v-if="isUnrelatedProject(record.projectId)"
            class="text-disabledText"
          >
            所属项目已被删除
          </div>
          <div v-else class="project-item" @click="handleViewTemplate(record)">
            <span class="project-text">{{ record.projectName }}</span>
            <Icon type="jiantou_you" class="moreIcon"></Icon>
          </div>
        </template>

        <!-- 操作列 -->
        <template #action="record">
          <div class="btn-group flex gap-[16px]">
            <fa-button type="link" @click="handlePreview(record)"
              >预览</fa-button
            >
            <fa-button
              type="link"
              @click="handleEdit(record)"
              :class="{ disabled: disabledEdit(record) }"
              >编辑</fa-button
            >
            <template v-if="!disabledDelete(record)">
              <!-- 删除按钮需要禁用, 点击不能出popconfirm小弹窗，这里需要区分开两个删除按钮节点，不然popconfirm会衍生出很多问题 -->
              <fa-popconfirm
                v-model="deleteConfirmVisible[record.id]"
                title="删除后无法恢复，你确定要删除这个作品吗？"
                :align="{ offset: [0, 10] }"
                placement="topRight"
                :getPopupContainer="getPopupContainer"
                overlayClassName="delete-popover"
                okType="danger"
                @confirm.stop="handleDelete(record)"
                @cancel.stop="deleteConfirmVisible[record.id] = false"
              >
                <fa-button type="link">删除</fa-button>
              </fa-popconfirm>
            </template>
            <fa-button
              v-else
              type="link"
              :class="{ disabled: disabledDelete(record) }"
              @click="handleClickDisabledDeleteBtn(record)"
              >删除</fa-button
            >
          </div>
        </template>
      </fa-table>

      <!-- 预览弹窗 -->
      <WorkPreviewModal
        :visible="workModalType == MODAL_TYPE_PREVIEW"
        :workId="currentPreviewWorkId"
        @edit="handleEdit"
        @retry="handleFailedStateRestore"
        @close="closeWorkModal"
        @showSelectModal="workModalType = MODAL_TYPE_SELECT"
      />
      <WorkSelectModal
        :visible="workModalType == MODAL_TYPE_SELECT"
        :workId="currentPreviewWorkId"
        @save-new="handleSaveWork"
        @save-all="handleSaveWork"
        @close="closeWorkModal"
      />
    </div>
  </fa-spin>
</template>

<script setup lang="ts">
import { defineProps, defineExpose } from 'vue';
import { useWorkList } from './hook/useWorkList';
import SearchInput from '@/components/ProjectView/SearchInput.vue';
import ScImg from '@/components/comm/ScImg.vue';
import WorkPreviewModal from '@/components/WorkView/WorkModal/WorkPreviewModal.vue';
import WorkSelectModal from '@/components/WorkView/WorkModal/WorkSelectModal.vue';
import EditNamePopconfirm from '@/components/ProjectView/EditNamePopconfirm.vue';
import RefreshBtn from '@/components/ProjectView/RefreshBtn.vue';
import { PROJECT_TYPE } from '@/constants/project';
import {
  MODAL_TYPE_PREVIEW,
  MODAL_TYPE_SELECT,
} from '@/components/WorkView/constants';

interface WorkListProps {
  projectId?: number;
}

const props = defineProps<WorkListProps>();

const composableResult = useWorkList(props);

// 解构出所有需要的变量，使模板中的变量可用
const {
  spinning,
  selectedProject,
  tableData,
  pagination,
  projectOptions,
  deleteConfirmVisible,
  editConfirmVisible,
  hoveredRowKey,
  workModalType,
  currentPreviewWorkId,
  columns,
  handleSelectPopupScroll,
  handleSelectDropdownVisibleChange,
  handleSelectProject,
  handleSearchSelectText,
  disabledEdit,
  disabledDelete,
  handleRowEvents,
  handleSearchText,
  handleDateChange,
  handleTableChange,
  handlePreview,
  handleViewTemplate,
  handleEdit,
  closeWorkModal,
  handleClickDisabledDeleteBtn,
  handleDelete,
  handleFailedStateRestore,
  handleUpdateWorkName,
  handleSaveWork,
  getPopupContainer,
  isUnrelatedProject,
  fetchTableData,
  isVideoWork,
  isShowNewGeneratedTag,
} = composableResult;

defineExpose(composableResult);
</script>

<style lang="scss" scoped>
.works-list-container {
  .work-list-header {
    @apply flex items-center justify-between;

    :deep(.fa-select) {
      @apply mr-[12px] w-[160px] text-[14px];
      .fa-select-selection {
        @apply rounded-[8px];
      }
    }

    :deep(.fa-calendar-picker) {
      @apply w-[220px];
      .fa-calendar-picker-input {
        @apply rounded-[8px] text-[14px];
      }
    }
  }

  .work-list-table {
    min-height: calc(100vh - 310px);
  }

  .project-item {
    @apply cursor-pointer;
    .moreIcon {
      @apply inline-block size-10px ml-[4px] mt-[-2px] text-[#707070];
    }
    &:hover .project-text,
    &:hover .moreIcon {
      @apply text-primary;
    }
  }

  .work-pic-wrap {
    @apply size-[64px] mr-[16px] position-relative;
    .work-pic {
      @apply border border-edge rounded-[8px] size-full object-cover; // 图片设置object-cover确保完全填充容器
    }

    &.work-generating {
      // 内层图片应用模糊滤镜，但其模糊扩散会被外层容器限制，外层容器preview-wrapper设置overflow: hidden来裁剪超出边界的模糊效果
      // 外层容器保持圆角设置rounded-[12px]，确保视觉效果一致
      @apply rounded-[8px] overflow-hidden bg-[#000055];

      .work-pic {
        filter: blur(20px);
        // 应用transform: scale(1.3)轻微放大图片，防止边缘因模糊后出现白边
        transform: scale(1.3);
      }
      .generating-status {
        @apply absolute top-[24px] left-0 w-full text-center text-[11px] text-white;
      }
    }
  }

  .btn-group {
    :deep(.fa-popover) {
      .fa-popover-content {
        @apply w-[280px];
      }
    }

    :deep(.fa-btn-link) {
      @apply p-0;
    }
  }
}
</style>
