/**
 * @fileoverview 无限滚动状态管理的可复用 Composable
 * @description 提供基于状态机的无限滚动状态管理
 */

import { ref, computed, Ref, ComputedRef } from 'vue';

/**
 * 无限加载状态枚举
 */
export enum InfiniteLoadingState {
  /** 空闲状态 */
  IDLE = 'idle',
  /** 首次加载中 */
  LOADING = 'loading',
  /** 加载更多中 */
  LOADING_MORE = 'loading_more',
  /** 加载成功 */
  SUCCESS = 'success',
  /** 加载失败 */
  ERROR = 'error',
  /** 重试中 */
  RETRYING = 'retrying',
  /** 全部加载完成 */
  ALL_LOADED = 'all_loaded',
}

/**
 * 无限加载错误信息
 */
export interface InfiniteLoadingError {
  /** 错误类型 */
  type: 'network' | 'business';
  /** 错误消息 */
  message: string;
  /** 错误代码 */
  code?: number;
  /** 是否可重试 */
  retryable: boolean;
  /** 原始错误对象 */
  original?: unknown;
}

/**
 * 无限加载状态管理器返回值
 */
export interface UseInfiniteLoadingStateReturn {
  /** 当前状态 */
  state: Ref<InfiniteLoadingState>;
  /** 错误信息 */
  error: Ref<InfiniteLoadingError | null>;
  /** 重试次数 */
  retryCount: Ref<number>;
  /** 是否可以加载更多 */
  canLoadMore: ComputedRef<boolean>;
  /** 是否可以重试 */
  canRetry: ComputedRef<boolean>;
  /** 是否正在加载 */
  isLoading: ComputedRef<boolean>;
  /** 是否正在加载更多 */
  isLoadingMore: ComputedRef<boolean>;
  /** 是否有错误 */
  hasError: ComputedRef<boolean>;
  /** 设置加载状态 */
  setLoading: () => void;
  /** 设置加载更多状态 */
  setLoadingMore: () => void;
  /** 设置成功状态 */
  setSuccess: () => void;
  /** 设置错误状态 */
  setError: (error: InfiniteLoadingError) => void;
  /** 设置全部加载完成状态 */
  setAllLoaded: () => void;
  /** 开始重试 */
  startRetry: () => void;
  /** 重置状态 */
  reset: () => void;
}

/**
 * 无限滚动数据管理器返回值
 */
export interface UseInfiniteScrollReturn<T> {
  /** 已加载的所有数据 */
  cachedItems: Ref<T[]>;
  /** 状态管理器 */
  stateManager: UseInfiniteLoadingStateReturn;
  /** 是否已加载全部 */
  hasLoadedAll: ComputedRef<boolean>;
  /** 添加新项目到缓存 */
  addItemsToCache: (newItems: T[]) => void;
  /** 重置缓存 */
  resetCache: () => void;
  /** 触发加载更多 */
  triggerLoadMore: () => void;
  /** 触发重试 */
  triggerRetry: () => void;
}

/**
 * 无限加载状态管理器
 * @param maxRetryCount 最大重试次数
 * @returns 状态管理器
 */
export function useInfiniteLoadingState(
  maxRetryCount = 3,
): UseInfiniteLoadingStateReturn {
  // 状态数据
  const state = ref<InfiniteLoadingState>(InfiniteLoadingState.IDLE);
  const error = ref<InfiniteLoadingError | null>(null);
  const retryCount = ref<number>(0);

  // 计算属性
  const canLoadMore = computed(() => {
    return (
      state.value === InfiniteLoadingState.IDLE ||
      state.value === InfiniteLoadingState.SUCCESS
    );
  });

  const canRetry = computed(() => {
    return (
      state.value === InfiniteLoadingState.ERROR &&
      error.value?.retryable === true &&
      retryCount.value < maxRetryCount
    );
  });

  const isLoading = computed(() => {
    return state.value === InfiniteLoadingState.LOADING;
  });

  const isLoadingMore = computed(() => {
    return (
      state.value === InfiniteLoadingState.LOADING_MORE ||
      state.value === InfiniteLoadingState.RETRYING
    );
  });

  const hasError = computed(() => {
    return state.value === InfiniteLoadingState.ERROR;
  });

  // 状态操作方法
  const setLoading = (): void => {
    state.value = InfiniteLoadingState.LOADING;
    error.value = null;
  };

  const setLoadingMore = (): void => {
    state.value = InfiniteLoadingState.LOADING_MORE;
    error.value = null;
  };

  const setSuccess = (): void => {
    state.value = InfiniteLoadingState.SUCCESS;
    error.value = null;
    retryCount.value = 0;
  };

  const setError = (errorInfo: InfiniteLoadingError): void => {
    state.value = InfiniteLoadingState.ERROR;
    error.value = errorInfo;
  };

  const setAllLoaded = (): void => {
    state.value = InfiniteLoadingState.ALL_LOADED;
    error.value = null;
  };

  const startRetry = (): void => {
    if (!canRetry.value) return;

    state.value = InfiniteLoadingState.RETRYING;
    retryCount.value++;
    error.value = null;
  };

  const reset = (): void => {
    state.value = InfiniteLoadingState.IDLE;
    error.value = null;
    retryCount.value = 0;
  };

  return {
    state,
    error,
    retryCount,
    canLoadMore,
    canRetry,
    isLoading,
    isLoadingMore,
    hasError,
    setLoading,
    setLoadingMore,
    setSuccess,
    setError,
    setAllLoaded,
    startRetry,
    reset,
  };
}

/**
 * 无限滚动数据管理器
 * @param totalItems 总条数
 * @returns 数据管理器
 */
export function useInfiniteScroll<T extends { id: number }>(
  totalItems: Ref<number>,
): UseInfiniteScrollReturn<T> {
  // 缓存已加载的所有项目
  const cachedItems = ref<T[]>([]) as Ref<T[]>;

  // 使用状态管理器
  const stateManager = useInfiniteLoadingState();

  /**
   * 是否已加载全部内容
   */
  const hasLoadedAll = computed(() => {
    return (
      stateManager.state.value === InfiniteLoadingState.ALL_LOADED ||
      cachedItems.value.length >= totalItems.value
    );
  });

  /**
   * 添加新项目到缓存
   * @param newItems 新项目列表
   */
  const addItemsToCache = (newItems: T[]): void => {
    // 过滤掉已存在的项目，避免重复
    const uniqueNewItems = newItems.filter(
      item => !cachedItems.value.some(cached => cached.id === item.id),
    );

    if (uniqueNewItems.length > 0) {
      cachedItems.value = [...cachedItems.value, ...uniqueNewItems];
    }
  };

  /**
   * 重置缓存
   */
  const resetCache = (): void => {
    cachedItems.value = [];
    stateManager.reset();
  };

  /**
   * 触发加载更多
   */
  const triggerLoadMore = (): void => {
    if (!stateManager.canLoadMore.value || hasLoadedAll.value) {
      return;
    }

    // 根据当前是否有数据决定状态
    if (cachedItems.value.length === 0) {
      stateManager.setLoading();
    } else {
      stateManager.setLoadingMore();
    }
  };

  /**
   * 触发重试
   */
  const triggerRetry = (): void => {
    if (!stateManager.canRetry.value) {
      return;
    }

    stateManager.startRetry();
  };

  return {
    cachedItems,
    stateManager,
    hasLoadedAll,
    addItemsToCache,
    resetCache,
    triggerLoadMore,
    triggerRetry,
  };
}
