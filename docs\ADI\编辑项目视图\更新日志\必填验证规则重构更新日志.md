# 必填验证规则重构说明

## 📋 重构概述

本次重构将 `createBasicFormItem` 函数中的必填验证规则从简单的 `required: true` 形式改为与 `createLengthRule` 相同的 `validator` 函数形式，以支持动态校验逻辑，并确保 `fa-form-model-item` 组件能够正确显示必填标识（红色星号）。

## 🔧 主要改进

### 1. 必填验证规则重构

**原来的实现**：

```typescript
const createRequiredRule = (
  _label: string,
  componentType: string,
): FormRule => {
  const rule: FormRule & { transform?: (value: string[]) => string } = {
    required: true,
    message: '输入不能为空',
    trigger: VALIDATION_TRIGGER.MAGIC,
    triggerAuto: true,
  };

  if (componentType === 'selectTags') {
    rule.transform = (value: string[]) => value.join(',');
  }

  return rule;
};
```

**现在的实现**：

```typescript
const createRequiredRule = (
  _label: string,
  componentType: string,
): FormRule => {
  return {
    trigger: VALIDATION_TRIGGER.MAGIC,
    triggerAuto: true,
    validator: (
      _rule: FormRule,
      value: unknown,
      callback: (error?: Error) => void,
    ) => {
      let isEmpty = false;

      if (componentType === 'selectTags') {
        // 特殊处理selectTags组件，检查数组是否为空
        if (Array.isArray(value)) {
          isEmpty = value.length === 0;
        } else {
          isEmpty = !value || String(value).trim() === '';
        }
      } else {
        // 通用处理：检查值是否为空
        if (Array.isArray(value)) {
          isEmpty = value.length === 0;
        } else {
          isEmpty = !value || String(value).trim() === '';
        }
      }

      if (isEmpty) {
        callback(new Error('输入不能为空'));
      } else {
        callback();
      }
    },
  };
};
```

### 2. 文件上传必填验证规则统一

同时更新了文件上传表单项的必填验证规则，保持与基础表单项的一致性：

```typescript
// 文件上传必填验证规则
if (item.required) {
  formItem.rules.push({
    trigger: VALIDATION_TRIGGER.MAGIC,
    triggerAuto: true,
    validator: (
      _rule: FormRule,
      value: unknown,
      callback: (error?: Error) => void,
    ) => {
      // 检查文件列表是否为空
      const isEmpty = !value || (Array.isArray(value) && value.length === 0);

      if (isEmpty) {
        // 根据媒体类型显示不同的错误信息
        const errorMessage =
          mediaType === MEDIA_TYPES.VIDEO
            ? '视频素材总时长需超过15s，建议上传多段视频素材'
            : '请上传相关素材';
        callback(new Error(errorMessage));
      } else {
        callback();
      }
    },
  });
  formItem.required = true;
}
```

### 3. DynamicFormItem 组件 required 属性传递

在 `DynamicFormItem.vue` 组件中添加了 `required` 属性的传递：

```vue
<fa-form-model-item
  :label="formItem.changeShape ? undefined : formItem.label"
  :prop="formItem.prop"
  :rules="rules"
  :required="formItem.required"
  :ref="formItem.prop"
  class="dynamic-form-item"
>
```

## 🎯 技术优势

### 1. 结构一致性

- 必填验证规则现在与长度验证规则使用相同的 `validator` 函数形式
- 保持了代码结构的一致性和可维护性

### 2. 动态校验支持

- 使用 `validator` 函数形式支持更复杂的动态校验逻辑
- 为未来的差异化校验（生成模式 vs 保存模式）提供了基础

### 3. 组件类型特殊处理

- 针对 `selectTags` 组件提供了特殊的空值检查逻辑
- 支持数组和字符串类型的统一处理

### 4. 必填标识显示

- 通过正确传递 `required` 属性，确保 `fa-form-model-item` 能够显示红色星号
- 提升了用户体验和表单可用性

## 🧪 测试验证

创建了完整的单元测试来验证重构的正确性：

- ✅ 验证规则对象结构正确性
- ✅ 空字符串验证
- ✅ 有效字符串验证
- ✅ selectTags 组件空数组验证
- ✅ selectTags 组件有效数组验证
- ✅ 文件上传空列表验证
- ✅ 文件上传有效列表验证

## 📁 涉及文件

### 主要修改文件

- `src/views/EditProjectView/pages/FirstStep/composables/useForm.ts` - 验证规则重构
- `src/components/DynamicForm/FormItems/DynamicFormItem.vue` - required 属性传递

### 测试文件

- `src/views/EditProjectView/pages/FirstStep/__tests__/useForm.test.ts` - 新增测试文件

## 🔄 向后兼容性

- ✅ 保持了现有 API 的完全兼容性
- ✅ 不影响现有的表单校验功能
- ✅ 支持现有的差异化校验逻辑
- ✅ 构建和测试全部通过

## 📝 使用说明

重构后的验证规则使用方式保持不变：

```typescript
// 创建必填表单项
const formItem = createBasicFormItem({
  label: '用户名',
  variable: 'username',
  required: true,
  filedType: FieldType.TEXT,
  // ... 其他配置
});

// formItem.required 会被正确设置为 true
// formItem.rules 会包含新的 validator 函数形式的必填验证规则
```

## 🎉 总结

本次重构成功地将必填验证规则统一为 `validator` 函数形式，提高了代码的一致性和可维护性，同时确保了必填标识的正确显示。所有测试通过，构建成功，完全向后兼容。
