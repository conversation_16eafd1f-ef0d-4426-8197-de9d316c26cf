# NavProject 组件跳转规则说明

## 概述

NavProject 组件是编辑项目视图中的步骤导航组件，负责管理用户在不同编辑步骤之间的跳转。该组件通过复用现有的权限控制函数，实现了基于项目状态的智能跳转控制，确保用户只能在合适的时机访问相应的编辑步骤。

## 魔法数字修复

### 修复前后对比

**修复前（使用魔法数字）**:

```typescript
// 硬编码的数字，缺乏语义
if (targetStep === 0) {
  /* 上传原料 */
}
if (targetStep === 1) {
  /* 预览效果 */
}
```

**修复后（使用语义化常量）**:

```typescript
// 使用现有的常量定义，语义清晰
if (targetStep === FIRST_STEP_INDEX) {
  /* 上传原料 */
}
if (targetStep === SECOND_STEP_INDEX) {
  /* 预览效果 */
}
```

### 常量复用

组件现在使用 `src/views/EditProjectView/constants/index.ts` 中定义的常量：

- `FIRST_STEP_INDEX = 0` - 第一步索引（上传原料）
- `SECOND_STEP_INDEX = 1` - 第二步索引（预览效果）

这确保了整个项目使用统一的步骤索引定义，避免了重复定义和不一致的问题。

### 配置文件管理

组件使用 `src/views/EditProjectView/components/NavProject/config.ts` 配置文件管理提示消息：

```typescript
export const NAVIGATION_MESSAGES = {
  /** 生成中状态不能访问上传原料页的提示 */
  GENERATING_CANNOT_ACCESS_UPLOAD: '正在生成作品，暂时无法更改原料或上传新内容',

  /** 草稿状态不能访问预览效果页的提示 */
  DRAFT_CANNOT_ACCESS_PREVIEW: '当前暂无作品可预览，请先操作生成作品',
} as const;
```

**配置化优势**:

- 集中管理所有提示消息，便于维护
- 支持国际化扩展
- 提供类型安全的消息引用

## 核心功能

### 1. 步骤定义

NavProject 组件支持两个主要编辑步骤：

- **步骤 0 (FIRST_STEP_INDEX)**: 上传原料 - 用户上传和配置项目所需的原料文件
- **步骤 1 (SECOND_STEP_INDEX)**: 预览效果 - 用户预览生成的作品效果

这些常量定义在 `src/views/EditProjectView/constants/index.ts` 中，确保整个项目使用统一的步骤索引定义。

### 2. 跳转控制机制

组件通过以下机制控制步骤跳转：

1. **基础权限检查**: 验证 `allowStepChange` 属性
2. **步骤有效性验证**: 使用 `isValidStepIndex()` 函数验证目标步骤索引
3. **项目状态检查**: 基于当前项目状态决定是否允许跳转
4. **用户反馈**: 在跳转被拒绝时显示相应的提示消息

## 项目状态访问规则

### 状态定义

NavProject 组件依赖以下项目状态进行跳转控制：

```typescript
enum PROJECT_STATUS {
  DRAFT = 0, // 草稿
  GENERATING = 1, // 生成中
  TO_BE_SAVED = 2, // 待保存
  COMPLETED = 3, // 已完成
}
```

### 访问控制矩阵

| 项目状态 | 上传原料 (步骤 0) | 预览效果 (步骤 1) | 说明 |
| --- | --- | --- | --- |
| 草稿 (DRAFT) | ✅ 允许 | ❌ 禁止 | 草稿状态需要先生成作品 |
| 生成中 (GENERATING) | ❌ 禁止 | ✅ 允许 | 生成中不能修改原料 |
| 待保存 (TO_BE_SAVED) | ✅ 允许 | ✅ 允许 | 可以自由切换 |
| 已完成 (COMPLETED) | ✅ 允许 | ✅ 允许 | 可以自由切换 |
| 无状态/未知 | ✅ 允许 | ✅ 允许 | 默认允许访问 |

### 限制规则详解

#### 规则 1: 草稿状态限制预览访问

- **触发条件**: 项目状态为 `PROJECT_STATUS.DRAFT` 且尝试访问步骤 1
- **限制原因**: 草稿状态表示项目尚未生成作品，无内容可预览
- **提示消息**: "当前暂无作品可预览，请先操作生成作品"

#### 规则 2: 生成中状态限制原料编辑

- **触发条件**: 项目状态为 `PROJECT_STATUS.GENERATING` 且尝试访问步骤 0
- **限制原因**: 生成过程中修改原料可能导致数据不一致
- **提示消息**: "正在生成作品，暂时无法更改原料或上传新内容"

## 与其他权限控制系统的关系

### 1. shouldRestrictProjectAccessByProjectId 函数

**位置**: `src/views/EditProjectView/utils/projectStatus.ts`

**关系**: NavProject 组件直接复用 `shouldRestrictProjectAccessByProjectId` 函数：

```typescript
// NavProject 中直接使用现有的权限控制函数
const canNavigateToStep = (targetStep: number): boolean => {
  // 步骤 0: 上传原料 - 复用现有权限控制逻辑
  if (targetStep === FIRST_STEP_INDEX) {
    return !shouldRestrictProjectAccessByProjectId(String(props.projectId));
  }

  // 步骤 1: 预览效果 - 复用现有权限控制逻辑
  if (targetStep === SECOND_STEP_INDEX) {
    return !shouldRestrictPreviewAccessByProjectId(String(props.projectId));
  }

  return true;
};
```

**新增权限控制函数**: 为了保持一致性，新增了预览访问控制函数：

- `shouldRestrictPreviewAccess()` - 基础权限判断函数
- `shouldRestrictPreviewAccessByProjectId()` - 基于项目 ID 的权限判断函数

**复用优势**:

- 两个步骤都使用统一的权限控制函数模式
- 确保权限控制逻辑的一致性
- 当权限规则变更时，只需修改对应的权限控制函数

### 2. validateProjectAccess 函数

**位置**: `src/api/EditProjectView/utils/dataValidator.ts`

**关系**: NavProject 组件在前端 UI 层面实现的跳转控制，与 `validateProjectAccess` 在 API 层面的验证形成双重保护：

```typescript
// API 层验证 (validateProjectAccess)
const shouldDenyAccess = shouldRestrictProjectAccess(projectData.status);
if (shouldDenyAccess) {
  return new Error('项目状态检查失败');
}

// UI 层验证 (NavProject)
if (!canNavigateToStep(targetStep)) {
  message.warning(restrictMessage);
  return;
}
```

**协同工作**:

- API 层验证确保数据安全性
- UI 层验证提供即时用户反馈
- 两层验证使用相同的业务规则

### 3. 权限控制层次结构

```
┌─────────────────────────────────────────┐
│           用户交互层                      │
│  NavProject 组件 - 即时跳转控制           │
└─────────────────┬───────────────────────┘
                  │
┌─────────────────▼───────────────────────┐
│           API 请求层                     │
│  validateProjectAccess - 数据验证        │
└─────────────────┬───────────────────────┘
                  │
┌─────────────────▼───────────────────────┐
│          业务逻辑层                      │
│  shouldRestrictProjectAccess - 核心规则  │
└─────────────────────────────────────────┘
```

## 实现细节

### 核心方法

#### canNavigateToStep(targetStep: number): boolean

检查是否可以跳转到指定步骤：

```typescript
const canNavigateToStep = (targetStep: number): boolean => {
  // 1. 验证步骤索引有效性
  if (!isValidStepIndex(targetStep)) {
    return false;
  }

  const projectStatus = currentProjectStatus.value;

  // 2. 无状态时默认允许
  if (projectStatus === undefined) {
    return true;
  }

  // 3. 应用具体的状态规则
  if (targetStep === STEP_INDEX.UPLOAD_MATERIALS) {
    return projectStatus !== PROJECT_STATUS.GENERATING;
  }

  if (targetStep === STEP_INDEX.PREVIEW_EFFECT) {
    return projectStatus !== PROJECT_STATUS.DRAFT;
  }

  return true;
};
```

#### getNavigationRestrictMessage(targetStep: number): string

获取跳转限制的提示消息：

```typescript
const getNavigationRestrictMessage = (targetStep: number): string => {
  const projectStatus = currentProjectStatus.value;

  if (
    targetStep === STEP_INDEX.UPLOAD_MATERIALS &&
    projectStatus === PROJECT_STATUS.GENERATING
  ) {
    return STEP_RESTRICTION_MESSAGES.GENERATING_CANNOT_ACCESS_UPLOAD;
  }

  if (
    targetStep === STEP_INDEX.PREVIEW_EFFECT &&
    projectStatus === PROJECT_STATUS.DRAFT
  ) {
    return STEP_RESTRICTION_MESSAGES.DRAFT_CANNOT_ACCESS_PREVIEW;
  }

  return '';
};
```

### 状态获取

组件通过 Vuex store 获取项目状态：

```typescript
const currentProjectStatus = computed(() => {
  if (!props.projectId) return undefined;
  return store.getters['project/getProjectStatus'](String(props.projectId));
});
```

## 使用示例

### 基础用法

```vue
<template>
  <NavProject
    :steps="['上传原料', '预览效果']"
    :current-step="currentStep"
    :allow-step-change="true"
    :project-id="projectId"
    @step-change="handleStepChange"
  />
</template>
```

### 自定义步骤

```vue
<template>
  <NavProject
    :steps="['配置参数', '生成内容', '导出结果']"
    :current-step="currentStep"
    :allow-step-change="canChangeStep"
    :project-id="projectId"
    @step-change="handleStepChange"
  />
</template>
```

## 测试覆盖

组件包含完整的测试用例，覆盖以下场景：

### 功能测试

- ✅ 基础渲染和样式应用
- ✅ 步骤切换事件处理
- ✅ 权限控制逻辑

### 状态测试

- ✅ 各种项目状态下的访问控制
- ✅ 边界情况处理
- ✅ 错误状态处理

### 集成测试

- ✅ 与 Vuex store 的集成
- ✅ 与消息提示系统的集成
- ✅ 常量定义的一致性

## 维护指南

### 添加新步骤

1. 在 `constants.ts` 中添加新的步骤索引和名称
2. 更新 `STEP_CONFIG_MAP` 配置
3. 在组件中添加相应的访问控制逻辑
4. 更新测试用例

### 修改访问规则

1. 更新 `canNavigateToStep` 方法中的逻辑
2. 添加或修改相应的提示消息
3. 确保与其他权限控制系统的一致性
4. 更新相关文档和测试

### 性能优化

- 使用 `computed` 属性缓存项目状态
- 避免在模板中进行复杂计算
- 合理使用事件防抖（如需要）

## 相关文件

### 核心文件

- `src/views/EditProjectView/components/NavProject/index.vue` - 主组件
- `src/views/EditProjectView/components/NavProject/constants.ts` - 常量定义

### 测试文件

- `src/views/EditProjectView/components/NavProject/__tests__/index.test.ts` - 组件测试
- `src/views/EditProjectView/components/NavProject/__tests__/constants.test.ts` - 常量测试

### 相关工具

- `src/views/EditProjectView/utils/projectStatus.ts` - 项目状态工具
- `src/api/EditProjectView/utils/dataValidator.ts` - API 验证工具
- `src/constants/project.ts` - 项目状态常量

## 总结

NavProject 组件通过实现基于项目状态的智能跳转控制，为用户提供了流畅且安全的编辑体验。组件与系统中其他权限控制机制协同工作，形成了完整的多层次访问控制体系，确保了数据的一致性和用户操作的合理性。
