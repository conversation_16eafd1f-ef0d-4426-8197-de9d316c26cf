/**
 * @fileoverview 智能轮询核心类型定义
 * @description 定义轮询过程中使用的内部类型
 */

import { WorkStatusInfo } from '@/constants/workStatus';

/**
 * 轮询上下文信息接口
 */
export interface PollingContext {
  /** 触发类型 */
  触发类型: string;
  /** 项目ID */
  项目ID: number;
  /** 项目类型 */
  项目类型: string;
  /** 轮询作品数量 */
  轮询作品数量: number;
  /** 页面可见状态 */
  页面可见状态: boolean;
  /** 时间戳 */
  时间戳: string;
}

/**
 * 状态映射类型
 * 存储作品ID到完整状态信息的映射
 */
export type StateMap = Map<number, WorkStatusInfo>;

/**
 * 进度映射类型
 */
export type ProgressMap = Map<number, number>;

/**
 * 轮询间隔计算参数接口
 */
export interface IntervalCalculationParams {
  /** 项目类型 */
  projectType: number;
  /** 最大进度 */
  maxProgress: number;
  /** 轮询作品数量 */
  pollingWorksCount: number;
}
