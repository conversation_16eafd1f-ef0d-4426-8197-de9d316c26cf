<template>
  <fa-popover
    v-model="isShow"
    trigger="click"
    :width="250"
    :align="{ offset: [0, 4] }"
  >
    <div @click="isShow = true">
      <Icon type="bianji" class="bianji-icon" />
    </div>
    <template #content>
      <div class="flex flex-col">
        <div class="mb-[16px] color-text">{{ inputPlaceholder }}</div>
        <fa-input
          v-model.trim="inputValue"
          :placeholder="inputPlaceholder"
          :maxLength="maxLength"
          class="w-[218px] mb-[16px] rounded-[6px]"
          @keyup.enter="handleConfirm"
        />
      </div>
      <div class="text-center">
        <fa-button
          type="primary"
          size="small"
          class="mr-8px w-[60px]"
          @click="handleConfirm"
          >确定</fa-button
        >
        <fa-button
          type="normal"
          size="small"
          class="w-[60px]"
          @click="handleCancel"
          >取消</fa-button
        >
      </div>
    </template>
  </fa-popover>
</template>

<script setup lang="ts">
import { computed, ref, watch, defineProps, defineEmits } from 'vue';
import { message } from '@fk/faicomponent';

/**
 * EditNamePopconfirm 组件 Props
 * @property itemId 编辑目标ID
 * @property initialValue 输入框初始值
 * @property inputLabel 输入框标签
 * @property visible 是否显示弹窗
 */
const props = defineProps({
  /** 编辑目标ID */
  itemId: {
    type: Number,
    required: true,
  },
  /** 输入框初始值 */
  initialValue: {
    type: String,
    required: true,
  },
  /** 输入框标签 */
  inputLabel: {
    type: String,
    default: '',
  },
  /** 是否显示弹窗 */
  visible: {
    type: Boolean,
    default: false,
  },
  maxLength: {
    type: Number,
    default: 50,
  },
});

const emit = defineEmits<{
  (e: 'cancel'): void;
  (e: 'confirm', value: { id: number; name: string }): void;
  (e: 'update:visible', value: boolean): void;
}>();

// State
const inputValue = ref('');
const isShow = ref(props.visible);

// Computed
const inputPlaceholder = computed(() => {
  return `请输入${props.inputLabel}`;
});

// Watchers
watch(
  () => props.visible,
  (val: boolean) => {
    isShow.value = val;
    val && (inputValue.value = props.initialValue);
  },
);
watch(
  () => isShow.value,
  (val: boolean) => {
    emit('update:visible', val);
  },
);

// Methods
const checkHasErr = (value: string) => {
  let nameError = '';
  if (!value) {
    nameError = inputPlaceholder.value;
  } else if (value.length > props.maxLength) {
    nameError = `${props.inputLabel}不能超过${props.maxLength}个字符`;
  } else {
    nameError = '';
  }
  if (nameError) {
    message.error(nameError);
    return true;
  } else {
    return false;
  }
};

const handleCancel = () => {
  emit('cancel');
  isShow.value = false;
};

const handleConfirm = () => {
  const value = inputValue.value;
  // 校验
  if (checkHasErr(value)) return;

  // 名称未改变
  if (value !== props.initialValue) {
    emit('confirm', {
      id: props.itemId,
      name: value,
    });
  }
  isShow.value = false;
};
</script>

<style lang="scss" scoped>
.bianji-icon {
  @apply size-[20px] text-disabledText cursor-pointer;
  &:hover {
    @apply text-assist;
  }
}
</style>
