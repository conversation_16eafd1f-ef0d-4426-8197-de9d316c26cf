# EditProjectView 更新日志

## [2025-08-18] 修复项目编辑模式状态管理 bug

### 问题描述

在项目编辑流程中存在状态管理错误，具体表现为：

1. **初始状态**：用户从新建模式进入项目（URL 中没有 projectId 参数）
2. **项目创建**：系统调用 `updateRouteWithProjectId` 获取项目 ID，完成新项目创建
3. **正常编辑**：用户可以正常编辑和生成项目数据
4. **页面跳转**：用户点击生成按钮跳转到第二步预览页，完成项目作品生成
5. **问题触发**：用户点击返回第一页时，触发 `handleStepChange` 方法
6. **状态错误**：`useProjectData` 中的 `isEditMode` 状态没有正确更新，仍然为 `false`
7. **重复创建**：再次调用 `fetchFormData` 时，系统误认为是新建项目，重新执行项目创建逻辑，导致错误

### 根本原因

- `isEditMode` 原本是基于 `routeProjectId` 的计算属性：`computed(() => Boolean(routeProjectId))`
- 在新建模式下，`routeProjectId` 是从路由参数中获取的初始值（空字符串）
- 当 `updateRouteWithProjectId` 更新路由后，`routeProjectId` 变量本身没有响应式更新
- 这导致 `isEditMode` 始终为 `false`，在页面切换时会重复创建项目

### 修复方案

按照建议，将 `isEditMode` 从计算属性改为响应式变量：

#### 1. 修改状态定义

```typescript
// 修改前：计算属性
const isEditMode = computed<boolean>(() => {
  return Boolean(routeProjectId);
});

// 修改后：响应式变量
const isEditMode = ref<boolean>(Boolean(routeProjectId));
```

#### 2. 更新路由后同步状态

```typescript
const updateRouteWithProjectId = (projectId: number): void => {
  // ... 原有逻辑 ...

  router.replace({
    path: router.currentRoute.path,
    query: {
      ...router.currentRoute.query,
      projectId: String(projectId),
    },
  });

  // 新增：更新编辑模式状态，防止页面切换时重复创建项目
  isEditMode.value = true;
  console.log('状态更新：已切换到编辑模式', {
    projectId,
    isEditMode: isEditMode.value,
  });
};
```

#### 3. 更新类型定义

```typescript
// 修改前
isEditMode: ComputedRef<boolean>;

// 修改后
isEditMode: Ref<boolean>;
```

### 修复效果

用户在完成项目创建后，无论如何在页面间切换，都应该保持在编辑模式，不会重复触发项目创建逻辑。

### 测试验证

创建了专门的单元测试 `useProjectData.editMode.test.ts` 来验证修复效果：

- ✅ 初始新建模式：`isEditMode` 正确为 `false`
- ✅ 初始编辑模式：`isEditMode` 正确为 `true`
- ✅ 新建模式转编辑模式：调用 `updateRouteWithProjectId` 后，`isEditMode` 正确更新为 `true`
- ✅ 编辑模式保持不变：编辑模式下不会调用路由更新
- ✅ 手动调用状态更新：手动调用 `updateRouteWithProjectId` 正确更新状态
- ✅ 编辑模式下的保护：编辑模式下调用 `updateRouteWithProjectId` 不执行任何操作

### 影响范围

- **文件修改**：
  - `src/views/EditProjectView/composables/useProjectData.ts`
  - `src/views/EditProjectView/types/components.ts`
- **新增测试**：
  - `src/views/EditProjectView/composables/__tests__/useProjectData.editMode.test.ts`
- **向后兼容**：完全兼容，不影响现有功能

### 技术细节

- 使用响应式变量替代计算属性，确保状态可以主动更新
- 在路由更新成功后立即同步状态，确保状态一致性
- 保持原有的保护逻辑，编辑模式下不执行路由更新
- 添加详细的控制台日志，便于调试和问题排查

## [2025-08-18] 变量重命名优化

### 变更内容

将 `routeProjectId` 参数重命名为 `initialProjectId`，以更准确地描述其用途：

- 该参数表示初始传入的项目 ID，用于区分新建模式和编辑模式
- 在新建模式下为空字符串，在编辑模式下为实际的项目 ID
- 重命名后的名称更清晰地表达了参数的语义

### 修改范围

- **接口定义**：`UseProjectDataOptions.routeProjectId` → `UseProjectDataOptions.initialProjectId`
- **函数参数**：所有使用该参数的地方都已更新
- **测试文件**：所有测试用例中的参数名都已同步更新
- **注释文档**：相关注释和文档都已更新

### 修改文件

- `src/views/EditProjectView/composables/useProjectData.ts`
- `src/views/EditProjectView/index.vue`
- `src/views/EditProjectView/composables/__tests__/useProjectData.editMode.test.ts`
- `src/views/EditProjectView/composables/__tests__/useProjectData.projectStatus.test.ts`

### 向后兼容性

此次重命名是内部实现的优化，不影响外部 API 的使用，完全向后兼容。

## [2025-08-18] 修复 currentProjectId 获取问题

### 问题描述

在变量重命名后发现 `currentProjectId` 获取到的是 `undefined`，原因是：

1. 在新建模式转编辑模式的过程中，`initialProjectId` 始终是空字符串
2. 虽然 `isEditMode` 被正确设置为 `true`，但 `currentProjectId` 仍然依赖空的 `initialProjectId`
3. 导致在编辑模式下无法正确获取项目 ID

### 修复方案

**第一次修复（错误的逻辑）：** 最初尝试优先使用 API 返回的项目 ID，但这个逻辑是错误的。

**第二次修复（正确的逻辑）：** 重新分析后发现，应该优先使用 `initialProjectId`，因为：

- 编辑模式下，URL 参数中的项目 ID 是最权威的
- 新建模式下，`initialProjectId` 为空，会自然降级到使用 API 返回的 ID

```typescript
const currentProjectId = computed<number | undefined>(() => {
  // 优先使用初始项目ID（编辑模式下ID不会变）
  if (initialProjectId) {
    return Number(initialProjectId);
  }

  // 其次使用API返回的项目ID（新建模式下获取到的ID）
  if (formData.value?.projectId) {
    return formData.value.projectId;
  }

  return undefined;
});
```

### 修复效果

- **新建模式**：`initialProjectId` 为空，使用 API 返回的项目 ID
- **编辑模式**：优先使用 URL 参数中的初始项目 ID（最权威）
- **状态转换**：新建模式转编辑模式后，依然使用 API 返回的项目 ID（因为 `initialProjectId` 仍为空）

### 测试验证

新增了专门的测试用例验证 `currentProjectId` 的正确性：

- 新建模式下初始为 `undefined`，API 调用后正确获取
- 编辑模式下能正确获取初始项目 ID
- 所有测试用例通过
