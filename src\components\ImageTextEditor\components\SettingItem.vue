<template>
  <div>
    <div class="flex mt-[24px]" :class="otherClass">
      <div class="mt-[8px] min-w-[56px] text-[#333] text-[14px] mr-[24px]">
        {{ label }}
      </div>
      <div class="flex-1 flex">
        <slot />
      </div>
    </div>
    <div
      v-if="errorToShow"
      class="ml-[83px] text-[#fa3534] text-[14px] mt-[4px]"
    >
      输入不能为空
    </div>
  </div>
</template>

<script lang="ts" setup>
import { computed, watch } from 'vue';
import { eventBus, EVENT_NAMES } from '@/utils/eventBus';

const props = defineProps<{
  label: string;
  required?: boolean;
  otherClass?: string;
  value?: string;
}>();

const errorToShow = computed(() => {
  return props.required && !props.value;
});

watch(errorToShow, val => {
  eventBus.emit(EVENT_NAMES.DISABLED_SAVE_BUTTON, val);
});
</script>

<style lang="scss" scoped>
::v-deep {
  .fa-input {
    @apply rounded-[8px];
  }
  .text-editor__textarea {
    @apply rounded-[8px];
  }
}
</style>
