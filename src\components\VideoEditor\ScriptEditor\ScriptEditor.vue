<template>
  <div class="h-full overflow-y-auto pt-[16px]">
    <!-- 脚本列表 -->
    <div class="pt-[8px] pb-[24px] px-[32px]">
      <div class="bg-[#fafafa] rounded-[8px] b-edge border">
        <div
          v-for="(segmentItem, index) in workInfo?.script.segments || []"
          class="flex flex-col justify-center b-edge b-b-1 last:border-0"
        >
          <div class="flex items-center">
            <!-- 左侧信息 -->
            <div class="w-[128px] flex flex-col justify-start pl-[24px]">
              <!-- 模块名称 -->
              <div class="text-text text-[14px] line-height-[19px] mb-[4px]">
                {{ segmentItem.module }}
              </div>
              <!-- 分镜开始时间 -->
              <div class="text-assist text-[13px] line-height-[17px] mb-[4px]">
                {{ formatStartTime(segmentStartTimeAndDuration[index]) }}
              </div>
              <!-- 分镜时长 -->
              <div>
                <span
                  class="bg-[#f5f5f5] text-assist text-[13px] line-height-[17px] px-[7px] pt-[2px] pb-[3px] b-disabledText border inline-block rounded-[4px]"
                  >{{ segmentStartTimeAndDuration[index].duration }}秒</span
                >
              </div>
            </div>
            <!-- 右侧内容 -->
            <div class="flex-1 py-[8px] pr-[12px]">
              <!-- 非编辑态 -->
              <div v-show="!scriptEditMode[index]">
                <fa-tooltip placement="top">
                  <template slot="title">
                    <span>点击编辑</span>
                  </template>
                  <div
                    class="cursor-pointer h-[84px] hover:bg-background rounded-[8px] flex items-center p-[12px] break-all whitespace-normal"
                    @click="handleClickEdit(index)"
                  >
                    {{ segmentItem.content }}
                  </div>
                </fa-tooltip>
              </div>
              <!-- 编辑态 -->
              <div
                v-show="scriptEditMode[index]"
                :class="{ 'has-error': segmentItem.content.length === 0 }"
              >
                <fa-textarea
                  class="override-textarea h-[84px]"
                  v-model.trim="segmentItem.content"
                  :rows="3"
                  showCount
                  :maxLength="200"
                  @keydown.native="handleKeydown"
                  @blur="handleBlur(index)"
                />
              </div>
            </div>
          </div>
          <!-- 底部按钮和状态 -->
          <div
            class="flex justify-between mb-[8px] mr-[12px] ml-[128px]"
            v-show="
              originWorkInfo?.script.segments &&
              segmentItem.content !==
                originWorkInfo.script.segments[index].content
            "
          >
            <div class="text-danger">
              <span v-show="segmentItem.content.length === 0">
                输入不能为空
              </span>
            </div>
            <div>
              <fa-button
                :disabled="segmentItem.content.length === 0"
                type="primary"
                ghost
                size="small"
                class="px-[16px] mr-[8px]"
                @click="handleClickSaveScriptItem(index)"
              >
                保存
              </fa-button>
              <fa-button
                size="small"
                class="px-[16px]"
                @click="handleReset(index)"
              >
                重置
              </fa-button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, nextTick, watch } from 'vue';
import {
  loading,
  closeStatus,
  originWorkInfo,
  workInfo,
  updateConsumePoint,
} from '../hook/useWorkInfo';
import { saveWork } from '@/api/VideoEditor/save';
import { message } from '@fk/faicomponent';
import { VIDEO_EDITOR_CLOSE_STATUS } from '../constants';

/** 分镜编辑状态 */
const scriptEditMode = ref<boolean[]>(
  workInfo.value?.script.segments?.map(() => false) || [],
);
// 初始化分镜编辑状态
watch(
  () => loading.value,
  newVal => {
    const isNotModifying =
      JSON.stringify(workInfo.value?.script.segments) ===
      JSON.stringify(originWorkInfo.value?.script.segments);
    if (!newVal && isNotModifying) {
      scriptEditMode.value =
        workInfo.value?.script.segments?.map(() => false) || [];
    }
  },
);

/** 分镜开始时间和持续时间 */
const segmentStartTimeAndDuration = computed(() => {
  if (!workInfo.value?.script.segments) return [];
  const result: { startTime: number; duration: number }[] = [];
  workInfo.value.script.segments.forEach((segment, index) => {
    if (index === 0) {
      result.push({
        startTime: Math.round(
          segment.beginTime / 1000 / (workInfo.value?.setting.voice.speed || 1),
        ),
        duration: Math.round(
          segment.length / 1000 / (workInfo.value?.setting.voice.speed || 1),
        ),
      });
      return;
    }
    result.push({
      startTime: Math.round(
        segment.beginTime / 1000 / (workInfo.value?.setting.voice.speed || 1),
      ),
      duration: Math.round(
        segment.length / 1000 / (workInfo.value?.setting.voice.speed || 1),
      ),
    });
  });
  return result;
});

/** 格式化开始时间 */
const formatStartTime = (
  timeInfo: { startTime: number; duration: number } | undefined,
) => {
  if (!timeInfo) return '-';

  const timeString = new Date(timeInfo.startTime * 1000)
    .toISOString()
    .substr(11, 8);
  const [hours, minutes, seconds] = timeString.split(':');

  // 如果小时为 00，只显示分:秒
  if (hours === '00') {
    return `${minutes}:${seconds}`;
  }

  return timeString;
};

/** 点击编辑 */
const handleClickEdit = (index: number) => {
  scriptEditMode.value = [
    ...scriptEditMode.value.slice(0, index),
    true,
    ...scriptEditMode.value.slice(index + 1),
  ];
  // focus到输入框
  nextTick(() => {
    // 获取所有文本域并聚焦到当前索引
    const allTextArea = document.querySelectorAll(
      '.override-textarea textarea',
    );
    (allTextArea[index] as HTMLTextAreaElement)?.focus();
  });
};

/** 失去焦点时判断是否重置回非编辑态 */
const handleBlur = (index: number) => {
  if (
    workInfo.value?.script.segments &&
    originWorkInfo.value?.script.segments &&
    workInfo.value.script.segments[index].content ===
      originWorkInfo.value.script.segments[index].content
  ) {
    scriptEditMode.value = [
      ...scriptEditMode.value.slice(0, index),
      false,
      ...scriptEditMode.value.slice(index + 1),
    ];
  }
};

/** 重置脚本 */
const handleReset = (index: number) => {
  if (
    workInfo.value?.script.segments &&
    originWorkInfo.value?.script.segments
  ) {
    workInfo.value.script.segments[index].content =
      originWorkInfo.value.script.segments[index].content || '';
  }
  scriptEditMode.value = [
    ...scriptEditMode.value.slice(0, index),
    false,
    ...scriptEditMode.value.slice(index + 1),
  ];
  nextTick(() => updateConsumePoint());
};

/** 阻止换行，同时更新点数 */
const handleKeydown = (event: KeyboardEvent) => {
  if (event.key === 'Enter' && !event.shiftKey) {
    event.preventDefault();
  }
  nextTick(() => {
    updateConsumePoint();
  });
};

/** 保存单个脚本 */
const handleClickSaveScriptItem = async (index: number) => {
  if (loading.value) return;
  await updateConsumePoint();
  if (
    workInfo.value?.script.segments &&
    originWorkInfo.value?.script.segments
  ) {
    loading.value = true;
    originWorkInfo.value.script.segments[index].content =
      workInfo.value.script.segments[index].content || '';
    scriptEditMode.value = [
      ...scriptEditMode.value.slice(0, index),
      false,
      ...scriptEditMode.value.slice(index + 1),
    ];
    const [err] = await saveWork(originWorkInfo.value, false);
    loading.value = false;
    if (err) {
      message.error(err.message || '保存失败');
      throw err;
    }
    closeStatus.value = VIDEO_EDITOR_CLOSE_STATUS.SAVED;
    message.success('保存成功');
  }
};
</script>

<style lang="scss">
.fa-tooltip-inner {
  @apply rounded-[8px] text-[14px] text-center text-white font-400 bg-[#000000] bg-op-80;
}
.override-textarea {
  @apply w-full;
  textarea.fa-input {
    @apply rounded-[8px] min-h-[84px];
  }
}
.fa-popover {
  @apply zi-notification;
  .fa-popover-buttons {
    @apply text-center;
  }
  .fa-popover-message-title {
    @apply w-[258px] line-height-[16px];
  }
}
</style>
