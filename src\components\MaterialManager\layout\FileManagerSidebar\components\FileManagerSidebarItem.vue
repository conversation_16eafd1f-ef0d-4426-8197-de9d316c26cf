<template>
  <div
    class="file-manager-demo-sidebar-item flex items-center"
    :class="{ selected }"
    @click="$emit('click')"
  >
    <span v-if="$scopedSlots.icon" class="icon flex items-center">
      <slot name="icon"></slot>
    </span>
    <span class="text">
      <slot></slot>
    </span>
  </div>
</template>

<script>
export default {
  name: 'FileManagerSidebarItem',

  props: {
    selected: {
      type: Boolean,
      default: false,
    },
  },
};
</script>

<style lang="scss" scoped>
.file-manager-demo-sidebar-item {
  padding: 8px 12px;
  margin-top: 8px;
  border-radius: 4px;
  color: #333;
  cursor: pointer;
  transition: 0.3s background-color, 0.3s color;

  &:nth-of-type(1) {
    margin: 0;
  }

  &:hover {
    background-color: #fafafa;
  }

  &.selected {
    background-color: #f0f7ff;
    color: #3a84fe;
  }

  .icon {
    margin-right: 8px;
    font-size: 20px;
  }

  .text {
    font-size: 13px;
  }
}
</style>
