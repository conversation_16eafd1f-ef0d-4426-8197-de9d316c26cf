import { ref, computed, Ref } from 'vue';
import { TAB_PANE_KEY, SORT_BY_KEY } from '../constants';
import {
  INFO_KEYS,
  SORT_MODE_KEY,
  IMG_UPLOAD_LIST,
  VIDEO_UPLOAD_LIST,
  ROOT_FOLDER_ID,
} from '@/constants/material';
import { FileData, FolderData } from '@/types/Material';
import { MaterialUploadFile } from '../types';
import store from '@/store';
import { FileViewTypeValues } from '../utils/index.ts';
import { useUploadingFileState } from './useUploadingFileState';

// Props 接口
export interface Props {
  visible: boolean;
  maxChosenFileCount: number;
  title?: string;
  isVideo?: boolean;
  onConfirm?: (files: MaterialUploadFile[]) => void;
}

// 定义返回值的类型
export interface UseMaterialBasicUploadStateReturns {
  rootFolder: Ref<number>;
  folderList: Ref<FolderData[]>;
  fileList: Ref<FileData[]>;
  chosenFileList: Ref<MaterialUploadFile[]>;
  confirmPending: Ref<boolean>;
  tabProps: Ref<{ activeKey: string }>;
  tabPaneList: Ref<{ key: string; tab: string }[]>;
  showFileDesc: Ref<boolean>;
  loading: Ref<boolean>;
  sortBy: Ref<string>;
  sortMode: Ref<SORT_MODE_KEY>;
  setting: Ref<{
    originPictureUpload: boolean;
    autoAddWaterMark: boolean;
    showSystemFolder: boolean;
  }>;
  isVideo: Ref<boolean>;
  ownerFileSearchKeywords: Ref<string>;
  ownerFilePageSize: Ref<number>;
  ownerFilePageCurrent: Ref<number>;
  ownerFilePageTotal: Ref<number>;
  pageCurrent: Ref<number>;
  pageSize: Ref<number>;
  fileTotal: Ref<number>;
  dropdownProps: Ref<Record<string, unknown>>;
  popoverProps: Ref<Record<string, unknown>>;
  fileUploading: Ref<boolean>;
  acceptList: Ref<string[]>;
  capacityUsedSize: Ref<number>;
  capacityTotalSize: Ref<number>;
  ownerFileViewType: Ref<FileViewTypeValues>;
  currentFolder: Ref<number>;
  currentPathList: Ref<FolderData[]>;
  modalOptions: Ref<Record<string, unknown>>;
  showFileList: Ref<FileData[]>;
  showFolderList: Ref<FolderData[]>;
  chosenFileKeyList: Ref<number[]>;
  showCapacity: Ref<boolean>;
  onConfirm?: (files: MaterialUploadFile[]) => void;
  maxChosenFileCount: number;
}

/**
 * 管理所有响应式状态 (ref 声明) 和基于这些状态的派生数据 (computed 属性)。
 * @param props 组件props
 * @returns 所有响应式状态和派生数据对象
 */
export function useMaterialBasicUploadState(
  props: Props,
): UseMaterialBasicUploadStateReturns {
  // 使用上传文件状态管理 hook
  const { getUploadingFileList } = useUploadingFileState();

  //  根目录
  const rootFolder = ref<number>(0);

  // 文件夹相关
  const folderList = ref<FolderData[]>([]);

  // 文件相关
  const fileList = ref<FileData[]>([]);
  const chosenFileList = ref<MaterialUploadFile[]>([]);

  // 标签页相关
  const confirmPending = ref<boolean>(false);
  const tabProps = ref<{ activeKey: string }>({
    activeKey: TAB_PANE_KEY.MyFile,
  });
  const tabPaneList = ref([
    {
      key: TAB_PANE_KEY.MyFile,
      tab: `我的${props.isVideo ? '视频' : '图片'}`,
    },
  ]);
  const showFileDesc = ref<boolean>(true);
  const loading = ref<boolean>(false);
  const sortBy = ref<string>(SORT_BY_KEY.Time);
  const sortMode = ref<SORT_MODE_KEY>(SORT_MODE_KEY.DESC);
  const setting = ref<{
    originPictureUpload: boolean;
    autoAddWaterMark: boolean;
    showSystemFolder: boolean;
  }>({
    originPictureUpload: false,
    autoAddWaterMark: false,
    showSystemFolder: false,
  });
  const ownerFileSearchKeywords = ref<string>(''); // 搜索文件的关键字

  // 暂时不开启分页 - 所以将相关参数设为1
  const ownerFilePageSize = ref<number>(1);
  const ownerFilePageCurrent = ref<number>(1);
  const ownerFilePageTotal = ref<number>(1);

  // 滚动加载分页的参数
  const pageCurrent = ref<number>(0); // 当前页码
  const pageSize = ref<number>(50); // 每页显示数量
  const fileTotal = ref<number>(0); // 文件总数

  const dropdownProps = ref<Record<string, unknown>>({});
  const popoverProps = ref<Record<string, unknown>>({});
  const fileUploading = ref<boolean>(false); // 文件是否正在上传中

  // 计算属性
  const acceptList = computed<string[]>(() => {
    return props.isVideo ? VIDEO_UPLOAD_LIST : IMG_UPLOAD_LIST;
  });

  const capacityUsedSize = computed<number>(() => {
    return store.state.materialUpload.capacityUsedSize;
  });
  const capacityTotalSize = computed<number>(() => {
    return store.state.materialUpload.capacityTotalSize;
  });

  const ownerFileViewType = computed<FileViewTypeValues>(() => {
    return store.state.materialUpload.ownerFileViewType as FileViewTypeValues;
  });

  const currentFolder = computed<number>(() => {
    return store.state.materialUpload.currentFolder;
  });

  const currentPathList = computed<FolderData[]>(() => {
    return store.state.materialUpload.currentPathList;
  });

  // 弹框选项
  const modalOptions = computed<Record<string, unknown>>(() => {
    let confirmLoading = confirmPending.value;
    let disabled = false;
    let okText = '确定';
    if (fileUploading.value) {
      confirmLoading = true;
      disabled = true;
      okText = '上传中';
    }

    return {
      confirmLoading,
      okText,
      okButtonProps: {
        disabled: !chosenFileList.value.length,
      },
      cancelButtonProps: {
        disabled,
      },
    };
  });

  // 展示的文件列表 - 用于过滤搜索
  const showFileList = computed<FileData[]>(() => {
    const uploadingFileList = getUploadingFileList(currentFolder.value);
    return [...uploadingFileList, ...fileList.value];
  });

  // 展示的文件夹列表
  const showFolderList = computed<FolderData[]>(() => {
    let res = folderList.value;
    // 文件夹列表内要包含根文件夹、当前路径列表，用于显示当前路径
    res = [
      { id: ROOT_FOLDER_ID, name: '全部' },
      ...currentPathList.value,
      ...res,
    ];
    return res;
  });

  const chosenFileKeyList = computed<number[]>(() => {
    return chosenFileList.value.map((item: MaterialUploadFile) => {
      return item.data[INFO_KEYS.ID];
    });
  });

  const showCapacity = computed<boolean>(() => {
    return capacityTotalSize.value > 0;
  });

  return {
    rootFolder,
    folderList,
    fileList,
    chosenFileList,
    confirmPending,
    tabProps,
    tabPaneList,
    showFileDesc,
    loading,
    sortBy,
    sortMode,
    setting,
    currentFolder,
    ownerFileSearchKeywords,
    capacityUsedSize,
    capacityTotalSize,
    ownerFileViewType,
    ownerFilePageSize,
    ownerFilePageCurrent,
    ownerFilePageTotal,
    pageCurrent,
    pageSize,
    fileTotal,
    dropdownProps,
    popoverProps,
    fileUploading,
    currentPathList,
    acceptList,
    modalOptions,
    showFileList,
    showFolderList,
    chosenFileKeyList,
    showCapacity,
    onConfirm: props.onConfirm,
    maxChosenFileCount: props.maxChosenFileCount,
    isVideo: ref(props.isVideo ?? false),
  } as const;
}
