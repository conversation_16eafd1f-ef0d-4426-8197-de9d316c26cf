/**
 * @fileoverview 项目编辑视图核心数据结构类型定义。
 * @description 此文件定义了 `EditProjectViewData` 接口，它是项目新建和编辑过程中的主要数据载体。
 * <AUTHOR>
 * @since 1.0.0
 */

import type { ProjectType } from './base';
import type { InputFormItem, ResFormItem } from './form';
import type {
  ScriptData,
  BgMusicSetting,
  VoiceSetting,
  FlowerText,
} from './media';

// ============== 项目数据类型 ==============

/**
 * @description 项目编辑视图的核心数据接口，聚合了项目配置、表单数据、媒体设置等所有相关信息。
 * @interface EditProjectViewData
 */
export interface EditProjectViewData {
  /**
   * 项目的唯一标识ID。在新建项目时可能为空，编辑时为现有项目ID。
   * @type {number}
   * @memberof EditProjectViewData
   * @optional
   */
  projectId?: number;
  /**
   * 项目关联的模板ID。
   * @type {number}
   * @memberof EditProjectViewData
   */
  templateId: number;
  /**
   * 项目类型，区分是视频项目还是图文项目。
   * @type {ProjectType}
   * @memberof EditProjectViewData
   * @see {@link ProjectType} - 定义于 `./base.ts`
   */
  type: ProjectType;
  /**
   * 基础信息表单项的配置数组，用于动态生成表单。
   * @type {InputFormItem[]}
   * @memberof EditProjectViewData
   * @see {@link InputFormItem} - 定义于 `./form.ts`
   */
  inputForm: InputFormItem[];
  /**
   * 文件资源表单项的配置数组，用于处理如图片、视频等文件上传。
   * @type {ResFormItem[]}
   * @memberof EditProjectViewData
   * @see {@link ResFormItem} - 定义于 `./form.ts`
   */
  resForm: ResFormItem[];
  /**
   * 项目的名称。
   * @type {string}
   * @memberof EditProjectViewData
   * @optional
   */
  name?: string;
  /**
   * 项目关联的示例脚本数据。
   * @type {ScriptData}
   * @memberof EditProjectViewData
   * @see {@link ScriptData} - 定义于 `./media.ts`
   * @optional
   */
  script?: ScriptData;
  /**
   * 计划生成的作品总数量。
   * @type {number}
   * @memberof EditProjectViewData
   * @optional
   */
  totalNum?: number;
  /**
   * 已经成功生成的作品数量。
   * @type {number}
   * @memberof EditProjectViewData
   * @optional
   */
  sucNum?: number;
  /**
   * 项目的当前状态。
   * 例如：0（草稿），1（生成中），2（待保存），3（已完成）。具体值参照后端定义。
   * @type {number}
   * @memberof EditProjectViewData
   * @optional
   */
  status?: number;
  /**
   * 项目的封面图片URL。
   * @type {string}
   * @memberof EditProjectViewData
   * @optional
   */
  coverImg?: string;
  /**
   * 项目的删除标记。
   * 例如：0（未删除），1（已删除）。具体值参照后端定义。
   * @type {number}
   * @memberof EditProjectViewData
   * @optional
   */
  del?: number;
  /**
   * 项目关联的某种主要资源ID（具体含义根据业务场景确定）。
   * @type {string}
   * @memberof EditProjectViewData
   * @optional
   */
  resId?: string;
  /**
   * 背景音乐的相关设置。
   * @type {BgMusicSetting}
   * @memberof EditProjectViewData
   * @see {@link BgMusicSetting} - 定义于 `./media.ts`
   * @optional
   */
  bgMusic?: BgMusicSetting;
  /**
   * 配音的相关设置。
   * @type {VoiceSetting}
   * @memberof EditProjectViewData
   * @see {@link VoiceSetting} - 定义于 `./media.ts`
   * @optional
   */
  voice?: VoiceSetting;
  /**
   * 花字（屏幕文字叠加）的设置数组。
   * @type {FlowerText[]}
   * @memberof EditProjectViewData
   * @see {@link FlowerText} - 定义于 `./media.ts`
   * @optional
   */
  flowerTexts?: FlowerText[];
}
