<template>
  <fa-modal
    :visible="visible"
    :width="304"
    :closable="false"
    :maskClosable="false"
    :bodyStyle="{ padding: '64px 32px 24px' }"
    wrapClassName="insufficient-points-modal-wrapClassName"
    centered
    class="insufficient-points-modal"
  >
    <div class="insufficient-points-modal__content">
      <div class="insufficient-points-modal__icon">
        <img
          src="@/assets/EditProject/InsufficientPointsModalIcon.webp"
          alt="积分不足"
          class="insufficient-points-modal__icon-img"
        />
      </div>
      <div class="insufficient-points-modal__text">
        当前创作点数不足，无法生成作品，
        <br />
        请先充值创作点数
      </div>
    </div>
    <template slot="footer">
      <fa-button
        type="primary"
        class="insufficient-points-modal__recharge-btn min-w-[88px]"
        @click="handleRecharge"
        >去充值</fa-button
      >
      <fa-button class="min-w-[88px] ml-[15px]!" @click="handleCancel"
        >取消</fa-button
      >
    </template>
  </fa-modal>
</template>

<script lang="ts">
import { defineComponent } from 'vue';

/**
 * @description 点数不足提示弹窗组件
 */
export default defineComponent({
  name: 'InsufficientPointsModal',
  props: {
    /**
     * 弹窗显示状态
     */
    visible: {
      type: Boolean,
      default: false,
    },
  },
  emits: ['update:visible', 'recharge', 'cancel'],
  setup(_props, { emit }) {
    /**
     * 处理取消按钮点击事件
     */
    const handleCancel = (): void => {
      emit('update:visible', false);
      emit('cancel');
    };

    /**
     * 处理去充值按钮点击事件
     */
    const handleRecharge = (): void => {
      emit('update:visible', false);
      emit('recharge');
    };

    return {
      handleCancel,
      handleRecharge,
    };
  },
});
</script>

<style lang="scss" scoped>
:deep(.insufficient-points-modal-wrapClassName) {
  .fa-modal-footer {
    /* 外观相关 */
    @apply border-none pt-0 pb-24px;
  }
}
.insufficient-points-modal {
  /* 布局相关 */
  @apply flex flex-col items-center;

  /* 弹窗盒子样式 */
  :deep(.fa-modal-content) {
    /* 布局相关 */
    @apply position-relative;
    /* 外观相关 */
    @apply border-rd-16px;
    background: linear-gradient(180deg, #ebefff 0%, #fff 28.03%, #fff 100%);
    box-shadow: 0 4px 12px #00000026;
  }
}

.insufficient-points-modal__content {
  /* 布局相关 */
  @apply flex flex-col items-center justify-center;

  /* 尺寸相关 */
  @apply w-full;
}

.insufficient-points-modal__icon {
  /* 布局相关 */
  @apply flex items-center justify-center position-absolute top-[-32px];
  /* 尺寸相关 */
  @apply w-88px h-88px;

  .insufficient-points-modal__icon-img {
    /* 尺寸相关 */
    @apply w-full h-full;
  }
}

.insufficient-points-modal__text {
  /* 文字相关 */
  @apply font-400 text-14px text-center text-[#333];
}

.insufficient-points-modal__recharge-btn {
  /* 外观相关 */
  @apply btn-special;
}
</style>
