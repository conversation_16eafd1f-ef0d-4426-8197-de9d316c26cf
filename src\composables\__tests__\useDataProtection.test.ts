import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import {
  useDataProtection,
  checkUnsavedChangesBeforeLeave,
} from '../useDataProtection';
import { eventBus, EVENT_NAMES } from '@/utils/eventBus';

// Mock lodash-es
vi.mock('lodash-es', () => ({
  debounce: vi.fn(fn => fn),
}));

// Mock eventBus
vi.mock('@/utils/eventBus', () => ({
  eventBus: {
    on: vi.fn(),
    off: vi.fn(),
    emit: vi.fn(),
  },
  EVENT_NAMES: {
    CHECK_UNSAVED_CHANGES: 'checkUnsavedChanges',
    UNSAVED_CHANGES_RESPONSE: 'unsavedChangesResponse',
  },
}));

describe('useDataProtection', () => {
  let mockAddEventListener: ReturnType<typeof vi.fn>;
  let mockRemoveEventListener: ReturnType<typeof vi.fn>;

  beforeEach(() => {
    vi.clearAllMocks();

    // Mock DOM event listeners
    mockAddEventListener = vi.fn();
    mockRemoveEventListener = vi.fn();

    Object.defineProperty(window, 'addEventListener', {
      value: mockAddEventListener,
      writable: true,
    });

    Object.defineProperty(window, 'removeEventListener', {
      value: mockRemoveEventListener,
      writable: true,
    });

    Object.defineProperty(document, 'addEventListener', {
      value: mockAddEventListener,
      writable: true,
    });

    Object.defineProperty(document, 'removeEventListener', {
      value: mockRemoveEventListener,
      writable: true,
    });
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('基础功能测试', () => {
    it('应该正确初始化默认状态', () => {
      const dataProtection = useDataProtection();

      expect(dataProtection.hasUnsavedChanges.value).toBe(false);
      expect(dataProtection.isInitialized.value).toBe(false);
      expect(dataProtection.isSilentPeriod.value).toBe(false);
    });

    it('应该支持自定义配置选项', () => {
      const options = {
        stepIndex: 1,
        enableBrowserProtection: false,
        enableExitProtection: true,
        debounceDelay: 500,
        silentPeriodDuration: 2000,
      };

      const dataProtection = useDataProtection(options);

      // 验证配置被正确应用（通过行为验证）
      expect(dataProtection.hasUnsavedChanges.value).toBe(false);
    });

    it('应该正确设置和重置未保存状态', () => {
      const dataProtection = useDataProtection();

      // 设置未保存状态
      dataProtection.setUnsavedChanges(true);
      expect(dataProtection.hasUnsavedChanges.value).toBe(true);

      // 重置未保存状态
      dataProtection.resetUnsavedChanges();
      expect(dataProtection.hasUnsavedChanges.value).toBe(false);
      expect(dataProtection.isSilentPeriod.value).toBe(true);
    });
  });

  describe('浏览器保护功能测试', () => {
    it('应该在启用浏览器保护时注册事件监听器', () => {
      const dataProtection = useDataProtection({
        enableBrowserProtection: true,
        enableExitProtection: false,
      });

      // 手动调用初始化，因为测试环境中 onMounted 不会自动执行
      dataProtection.initialize();

      // 验证 beforeunload 事件监听器被注册
      expect(mockAddEventListener).toHaveBeenCalledWith(
        'beforeunload',
        expect.any(Function),
      );

      // 验证表单变更事件监听器被注册
      expect(mockAddEventListener).toHaveBeenCalledWith(
        'input',
        expect.any(Function),
        true,
      );
      expect(mockAddEventListener).toHaveBeenCalledWith(
        'change',
        expect.any(Function),
        true,
      );
    });

    it('应该在禁用浏览器保护时不注册事件监听器', () => {
      const dataProtection = useDataProtection({
        enableBrowserProtection: false,
        enableExitProtection: false,
      });

      // 手动调用初始化
      dataProtection.initialize();

      // 验证 beforeunload 事件监听器未被注册
      expect(mockAddEventListener).not.toHaveBeenCalledWith(
        'beforeunload',
        expect.any(Function),
      );
    });
  });

  describe('EventBus 通信功能测试', () => {
    it('应该在启用退出保护时注册 EventBus 监听器', () => {
      const dataProtection = useDataProtection({
        stepIndex: 0,
        enableBrowserProtection: false,
        enableExitProtection: true,
      });

      // 手动调用初始化
      dataProtection.initialize();

      expect(eventBus.on).toHaveBeenCalledWith(
        EVENT_NAMES.CHECK_UNSAVED_CHANGES,
        expect.any(Function),
      );
    });

    it('应该在禁用退出保护时不注册 EventBus 监听器', () => {
      const dataProtection = useDataProtection({
        enableBrowserProtection: false,
        enableExitProtection: false,
      });

      // 手动调用初始化
      dataProtection.initialize();

      expect(eventBus.on).not.toHaveBeenCalled();
    });

    it('应该正确响应数据保护检查事件', () => {
      const dataProtection = useDataProtection({
        stepIndex: 0,
        enableExitProtection: true,
      });

      // 手动调用初始化
      dataProtection.initialize();

      // 设置未保存状态
      dataProtection.setUnsavedChanges(true);

      // 获取注册的事件处理函数
      const eventHandler = (eventBus.on as any).mock.calls.find(
        (call: any) => call[0] === EVENT_NAMES.CHECK_UNSAVED_CHANGES,
      )?.[1];

      expect(eventHandler).toBeDefined();

      // 模拟事件调用
      const mockEventData = {
        requestId: 'test-request-id',
        currentStep: 0,
      };

      eventHandler(mockEventData);

      // 验证响应事件被发送
      expect(eventBus.emit).toHaveBeenCalledWith(
        EVENT_NAMES.UNSAVED_CHANGES_RESPONSE,
        {
          requestId: 'test-request-id',
          hasUnsavedChanges: true,
        },
      );
    });
  });

  describe('静默期机制测试', () => {
    it('应该在重置后进入静默期', async () => {
      const dataProtection = useDataProtection();

      // 设置未保存状态
      dataProtection.setUnsavedChanges(true);
      expect(dataProtection.hasUnsavedChanges.value).toBe(true);

      // 重置状态
      dataProtection.resetUnsavedChanges();
      expect(dataProtection.hasUnsavedChanges.value).toBe(false);
      expect(dataProtection.isSilentPeriod.value).toBe(true);

      // 等待静默期结束
      await new Promise(resolve => setTimeout(resolve, 1100));
      expect(dataProtection.isSilentPeriod.value).toBe(false);
    });
  });

  describe('生命周期管理测试', () => {
    it('应该在清理时移除所有事件监听器', () => {
      const dataProtection = useDataProtection({
        stepIndex: 0,
        enableBrowserProtection: true,
        enableExitProtection: true,
      });

      // 先初始化，再清理
      dataProtection.initialize();
      dataProtection.cleanup();

      // 验证浏览器事件监听器被移除
      expect(mockRemoveEventListener).toHaveBeenCalledWith(
        'beforeunload',
        expect.any(Function),
      );

      // 验证 EventBus 监听器被移除
      expect(eventBus.off).toHaveBeenCalledWith(
        EVENT_NAMES.CHECK_UNSAVED_CHANGES,
        expect.any(Function),
      );
    });

    it('应该支持重新激活功能', () => {
      const dataProtection = useDataProtection({
        enableBrowserProtection: true,
      });

      // 先初始化
      dataProtection.initialize();
      const initialCallCount = mockAddEventListener.mock.calls.length;

      // 清理后重新激活
      dataProtection.cleanup();
      dataProtection.reactivate();

      // 验证事件监听器被重新注册（应该比初始调用次数多）
      expect(mockAddEventListener.mock.calls.length).toBeGreaterThan(
        initialCallCount,
      );
    });
  });
});

describe('checkUnsavedChangesBeforeLeave', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('应该正确发送检查请求并处理响应', async () => {
    const currentStep = 0;

    // 模拟异步响应
    setTimeout(() => {
      const emitCall = (eventBus.emit as any).mock.calls.find(
        (call: any) => call[0] === EVENT_NAMES.CHECK_UNSAVED_CHANGES,
      );

      if (emitCall) {
        const requestData = emitCall[1];

        // 模拟响应
        const onCall = (eventBus.on as any).mock.calls.find(
          (call: any) => call[0] === EVENT_NAMES.UNSAVED_CHANGES_RESPONSE,
        );

        if (onCall) {
          const responseHandler = onCall[1];
          responseHandler({
            requestId: requestData.requestId,
            hasUnsavedChanges: true,
          });
        }
      }
    }, 10);

    const result = await checkUnsavedChangesBeforeLeave(currentStep);

    expect(result).toBe(true);
    expect(eventBus.emit).toHaveBeenCalledWith(
      EVENT_NAMES.CHECK_UNSAVED_CHANGES,
      expect.objectContaining({
        currentStep: 0,
        requestId: expect.any(String),
      }),
    );
  });

  it('应该在超时时返回 false', async () => {
    const result = await checkUnsavedChangesBeforeLeave(0, 100);

    expect(result).toBe(false);
  });
});
