<template>
  <div class="adm-set">
    <template v-if="!isPreloading">
      <template v-if="hasPermission">
        <div class="adm-set__layout">
          <!-- 左侧导航 -->
          <div class="adm-set__sider">
            <fa-menu
              mode="inline"
              v-model:selectedKeys="selectedKeys"
              class="adm-set__menu"
            >
              <fa-menu-item v-for="menu in menuConfig" :key="menu.key">
                {{ menu.title }}
              </fa-menu-item>
            </fa-menu>
          </div>

          <!-- 右侧内容区 -->
          <div class="adm-set__content">
            <div
              v-for="content in contentConfig"
              :key="content.key"
              v-show="selectedKeys[0] === content.key"
              class="adm-set__section"
            >
              <h2 class="adm-set__section-title">{{ content.title }}</h2>
              <div class="adm-set__section-content">
                <component :is="content.component" />
              </div>
            </div>
          </div>
        </div>
      </template>
      <template v-else>
        <div class="adm-set__no-permission">
          <p class="adm-set__tip">抱歉，您没有访问权限</p>
          <fa-button type="primary" @click="goHome">返回首页</fa-button>
        </div>
      </template>
    </template>
    <template v-else>
      <div class="adm-set__loading">
        <fa-spin :spinning="true">
          <div class="adm-set__skeleton">
            <!-- 骨架屏内容 -->
          </div>
        </fa-spin>
      </div>
    </template>
  </div>
</template>

<script lang="ts" setup>
import { computed, ref } from 'vue';
import store from '@/store';
import AidVersionContent from './components/AidVersionContent.vue';
import CloneProjectContent from './components/CloneProjectContent.vue';

/**
 * @description 菜单配置
 */
interface MenuItem {
  key: string;
  title: string;
}

const menuConfig: MenuItem[] = [
  {
    key: 'aid-version',
    title: '设置版本',
  },
  {
    key: 'clone-project',
    title: '克隆项目',
  },
];

/**
 * @description 内容区域配置
 */
type ContentItem = {
  key: string;
  title: string;
  component: typeof AidVersionContent;
};

const contentConfig: ContentItem[] = [
  {
    key: 'aid-version',
    title: '设置版本',
    component: AidVersionContent,
  },
  {
    key: 'clone-project',
    title: '克隆项目',
    component: CloneProjectContent,
  },
];

/**
 * @description 是否正在预加载
 */
const isPreloading = computed(() => !store.state.system.isPreload);

/**
 * @description 是否有权限访问
 */
const hasPermission = computed(() => store.state.user.isStaff);

/**
 * @description 当前选中的菜单项
 */
const selectedKeys = ref(['aid-version']);

/**
 * @description 返回首页
 */
const goHome = () => {
  window.location.href = '/';
};
</script>

<style scoped>
.adm-set {
  /* 尺寸相关 */
  @apply size-full p-24px;
}

.adm-set__layout {
  /* 布局相关 */
  @apply flex;
  /* 尺寸相关 */
  @apply size-full;
}

.adm-set__sider {
  /* 尺寸相关 */
  @apply w-200px flex-shrink-0;
  /* 外观相关 */
  @apply bg-white rounded-l-4px;
  /* 阴影相关 */
  @apply shadow-[0_2px_8px_rgba(0,0,0,0.05)];
}

.adm-set__menu {
  /* 尺寸相关 */
  @apply h-full;
  /* 外观相关 */
  @apply border-none;
}

.adm-set__content {
  /* 布局相关 */
  @apply flex-1;
  /* 外观相关 */
  @apply bg-white rounded-r-4px p-24px ml-24px;
  /* 阴影相关 */
  @apply shadow-[0_2px_8px_rgba(0,0,0,0.05)];
}

.adm-set__section {
  /* 布局相关 */
  @apply flex flex-col;
}

.adm-set__section-title {
  /* 文字相关 */
  @apply text-20px font-medium text-#333 mb-24px;
}

.adm-set__section-content {
  /* 布局相关 */
  @apply flex-1;
}

.adm-set__no-permission {
  /* 布局相关 */
  @apply flex flex-col items-center justify-center;
  /* 尺寸相关 */
  @apply h-300px;
}

.adm-set__tip {
  /* 文字相关 */
  @apply text-16px text-#666 mb-16px;
}

.adm-set__loading {
  /* 尺寸相关 */
  @apply size-full;
}

.adm-set__skeleton {
  /* 尺寸相关 */
  @apply h-300px;
  /* 外观相关 */
  @apply bg-#f5f5f5 rounded-4px;
}
</style>
