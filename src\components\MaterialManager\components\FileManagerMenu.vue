<template>
  <ul class="file-manager-demo-menu notCancelSelect">
    <li
      v-for="item in list"
      :key="item.key"
      class="flex ai-center jc-center"
      @click="$emit('click', item.key)"
    >
      <FaIcon :type="item.icon" class="icon" />
      <span class="text">{{ item.name }}</span>
    </li>
  </ul>
</template>

<script>
import { Icon as FaIcon } from '@fk/faicomponent';
import store from '@/store';

export default {
  name: 'FileManagerMenu',
  components: { FaIcon },

  computed: {
    category() {
      return store.state.meta.category;
    },

    list() {
      const list = [];

      if (this.category === 'file') {
        list.push({
          key: 'move',
          icon: 'drag',
          name: '移动',
        });
      } else {
        list.push({
          key: 'restore',
          icon: 'drag',
          name: '还原',
        });
      }

      list.push({
        key: 'delete',
        icon: 'delete',
        name: this.category === 'file' ? '删除' : '彻底删除',
      });

      return list;
    },
  },
};
</script>

<style lang="scss" scoped>
.file-manager-demo-menu {
  width: 88px;
  padding: 4px 0;
  background-color: #fff;
  list-style-type: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);

  li {
    height: 32px;
    cursor: pointer;

    &:hover {
      background-color: #f0f7ff;
    }
  }

  .icon {
    margin-right: 8px;
    font-size: 16px;
    color: rgb(153, 153, 153);
  }

  .text {
    font-size: 14px;
    color: #333;
  }
}
</style>
