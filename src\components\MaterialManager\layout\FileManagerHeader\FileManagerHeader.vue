<template>
  <FaFileManagerFolderPath
    :folder-list="currDirPath"
    :style="{ margin: '0 30px 4px' }"
    @click="handleFolderPathClick"
  />
</template>

<script>
import { FileManager as FaFileManager } from '@fk/fa-component-cus';
import store from '@/store';
import { INFO_KEYS } from '@/constants/material';

export default {
  name: 'FileManagerHeader',
  components: {
    FaFileManagerFolderPath: FaFileManager.FileManagerFolderPath,
  },

  data() {
    return {};
  },

  computed: {
    currDirPath() {
      return store.getters.currDirPath;
    },
  },

  methods: {
    handleFolderPathClick(info) {
      store.commit('changeDir', info[INFO_KEYS.ID]);
      store.dispatch('updateFolderContent');
    },
  },
};
</script>

<style lang="scss" scoped></style>
