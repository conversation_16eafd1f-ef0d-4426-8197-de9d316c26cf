<template>
  <div class="clone-project">
    <div class="clone-project__form">
      <fa-form layout="vertical">
        <fa-form-item label="输入aid">
          <fa-input
            v-model:value="aidValue"
            :allowClear="true"
            placeholder="请输入aid"
          />
        </fa-form-item>
        <fa-form-item label="输入项目id">
          <fa-input
            v-model:value="projectIdValue"
            :allowClear="true"
            placeholder="请输入项目id"
          />
        </fa-form-item>
        <fa-form-item>
          <fa-button type="primary" @click="handleSubmit">克隆</fa-button>
        </fa-form-item>
      </fa-form>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import { message } from '@fk/faicomponent';
import { cloneProject } from '@/api/AdmSetView';

const aidValue = ref('');
const projectIdValue = ref('');

const handleSubmit = async () => {
  if (!aidValue.value || !projectIdValue.value) {
    message.error('请输入aid和项目id');
    return;
  }
  const [err] = await cloneProject(
    Number(aidValue.value),
    Number(projectIdValue.value),
  );
  if (err) {
    message.error(err?.message || '克隆失败');
    return;
  }
  message.success('克隆成功');
};
</script>

<style scoped>
.clone-project__form {
  /* 尺寸相关 */
  @apply max-w-250px;
}
</style>
