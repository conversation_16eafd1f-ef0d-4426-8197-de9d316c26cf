import { ref, computed } from 'vue';

/** 任务数量 */
const taskCount = ref(0);

/** 是否显示骨架屏 */
const isShowSkeleton = computed(() => taskCount.value > 0);

/** 增加任务 */
export function addSkeletonTask() {
  taskCount.value++;
}

/** 完成任务 */
export function finishSkeletonTask() {
  if (taskCount.value > 0) {
    taskCount.value--;
  }
}

export function useSkeleton() {
  return {
    /** 是否显示骨架屏 */
    isShowSkeleton,
    /** 增加任务 */
    addSkeletonTask,
    /** 完成任务 */
    finishSkeletonTask,
  };
}
