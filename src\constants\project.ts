/** 项目类型 */
export enum PROJECT_TYPE {
  /** 视频 */
  VIDEO = 0,
  /** 图文 */
  IMAGE = 1,
}

/** 项目类型名称 */
export const PROJECT_TYPE_NAME: Record<PROJECT_TYPE, string> = {
  [PROJECT_TYPE.IMAGE]: '图文',
  [PROJECT_TYPE.VIDEO]: '视频',
};

/** 项目状态 */
export enum PROJECT_STATUS {
  /** 全部 */
  ALL = -1,
  /** 草稿 */
  DRAFT = 0,
  /** 生成中 */
  GENERATING = 1,
  /** 待保存 */
  TO_BE_SAVED = 2,
  /** 已完成 */
  COMPLETED = 3,
}

/** 项目状态名称 */
export const PROJECT_STATUS_NAME: Record<PROJECT_STATUS, string> = {
  [PROJECT_STATUS.ALL]: '全部',
  [PROJECT_STATUS.DRAFT]: '草稿',
  [PROJECT_STATUS.GENERATING]: '生成中',
  [PROJECT_STATUS.TO_BE_SAVED]: '待保存',
  [PROJECT_STATUS.COMPLETED]: '已完成',
};

/** 项目状态样式类名 */
export const PROJECT_STATUS_CLASS_NAME: Record<PROJECT_STATUS, string> = {
  [PROJECT_STATUS.ALL]: '',
  [PROJECT_STATUS.DRAFT]: 'status-draft',
  [PROJECT_STATUS.GENERATING]: 'status-processing',
  [PROJECT_STATUS.TO_BE_SAVED]: 'status-processing',
  [PROJECT_STATUS.COMPLETED]: 'status-completed',
};

/** 项目状态列表 */
export const PROJECT_STATUS_LIST = [
  {
    status: PROJECT_STATUS.ALL,
    text: PROJECT_STATUS_NAME[PROJECT_STATUS.ALL],
  },
  {
    status: PROJECT_STATUS.DRAFT,
    text: PROJECT_STATUS_NAME[PROJECT_STATUS.DRAFT],
  },
  {
    status: PROJECT_STATUS.GENERATING,
    text: PROJECT_STATUS_NAME[PROJECT_STATUS.GENERATING],
  },
  {
    status: PROJECT_STATUS.TO_BE_SAVED,
    text: PROJECT_STATUS_NAME[PROJECT_STATUS.TO_BE_SAVED],
  },
  {
    status: PROJECT_STATUS.COMPLETED,
    text: PROJECT_STATUS_NAME[PROJECT_STATUS.COMPLETED],
  },
];

// 编辑器类型全局状态
export let currentEditorType: 'image' | 'video' = 'video';
export function setEditorType(type: 'image' | 'video') {
  currentEditorType = type;
}
