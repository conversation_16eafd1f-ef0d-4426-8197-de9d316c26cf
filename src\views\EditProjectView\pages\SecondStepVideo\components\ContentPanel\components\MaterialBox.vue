<template>
  <div class="material-box" :class="materialBoxClasses">
    <!-- 纯内容区布局 -->
    <template v-if="layout === 'content-only'">
      <!-- 使用 FaScrollArea 组件替代原生滚动实现 -->
      <FaScrollArea
        class="material-box--layout-content-only__scroll-area"
        max-height="360px"
        :thumb-style="scrollAreaStyles.thumbStyle"
        :bar-style="scrollAreaStyles.barStyle"
      >
        <div class="material-box--layout-content-only__content-full">
          <slot name="content">
            <pre>{{ content }}</pre>
          </slot>
        </div>
      </FaScrollArea>
    </template>

    <!-- 左侧内容区+右侧操作按钮布局 -->
    <template v-else-if="layout === 'content-actions'">
      <!-- 使用 FaScrollArea 组件替代原生滚动实现 -->
      <FaScrollArea
        class="material-box--layout-content-actions__scroll-area"
        max-height="360px"
        :thumb-style="scrollAreaStyles.thumbStyle"
        :bar-style="scrollAreaStyles.barStyle"
      >
        <div class="material-box--layout-content-actions__content">
          <slot name="content" v-if="type === 'text'">
            <pre>{{ content }}</pre>
          </slot>
          <slot name="content" v-else>
            <div class="content-panel__media-info">
              <div class="content-panel__media-icon">
                <el-image fit="cover" :src="mediaInfo.img" />
              </div>
              <div class="content-panel__media-details">
                <div class="content-panel__media-name">
                  {{ mediaInfo.name }}
                </div>
                <div class="content-panel__media-description">
                  {{ mediaInfo.description }}
                </div>
              </div>
            </div>
          </slot>
        </div>
      </FaScrollArea>
      <div class="material-box--layout-content-actions__actions">
        <slot name="actions">
          <fa-button
            type="default"
            size="small"
            :disabled="disabled"
            @click="handleActionButtonClick"
            >{{ computedActionButtonText }}</fa-button
          >
        </slot>
      </div>
    </template>

    <!-- 顶部区(左侧标题+右侧按钮)+内容区布局 -->
    <template v-else-if="layout === 'header-content'">
      <div class="material-box--layout-header-content__header">
        <div class="material-box--layout-header-content__header-title">
          <slot name="header-title">{{ title }}</slot>
        </div>
        <div class="material-box--layout-header-content__header-actions">
          <slot name="header-actions">
            <fa-button
              class="material-box--layout-header-content__header-actions-copy"
              type="link"
              size="small"
              @click="handleCopyButtonClick"
            >
              <Icon type="fuzhi" class="size-[16px] mr-[4px]" />
              {{ computedActionButtonText }}</fa-button
            >
          </slot>
        </div>
      </div>
      <!-- 使用 FaScrollArea 组件替代原生滚动实现 296px=360px-64px(顶部区高度、padding、margin) -->
      <FaScrollArea
        class="material-box--layout-header-content__scroll-area"
        max-height="296px"
        :thumb-style="scrollAreaStyles.thumbStyle"
        :bar-style="scrollAreaStyles.barStyle"
      >
        <div class="material-box--layout-header-content__body">
          <slot name="body">
            <pre>{{ content }}</pre>
          </slot>
        </div>
      </FaScrollArea>
    </template>
  </div>
</template>

<script>
import copy from 'clipboard-copy';
import { getDefaultScrollAreaStyles } from '@/constants/scrollArea';
import { message } from '@fk/faicomponent';

/**
 * @description 素材显示容器组件，支持文本、音乐、配音等不同类型的素材显示
 * 使用 FaScrollArea 组件实现统一的滚动区域样式
 */
export default {
  name: 'MaterialBox',
  props: {
    /**
     * @description 素材类型
     * @values text, music, voice
     */
    type: {
      type: String,
      default: 'text',
      validator: value => ['text', 'music', 'voice'].includes(value),
    },
    /**
     * @description 布局类型
     * @values content-only (仅内容区), content-actions (左侧内容区+右侧操作按钮), header-content (顶部区+内容区)
     */
    layout: {
      type: String,
      default: 'content-actions',
      validator: value =>
        ['content-only', 'content-actions', 'header-content'].includes(value),
    },

    /**
     * @description 操作按钮文本
     */
    actionButtonText: {
      type: String,
      default: '操作按钮',
    },
    /**
     * @description 操作按钮是否禁用
     */
    disabled: {
      type: Boolean,
      default: false,
    },
    /**
     * @description 要复制的文本内容
     */
    copyText: {
      type: String,
      default: '',
    },
    /**
     * @description 复制失败时的提示文案
     */
    emptyMessage: {
      type: String,
      default: '没有可复制的内容',
    },
    /**
     * @description 标题
     */
    title: {
      type: String,
      default: '',
    },
    /**
     * @description 显示的内容
     */
    content: {
      type: String,
      default: '',
    },
    /**
     * @description 音乐或配音信息
     */
    mediaInfo: {
      type: Object,
      default: () => ({}),
    },
  },
  computed: {
    /**
     * @description 组件类名
     */
    materialBoxClasses() {
      return [
        `material-box--type-${this.type}`,
        `material-box--layout-${this.layout}`,
      ];
    },
    /**
     * @description 根据type和layout动态计算操作按钮文本
     */
    computedActionButtonText() {
      // 如果传入了actionButtonText，优先使用传入的值
      if (this.actionButtonText !== '操作按钮') {
        return this.actionButtonText;
      }

      // 根据type和layout动态计算
      if (this.type === 'text') {
        return this.layout === 'header-content' ? '复制' : '编辑脚本';
      }
      if (this.type === 'music') {
        return '更换音乐';
      }
      if (this.type === 'voice') {
        return '更换配音';
      }

      return '操作按钮';
    },

    /**
     * @description 滚动区域样式配置
     * 使用项目统一的滚动区域样式
     */
    scrollAreaStyles() {
      return getDefaultScrollAreaStyles({
        thumbStyle: { right: '-16px' },
      });
    },
  },
  methods: {
    /**
     * @description 处理操作按钮点击事件
     * @emits click 触发操作按钮点击事件
     */
    handleActionButtonClick() {
      this.$emit('click');
    },
    /**
     * @description 复制文本到剪贴板
     */
    async handleCopyButtonClick() {
      try {
        const textToCopy = this.copyText;
        if (!textToCopy) {
          message.warning(this.emptyMessage);
          this.$emit('copyButtonClick', false);
          return;
        }
        await copy(textToCopy);
        message.success('复制成功');
        this.$emit('copyButtonClick', true);
      } catch (err) {
        console.error('复制失败:', err);
        message.error('复制失败');
        this.$emit('copyButtonClick', false);
      }
    },
  },
};
</script>

<style lang="scss" scoped>
/* 基础容器样式 */
.material-box {
  /* 布局相关 */
  @apply flex flex-col;
  /* 尺寸相关 */
  @apply w-full p-16px;
  /* 外观相关 */
  @apply bg-[#f3f3f5] border-rd-8px;
}

/* 纯内容布局变体 */
.material-box--layout-content-only {
  /* 布局相关 */
  @apply block py-0px;
}

/* 纯内容布局的滚动区域容器 */
.material-box--layout-content-only__scroll-area {
  /* 布局相关 */
  @apply w-full;
}

.material-box--layout-content-only__content-full {
  /* 布局相关 */
  @apply w-full py-16px;
  /* 文字相关 */
  @apply text-14px text-left text-[#333] font-400 lh-21px;

  & > pre {
    /* 文字换行处理 */
    @apply sc-text-wrap-smart;
  }
}

/* 左侧内容+右侧操作布局变体 */
.material-box--layout-content-actions {
  /* 布局相关 */
  @apply flex flex-row items-center;
}

/* 左侧内容+右侧操作布局的滚动区域容器 */
.material-box--layout-content-actions__scroll-area {
  /* 布局相关 */
  @apply flex-1;
}

/* 内容区域样式 */
.material-box--layout-content-actions__content {
  /* 布局相关 */
  @apply w-full;
  /* 文字相关 */
  @apply text-14px text-left text-[#333] font-400 lh-21px;
  /* 性能优化：启用硬件加速 */
  @apply transform-gpu;

  & > pre {
    /* 文字换行处理 */
    @apply sc-text-wrap-smart;
  }
}

/* 操作按钮区域基础样式 */
.material-box--layout-content-actions__actions {
  /* 布局相关 */
  @apply flex items-center;
  /* 尺寸相关 */
  @apply ml-12px;
  /* 过渡动画 */
  @apply transition-all duration-300 ease-in-out;
}

/* 顶部+内容布局变体 */
.material-box--layout-header-content {
  /* 布局相关 */
  @apply flex flex-col pb-0;
}

.material-box--layout-header-content__header {
  /* 布局相关 */
  @apply flex items-center w-full h-[20px];
}

.material-box--layout-header-content__header-title {
  /* 布局相关 */
  @apply flex-1;
  /* 文字相关 */
  @apply text-14px text-left text-[#111] font-700;
}

.material-box--layout-header-content__header-actions {
  /* 布局相关 */
  @apply flex items-center;

  /* 复制按钮链接样式 */
  :deep(.fa-btn-link) {
    /* 文字相关 */
    @apply text-[#999] pr-0;
  }
  /* 复制按钮链接悬停和焦点状态（排除disabled状态） */
  :deep(.fa-btn-link:hover:not([disabled])),
  :deep(.fa-btn-link:focus:not([disabled])) {
    /* 文字相关 */
    @apply text-[#666];
  }
}

.material-box--layout-header-content__header-actions-copy.fa-btn {
  /* 布局相关 */
  @apply flex items-center;
  /* 文字相关 */
  @apply text-14px text-left text-[#999] font-400;
}

/* 顶部+内容布局的滚动区域容器 */
.material-box--layout-header-content__scroll-area {
  /* 布局相关 */
  @apply w-full mt-12px;
}

.material-box--layout-header-content__body {
  /* 布局相关 */
  @apply w-full;
  /* 文字相关 */
  @apply text-14px text-left text-[#333] font-400 lh-21px pb-16px;
  & > pre {
    /* 文字换行处理 */
    @apply sc-text-wrap-smart;
  }
}

/* 素材类型特有样式 */
/* 文本类型素材样式 - 使用默认样式 */

.material-box--type-music,
.material-box--type-voice {
  @apply py-12px px-16px;
  .content-panel__media-info {
    /* 布局相关 */
    @apply flex items-center;
    /* 尺寸相关 */
    @apply w-full;
  }

  .content-panel__media-icon {
    /* 布局相关 */
    @apply flex-shrink-0;
    /* 尺寸相关 */
    @apply w-40px h-40px mr-12px;
    /* 外观相关 */
    @apply border-rd-40px overflow-hidden;

    :deep(.el-image) {
      @apply w-full h-full;
    }
  }

  .content-panel__media-details {
    /* 布局相关 */
    @apply flex-1 flex flex-col;
    /* 尺寸相关 */
    @apply min-w-0;
  }

  .content-panel__media-name {
    /* 文字相关 */
    @apply font-400 text-14px lh-21px text-left text-[#333];
    /* 布局相关 */
    @apply truncate;
  }

  .content-panel__media-description {
    /* 文字相关 */
    @apply font-400 text-14px lh-21px text-left text-[#bfbfbf] mt-2px;
    /* 布局相关 */
    @apply truncate;
  }
}
</style>
