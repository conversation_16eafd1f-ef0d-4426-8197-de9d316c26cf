# InsufficientPointsModal 点数不足提示弹窗

## 组件描述

点数不足提示弹窗组件，用于在用户点数不足时显示友好的提示信息，并提供充值入口。

## 功能特性

- 🎯 专用于点数不足场景的提示弹窗
- 🔒 不可通过遮罩或 ESC 键关闭，确保用户看到提示
- 🎨 统一的 UI 设计，符合项目整体风格
- 📱 响应式设计，适配不同屏幕尺寸
- 🔄 支持.sync 修饰符，便于状态管理
- 🎪 提供充值和取消两个操作选项

## Props

| 参数    | 类型    | 默认值 | 说明                           |
| ------- | ------- | ------ | ------------------------------ |
| visible | Boolean | false  | 弹窗显示状态，支持.sync 修饰符 |

## Events

| 事件名         | 说明                   | 回调参数           |
| -------------- | ---------------------- | ------------------ |
| update:visible | 弹窗显示状态变化时触发 | (visible: boolean) |
| recharge       | 点击"去充值"按钮时触发 | -                  |
| cancel         | 点击"取消"按钮时触发   | -                  |

## 基本用法

```vue
<template>
  <div>
    <!-- 触发按钮 -->
    <fa-button @click="showModal">测试点数不足弹窗</fa-button>

    <!-- 点数不足弹窗 -->
    <InsufficientPointsModal
      :visible.sync="modalVisible"
      @recharge="handleRecharge"
      @cancel="handleCancel"
    />
  </div>
</template>

<script>
import InsufficientPointsModal from '@/components/InsufficientPointsModal/index.vue';

export default {
  components: {
    InsufficientPointsModal,
  },
  data() {
    return {
      modalVisible: false,
    };
  },
  methods: {
    showModal() {
      this.modalVisible = true;
    },
    handleRecharge() {
      console.log('跳转到充值页面');
      // 实现充值逻辑
      // 例如：this.$router.push('/recharge')
      // 或者：window.open('/recharge', '_blank')

      // 注意：如果使用新窗口打开充值页面，
      // 需要监听页面可见性变化来更新积分
    },
    handleCancel() {
      console.log('用户取消充值');
      // 可以在这里添加取消后的逻辑
    },
  },
};
</script>
```

## Composition API 用法

```vue
<template>
  <div>
    <fa-button @click="showModal">测试点数不足弹窗</fa-button>

    <InsufficientPointsModal
      :visible.sync="modalVisible"
      @recharge="handleRecharge"
      @cancel="handleCancel"
    />
  </div>
</template>

<script setup>
import { ref } from 'vue';
import InsufficientPointsModal from '@/components/InsufficientPointsModal/index.vue';

const modalVisible = ref(false);

const showModal = () => {
  modalVisible.value = true;
};

const handleRecharge = () => {
  console.log('跳转到充值页面');
  // 实现充值逻辑
};

const handleCancel = () => {
  console.log('用户取消充值');
  // 可以在这里添加取消后的逻辑
};
</script>
```

## 在业务场景中的使用

### 场景 1：生成作品时点数不足

```vue
<script>
export default {
  methods: {
    async handleGenerate() {
      // 校验表单
      const formValid = await this.validateForm();
      if (!formValid) {
        return;
      }

      // 校验点数
      if (this.userPoints < this.requiredPoints) {
        this.insufficientPointsVisible = true;
        return;
      }

      // 执行生成逻辑
      this.generateContent();
    },

    handleRecharge() {
      // 跳转到充值页面
      this.$router.push('/recharge');
    },
  },
};
</script>
```

### 场景 2：结合状态管理和积分更新

```vue
<script>
import { mapState } from 'vuex';

export default {
  data() {
    return {
      isReturningFromRecharge: false,
    };
  },
  computed: {
    ...mapState(['userPoints']),
  },
  mounted() {
    // 监听页面可见性变化，用于充值后更新积分
    document.addEventListener('visibilitychange', this.handleVisibilityChange);
  },
  beforeDestroy() {
    document.removeEventListener(
      'visibilitychange',
      this.handleVisibilityChange,
    );
  },
  methods: {
    checkPoints(requiredPoints) {
      if (this.userPoints < requiredPoints) {
        this.insufficientPointsVisible = true;
        return false;
      }
      return true;
    },

    handleRecharge() {
      // 设置标记，表示用户即将去充值
      this.isReturningFromRecharge = true;

      // 跳转到充值页面
      this.$router.push('/recharge');
      // 或者在新窗口打开
      // window.open('/recharge', '_blank');
    },

    handleVisibilityChange() {
      // 当页面变为可见且标记为从充值页面返回时，更新积分
      if (!document.hidden && this.isReturningFromRecharge) {
        console.log('用户从充值页面返回，更新积分');
        this.$store.dispatch('updateUserPoints');
        this.isReturningFromRecharge = false;
      }
    },
  },
};
</script>
```

## 样式定制

组件使用了 UnoCSS 和 BEM 命名规范，可以通过以下方式进行样式定制：

```scss
// 自定义弹窗样式
.insufficient-points-modal {
  // 自定义弹窗容器样式
}

.insufficient-points-modal__content {
  // 自定义内容区域样式
}

.insufficient-points-modal__text {
  // 自定义文本样式
}
```

## 注意事项

1. **不可关闭设计**：弹窗设计为不可通过遮罩或 ESC 键关闭，确保用户必须做出选择
2. **状态管理**：建议使用.sync 修饰符来管理弹窗显示状态
3. **充值逻辑**：需要在父组件中实现具体的充值跳转逻辑
4. **用户体验**：建议在点数校验前先进行表单校验，避免用户填写完表单后才发现点数不足

## 相关组件

- `ProjectFooter` - 项目底部操作栏
- `UserPointDisplay` - 用户点数显示组件
- `GenerateButton` - 生成按钮组件
