/**
 * @file API 响应数据类型定义
 * @description 该文件集中定义了 EditProjectView 模块后端接口的响应体类型。
 */

import type { FileStatus } from '@/views/EditProjectView/types/index';
import type { ApiResponse } from '@/api/request';

/**
 * 模板表单项配置（后端返回的原始结构）
 */
export interface TemplateInputFormItem {
  /** 字段标签 */
  label: string;
  /** 字段变量名 */
  variable: string;
  /** 是否必填 */
  required: boolean;
  /** 最大长度 */
  maxLength: number;
  /** 字段类型 */
  filedType: number;
  /** 描述信息 */
  desc: string;
  /** 是否开启AI提示 */
  openAiTip: boolean;
  /** 提示语 */
  prompt: string;
  /** 默认填写内容 */
  defaultContent: string;
}

/**
 * 模板资源表单项配置（后端返回的原始结构）
 */
export interface TemplateResFormItem {
  /** 表单项ID */
  id: number;
  /** 类型（0: 视频，1: 图片） */
  type: number;
  /** 标签 */
  label: string;
  /** 是否必填 */
  required: boolean;
  /** 是否可改变形状 */
  modifyMouth: boolean;
  /** 资源ID */
  resId: string;
  /** 封面ID */
  coverId: string;
  /** 资源类型 */
  resType: number;
  /** 封面类型 */
  coverType: number;
}

/**
 * 模板脚本数据（后端返回的原始结构）
 */
export interface TemplateScript {
  /** 脚本标题 */
  title: string;
  /** 脚本内容 */
  content: string;
}

/**
 * 获取模板列表接口 - 响应数据
 */
export interface GetTemplateListResponse {
  /** 模板ID */
  id: number;
  /** 原型ID */
  protoId: number;
  /** 类型 */
  type: number;
  /** 脚本数据 */
  script: TemplateScript;
  /** 输入表单项列表 */
  inputFormList: TemplateInputFormItem[];
  /** 资源表单项列表 */
  resFormList: TemplateResFormItem[];
}

/**
 * 项目资源上传项（后端返回的原始结构）
 */
export interface ProjectResUploadItem {
  /** 上传ID */
  uploadId: string;
  /** 上传类型 */
  uploadType: number;
  /** 封面ID */
  coverId: string;
  /** 封面类型 */
  coverType: number;
  /** 状态 */
  status: FileStatus;
  /** 时长-视频才有 */
  duration?: number;
}

/**
 * 项目资源表单项（后端返回的原始结构）
 */
export interface ProjectResFormItem {
  /** 表单项ID */
  id: number;
  /** 资源上传项列表 */
  resIds: ProjectResUploadItem[];
  /** 是否修改嘴型 */
  modifyMouth?: boolean;
}

/**
 * 项目背景音乐设置（后端返回的原始结构）
 */
export interface ProjectBgmSetting {
  /** 是否开启 */
  open: boolean;
  /** 是否自动 */
  auto: boolean;
  /** 资源ID列表 */
  resIds: string[];
  /** 名称 */
  name: string;
}

/**
 * 项目配音设置（后端返回的原始结构）
 */
export interface ProjectVoiceSetting {
  /** 是否自动 */
  auto: boolean;
  /** 配音ID */
  voiceType: string;
  /** 名称 */
  name: string;
  /** 额外类型 */
  extraType?: string;
}

/**
 * 项目设置（后端返回的原始结构）
 */
export interface ProjectSetting {
  /** 背景音乐设置 */
  bgm: ProjectBgmSetting;
  /** 配音设置 */
  voice: ProjectVoiceSetting;
}

/**
 * 获取项目编辑信息接口 - 响应数据
 */
export interface GetEditInfoResponse {
  /** 应用ID */
  aid: number;
  /** 项目ID */
  id: number;
  /** 模板ID */
  templateId: number;
  /** 类型 */
  type: number;
  /** 项目状态 */
  status?: number;
  /** 输入表单数据（key-value形式） */
  inputForm: Record<string, string>;
  /** 资源表单数据 */
  resForm: ProjectResFormItem[];
  /** 项目设置 */
  setting: ProjectSetting;
}

/**
 * 生成项目预览结果接口
 */
export interface GeneratePreviewResult {
  /** 任务ID */
  taskId: string;
  /** 预览项列表 */
  previewItems: Array<{
    /** 预览项ID */
    id: number;
    /** 预览URL */
    previewUrl: string;
    /** 缩略图URL */
    thumbnailUrl: string;
  }>;
}

/**
 * 项目更新接口 - 响应数据
 */
export interface UpdateProjectResponse {
  /** 是否成功 */
  success: boolean;
  /** 提示信息 */
  message: string;
}

/**
 * 新版作品列表响应结构
 */
export interface WorkListResponse
  extends ApiResponse<{
    /** 作品列表数据 */
    workList: ApiWorkListItem[];
    /** 总数量 */
    totalSize: number;
    /** 项目状态 */
    projectStatus: number;
    /** 成功数量 */
    sucNum: number;
  }> {}

// ============= 作品列表相关响应类型 =============

/**
 * 作品列表项（API 返回的原始数据结构）
 */
export interface ApiWorkListItem {
  /** 作品ID */
  id: number;
  /** 账号ID */
  aid: number;
  /** 项目ID */
  projectId: number;
  /** 作品类型（视频：0，图文：1） */
  type: number;
  /** 作品名称 */
  name: string;
  /** 作品状态 */
  status: number;
  /** 作品生产进度（0~100） */
  progress: number;
  /** 作品时长（秒） */
  duration?: number;
  /** 作品文件大小(单位：Byte) */
  size: number;
  /** 格式化的文件大小 */
  sizeName: string;
  /** 类型名称 */
  typeName: string;
  /** 状态名称 */
  statusName: string;
  /** 项目名称 */
  projectName: string;
  /** 项目状态 */
  projectStatus: number;
  /** 封面图资源ID */
  coverImg: string;
  /** 封面图类型 */
  coverImgType: number;
  /** 创建时间 */
  createTime: number;
  /** 更新时间 */
  updateTime: number;
  /** 保存时间（可选） */
  saveTime?: number;
  /** 关联作品ID */
  relWorkId?: number;
  /** 错误信息（生成失败时） */
  errMsg?: string;
  /** 图文是否二次编辑（仅当作品状态为已生成时使用此字段判断是否显示新生成标签） */
  editAgainGraphic?: boolean;
  /** 是否重新编辑（用于统一状态处理系统） */
  editAgain?: boolean;
  /** 是否需要扣除点数（true=需要扣点，false=不需要扣点） */
  pinchPoint?: boolean;
  /** 作品数据（视频或图文） */
  data?: ApiVideoData | ApiImageTextData;
}

/**
 * 作品列表响应（新版本）
 */
export type GetWorkListResponse = WorkListResponse;

// ============= 作品详情相关响应类型 =============

/**
 * 视频脚本段落（API 返回的原始数据结构）
 */
export interface ApiVideoSegment {
  /** 模块名称 */
  module: string;
  /** 内容 */
  content: string;
  /** 高亮词 */
  highlighted: string[];
  /** 开始时间(ms) */
  beginTime: number;
  /** 结束时间(ms) */
  endTime: number;
}

/**
 * 视频脚本（API 返回的原始数据结构）
 */
export interface ApiVideoScript {
  /** 标题 */
  title: string;
  /** 标签 */
  hashtags: string[];
  /** 视频脚本段落 */
  segments: ApiVideoSegment[];
}

/**
 * 图文脚本（API 返回的原始数据结构）
 */
export interface ApiImageScript {
  /** 标题 */
  title: string;
  /** 标签 */
  hashtags: string[];
  /** 图文内容 */
  content: string;
}

/**
 * 背景音乐设置（API 返回的原始数据结构）
 */
export interface ApiBgmSetting {
  /** 是否开启 */
  open: boolean;
  /** 资源ID */
  resId?: string;
  /** 音量 */
  volume: number;
}

/**
 * 配音设置（API 返回的原始数据结构）
 */
export interface ApiVoiceSetting {
  /** 是否开启 */
  open: boolean;
  /** 配音员ID */
  voiceType?: string;
  /** 语速 */
  speed: number;
  /** 额外类型 */
  extraType?: string;
}

/**
 * 样式基础类型（API 返回的原始数据结构）
 */
export interface ApiStyleBase {
  /** 类型 */
  type: string;
  /** X坐标 */
  x: number;
  /** Y坐标 */
  y: number;
}

/**
 * 文字样式（API 返回的原始数据结构）
 */
export interface ApiTextStyle extends ApiStyleBase {
  /** 花字预设id */
  id?: number;
  /** 花字预设id */
  styleId?: number;
  /** 类型 */
  type: 'text';

  /** 文本内容 */
  text?: string;
  /** 对齐方式 */
  align?: 'left' | 'center' | 'right';
  /** 后端使用的字体 */
  fontName?: string;
  /** 字体文件名, 唯一标识 */
  fileName?: string;
  /** 字号 */
  fontSize?: number;
  /** 颜色 */
  color?: string;
  /** 描边颜色 */
  strokeColor?: string;
  /** 描边宽度 */
  strokeWidth?: number;
  /** 阴影填充颜色 */
  shadowColor?: string;
  /** 阴影X轴偏移 */
  shadowX?: number;
  /** 阴影Y轴偏移 */
  shadowY?: number;
  /** 阴影描边颜色 */
  shadowStrokeColor?: string;
  /** 阴影描边宽度 */
  shadowStrokeWidth?: number;
  /** 背景颜色 */
  boxColor?: string;
}

/**
 * 贴图样式（API 返回的原始数据结构）
 */
export interface ApiImageStyle extends ApiStyleBase {
  /** 类型 */
  type: 'image';
  /** 资源ID */
  resId: string;
  /** 资源类型 */
  resType: number;
  /** 宽度 */
  width?: number;
  /** 高度 */
  height?: number;
}

/**
 * 视频设置（API 返回的原始数据结构）
 */
export interface ApiVideoSetting {
  /** 背景音乐 */
  bgm: ApiBgmSetting;
  /** 配音 */
  voice: ApiVoiceSetting;
  /** 贴图列表 */
  graphicList?: unknown[];
  /** 样式列表 */
  style: (ApiTextStyle | ApiImageStyle)[];
}

/**
 * 图片资源（API 返回的原始数据结构）
 */
export interface ApiImageResource {
  /** 资源ID */
  id: string;
  /** 资源类型 */
  type: number;
}

/**
 * 图片拼图样式（API 返回的原始数据结构）
 */
export interface ApiImagePuzzleStyle {
  /** X坐标 */
  x: number;
  /** Y坐标 */
  y: number;
  /** 用户设置的缩放比例 */
  scale: number;
  /** 图片资源id */
  resId: string;
  /** 图片类型  */
  type: number;
}

/**
 * 图文设置项（API 返回的原始数据结构）
 */
export interface ApiImageTextSettingItem {
  /** 索引值(后端用) */
  id: number;
  /** 图片拼图类型 */
  puzzleType: number;
  /** 图片比例 */
  ratio: number;
  /** 图片拼图样式 */
  puzzleStyle: ApiImagePuzzleStyle[];
  /** 样式列表 */
  style: (ApiTextStyle | ApiImageStyle)[];
}

/**
 * 图文设置（API 返回的原始数据结构）
 */
export type ApiImageTextSetting = {
  graphicList: ApiImageTextSettingItem[];
};

/**
 * 视频资源（API 返回的原始数据结构）
 */
export interface ApiVideoResource {
  /** 完整ID */
  fullId: string;
  /** 完整类型 */
  fullType: number;
  /** 基础ID */
  baseId: string;
  /** 基础类型 */
  baseType: number;
}

/**
 * 视频数据（API 返回的原始数据结构）
 */
export interface ApiVideoData {
  baseVideo: { id: string; type: number; duration?: number };
  baseCover: { id: string; type: number };
  fullVideo: { id: string; type: number };
  fullCover: { id: string; type: number };
  /** 图形资源 */
  graphic: Record<string, unknown>[];
  /** 关联作品ID */
  relWorkId?: number;
  /** 配音ID */
  voiceType?: string;
  /** 错误信息（生成失败时） */
  errMsg?: string;
}

/**
 * 图文资源项（API 返回的原始数据结构）
 */
export interface ApiImageGraphicItem {
  /** 资源ID */
  resId: string;
  /** 资源类型 */
  type: number;
}

/**
 * 图文数据（API 返回的原始数据结构）
 */
export interface ApiImageTextData {
  /** 图文资源 */
  graphic: ApiImageGraphicItem[];
  /** 关联作品ID */
  relWorkId?: number;
  /** 错误信息（生成失败时） */
  errMsg?: string;
}

/**
 * 作品详情（API 返回的原始数据结构）
 */
export interface ApiWorkInfo {
  /** 作品ID */
  id: number;
  /** 账号ID */
  aid: number;
  /** 项目ID */
  projectId: number;
  /** 作品类型（视频：0，图文：1） */
  type: number;
  /** 作品名称 */
  name: string;
  /** 作品文件大小 */
  size: number;
  /** 标记 */
  flag: number;
  /** 脚本 */
  script: ApiVideoScript | ApiImageScript;
  /** 字幕 */
  subtitle: Record<string, unknown>;
  /** 设置 */
  setting: ApiVideoSetting | ApiImageTextSetting;
  /** 数据 */
  data: ApiVideoData | ApiImageTextData;
  /** 进度 */
  progress: number;
  /** 状态 */
  status: number;
  /** 创建时间 */
  createTime: number;
  /** 更新时间 */
  updateTime: number;
  /** 保存时间（可选） */
  saveTime?: number;
  /** 标题 */
  title?: string;
  /** 内容 */
  content?: string;
  /** 图文是否二次编辑（仅当作品状态为已生成时使用此字段判断是否显示新生成标签） */
  editAgainGraphic?: boolean;
  /** 是否重新编辑（用于统一状态处理系统） */
  editAgain?: boolean;
  /** 是否需要扣除点数（true=需要扣点，false=不需要扣点） */
  pinchPoint?: boolean;
}

/**
 * 作品详情响应
 */
export type GetWorkInfoResponse = ApiResponse<ApiWorkInfo>;

/**
 * 保存作品响应
 */
export interface SaveWorksResponse {
  /** 是否成功 */
  success: boolean;
  /** 提示信息 */
  message: string;
}

/**
 * 删除作品响应
 */
export interface DeleteWorksResponse {
  /** 是否成功 */
  success: boolean;
  /** 提示信息 */
  message: string;
}

/**
 * 获取消耗点数响应
 * @description 消耗点数查询接口的响应数据结构，直接返回点数值
 */
export type GetConsumePointResponse = number;

/**
 * 获取唯一ID响应
 * @description 获取幂等性唯一标识的响应数据结构
 */
export interface GetUniqueIdResponse {
  /** 唯一标识 */
  uniqueId: string;
}
