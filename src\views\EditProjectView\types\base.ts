/**
 * @fileoverview 基础类型定义和枚举
 * @description 包含动态表单系统的基础类型、枚举和常量定义
 * <AUTHOR>
 * @since 1.0.0
 */

import { PROJECT_TYPE_VIDEO, PROJECT_TYPE_IMAGE } from '../constants';

// ============= 基础类型别名 =============

/**
 * @description 项目类型：视频或图片
 * @typedef {typeof PROJECT_TYPE_VIDEO | typeof PROJECT_TYPE_IMAGE} ProjectType
 * - `0 (PROJECT_TYPE_VIDEO)`: 视频类型项目
 * - `1 (PROJECT_TYPE_IMAGE)`: 图片类型项目
 */
export type ProjectType = typeof PROJECT_TYPE_VIDEO | typeof PROJECT_TYPE_IMAGE;

/**
 * @description 字段类型枚举，定义表单中不同类型的字段
 * @enum {number}
 */
export enum FieldType {
  /** 文本输入字段 */
  TEXT = 0,
  /** 标签选择字段 */
  TAGS = 1,
  /** 下拉选择字段 */
  SELECT = 2,
}

// ============= 媒体类型枚举 =============

/**
 * @description 媒体类型枚举，定义支持的媒体类型常量
 * @constant
 */
export const MEDIA_TYPES = {
  /** 图片媒体类型 */
  IMAGE: 'image',
  /** 视频媒体类型 */
  VIDEO: 'video',
} as const;

/**
 * @description 媒体类型联合类型，基于MEDIA_TYPES常量创建
 * @typedef {(typeof MEDIA_TYPES)[keyof typeof MEDIA_TYPES]} MediaType
 * @type {'image' | 'video'}
 */
export type MediaType = (typeof MEDIA_TYPES)[keyof typeof MEDIA_TYPES];

// ============= 文件状态枚举 =============

/**
 * @description 文件状态常量枚举，与后端定义保持一致
 * @constant
 */
export const FILE_STATUS = {
  /**
   * 正常 - 文件可用，通常表示上传完成或处理完毕
   * @type {number}
   */
  NORMAL: 0,
  /**
   * 临时 - 文件为临时状态，可能正在上传、等待处理或处于其他中间状态
   * @type {number}
   */
  TEMP: 1,
  /**
   * 回收站 - 文件已被移至回收站，前端通常视为失效状态
   * @type {number}
   */
  RECYCLE: 2,
  /**
   * 删除 - 文件已被永久删除，前端通常视为失效状态
   * @type {number}
   */
  DELETE: 3,
} as const;

/**
 * @description 文件状态类型联合，基于FILE_STATUS常量创建
 * @typedef {(typeof FILE_STATUS)[keyof typeof FILE_STATUS]} FileStatus
 * @type {0 | 1 | 2 | 3} 对应 NORMAL | TEMP | RECYCLE | DELETE
 */
export type FileStatus = (typeof FILE_STATUS)[keyof typeof FILE_STATUS];

// ============= 表单布局枚举 =============

/**
 * @description 表单布局类型枚举，定义表单可用的布局方式
 * @constant
 */
export const FORM_LAYOUT = {
  /**
   * 水平布局 - 标签在左，控件在右
   * @type {string}
   */
  HORIZONTAL: 'horizontal',
  /**
   * 垂直布局 - 标签在上，控件在下
   * @type {string}
   */
  VERTICAL: 'vertical',
  /**
   * 内联布局 - 标签和控件在同一行
   * @type {string}
   */
  INLINE: 'inline',
} as const;

/**
 * @description 表单布局联合类型，基于FORM_LAYOUT常量创建
 * @typedef {(typeof FORM_LAYOUT)[keyof typeof FORM_LAYOUT]} FormLayout
 * @type {'horizontal' | 'vertical' | 'inline'}
 */
export type FormLayout = (typeof FORM_LAYOUT)[keyof typeof FORM_LAYOUT];

// ============= 验证相关 =============

/**
 * @description 验证触发器枚举，定义表单验证的触发时机
 * @constant
 */
export const VALIDATION_TRIGGER = {
  /**
   * 当值变化时触发验证
   * @type {string}
   */
  CHANGE: 'change',
  /**
   * 当失去焦点时触发验证
   * @type {string}
   */
  BLUR: 'blur',
  /**
   * 当获得焦点时触发验证
   * @type {string}
   */
  FOCUS: 'focus',
  /**
   * 特殊触发器，用于自定义触发时机
   * @type {string}
   */
  MAGIC: ' ',
} as const;

/**
 * @description 验证触发器联合类型，基于VALIDATION_TRIGGER常量创建
 * @typedef {(typeof VALIDATION_TRIGGER)[keyof typeof VALIDATION_TRIGGER]} ValidationTrigger
 * @type {'change' | 'blur' | 'focus' | ' '}
 */
export type ValidationTrigger =
  (typeof VALIDATION_TRIGGER)[keyof typeof VALIDATION_TRIGGER];

/**
 * @description 表单状态枚举，描述表单在不同阶段的状态
 * @constant
 */
export const FORM_STATE = {
  /**
   * 初始状态 - 表单未被修改
   * @type {string}
   */
  PRISTINE: 'pristine',
  /**
   * 已修改状态 - 表单已被用户修改
   * @type {string}
   */
  DIRTY: 'dirty',
  /**
   * 验证中状态 - 表单正在进行验证
   * @type {string}
   */
  VALIDATING: 'validating',
  /**
   * 有效状态 - 表单验证通过
   * @type {string}
   */
  VALID: 'valid',
  /**
   * 无效状态 - 表单验证未通过
   * @type {string}
   */
  INVALID: 'invalid',
  /**
   * 提交中状态 - 表单正在提交
   * @type {string}
   */
  SUBMITTING: 'submitting',
  /**
   * 已提交状态 - 表单已提交完成
   * @type {string}
   */
  SUBMITTED: 'submitted',
  /**
   * 错误状态 - 表单提交或处理过程中出现错误
   * @type {string}
   */
  ERROR: 'error',
} as const;

/**
 * @description 表单状态联合类型，基于FORM_STATE常量创建
 * @typedef {(typeof FORM_STATE)[keyof typeof FORM_STATE]} FormState
 * @type {'pristine' | 'dirty' | 'validating' | 'valid' | 'invalid' | 'submitting' | 'submitted' | 'error'}
 */
export type FormState = (typeof FORM_STATE)[keyof typeof FORM_STATE];

// ============= 通用基础接口 =============

/**
 * @description 基础验证结果接口，定义验证操作的通用返回结构
 * @interface BaseValidationResult
 */
export interface BaseValidationResult {
  /**
   * 验证是否通过
   * @type {boolean}
   */
  valid: boolean;
  /**
   * 验证相关数据，可包含验证后的表单数据或其他上下文信息
   * @type {Record<string, unknown>}
   */
  data: Record<string, unknown>;
}

/**
 * @description 表单选项项接口，用于下拉框、单选框、复选框等选择类控件的选项定义
 * @interface FormItemOption
 */
export interface FormItemOption {
  /**
   * 选项显示标签，用于UI展示
   * @type {string}
   */
  label: string;
  /**
   * 选项值，提交表单时的实际值
   * @type {string | number}
   */
  value: string | number;
  /**
   * 是否禁用该选项
   * @type {boolean}
   * @default false
   */
  disabled?: boolean;
  /**
   * 扩展属性，用于存储与选项相关的自定义数据
   * @type {unknown}
   */
  [key: string]: unknown;
}
