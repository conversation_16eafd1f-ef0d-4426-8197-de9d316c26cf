import { GET, POST_JSON } from '@/api/request';
import {
  WorkViewItem,
  WorkDetailApiResponse,
} from '@/components/WorkView/types';
export interface WorkQuery extends Record<string, unknown> {
  pageNow: number;
  limit: number;
  name?: string;
  projectId?: number;
  type?: string;
  saveStartTime?: number;
  saveEndTime?: number;
  sortKey?: string;
  desc?: boolean;
}

/**
 * 获取作品列表
 * @param params
 * @param params.isSave 是否保存作品
 * @param params.pageNow 页码
 * @param params.limit 每页数量
 * @param params.name 名称
 * @param params.type 类型
 * @param params.status 状态
 * @param params.sortKey 排序字段
 * @param params.desc 是否倒序
 * @returns
 */
export const getWorkList = (params: WorkQuery) => {
  return POST_JSON<{
    workList: WorkViewItem[];
    totalSize: number;
  }>('/api/work/getList', params);
};

/**
 * 获取作品详情
 * @param params
 * @param params.id 作品id
 * @param params.viewMode 1：编辑器弹窗 2：预览
 * @returns
 */
export const getWorkDetailInfo = (params: { id: number; viewMode: number }) => {
  return GET<WorkDetailApiResponse>('/api/work/getInfo', params);
};

/**
 * 获取资源token，用于资源鉴权
 * @returns token
 */
export const getResourceToken = () => {
  return GET<{
    token: string;
  }>('/api/resource/getToken');
};

/**
 * 删除作品
 * @param id 作品id
 * @returns
 */
export const deleteWork = (id: number) => {
  return GET<{ success: true }>('/api/work/del', {
    id,
  });
};

/**
 * 新旧视频作品选择
 * @summary 保存新的，不用传oldId，保存新旧都传
 * @param params
 * @param params.newId 新作品id
 * @param params.oldId 旧作品id
 * @returns
 */
export const selectWork = (params: { newId: string; oldId?: string }) => {
  return GET<{ success: true }>('/api/work/selectWork', params);
};

/**
 * 恢复旧作品
 * @param params
 * @param params.oldId 旧作品id
 * @returns
 */
export const restoreOldWork = (params: { oldId: string }) => {
  return GET<{ success: true }>('/api/work/selectWork', {
    oldId: params.oldId,
  });
};

/**
 * 修改作品名称
 * @param params
 * @param params.id 作品id
 * @param params.name 新名称
 * @returns
 */
export const updateWorkName = (params: { id: number; name: string }) => {
  return POST_JSON<{ success: true }>('/api/work/update', params);
};

/**
 * @description 更新作品标识
 * @summary 用于标识作品已被用户查看，通常在用户点击新生成的作品后调用
 * @param params 请求参数
 * @param params.id 作品ID（必填）- 需要更新标识的作品唯一标识
 */
export const updateWorkFlag = async (params: { id: number }) => {
  return GET('/api/work/updateFlag', {
    id: params.id,
  });
};
