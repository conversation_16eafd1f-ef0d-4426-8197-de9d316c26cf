import Vue, { ComponentOptions } from 'vue';
import ImageTextEditor from '@/components/ImageTextEditor/ImageTextEditor.vue';
import { ImageWorkItem } from '@/types/Work';
import {
  originWorkInfo,
  workInfo,
  loading,
  closeStatus,
  selectedSettingIndex,
  selectedIndexToOriginWMap,
  selectedIndexToNewOriginWMap,
} from '../hook/useWorkInfo';
import { cloneDeep } from 'lodash-es';
import { VIEW_MODE } from '@/views/EditProjectView/constants';
import { getWorkInfoWithTransform } from '@/api/EditProjectView/work';
import { message } from '@fk/faicomponent';
import {
  IMAGE_TEXT_EDITOR_TAB,
  IMAGE_TEXT_EDITOR_CLOSE_STATUS,
} from '../constants';
import { setEditorType } from '@/constants/project';
import { DEFAULT_FONT, loadFont } from '@/utils/loadFont';

let instance: InstanceType<typeof Vue> | null = null;
// 当前请求的AbortController
let currentAbortController: AbortController | null = null;

/**
 * 显示图文编辑器
 * @param workId 作品id
 * @param defaultTab 默认选中的编辑器标签
 * @param closeCallback 关闭回调
 */
export const showImageTextEditor = (
  workId: number,
  defaultTab?: (typeof IMAGE_TEXT_EDITOR_TAB)[keyof typeof IMAGE_TEXT_EDITOR_TAB],
  closeCallback?: (
    closeStatus: (typeof IMAGE_TEXT_EDITOR_CLOSE_STATUS)[keyof typeof IMAGE_TEXT_EDITOR_CLOSE_STATUS],
  ) => void,
) => {
  if (instance || loading.value) return;
  loading.value = true;

  // 取消上一个请求
  if (currentAbortController) {
    currentAbortController.abort();
  }
  currentAbortController = new AbortController();

  // 预加载字体
  loadFont(DEFAULT_FONT);

  getWorkInfoWithTransform(
    {
      id: workId,
      viewMode: VIEW_MODE.EDIT,
    },
    { signal: currentAbortController.signal },
  ).then(([err, res]) => {
    loading.value = false;
    if (err) {
      // 如果是主动取消，不提示错误
      if (
        err.message === 'canceled' ||
        err.message === 'canceled by user' ||
        err.message === 'canceled by AbortController'
      )
        return;
      message.error(err.message);
      return;
    }
    workInfo.value = cloneDeep(res.data) as ImageWorkItem;
    originWorkInfo.value = cloneDeep(res.data) as ImageWorkItem;
  });
  closeStatus.value = IMAGE_TEXT_EDITOR_CLOSE_STATUS.UNMODIFIED;
  // 进入图文编辑器前设置全局编辑器类型
  setEditorType('image');
  const Constructor = Vue.extend(ImageTextEditor as ComponentOptions<Vue>);
  instance = new Constructor({
    propsData: {
      defaultTab,
    },
  });

  instance.$mount();
  document.body.appendChild(instance.$el);

  instance.$on('close', () => {
    closeCallback?.(closeStatus.value);
    // 清空workInfo，下次打开编辑器重新赋值，避免数据缓存
    workInfo.value = undefined;
    // 重置selectedSettingIndex，每次打开预览都要重置为封面图
    selectedSettingIndex.value = 0;
    selectedIndexToOriginWMap.clear();
    selectedIndexToNewOriginWMap.clear();
    closeImageTextEditor();
  });
};

/** 关闭图文编辑器 */
export const closeImageTextEditor = () => {
  if (currentAbortController) {
    currentAbortController.abort();
    currentAbortController = null;
  }
  if (instance) {
    document.body.removeChild(instance.$el);
    instance.$destroy();
    instance = null;
  }
};
