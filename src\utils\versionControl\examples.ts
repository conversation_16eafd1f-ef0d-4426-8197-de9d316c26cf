/**
 * 版本校验工具使用示例
 * @description 展示如何使用通用版本校验工具的各种功能
 */

import VersionController from './index';

// ============= 示例1：基础权限检查 =============

/**
 * 检查AI改嘴型功能权限
 */
export function checkAIMouthShapePermission(): boolean {
  return VersionController.hasPermission('AI_MOUTH_SHAPE');
}

/**
 * 检查文件上传权限
 */
export function checkFileUploadPermission(): boolean {
  return VersionController.hasPermission('FILE_UPLOAD_SIZE');
}

// ============= 示例2：消息提示式校验 =============

/**
 * 文件上传操作示例
 */
export function handleFileUpload(): void {
  // 方式1：使用checkAndExecute
  const executed = VersionController.checkAndExecute('FILE_UPLOAD_SIZE', () => {
    console.log('执行文件上传操作');
    // 实际的文件上传逻辑
  });
  
  if (!executed) {
    console.log('文件上传被阻止：版本权限不足');
  }
}

/**
 * 成员权限设置操作示例
 */
export function handleMemberPermissionSetting(): void {
  // 方式2：手动检查
  if (!VersionController.hasPermission('MEMBER_PERMISSION')) {
    VersionController.showUpgradeMessage('MEMBER_PERMISSION');
    return;
  }
  
  console.log('执行成员权限设置操作');
  // 实际的权限设置逻辑
}

// ============= 示例3：接口级自动降级 =============

/**
 * 模拟表单数据结构
 */
interface MockFormData {
  id: number;
  label: string;
  hasModifyMouth: boolean;
  openModifyMouth: boolean;
}

/**
 * 处理表单数据的降级示例
 */
export function processFormData(rawData: MockFormData): MockFormData {
  // 应用版本降级处理
  return VersionController.applyDowngrade(rawData, 'AI_MOUTH_SHAPE');
}

/**
 * 批量处理表单数据的降级示例
 */
export function processFormDataList(rawDataList: MockFormData[]): MockFormData[] {
  // 批量应用版本降级处理
  return VersionController.applyDowngradeToList(rawDataList, 'AI_MOUTH_SHAPE');
}

// ============= 示例4：获取版本要求信息 =============

/**
 * 获取功能版本要求信息示例
 */
export function getFeatureVersionInfo(featureKey: string): void {
  const versionInfo = VersionController.getVersionRequirement(featureKey);
  
  if (versionInfo) {
    console.log(`功能: ${versionInfo.featureName}`);
    console.log(`要求版本: ${versionInfo.requiredVersion}`);
    console.log(`是否有权限: ${versionInfo.hasPermission}`);
  } else {
    console.log(`未找到功能配置: ${featureKey}`);
  }
}

// ============= 示例5：Vue组件中的使用 =============

/**
 * Vue组件中使用VersionSwitch的示例代码
 * 
 * ```vue
 * <template>
 *   <div class="feature-controls">
 *     <!-- AI改嘴型功能开关 -->
 *     <VersionSwitch
 *       feature-key="AI_MOUTH_SHAPE"
 *       v-model="aiMouthShapeEnabled"
 *       label="AI改嘴型"
 *       @permission-denied="handlePermissionDenied"
 *     />
 *     
 *     <!-- 其他功能开关 -->
 *     <VersionSwitch
 *       feature-key="ANOTHER_FEATURE"
 *       v-model="anotherFeatureEnabled"
 *       label="其他功能"
 *       :show-icon="false"
 *     />
 *   </div>
 * </template>
 * 
 * <script setup>
 * import { ref } from 'vue';
 * import { VersionSwitch } from '@/components/version';
 * 
 * const aiMouthShapeEnabled = ref(false);
 * const anotherFeatureEnabled = ref(false);
 * 
 * const handlePermissionDenied = (featureKey: string) => {
 *   console.log(`权限不足，无法开启功能: ${featureKey}`);
 *   // 可以添加埋点统计等逻辑
 * };
 * </script>
 * ```
 */

// ============= 示例6：错误处理 =============

/**
 * 安全的版本校验调用示例
 */
export function safeVersionCheck(featureKey: string): boolean {
  try {
    return VersionController.hasPermission(featureKey);
  } catch (error) {
    console.error(`版本校验失败 (${featureKey}):`, error);
    return true; // 出错时默认允许访问
  }
}

/**
 * 安全的降级处理示例
 */
export function safeDowngrade<T>(data: T, featureKey: string): T {
  try {
    return VersionController.applyDowngrade(data, featureKey);
  } catch (error) {
    console.error(`降级处理失败 (${featureKey}):`, error);
    return data; // 出错时返回原数据
  }
}
