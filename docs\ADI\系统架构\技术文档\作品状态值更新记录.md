# 作品状态值更新记录

## 更新概述

**更新时间**: 2025-07-14  
**更新原因**: 后端启用新的作品状态逻辑，简化状态值定义  
**影响范围**: 前端状态常量定义、状态判断函数、状态显示逻辑

## 状态值变更对照表

### 新的后端状态值定义

| 状态名称   | 新状态值 | 说明                           |
| ---------- | -------- | ------------------------------ |
| GENERATING | 0        | 生成中（项目点生成作品的时候） |
| COMPLETED  | 1        | 已生成                         |
| HIDDEN     | 2        | 隐藏                           |
| EDIT       | 3        | 编辑中                         |
| FAILED     | 4        | 错误状态，剪辑失败时设置的     |

### 旧状态值对照

| 状态名称     | 旧状态值 | 新状态值 | 变更说明                   |
| ------------ | -------- | -------- | -------------------------- |
| GENERATING   | 0        | 0        | 无变更                     |
| COMPLETED    | 1        | 1        | 无变更                     |
| HIDDEN       | 2        | 2        | 无变更                     |
| EDIT         | 3        | 3        | 无变更                     |
| REGENERATING | 4        | -        | 通过 isRegenerate 字段区分 |
| RECOMPLETED  | 5        | -        | 通过 isRegenerate 字段区分 |
| FAILED       | 6        | 4        | **重要变更**               |
| FAILED_AGAIN | 7        | -        | 通过 isRegenerate 字段区分 |

## 重新生成状态逻辑变更

### 旧逻辑

- 使用独立的状态值区分重新生成状态
- REGENERATING = 4（重新生成中）
- RECOMPLETED = 5（重新生成完成）
- FAILED_AGAIN = 7（重新生成失败）

### 新逻辑

- 使用主状态 + isRegenerate 布尔值组合判断
- 重新生成中：status = 0 + isRegenerate = true
- 重新生成完成：status = 1 + isRegenerate = true
- 重新生成失败：status = 4 + isRegenerate = true

## 前端适配方案

### 1. 状态常量更新

```typescript
// 新的状态常量定义（只包含后端实际使用的状态值）
export const WORK_STATUS = {
  GENERATING: 0,
  COMPLETED: 1,
  HIDDEN: 2,
  EDIT: 3,
  FAILED: 4, // 从 6 改为 4
} as const;

// 旧版本状态常量（用于回滚支持）
export const WORK_STATUS_OLD = {
  GENERATING: 0,
  COMPLETED: 1,
  HIDDEN: 2,
  EDIT: 3,
  REGENERATING: 4,
  RECOMPLETED: 5,
  FAILED: 6,
  FAILED_AGAIN: 7,
} as const;
```

### 2. 状态判断函数适配

所有状态判断函数已更新为支持新旧逻辑切换：

- 新逻辑：使用主状态 + isRegenerate 组合判断
- 旧逻辑：使用 WORK_STATUS_OLD 中的状态值

### 3. 功能开关

#### 配置开关

通过 `WORK_STATUS_CONFIG.USE_NEW_STATUS_LOGIC` 控制新旧逻辑切换：

```typescript
export const WORK_STATUS_CONFIG = {
  USE_NEW_STATUS_LOGIC: false, // 默认使用旧逻辑
};
```

#### URL Query 参数开关

支持通过 URL query 参数 `useNewStatus` 动态控制状态逻辑：

- `?useNewStatus=1` - 强制使用新逻辑
- `?useNewStatus=0` - 强制使用旧逻辑
- 无参数或无效值 - 使用配置中的默认值

**优先级**: URL 参数 > 配置文件

**使用示例**:

```url
https://example.com/project?useNewStatus=1  // 使用新逻辑
https://example.com/project?useNewStatus=0  // 使用旧逻辑
https://example.com/project                 // 使用配置默认值
```

## 向后兼容性保证

1. **状态常量保留**: 所有旧状态值都保留在 WORK_STATUS 中
2. **旧版本常量**: 提供 WORK_STATUS_OLD 用于回滚
3. **功能开关**: 支持新旧逻辑平滑切换
4. **状态判断函数**: 自动适配新旧逻辑

## 启用新逻辑步骤

### 方式一：配置文件启用（推荐用于生产环境）

1. 确认后端已部署新的状态逻辑
2. 将 `WORK_STATUS_CONFIG.USE_NEW_STATUS_LOGIC` 设为 `true`
3. 验证所有功能正常工作
4. 如有问题，可立即回滚到旧逻辑

### 方式二：URL 参数启用（推荐用于测试验证）

1. 在 URL 中添加 `?useNewStatus=1` 参数
2. 验证新逻辑功能是否正常
3. 测试完成后移除参数或改为 `?useNewStatus=0`
4. 确认无问题后再通过配置文件全面启用

## 回滚方案

如需回滚到旧逻辑：

1. 将 `WORK_STATUS_CONFIG.USE_NEW_STATUS_LOGIC` 设为 `false`
2. 或者使用 WORK_STATUS_OLD 替换 WORK_STATUS
3. 所有状态判断函数会自动切换到旧逻辑

## 测试验证

- ✅ 状态常量定义正确
- ✅ 状态判断函数适配完成
- ✅ 状态显示名称更新
- ✅ 向后兼容性保持
- ✅ 组件兼容性验证通过

## 相关文件

- `src/constants/workStatus.ts` - 状态常量和判断函数
- `src/constants/__tests__/workStatus.test.ts` - 状态测试文件
- `docs/ADI/系统架构/技术文档/统一状态处理系统设计.md` - 系统设计文档
