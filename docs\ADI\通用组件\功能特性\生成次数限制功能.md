# 生成数量限制功能实现文档

## 概述

为 EditProjectView 组件实现了基于项目类型的生成数量限制功能，支持图文和视频两种项目类型的差异化配置。

## 功能特性

### 1. 项目类型差异化限制

- **图文类型项目**：最多允许生成 99 个作品，最少1个，默认值为10个
- **视频类型项目**：最多允许生成 10 个作品，最少1个，默认值为5个

### 2. 统一配置管理

所有生成数量相关的配置都集中在 `useFirstStep.ts` 的 `generateButtonConfig` 中，便于后续统一管理和修改。

## 实现细节

### 1. 核心函数

#### `getDefaultGenerateNum(projectType: WorkTypeValue): number`
根据项目类型返回默认生成数量：
- 视频类型：5个
- 图文类型：10个

#### `getMaxGenerateCount(projectType: WorkTypeValue): number`
根据项目类型返回最大生成数量：
- 视频类型：10个
- 图文类型：99个

### 2. 配置对象结构

```typescript
generateButtonConfig = {
  num: number,           // 当前生成数量
  maxCount: number,      // 最大生成数量
  minCount: number,      // 最小生成数量（固定为1）
  integral: number,      // 消耗积分
  showIntegral: boolean  // 是否显示积分
}
```

### 3. 组件层级传递

```
EditProjectView (主组件)
    ↓ 传递 generateButtonConfig
ProjectFooter (页脚组件)
    ↓ 传递 minCount, maxCount
GenerateButton (生成按钮组件)
    ↓ 使用限制进行数量控制
```

## 修改的文件

### 1. 核心逻辑文件
- `src/views/EditProjectView/composables/useFirstStep.ts`
  - 添加 `getMaxGenerateCount` 函数
  - 更新 `generateButtonConfig` 配置对象

### 2. 类型定义文件
- `src/views/EditProjectView/types/components.ts`
  - 更新 `UseFirstStepReturn` 接口中的 `generateButtonConfig` 类型

### 3. 组件文件
- `src/views/EditProjectView/components/GenerateButton/index.vue`
  - 添加 `minCount` 和 `maxCount` props
  - 移除硬编码的数量限制

- `src/views/EditProjectView/components/ProjectFooter/index.vue`
  - 添加 `minCount` 和 `maxCount` props
  - 传递配置到 GenerateButton 组件

- `src/views/EditProjectView/index.vue`
  - 传递 `generateButtonConfig` 中的限制配置到 ProjectFooter

### 4. 测试文件
- `src/views/EditProjectView/composables/__tests__/useFirstStep.test.ts`
  - 新增单元测试验证配置的正确性

## 使用方式

### 1. 在组件中使用

```typescript
const { generateButtonConfig } = useFirstStep({
  templateId: 1,
  projectType: PROJECT_TYPE_VIDEO // 或 PROJECT_TYPE_IMAGE
});

// 访问配置
console.log(generateButtonConfig.value.maxCount); // 视频: 10, 图文: 99
console.log(generateButtonConfig.value.num);      // 视频: 5,  图文: 10
```

### 2. 修改限制配置

如需修改生成数量限制，只需在 `useFirstStep.ts` 中修改对应函数：

```typescript
const getMaxGenerateCount = (projectType: WorkTypeValue): number => {
  switch (projectType) {
    case PROJECT_TYPE_VIDEO:
      return 15; // 修改视频类型最大值
    case PROJECT_TYPE_IMAGE:
      return 120; // 修改图文类型最大值
    default:
      return 15;
  }
};
```

## 测试验证

运行测试命令验证功能：

```bash
npm test src/views/EditProjectView/composables/__tests__/useFirstStep.test.ts
```

测试覆盖：
- ✅ 视频类型项目的默认值和限制
- ✅ 图文类型项目的默认值和限制
- ✅ 未知类型的默认处理
- ✅ 配置对象结构完整性
- ✅ 数量限制的合理性验证

## 注意事项

1. **向后兼容性**：现有代码无需修改，新的配置会自动生效
2. **类型安全**：所有配置都有完整的 TypeScript 类型定义
3. **集中管理**：所有限制配置都在 `useFirstStep.ts` 中，便于维护
4. **测试覆盖**：提供了完整的单元测试确保功能正确性

## 扩展性

如需添加新的项目类型或修改限制规则，只需：
1. 在 `getDefaultGenerateNum` 和 `getMaxGenerateCount` 函数中添加新的 case
2. 更新相应的测试用例
3. 确保类型定义保持同步
