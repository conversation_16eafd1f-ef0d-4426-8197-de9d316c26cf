<template>
  <div class="point-detail">
    <fa-range-picker
      size="large"
      format="YYYY-MM-DD"
      class="point-detail__date-picker"
      :showTime="false"
      :placeholder="['开始时间', '结束时间']"
      @change="changeDate"
    />

    <fa-table
      :pagination="showPagination"
      :columns="columns"
      :data-source="dataList"
      skin-type="base"
      row-key="id"
      @change="handlePageChange"
      class="point-detail__table"
    >
      <template #point="record">
        <p
          :class="{
            'point-detail__point-cell--positive': record.point > 0,
            'point-detail__point-cell--negative': record.point <= 0,
          }"
        >
          {{ record.point > 0 ? '+' + record.point : record.point }}
        </p>
      </template>
    </fa-table>
  </div>
</template>

<script lang="ts" setup>
import { reactive, computed, ref } from 'vue';
import { day } from '@/utils/dayjs';
import { message } from '@fk/faicomponent';
// api
import { getPointDetail } from '@/api/AcctInfoView';
// constant
import { POINT_TYPE_NAME } from '@/constants/pointType';

/**
 * @description 定义点数明细项接口
 */
interface PointDetailItem {
  id: string | number;
  createTime: number; // 时间戳
  typeStr: string; // 类型
  relatedStr: string; // 关联内容
  point: number; // 点数变化
  balance: number; // 余额
}

/**
 * @description 筛选数据，包含开始时间和结束时间
 */
const filtersData = reactive<{
  beginTime: number;
  endTime: number;
  filterType: number[];
  desc: boolean;
}>({
  beginTime: 0,
  endTime: 0,
  filterType: [],
  desc: true, // 是否降序，默认为是
});

/**
 * @description 分页配置
 */
const pageConfig = reactive({
  /** 是否显示分页器 */
  showPagination: true,
  /** 是否显示分页器大小选择器 */
  showSizeChanger: true,
  /** 是否显示快速跳转器 */
  showQuickJumper: true,
  /** 是否隐藏单页 */
  hideOnSinglePage: true,
  /** 当前页 */
  current: 1,
  /** 每页条数 */
  pageSize: 10,
  /** 总条数 */
  total: 100,
});

/**
 * @description 是否显示分页，超过10条数据显示分页
 */
const showPagination = computed(() => {
  return pageConfig.total > 10 ? pageConfig : false;
});

/**
 * @description 表格列配置
 */
const columns = [
  {
    title: '时间',
    dataIndex: 'createTime',
    width: '23%',
    sorter: true,
    customRender: (text: number) => {
      return text ? day(text).format('YYYY-MM-DD HH:mm:ss') : '-';
    },
  },
  {
    title: '类型',
    dataIndex: 'typeStr',
    width: '10%',
    filters: Object.entries(POINT_TYPE_NAME).map(([key, value]) => ({
      text: value,
      value: key,
    })),
  },
  {
    title: '关联内容',
    dataIndex: 'relatedStr',
    width: '47%',
  },
  {
    title: '变动点数',
    width: '10%',
    scopedSlots: { customRender: 'point' },
  },
  {
    title: '余额',
    width: '10%',
    dataIndex: 'balance',
  },
];

/**
 * @description 点数明细列表数据
 */
const dataList = ref<PointDetailItem[]>([]);

/**
 * @description 处理表格分页变化
 * @param {{ current: number; pageSize: number }} page 分页信息
 */
const handlePageChange = (
  page: { current: number; pageSize: number },
  filters: { typeStr: string[] },
  sorter: { field: string; order: string },
) => {
  console.log(filters, 'filters');
  console.log(sorter, 'sorter');
  filtersData.desc =
    (sorter.field === 'createTime' && sorter.order === 'descend') ||
    !sorter.order; // 设定倒序，或者默认倒序
  filters.typeStr &&
    (filtersData.filterType = filters.typeStr.map(val => Number(val)));
  pageConfig.current = page.current;
  pageConfig.pageSize = page.pageSize;
  getPointList();
};

/**
 * @description 处理日期范围选择器的值变化
 * @param {Date[]} _ - 原始日期对象数组 (未使用)
 * @param {string[]} dateStrList - 格式化后的日期字符串数组 [开始日期, 结束日期]
 */
const changeDate = (_: Date, dateStrList: string[]) => {
  filtersData.beginTime = dateStrList[0]
    ? new Date(dateStrList[0] + ' 00:00:00').getTime()
    : 0;
  filtersData.endTime = dateStrList[1]
    ? new Date(dateStrList[1] + ' 23:59:59').getTime()
    : 0;
  pageConfig.current = 1; // 日期筛选后重置到第一页
  getPointList();
};

/**
 * @description 获取点数明细列表数据
 */
const getPointList = async () => {
  // 显示加载状态（如果需要）
  // spinning.value = true;
  let params: {
    type: number[];
    desc: boolean;
    pageNow: number;
    limit: number;
    startDate?: number;
    endDate?: number;
  } = {
    type: filtersData.filterType, // 空数组表示所有类型
    desc: filtersData.desc,
    pageNow: pageConfig.current,
    limit: pageConfig.pageSize,
  };
  if (filtersData.beginTime && filtersData.endTime) {
    // 时间筛选
    Object.assign(params, {
      startDate: filtersData.beginTime,
      endDate: filtersData.endTime,
    });
  }
  const [err, res] = await getPointDetail(params);
  // spinning.value = false;
  if (err) {
    message.error(err.message || '获取失败');
    dataList.value = []; // 清空数据
    return;
  }
  pageConfig.total = res.total;
  dataList.value = res.data;
};

getPointList(); // 组件挂载时获取初始数据
</script>

<style scoped>
.point-detail {
  /* 尺寸相关 */
  @apply pt-24px size-full;
}

.point-detail__date-picker {
  /* 尺寸相关 */
  @apply w-220px mb-24px;
}

::v-deep(.fa-calendar-picker-input) {
  @apply rounded-8px text-14px;
}

.point-detail__table {
  /* 布局相关 */
  /* 如果fa-table本身没有特定布局，此处可留空或添加通用布局如flex-col */
}

.point-detail__point-cell--positive {
  /* 文字相关 */
  @apply text-#19BE6B;
}

.point-detail__point-cell--negative {
  /* 文字相关 */
  @apply text-#FA3534;
}
</style>
