/**
 * @fileoverview 视频时长校验功能的单元测试
 * @description 测试视频类型文件表单的时长校验规则
 */

import { describe, it, expect, beforeEach } from 'vitest';
import type { FormRule, FileInfo } from '@/views/EditProjectView/types/index';
import {
  MEDIA_TYPES,
  VALIDATION_TRIGGER,
} from '@/views/EditProjectView/types/index';

// 模拟校验模式
const VALIDATION_MODE = {
  GENERATE: 'generate', // 生成模式：完整校验
  SAVE: 'save', // 保存模式：仅最大长度校验
} as const;

let currentValidationMode: string = VALIDATION_MODE.GENERATE;

/**
 * 创建视频时长校验规则（测试版本）
 */
const createVideoDurationRule = (): FormRule => {
  return {
    trigger: VALIDATION_TRIGGER.MAGIC,
    triggerAuto: true,
    validator: (
      _rule: FormRule,
      value: unknown,
      callback: (error?: Error) => void,
    ) => {
      // 保存模式跳过时长校验
      if (currentValidationMode === VALIDATION_MODE.SAVE) {
        console.log('保存模式：跳过视频时长校验');
        return callback();
      }

      // 检查文件列表是否为空
      if (!value || (Array.isArray(value) && value.length === 0)) {
        // 如果没有文件，跳过时长校验（由必填校验处理）
        return callback();
      }

      // 计算视频文件总时长
      const files = Array.isArray(value) ? value : [];
      let totalDuration = 0;

      for (const file of files) {
        if (file && typeof file === 'object' && 'duration' in file) {
          const duration = Number(file.duration) || 0;
          totalDuration += duration;
        }
      }

      // 时长校验：最小15秒，最大600秒（10分钟）
      const MIN_DURATION = 15; // 15秒
      const MAX_DURATION = 600; // 10分钟

      if (totalDuration < MIN_DURATION) {
        callback(new Error('视频素材总时长需超过15s，建议上传多段视频素材'));
      } else if (totalDuration > MAX_DURATION) {
        callback(
          new Error(
            '视频素材总时长不能超过10min，建议减少视频素材或上传更短的视频素材',
          ),
        );
      } else {
        callback();
      }
    },
  };
};

/**
 * 创建测试用的视频文件对象
 */
const createTestVideoFile = (duration: number): FileInfo => ({
  name: `test-video-${duration}s.mp4`,
  url: 'https://example.com/video.mp4',
  status: 0,
  mediaType: MEDIA_TYPES.VIDEO,
  resId: 'test-res-id',
  resType: 58, // FILE_TYPES.MP4
  duration,
});

describe('视频时长校验功能', () => {
  let rule: FormRule;

  beforeEach(() => {
    // 重置校验模式为生成模式
    currentValidationMode = VALIDATION_MODE.GENERATE;
    rule = createVideoDurationRule();
  });

  describe('生成模式下的时长校验', () => {
    it('应该通过：单个视频时长在有效范围内（30秒）', async () => {
      const files = [createTestVideoFile(30)];

      return new Promise<void>(resolve => {
        rule.validator!(rule, files, (error?: Error) => {
          expect(error).toBeUndefined();
          resolve();
        });
      });
    });

    it('应该通过：多个视频总时长在有效范围内（10+20=30秒）', async () => {
      const files = [createTestVideoFile(10), createTestVideoFile(20)];

      return new Promise<void>(resolve => {
        rule.validator!(rule, files, (error?: Error) => {
          expect(error).toBeUndefined();
          resolve();
        });
      });
    });

    it('应该通过：总时长刚好等于最小值（15秒）', async () => {
      const files = [createTestVideoFile(15)];

      return new Promise<void>(resolve => {
        rule.validator!(rule, files, (error?: Error) => {
          expect(error).toBeUndefined();
          resolve();
        });
      });
    });

    it('应该通过：总时长刚好等于最大值（600秒）', async () => {
      const files = [createTestVideoFile(600)];

      return new Promise<void>(resolve => {
        rule.validator!(rule, files, (error?: Error) => {
          expect(error).toBeUndefined();
          resolve();
        });
      });
    });

    it('应该失败：总时长小于最小值（10秒）', async () => {
      const files = [createTestVideoFile(10)];

      return new Promise<void>(resolve => {
        rule.validator!(rule, files, (error?: Error) => {
          expect(error).toBeDefined();
          expect(error?.message).toBe(
            '视频素材总时长需超过15s，建议上传多段视频素材',
          );
          resolve();
        });
      });
    });

    it('应该失败：总时长超过最大值（700秒）', async () => {
      const files = [createTestVideoFile(700)];

      return new Promise<void>(resolve => {
        rule.validator!(rule, files, (error?: Error) => {
          expect(error).toBeDefined();
          expect(error?.message).toBe(
            '视频素材总时长不能超过10min，建议减少视频素材或上传更短的视频素材',
          );
          resolve();
        });
      });
    });

    it('应该失败：多个视频总时长超过最大值（400+300=700秒）', async () => {
      const files = [createTestVideoFile(400), createTestVideoFile(300)];

      return new Promise<void>(resolve => {
        rule.validator!(rule, files, (error?: Error) => {
          expect(error).toBeDefined();
          expect(error?.message).toBe(
            '视频素材总时长不能超过10min，建议减少视频素材或上传更短的视频素材',
          );
          resolve();
        });
      });
    });

    it('应该跳过：空文件列表', async () => {
      const files: FileInfo[] = [];

      return new Promise<void>(resolve => {
        rule.validator!(rule, files, (error?: Error) => {
          expect(error).toBeUndefined();
          resolve();
        });
      });
    });

    it('应该跳过：null值', async () => {
      return new Promise<void>(resolve => {
        rule.validator!(rule, null, (error?: Error) => {
          expect(error).toBeUndefined();
          resolve();
        });
      });
    });

    it('应该处理：文件没有duration字段', async () => {
      const files = [
        {
          name: 'test-video.mp4',
          url: 'https://example.com/video.mp4',
          status: 0,
          mediaType: MEDIA_TYPES.VIDEO,
          resId: 'test-res-id',
          resType: 58,
          // 没有duration字段
        },
      ];

      return new Promise<void>(resolve => {
        rule.validator!(rule, files, (error?: Error) => {
          expect(error).toBeDefined();
          expect(error?.message).toBe(
            '视频素材总时长需超过15s，建议上传多段视频素材',
          );
          resolve();
        });
      });
    });

    it('应该处理：duration为0的文件', async () => {
      const files = [createTestVideoFile(0)];

      return new Promise<void>(resolve => {
        rule.validator!(rule, files, (error?: Error) => {
          expect(error).toBeDefined();
          expect(error?.message).toBe(
            '视频素材总时长需超过15s，建议上传多段视频素材',
          );
          resolve();
        });
      });
    });
  });

  describe('保存模式下的时长校验', () => {
    beforeEach(() => {
      currentValidationMode = VALIDATION_MODE.SAVE;
    });

    it('应该跳过：保存模式下不进行时长校验（时长不足）', async () => {
      const files = [createTestVideoFile(5)]; // 时长不足

      return new Promise<void>(resolve => {
        rule.validator!(rule, files, (error?: Error) => {
          expect(error).toBeUndefined();
          resolve();
        });
      });
    });

    it('应该跳过：保存模式下不进行时长校验（时长超限）', async () => {
      const files = [createTestVideoFile(800)]; // 时长超限

      return new Promise<void>(resolve => {
        rule.validator!(rule, files, (error?: Error) => {
          expect(error).toBeUndefined();
          resolve();
        });
      });
    });
  });

  describe('边界情况测试', () => {
    it('应该处理：混合有效和无效duration的文件', async () => {
      const files = [
        createTestVideoFile(10), // 有效时长
        {
          name: 'invalid-video.mp4',
          url: 'https://example.com/video.mp4',
          status: 0,
          mediaType: MEDIA_TYPES.VIDEO,
          resId: 'test-res-id',
          resType: 58,
          duration: 'invalid', // 无效时长
        },
        createTestVideoFile(8), // 有效时长
      ];

      return new Promise<void>(resolve => {
        rule.validator!(rule, files, (error?: Error) => {
          // 总时长应该是 10 + 0 + 8 = 18秒，应该通过校验
          expect(error).toBeUndefined();
          resolve();
        });
      });
    });
  });
});
