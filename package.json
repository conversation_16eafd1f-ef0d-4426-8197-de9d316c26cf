{"name": "scportal-web", "private": true, "version": "0.0.0", "type": "module", "packageManager": "pnpm@10.12.1", "scripts": {"preinstall": "npx only-allow pnpm", "dev": "pnpm i && node ./scripts/checkConfig.js && vite", "build": "vue-tsc && vite build", "tsc": "vue-tsc", "after:build": "node ./scripts/afterBuild.js", "preview": "vite preview", "test": "vitest run", "test:watch": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest run --coverage", "test:browser": "vitest --browser", "prepare": "husky", "lint": "eslint .", "add-view": "sh .vscode/shell/add-view.shell"}, "pnpm": {"overrides": {"moment": "2.30.1"}}, "dependencies": {"@fk/fa-component-cus": "0.27.65", "@fk/faicomponent": "2.16.21", "@fk/faiupload": "^1.5.8", "@vitejs/plugin-vue2": "^2.3.3", "axios": "~1.8.4", "clipboard-copy": "^4.0.1", "dayjs": "^1.11.13", "element-ui": "^2.15.14", "js-cookie": "^3.0.5", "md5": "^2.3.0", "moment": "2.30.1", "qs": "^6.14.0", "video.js": "^8.22.0", "vue": "^2.7.16", "vue-draggable-resizable": "2", "vue-router": "^3.6.5", "vue-virtual-scroller": "1", "vuex": "^3.6.2", "vuex-persistedstate": "^4.1.0", "webdriverio": "8.23"}, "devDependencies": {"@eslint/js": "^9.29.0", "@types/js-cookie": "^3.0.6", "@types/jsdom": "^21.1.7", "@types/lodash-es": "^4.17.12", "@types/md5": "^2.3.5", "@types/moment": "^2.13.0", "@types/node": "^22.14.0", "@types/qs": "^6.9.18", "@typescript-eslint/eslint-plugin": "6", "@typescript-eslint/parser": "6.0.0", "@unocss/reset": "0.60.4", "@unocss/transformer-directives": "0.60.4", "@vitest/browser": "^0.34.6", "@vitest/coverage-v8": "0.34.6", "@vitest/ui": "0", "@vue/test-utils": "1.3.6", "eslint": "8.57.0", "eslint-plugin-vue": "8.7.1", "globals": "^16.0.0", "husky": "^9.1.7", "jsdom": "22.1.0", "less": "3", "less-loader": "4", "lint-staged": "^14.0.1", "lodash-es": "^4.17.21", "prettier": "2", "rollup-plugin-visualizer": "^6.0.3", "sass": "^1.86.3", "terser": "^5.43.1", "typescript": "^5.4.5", "unocss": "0.60.4", "unocss-preset-scrollbar": "^3.2.0", "unplugin-vue-components": "^28.5.0", "vite": "5.4.19", "vite-plugin-mock": "^3.0.2", "vitest": "3.2.4", "vue-eslint-parser": "^9.0.0", "vue-tsc": "^2.2.8"}, "lint-staged": {"*.{js,ts,vue}": "eslint --fix"}, "volta": {"node": "20.19.2"}}