<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="104" height="125" viewBox="0 0 104 125">
  <defs>
    <linearGradient id="linear-gradient" x1="0.208" x2="1.061" y2="0.631" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#003afb"/>
      <stop offset="0.432" stop-color="#8c6df3"/>
      <stop offset="1" stop-color="#fff"/>
    </linearGradient>
    <linearGradient id="linear-gradient-2" x1="1.148" x2="-0.068" y2="0.81" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#fcfbff"/>
      <stop offset="0.431" stop-color="#edecff"/>
      <stop offset="1" stop-color="#cfdaff"/>
    </linearGradient>
    <linearGradient id="linear-gradient-3" x1="0.208" x2="1.061" y2="0.631" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#003afb"/>
      <stop offset="1" stop-color="#9275f4"/>
    </linearGradient>
    <filter id="路径_5092" x="26.743" y="66.847" width="46.258" height="46.258" filterUnits="userSpaceOnUse">
      <feOffset dy="3" input="SourceAlpha"/>
      <feGaussianBlur stdDeviation="3" result="blur"/>
      <feFlood flood-color="#5b5bf6" flood-opacity="0.212"/>
      <feComposite operator="in" in2="blur"/>
      <feComposite in="SourceGraphic"/>
    </filter>
    <filter id="路径_5093" x="49.85" y="86.227" width="30.26" height="30.262" filterUnits="userSpaceOnUse">
      <feOffset dy="3" input="SourceAlpha"/>
      <feGaussianBlur stdDeviation="3" result="blur-2"/>
      <feFlood flood-color="#5b5bf6" flood-opacity="0.212"/>
      <feComposite operator="in" in2="blur-2"/>
      <feComposite in="SourceGraphic"/>
    </filter>
    <filter id="路径_5094" x="49.85" y="63.184" width="27.25" height="27.25" filterUnits="userSpaceOnUse">
      <feOffset dy="3" input="SourceAlpha"/>
      <feGaussianBlur stdDeviation="3" result="blur-3"/>
      <feFlood flood-color="#5b5bf6" flood-opacity="0.212"/>
      <feComposite operator="in" in2="blur-3"/>
      <feComposite in="SourceGraphic"/>
    </filter>
  </defs>
  <g id="组_18127" data-name="组 18127" transform="translate(-908 -345)">
    <path id="矩形_15766" data-name="矩形 15766" d="M26.238-7.849a33.871,33.871,0,0,1,34,34v30a34,34,0,0,1-68,0v-30A33.871,33.871,0,0,1,26.238-7.849Zm0,87a23.151,23.151,0,0,0,23-23v-30a23.151,23.151,0,0,0-23-23,23.151,23.151,0,0,0-23,23v30A23.151,23.151,0,0,0,26.238,79.151Z" transform="translate(933.762 352.849)" fill="url(#linear-gradient)"/>
    <rect id="矩形_15765" data-name="矩形 15765" width="104" height="76" rx="8" transform="translate(908 394)" fill="url(#linear-gradient-2)"/>
    <g id="组_18126" data-name="组 18126" transform="translate(943.743 414.184)">
      <g transform="matrix(1, 0, 0, 1, -35.74, -69.18)" filter="url(#路径_5092)">
        <path id="路径_5092-2" data-name="路径 5092" d="M67.166,9.561,59.515,6.729a1.678,1.678,0,0,1-.988-.986L55.7-1.908a1.672,1.672,0,0,0-3.136,0l-2.83,7.651a1.678,1.678,0,0,1-.988.986L41.092,9.561a1.672,1.672,0,0,0,0,3.136l7.651,2.83a1.685,1.685,0,0,1,.988.988l2.83,7.651a1.672,1.672,0,0,0,3.136,0l2.83-7.651a1.685,1.685,0,0,1,.988-.988l7.651-2.83A1.672,1.672,0,0,0,67.166,9.561Z" transform="translate(-4.26 75.85)" fill="url(#linear-gradient-3)"/>
      </g>
      <g transform="matrix(1, 0, 0, 1, -35.74, -69.18)" filter="url(#路径_5093)">
        <path id="路径_5093-2" data-name="路径 5093" d="M64,13.57l-3.318-1.23a.72.72,0,0,1-.43-.428l-1.228-3.32a.725.725,0,0,0-1.36,0l-1.23,3.32a.722.722,0,0,1-.428.428l-3.32,1.23a.726.726,0,0,0,0,1.36l3.32,1.228a.722.722,0,0,1,.428.428l1.23,3.32a.725.725,0,0,0,1.36,0l1.228-3.32a.72.72,0,0,1,.43-.428L64,14.931A.725.725,0,0,0,64,13.57Z" transform="translate(6.64 84.11)" fill="url(#linear-gradient-3)"/>
      </g>
      <g transform="matrix(1, 0, 0, 1, -35.74, -69.18)" filter="url(#路径_5094)">
        <path id="路径_5094-2" data-name="路径 5094" d="M49.107,14.327,46.6,13.4a.543.543,0,0,1-.324-.323l-.926-2.5a.547.547,0,0,0-1.026,0l-.928,2.5a.545.545,0,0,1-.323.323l-2.5.928a.547.547,0,0,0,0,1.026l2.5.926a.545.545,0,0,1,.323.323l.928,2.5a.547.547,0,0,0,1.026,0l.926-2.5a.543.543,0,0,1,.324-.323l2.5-.926A.547.547,0,0,0,49.107,14.327Z" transform="translate(18.64 58.97)" fill="url(#linear-gradient-3)"/>
      </g>
    </g>
  </g>
</svg>
