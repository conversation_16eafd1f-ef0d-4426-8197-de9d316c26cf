<template>
  <div class="project-empty-state">
    <div class="project-empty-state__img-box">
      <img
        class="project-empty-state__img"
        src="@/assets/common/empty.webp"
        alt="暂无数据"
      />
    </div>
    <div class="project-empty-state__desc">暂无数据</div>
    <fa-button
      type="primary"
      size="small"
      class="project-empty-state__btn"
      @click="handleCreate"
      >去创作</fa-button
    >
  </div>
</template>

<script setup lang="ts">
import { defineEmits } from 'vue';

const emit = defineEmits(['create']);
function handleCreate() {
  emit('create');
}
</script>

<style lang="scss" scoped>
.project-empty-state {
  /* 布局相关 */
  @apply flex flex-col items-center w-full min-h-[calc(100vh-222px)] pt-[210px];

  &__img-box {
    @apply mb-[27px];
  }
  &__img {
    @apply w-[159px] h-[117px] object-contain;
  }
  &__desc {
    @apply text-assist text-[15px] mb-[16px];
  }
  &__btn {
    @apply w-[66px];
  }
}
</style>
