# 轮播图指示器同步功能

## 功能描述

实现了当用户点击轮播图下方指示器切换图片时，ImageSidebar.vue 的 selectedImgIdx 也会同步更新的功能。这确保了图片预览区和缩略图列表之间的状态保持一致。

## 实现方案

### 1. 核心组件修改

#### 1.1 ImageWorkPreview.vue

**添加轮播图 change 事件监听：**

```vue
<el-carousel
  ref="carousel"
  class="w-full h-full bg-black"
  :style="{ maxHeight: carouselMaxHeight }"
  arrow="never"
  trigger="click"
  :autoplay="!disableAutoplay"
  @change="handleCarouselChange"
>
```

**添加事件处理方法：**

```typescript
/**
 * 处理轮播图切换事件
 * @param activeIndex 当前激活的轮播图索引
 * @param _oldActiveIndex 之前激活的轮播图索引（未使用）
 */
const handleCarouselChange = (activeIndex: number, _oldActiveIndex: number) => {
  // 只有在启用轮播图控制功能时才发送事件
  if (props.enableCarouselControl) {
    // 通过事件总线通知ImageSidebar更新选中状态
    eventBus.emit(EVENT_NAMES.CAROUSEL_CHANGE, activeIndex);
  }
};
```

#### 1.2 ContentPanel/index.vue

**添加轮播图切换事件监听：**

```typescript
methods: {
  /**
   * 处理轮播图切换事件
   */
  handleCarouselChange(...args: unknown[]) {
    const activeIndex = args[0] as number;
    this.selectedImageIndex = activeIndex;
  },
},
mounted() {
  // 监听轮播图切换事件
  eventBus.on(EVENT_NAMES.CAROUSEL_CHANGE, this.handleCarouselChange);
},
beforeDestroy() {
  // 移除轮播图切换事件监听
  eventBus.off(EVENT_NAMES.CAROUSEL_CHANGE, this.handleCarouselChange);
},
```

#### 1.3 eventBus.ts

**添加新的事件常量：**

```typescript
export const EVENT_NAMES = {
  // ... 其他事件
  // 图片预览切换事件
  IMAGE_PREVIEW_CHANGE: 'imagePreviewChange',
  // 轮播图切换事件
  CAROUSEL_CHANGE: 'carouselChange',
  // ...
} as const;
```

### 2. 事件流程

1. **用户操作：** 用户点击轮播图下方的指示器
2. **Element UI 事件：** el-carousel 组件触发 change 事件
3. **事件处理：** ImageWorkPreview 的 handleCarouselChange 方法被调用
4. **条件检查：** 检查是否启用了 enableCarouselControl 功能
5. **事件发送：** 通过 eventBus 发送 CAROUSEL_CHANGE 事件
6. **事件接收：** ContentPanel 监听到事件，调用 handleCarouselChange 方法
7. **状态更新：** ContentPanel 更新 selectedImageIndex，ImageSidebar 显示对应的选中状态

### 3. 技术要点

#### 3.1 Element UI Carousel Change 事件

- Element UI 的 el-carousel 组件支持 `@change` 事件
- 事件回调参数：`(activeIndex: number, oldActiveIndex: number)`
- activeIndex：当前激活的轮播项索引
- oldActiveIndex：之前激活的轮播项索引

#### 3.2 条件性事件发送

- 只有在 `enableCarouselControl` 为 true 时才发送 CAROUSEL_CHANGE 事件
- 避免在不需要同步功能的场景中产生不必要的事件
- 保持功能的隔离性和可控性

#### 3.3 事件总线类型处理

- EventBus 的回调函数接收 `...args: unknown[]` 参数
- 需要在事件处理方法中进行类型断言：`const activeIndex = args[0] as number`
- 确保类型安全的同时保持事件总线的通用性

### 4. 应用场景

#### 4.1 图文编辑器预览区

在 `src/views/EditProjectView/pages/SecondStepImage/components/ImagePreview/index.vue` 中：

```vue
<ImageWorkPreview
  :title="workInfo?.script?.title || '标题'"
  :content="workInfo?.script?.content || '内容'"
  :imgList="imgListCal"
  height="full"
  carousel-max-height="78%"
  :enableCarouselControl="true"
  :disableAutoplay="true"
  class="image-preview__work-preview"
/>
```

- 启用了 `enableCarouselControl` 功能
- 禁用了自动轮播（`disableAutoplay="true"`）
- 支持双向同步：缩略图点击 ↔ 轮播图指示器点击

### 5. 向后兼容性

- 新增的事件处理逻辑只在 `enableCarouselControl="true"` 时生效
- 现有使用 ImageWorkPreview 的地方无需修改
- 不会影响其他页面的轮播图行为

### 6. 测试建议

1. **基础功能测试：**

   - 点击 ImageSidebar 缩略图，验证轮播图是否切换
   - 点击轮播图指示器，验证 ImageSidebar 选中状态是否更新

2. **边界情况测试：**

   - 在禁用 enableCarouselControl 的页面，验证不会触发同步
   - 快速连续点击，验证状态同步的稳定性

3. **兼容性测试：**
   - 验证其他使用 ImageWorkPreview 的页面功能正常
   - 确认不会产生额外的性能开销

### 7. 相关文件

- `src/components/TemplateView/ImageWorkPreview.vue` - 轮播图组件
- `src/views/EditProjectView/pages/SecondStepImage/components/ContentPanel/index.vue` - 内容面板
- `src/views/EditProjectView/pages/SecondStepImage/components/ImagePreview/index.vue` - 图片预览组件
- `src/utils/eventBus.ts` - 事件总线

### 8. 注意事项

1. **事件命名：** 使用 EVENT_NAMES 常量确保事件名称一致性
2. **生命周期管理：** 确保在组件销毁时清理事件监听器
3. **功能隔离：** 只在需要同步功能的场景中启用
4. **类型安全：** 事件参数需要进行适当的类型断言
