import Vue from 'vue';
import MaterialBasicUpload from './MaterialBasicUpload.vue';
import type { MaterialUploadFile } from './types';

/**
 * 显示素材库弹窗组件
 * @param props 配置参数
 * @param props.maxChosenFileCount 最大选择数量
 * @param props.title 标题
 * @param props.isVideo 是否为视频
 * @param props.onConfirm 选择文件回调
 * @returns void
 */
export const showMaterialBasicUpload = (props: {
  maxChosenFileCount: number;
  title: string;
  isVideo: boolean;
  onConfirm?: (files: MaterialUploadFile[]) => void;
}) => {
  Vue.prototype.$modal.open('MaterialBasicUpload', {
    component: MaterialBasicUpload,
    props: {
      visible: true,
      ...props,
    },
    onClose: () => {
      console.log('MaterialBasicUpload已关闭');
    },
  });
};

/**
 * 关闭素材库弹窗组件
 */
export const closeMaterialBasicUpload = () => {
  Vue.prototype.$modal.close('MaterialBasicUpload');
};
