# ImageWorkPreview 自动轮播控制功能

## 功能描述

为 ImageWorkPreview 组件添加了 `disableAutoplay` prop，用于控制轮播图的自动播放功能。在图文编辑器预览区中关闭自动轮播，提供更好的用户体验。

## 实现方案

### 1. 组件修改

#### ImageWorkPreview.vue

**添加新的 prop：**

```typescript
const props = withDefaults(
  defineProps<{
    // ... 其他props
    /** 是否禁用自动轮播 */
    disableAutoplay?: boolean;
  }>(),
  {
    disableAutoplay: false,
  },
);
```

**修改轮播图配置：**

```vue
<el-carousel
  ref="carousel"
  class="w-full h-full bg-black"
  :style="{ maxHeight: carouselMaxHeight }"
  arrow="never"
  trigger="click"
  :autoplay="!disableAutoplay"
>
```

### 2. 使用场景

#### 2.1 图文编辑器预览区 (ImagePreview/index.vue)

```vue
<ImageWorkPreview
  :title="workInfo?.script?.title || '标题'"
  :content="workInfo?.script?.content || '内容'"
  :imgList="imgListCal"
  height="full"
  carousel-max-height="78%"
  :enableCarouselControl="true"
  :disableAutoplay="true"
  class="image-preview__work-preview"
/>
```

## 3. 技术要点

### 3.1 Element UI Carousel 自动播放控制

- Element UI 的 `el-carousel` 组件默认启用自动播放
- 通过 `:autoplay="false"` 可以禁用自动播放
- 使用计算属性 `:autoplay="!disableAutoplay"` 实现动态控制

### 3.2 向后兼容性设计

- `disableAutoplay` prop 默认值为 `false`
- 现有使用 ImageWorkPreview 的地方无需修改
- 只有明确传入 `disableAutoplay="true"` 的地方才会禁用自动播放

### 3.3 用户体验优化

- 在图文编辑器预览区中，用户通常需要仔细查看和编辑内容
- 自动轮播可能会干扰用户的编辑体验
- 禁用自动轮播让用户可以按自己的节奏浏览和编辑内容

## 4. 使用指南

### 4.1 启用自动轮播（默认行为）

```vue
<ImageWorkPreview :title="title" :content="content" :imgList="imgList" />
```

### 4.2 禁用自动轮播

```vue
<ImageWorkPreview
  :title="title"
  :content="content"
  :imgList="imgList"
  :disableAutoplay="true"
/>
```

## 5. 测试建议

1. **默认行为测试：**

   - 验证不传入 `disableAutoplay` 时轮播图正常自动播放
   - 确认现有页面的轮播图行为未改变

2. **禁用功能测试：**

   - 验证传入 `disableAutoplay="true"` 时轮播图不自动播放
   - 确认用户仍可手动切换轮播图

3. **图文编辑器预览区测试：**
   - 测试图文编辑器预览区的轮播图不自动播放
   - 确认用户可以通过点击缩略图切换预览内容

## 6. 相关文件

- `src/components/TemplateView/ImageWorkPreview.vue` - 主要组件
- `src/views/EditProjectView/pages/SecondStepImage/components/ImagePreview/index.vue` - 图文编辑器预览区

## 7. 注意事项

1. **prop 命名：** 使用 `disableAutoplay` 而不是 `enableAutoplay`，因为默认行为是启用自动播放
2. **向后兼容：** 确保默认值为 `false`，保持原有行为
3. **用户体验：** 在图文编辑器等需要用户专注编辑的场景中禁用自动播放
4. **功能隔离：** 只在特定的编辑场景中禁用自动播放，其他预览场景保持默认行为
