/**
 * @fileoverview 作品列表管理的可复用 Composable
 * @description 提供获取、管理作品列表（包括分页、选中作品详情）相关的功能
 */
import { ref, computed, Ref, ComputedRef } from 'vue';
import { debounce } from 'lodash-es';
import { getWorkListWithTransform } from '@/api/EditProjectView/work';
import { WorkItem, VideoWorkItem, ImageWorkItem } from '@/types';
import { PROJECT_TYPE_VIDEO } from '@/views/EditProjectView/constants/index';
import { usePagination } from './usePagination';
import { useWorkDetail } from './useWorkDetail';
import { message } from '@fk/faicomponent';

export interface UseWorkListReturn<T extends WorkItem> {
  /** 当前选中作品的ID */
  currentWorkId: Ref<number>;
  /** 是否正在加载作品列表 */
  isLoading: Ref<boolean>;
  /** 当前页的作品列表数据 */
  workList: Ref<WorkItem[]>;
  /** 当前选中的作品数据详情 */
  currentWork: Ref<T | undefined>;
  /** 当前作品的进度（仅视频） */
  currentProgress: ComputedRef<number>;
  /** 获取作品列表数据的方法 */
  fetchWorkList: () => Promise<[Error | null, void | null]>;
  /** 选中的作品ID列表 */
  selectedWorkIds: Ref<number[]>;
  /** 处理作品选择变更的方法 */
  handleSelectChange: (ids: number[]) => void;
  // 分页相关
  /** 每页条数 */
  pageSize: Ref<number>;
  /** 当前页码 */
  currentPage: Ref<number>;
  /** 总条目数 */
  totalItems: Ref<number>;
  /** 成功生成的作品数量 */
  sucNum: Ref<number>;
  /** 处理页码变更的方法 */
  handlePageChange: (page: number) => Promise<[Error | null, void | null]>;
  // 作品详情
  /** 是否正在加载作品详情 */
  isLoadingDetail: Ref<boolean>;
  // 切换作品并获取详情
  /** 切换到指定作品并获取其详情的方法 */
  switchToWork: (workId: number) => Promise<[Error | null, void | null]>;
  /** 清除指定作品的详情缓存 */
  clearWorkDetailCacheEntry: (workId: number) => void;
  /** 清除所有作品详情缓存 */
  clearAllWorkDetailCache: () => void;
  /** 局部更新当前作品的进度 */
  updateCurrentWorkProgress: (progress: number) => void;
  /** 检查指定作品是否有详情缓存 */
  hasWorkDetailCache: (workId: number) => boolean;
  /** 批量更新作品详情缓存中的进度 */
  batchUpdateWorkProgress: (
    updates: Array<{ workId: number; progress: number }>,
  ) => void;
}

/**
 * 视频作品列表管理
 * @param projectId 项目ID
 * @returns {UseWorkListReturn<VideoWorkItem>} 视频作品列表管理相关的状态和方法
 */
export function useVideoWorkList(
  projectId: number,
): UseWorkListReturn<VideoWorkItem> {
  return useWorkListImpl<VideoWorkItem>(projectId);
}

/**
 * 图文作品列表管理
 * @param projectId 项目ID
 * @returns {UseWorkListReturn<ImageWorkItem>} 图文作品列表管理相关的状态和方法
 */
export function useImageWorkList(
  projectId: number,
): UseWorkListReturn<ImageWorkItem> {
  return useWorkListImpl<ImageWorkItem>(projectId);
}

/**
 * 作品列表管理的内部实现
 * @template T WorkItem的子类型，表示具体的作品类型（视频或图文）
 * @param projectId 项目ID
 * @returns {UseWorkListReturn<T>} 作品列表管理相关的状态和方法
 */
function useWorkListImpl<T extends WorkItem>(
  projectId: number,
): UseWorkListReturn<T> {
  // 是否正在加载数据
  const isLoading = ref<boolean>(false);
  // 作品列表数据
  const workList = ref<WorkItem[]>([]);
  // 选中的作品ID列表
  const selectedWorkIds = ref<number[]>([]);

  // 使用分页组件
  const {
    currentPage,
    pageSize,
    totalItems,
    setTotalItems,
    getPaginationParams,
  } = usePagination(5);

  // 成功生成的作品数量
  const sucNum = ref<number>(0);

  // 使用作品详情组件
  const {
    currentWorkId,
    currentWork,
    isLoadingDetail,
    fetchWorkDetail,
    setCurrentWorkId,
    clearWorkDetailCacheEntry,
    clearAllWorkDetailCache,
    updateCurrentWorkProgress,
    hasWorkDetailCache,
    batchUpdateWorkProgress,
  } = useWorkDetail<T>();

  /**
   * 当前作品的进度（仅视频作品有此属性）
   */
  const currentProgress = computed<number>(() =>
    currentWork.value?.type === PROJECT_TYPE_VIDEO
      ? (currentWork.value as VideoWorkItem).progress
      : 0,
  );

  /**
   * 切换到指定作品并获取详情
   * @param workId 作品ID
   */
  const switchToWork = async (
    workId: number,
  ): Promise<[Error | null, void | null]> => {
    try {
      if (!workId) return [null, null];

      // 设置当前作品ID
      setCurrentWorkId(workId);

      // 获取作品详情
      await fetchWorkDetail(workId);
      return [null, null];
    } catch (error) {
      console.error('切换作品失败:', error);
      return [error as Error, null];
    }
  };

  /**
   * 获取作品列表数据
   * @returns Promise<[Error | null, void | null]>
   */
  const fetchWorkList = async (): Promise<[Error | null, void | null]> => {
    isLoading.value = true;

    // 获取分页参数
    const { pageNow, limit } = getPaginationParams();

    const [err, res] = await getWorkListWithTransform({
      projectId,
      limit,
      pageNow,
      sortKey: 'sortTime',
    });

    // ADI-TEST
    // if (pageNow >= 2) {
    //   return [new Error('获取作品列表失败'), null];
    // }

    if (err || !res?.workList) {
      isLoading.value = false;
      const error =
        err instanceof Error
          ? err
          : new Error(err?.message || '获取作品列表失败');
      message.error(err?.message || '获取作品列表失败');
      return [error, null];
    }

    // 更新总条数
    setTotalItems(res.total || 0);

    // 更新成功数量
    sucNum.value = res.sucNum || 0;

    workList.value = res.workList;

    // 如果有作品且没有选中作品，则选中第一个
    if (workList.value.length > 0) {
      // 更新选中的作品ID列表
      if (selectedWorkIds.value.length === 0) {
        // 如果没有选中的作品ID，默认选中第一个并切换到该作品
        selectedWorkIds.value = [workList.value[0].id];

        const [switchErr] = await switchToWork(workList.value[0].id);
        if (switchErr) {
          console.error('切换到第一个作品失败:', switchErr.message);
        }
      }
    }

    isLoading.value = false;
    return [null, null];
  };

  /**
   * 处理页码变更事件
   * @param page 页码
   * @returns Promise<[Error | null, void | null]>
   */
  const handlePageChange = async (
    page: number,
  ): Promise<[Error | null, void | null]> => {
    currentPage.value = page;
    return await fetchWorkList();
  };

  /**
   * 处理作品选择变更事件（内部实现）
   * @param ids 选中的作品ID列表
   */
  const _handleSelectChange = async (ids: number[]): Promise<void> => {
    selectedWorkIds.value = ids;
    if (ids.length > 0) {
      const [err] = await switchToWork(ids[0]);
      if (err) {
        console.error('切换作品失败:', err.message);
      }
    }
  };

  /**
   * 处理作品选择变更事件（带防抖）
   * @param ids 选中的作品ID列表
   */
  const handleSelectChange = debounce(_handleSelectChange, 100);

  return {
    currentWorkId,
    isLoading,
    workList,
    currentWork,
    currentProgress,
    fetchWorkList,
    selectedWorkIds,
    handleSelectChange,
    // 分页相关
    pageSize,
    currentPage,
    totalItems,
    sucNum,
    handlePageChange,
    // 详情相关
    isLoadingDetail,
    switchToWork,
    clearWorkDetailCacheEntry,
    clearAllWorkDetailCache,
    updateCurrentWorkProgress,
    hasWorkDetailCache,
    batchUpdateWorkProgress,
  };
}
