# 图文作品新生成标签功能

## 概述

本功能为图文作品实现了"新生成标签"功能，对接后端新增的接口和字段，使图文作品能够像视频作品一样显示新生成标签。

## 背景

图文作品重新生成后，由于没有重新生成相关的状态（与视频作品不同），无法正确显示"新生成标签"。后端已新增字段和接口来支持图文作品的新生成标签功能。

## 功能特性

### 1. 新生成标签显示

- **视频作品**：继续使用原有的重新生成完成状态（`status=5`）判断
- **图文作品**：使用新增的 `editAgainGraphic` 字段结合作品状态（`status=1`）判断

### 2. 用户交互

- 用户点击新生成的图文作品后，自动调用 `/api/work/updateFlag` 接口
- 告知后端该作品已被查看，下次不再显示新生成标签

## 技术实现

### 1. 本地状态管理

为了提供即时的用户体验反馈，同时遵循 Vue 2.7 的最佳实践，采用简单的对象状态管理方案：

```typescript
// 本地状态：记录已标识的作品ID
const flaggedWorkIds = ref<Record<number, boolean>>({});
```

**设计原理：**

- **不直接修改 props**：遵循 Vue 单向数据流原则，保持 props 只读性
- **本地状态隔离**：每个组件实例维护独立的标识状态
- **即时用户反馈**：API 调用成功后立即更新本地状态，标签即时消失
- **简单高效**：使用对象结构，简单直接的属性访问

**生命周期管理：**

- 组件销毁重建时，本地状态自动重置
- 用户刷新页面时，重新获取最新的后端数据
- 后端已记录标识状态，不会重复显示已查看的标签

### 2. API 接口变更

#### 作品列表接口 `/api/work/list`

**新增返回字段：**

```typescript
interface ApiWorkListItem {
  // ... 其他字段
  /** 图文是否二次编辑（仅当作品状态为已生成时使用此字段判断是否显示新生成标签） */
  editAgainGraphic?: boolean;
}
```

#### 标识接口 `/api/work/updateFlag`

**接口参数：**

```typescript
interface UpdateWorkFlagParams {
  /** 作品ID */
  id: number;
}
```

### 3. 前端实现

#### 数据类型定义

- 在 `ApiWorkListItem` 接口中添加 `editAgainGraphic` 字段
- 在 `BaseWorkItem` 接口中添加 `editAgainGraphic` 字段
- 在数据转换函数中传递该字段

#### 显示逻辑

```typescript
// 计算是否为重新生成完成状态（视频）或新生成状态（图文）
const isRegenerated = computed(() => {
  // 防御性编程：确保workItem存在
  if (!props.workItem) {
    return false;
  }

  // 视频作品：使用重新生成完成状态判断
  if (props.workItem.type === WorkType.VIDEO) {
    return isRecompletedWork(statusInfo.value);
  }

  // 图文作品：当作品状态为已生成且editAgainGraphic为true时显示新生成标签
  if (props.workItem.type === WorkType.IMAGE) {
    // 如果该作品已被本地标识为已查看，则不显示新生成标签
    if (!!flaggedWorkIds.value[props.workItem.id]) {
      return false;
    }

    return (
      statusInfo.value.status === WORK_STATUS.COMPLETED &&
      props.workItem.editAgainGraphic === true
    );
  }

  return false;
});
```

#### 用户点击处理

```typescript
// 本地状态：记录已标识的作品ID
const flaggedWorkIds = ref<Record<number, boolean>>({});

const handleClick = async () => {
  // 如果是图文作品且显示新生成标签，调用updateFlag接口
  if (
    props.workItem.type === WorkType.IMAGE &&
    isRegenerated.value &&
    props.workItem.editAgainGraphic === true
  ) {
    const [err] = await updateWorkFlag({ id: props.workItem.id });

    if (err) {
      console.error('更新作品标识失败:', err.message);
      // 不阻止选择操作，继续执行
    } else {
      console.log('作品标识更新成功:', props.workItem.id);
      // 记录该作品已被标识，用于本地状态管理
      flaggedWorkIds.value = {
        ...flaggedWorkIds.value,
        [props.workItem.id]: true,
      };
    }
  }

  emit('select', props.workItem.id);
};
```

## 文件变更清单

### 新增文件

- `docs/ADI/图文作品新生成标签功能/README.md` - 功能文档
- `src/components/WorkListBar/__tests__/WorkListBarItem.newGenerated.test.ts` - 测试文件

### 修改文件

1. **`src/api/EditProjectView/types/response.ts`**

   - 在 `ApiWorkListItem` 接口中添加 `editAgainGraphic` 字段

2. **`src/api/EditProjectView/types/request.ts`**

   - 添加 `UpdateWorkFlagParams` 接口定义

3. **`src/types/Work.ts`**

   - 在 `BaseWorkItem` 接口中添加 `editAgainGraphic` 字段

4. **`src/api/EditProjectView/utils/inputDataTransform.ts`**

   - 在 `transformWorkListItemFromApiToProject` 函数中传递 `editAgainGraphic` 字段

5. **`src/api/EditProjectView/work.ts`**

   - 添加 `updateWorkFlag` API 接口函数
   - 导入 `UpdateWorkFlagParams` 类型

6. **`src/components/WorkListBar/WorkListBarItem.vue`**
   - 修改 `isRegenerated` 计算属性以支持图文作品
   - 修改 `handleClick` 函数以调用 `updateWorkFlag` 接口
   - 导入 `updateWorkFlag` API 函数

## 使用条件

### 显示新生成标签的条件

- **图文作品**：`type === 1` && `status === 1` && `editAgainGraphic === true`
- **视频作品**：`type === 0` && `status === 5`（保持原有逻辑）

### 调用标识接口的条件

- 作品类型为图文（`type === 1`）
- 当前显示新生成标签（`isRegenerated.value === true`）
- `editAgainGraphic` 字段为 `true`
- 该作品未被本地标识为已查看（`!flaggedWorkIds.value[workItem.id]`）

## 错误处理

- API 调用失败时记录错误日志，但不阻止用户选择操作
- 遵循现有的 API 请求规范（使用 await-to 模式）
- 确保错误处理和类型定义的完整性

## 技术特点

- **向后兼容**：不影响现有视频作品的新生成标签功能
- **类型安全**：使用 TypeScript 确保类型安全
- **错误处理**：遵循 await-to 模式，提供完善的错误处理
- **防御性编程**：添加了必要的空值检查
- **代码复用**：复用了现有的组件和样式
- **遵循最佳实践**：不直接修改 props，使用本地状态管理
- **即时用户反馈**：API 调用成功后立即更新 UI，无需等待数据刷新
- **简单高效**：使用对象结构，简单直接

## 兼容性

- 与现有视频作品新生成标签功能完全兼容
- 不影响现有的作品状态显示逻辑
- 向后兼容，`editAgainGraphic` 字段为可选字段

## 测试

功能包含完整的单元测试，覆盖核心逻辑：

### 测试覆盖范围

1. **本地状态管理逻辑**

   - 已标识作品的本地状态管理
   - 新生成标签显示计算逻辑

2. **API 调用逻辑**

   - 满足条件时调用 updateWorkFlag 接口
   - 已标识作品不重复调用接口
   - 非图文作品不调用接口

3. **错误处理**
   - API 调用失败时的错误处理
   - 本地状态在错误情况下的保护

### 运行测试

```bash
npm run test src/components/WorkListBar/__tests__/WorkListBarItem.newGenerated.test.ts
```

### 测试结果

所有测试用例均通过，验证了：

- ✅ 本地状态管理的正确性
- ✅ API 调用逻辑的准确性
- ✅ 错误处理的健壮性
- ✅ 不同场景下的行为一致性
- ✅ 保存作品逻辑的正确性（图文作品不触发新旧选择弹窗）

## Bug 修复记录

### 问题描述

图文作品修改保存成功后，点击保存作品时错误地出现了新旧选择弹窗。

### 问题原因

在保存作品的逻辑中，代码检查 `isRegenerated.value` 来决定是否显示新旧选择弹窗。但是对于图文作品，当 `editAgainGraphic` 为 `true` 时，`isRegenerated` 也会返回 `true`，导致图文作品也会触发新旧选择弹窗。

### 解决方案

修改保存作品的逻辑，明确区分视频作品和图文作品：

1. **单个作品保存**（`WorkListBarItem.vue`）：

   ```typescript
   // 只有视频作品的重新生成完成状态才显示新旧选择弹窗
   if (
     props.workItem.type === WorkType.VIDEO &&
     isRecompletedWork(statusInfo.value)
   ) {
     eventBus.emit(EVENT_NAMES.SHOW_NEW_OLD_CHOICE_MODAL, props.workItem);
     return;
   }
   ```

2. **批量保存**（`useSecondStep.ts`）：

   ```typescript
   // 只有视频作品（type === 0）才检查重新生成完成状态
   const regeneratedWork = selectedWorks.find(work => {
     const statusInfo = getWorkStatusInfo(work);
     return work.type === 0 && isRecompletedWork(statusInfo);
   });
   ```

### 修复效果

- ✅ 图文作品保存时不再出现新旧选择弹窗
- ✅ 视频作品的新旧选择弹窗功能保持不变
- ✅ 图文作品的新生成标签功能正常工作

## 响应式更新修复

### 响应式问题描述

发现 `flaggedWorkIds` 本地状态没有生效，点击图文作品后新生成标签没有移除。

### 响应式问题原因

在 Vue 2.7 中，直接修改对象属性或使用 Set 等集合类型时，可能无法正确触发响应式更新。

### 响应式解决方案

简化为使用对象，并通过创建新对象的方式确保响应式更新：

```typescript
// 修复前：使用Set结构（过度设计）
const flaggedWorkIds = ref(new Set<number>());
flaggedWorkIds.value.add(props.workItem.id);

// 修复后：使用简单对象结构
const flaggedWorkIds = ref<Record<number, boolean>>({});
flaggedWorkIds.value = { ...flaggedWorkIds.value, [props.workItem.id]: true };
```

### 调试信息增强

添加了详细的调试日志来跟踪状态变化：

```typescript
console.log(`作品${props.workItem.id}新生成标签检查:`, {
  isInFlaggedSet,
  isCompleted,
  hasEditFlag,
  flaggedIds: Object.keys(flaggedWorkIds.value),
});
```

### 响应式修复效果

- ✅ 点击图文作品后新生成标签立即消失
- ✅ 本地状态正确更新并触发响应式计算
- ✅ 调试信息帮助开发者跟踪状态变化
