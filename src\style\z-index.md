# z-index 管理规范与最佳实践

## 一、背景说明

### 1.1 问题痛点

在前端开发中，z-index 管理面临以下核心问题：

- 缺乏统一的层级管理标准，导致值分配混乱
- 层级关系复杂且难以追踪，增加维护成本
- 组件间层级冲突频繁，影响用户体验
- 项目扩展时难以合理分配新的层级值

### 1.2 解决思路

采用基于 SCSS 的统一层级管理方案：

- 建立分层管理机制，明确层级边界
- 实现层级值的集中配置和管理
- 提供标准化的层级获取方法
- 支持灵活扩展和维护

## 二、架构设计

### 2.1 层级架构

系统将 z-index 划分为以下层级：

| 层级类型 | 数值范围  | 适用场景                   |
| -------- | --------- | -------------------------- |
| 普通元素 | 0-100     | 基础 UI 组件、小型交互元素 |
| 浮动元素 | 101-200   | 固定头部、底部导航         |
| 交互组件 | 201-300   | 下拉菜单、提示框           |
| 弹窗层   | 1001-2000 | 模态框、对话框             |
| 遮罩层   | 2001-3000 | loading、分享蒙层          |
| 特殊层   | 3001-9999 | 通知、广告等               |

### 2.2 技术实现

#### 2.2.1 核心配置

```scss
$z-indexes: (
  'sidebar': 20,
  'header': 101,
  'modal': 1001,
  // ... 后续扩展
);
@function z($key) {
  @if map-has-key($z-indexes, $key) {
    @return map-get($z-indexes, $key);
  }
  @return 0;
}
```

## 三、使用指南

### 3.1 基础用法

```scss
@import '@/styles/z-index.scss';

.component {
  // 使用 z() 函数获取层级值
  z-index: z('modal');

  &__mask {
    z-index: z('modal-mask');
  }
}
```

### 3.2 扩展配置

新增 z-index 配置流程：

1. 评估组件层级类型
2. 确认目标数值范围
3. 在 `$z-indexes` 中添加配置

```scss
// 示例：添加新的弹窗组件配置
$z-indexes: (
  // ... 现有配置
  'new-modal': 1002,
  // 新增配置
);
```

### 3.3 开发规范

#### 禁止事项

- ❌ 禁止使用魔法数字
- ❌ 禁止越界使用
- ❌ 禁止直接操作 style.zIndex

#### 最佳实践

- ✅ 统一使用 z() 函数
- ✅ 遵循层级范围限制
- ✅ 通过 CSS 类名控制层级

### 3.4 命名规范

- 采用 kebab-case 命名
- 使用语义化描述，如 `modal-header`
- 相关组件使用统一前缀，如 `modal-*`
- 避免数字作为名称前缀

### 3.5 现已加入 Unocss

在 Unocss 中也可以使用类似的方式来管理`zi-xxx`，其中的`xxx`为变量名，变量名详情看`uno.config.ts`，使用方法如下：

```html
<div class="zi-modal">...</div>
```

## 四、最佳实践

### 4.1 组件层级设计

```scss
.component {
  // 基础层级
  &base {
    z-index: z('component-base');
  }
  // 浮层
  &overlay {
    z-index: z('component-overlay');
  }
}
```

### 4.2 动态层级处理

```typescript
// 推荐：通过类名控制
const toggleModal = (visible: boolean) => {
  const modalEl = document.querySelector('.modal');
  modalEl?.classList.toggle('modal--active', visible);
};

// 不推荐：直接操作 style
const setModalZIndex = (zIndex: number) => {
  modal.style.zIndex = String(zIndex); // ❌ 避免这种做法
};
```

## 五、参考资料

- [MDN: z-index](https://developer.mozilla.org/zh-CN/docs/Web/CSS/z-index)
- [Understanding CSS z-index](https://developer.mozilla.org/zh-CN/docs/Web/CSS/CSS_Positioning/Understanding_z_index)
- [Sass Maps Documentation](https://sass-lang.com/documentation/values/maps)
