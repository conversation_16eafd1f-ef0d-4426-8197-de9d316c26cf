/**
 * @fileoverview 轮询配置验证工具函数
 * @description 提供轮询配置参数的验证功能
 */

import { logger } from '@/utils/logger';
import { PROJECT_TYPE_VIDEO, PROJECT_TYPE_IMAGE } from '@/views/EditProjectView/constants';

/**
 * 验证配置选项
 * @param projectId 项目ID
 * @param projectType 项目类型
 */
export function validateOptions(projectId: number, projectType: number): void {
  if (!projectId || projectId <= 0) {
    logger.debug('⚠️ 无效的项目ID，轮询功能可能无法正常工作', {
      项目ID: projectId,
    });
  }

  if (
    typeof projectType !== 'number' ||
    (projectType !== PROJECT_TYPE_VIDEO && projectType !== PROJECT_TYPE_IMAGE)
  ) {
    logger.debug('⚠️ 无效的项目类型，将使用视频项目的轮询逻辑', {
      项目类型: projectType,
      有效类型: `${PROJECT_TYPE_VIDEO}(视频) 或 ${PROJECT_TYPE_IMAGE}(图文)`,
    });
  }
}
