# VideoPlayer 延迟加载功能

## 概述

VideoPlayer 组件已修改为支持延迟加载功能，不再在组件挂载时自动加载视频，而是在用户首次点击播放按钮时才开始加载视频资源。

## 修改内容

### 1. 配置文件修改

**文件**: `src/views/EditProjectView/pages/SecondStepVideo/components/VideoPlayer/config/player.config.ts`

```typescript
// 修改前
preload: 'auto',

// 修改后  
preload: 'none', // 设置为none，只有用户点击播放时才加载
```

### 2. 播放器初始化修改

**文件**: `src/views/EditProjectView/pages/SecondStepVideo/components/VideoPlayer/hooks/usePlayer.ts`

```typescript
// 修改前
if (player.value) {
  player.value.preload('metadata');
}

// 修改后
if (player.value) {
  player.value.preload('none'); // 设置为none，只有用户点击播放时才加载
}
```

### 3. HTML 元素修改

**文件**: `src/views/EditProjectView/pages/SecondStepVideo/components/VideoPlayer/index.vue`

```html
<!-- 修改前 -->
<video preload="auto" />

<!-- 修改后 -->
<video preload="none" />
```

### 4. 播放逻辑增强

**文件**: `src/views/EditProjectView/pages/SecondStepVideo/components/VideoPlayer/index.vue`

```typescript
// 修改前
const togglePlay = () => {
  if (!player.value) return;
  
  if (isPlaying.value) {
    player.value.pause();
  } else {
    player.value.play();
  }
};

// 修改后
const togglePlay = async () => {
  if (!player.value) return;

  if (isPlaying.value) {
    player.value.pause();
  } else {
    // 检查是否需要加载视频（首次播放或视频未加载）
    const currentPreload = player.value.preload();
    if (currentPreload === 'none' || player.value.readyState() < 1) {
      // 设置预加载并开始加载
      player.value.preload('metadata');
      player.value.load();
    }
    
    try {
      await player.value.play();
    } catch (error) {
      console.error('视频播放失败:', error);
    }
  }
};
```

## 功能特点

1. **延迟加载**: 视频资源只在用户首次点击播放时才开始加载
2. **智能检测**: 自动检测视频是否已加载，避免重复加载
3. **错误处理**: 包含播放失败的错误处理机制
4. **性能优化**: 减少页面初始加载时的网络请求和资源消耗

## 使用场景

- 减少页面初始加载时间
- 节省用户流量（特别是移动端用户）
- 提升页面性能，特别是在有多个视频的页面中
- 符合现代 Web 应用的最佳实践

## 注意事项

1. 首次播放可能会有短暂的加载延迟
2. 视频封面图（poster）仍会正常显示
3. 播放控制逻辑保持不变，用户体验基本一致
4. 兼容现有的全屏、进度控制等功能

## 测试建议

1. 验证页面加载时视频不会自动开始下载
2. 确认首次点击播放按钮时视频能正常加载和播放
3. 测试后续的播放/暂停操作正常工作
4. 验证进度条、全屏等功能不受影响
