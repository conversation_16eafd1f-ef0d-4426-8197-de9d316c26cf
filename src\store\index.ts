import Vue from 'vue';
import Vuex from 'vuex';
import createPersistedState from 'vuex-persistedstate';
import user from '@/store/modules/user';
import { UserState } from '@/store/modules/user';
import system, { SystemState } from './modules/system';
import meta, { MetaState } from '@/store/modules/meta';
import project, { ProjectState } from '@/store/modules/project';
import materialUpload, {
  MaterialUploadState,
} from '@/store/modules/materialUpload';

Vue.use(Vuex);

export interface RootState {
  /** 用户相关 */
  user: UserState;
  /** 系统相关 */
  system: SystemState;
  /** 素材库相关 */
  meta: MetaState;
  /** 项目相关 */
  project: ProjectState;
  /** 素材编辑器相关 */
  materialUpload: MaterialUploadState;
}

// 获取当前用户AID，用于生成唯一的缓存key
const getCurrentUserId = (): string => {
  const userAid = localStorage.getItem('user-aid');
  return userAid || '';
};

export default new Vuex.Store<RootState>({
  modules: {
    user,
    system,
    meta,
    project,
    materialUpload,
  },
  plugins: [
    createPersistedState({
      // 存储的key名称 - 使用用户AID确保缓存隔离
      key: `scportal-web-vuex-${getCurrentUserId()}`,
      // 存储方式
      storage: window.localStorage,
      // 需要持久化的模块（需要检查模块内是否开启命名空间namespaced）
      paths: [
        // 素材编辑器
        'materialUpload.ownerFileViewType',
        'materialUpload.currentFolder',
        'materialUpload.currentPathList',
      ],
    }),
  ],
});

declare module 'vue/types/vue' {
  interface Vue {
    $store: Vuex.Store<RootState>;
  }
}
