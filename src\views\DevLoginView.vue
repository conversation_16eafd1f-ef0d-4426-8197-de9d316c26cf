<template>
  <div class="flex flex-col justify-center items-center h-screen w-screen">
    <div class="login-title">开发模式登录</div>
    <fa-form-model
      class="login-form"
      :model="form"
      :rules="rules"
      ref="formRef"
    >
      <fa-form-model-item prop="cacct" label="账号">
        <fa-input v-model="form.cacct" size="large" placeholder="账号" />
      </fa-form-model-item>
      <fa-form-model-item v-if="staffLogin" prop="sacct" label="成员账号">
        <fa-input v-model="form.sacct" size="large" placeholder="成员账号" />
      </fa-form-model-item>
      <fa-form-model-item prop="pwd" label="密码">
        <fa-input
          v-model="form.pwd"
          size="large"
          type="password"
          placeholder="密码"
          @pressEnter="login"
        />
      </fa-form-model-item>
      <fa-form-model-item>
        <fa-checkbox @change="staffLogin = !staffLogin">
          使用员工账号密码
        </fa-checkbox>
      </fa-form-model-item>
      <fa-button size="large" type="primary" @click="login"> 登录 </fa-button>
    </fa-form-model>
  </div>
</template>
<script lang="ts" setup>
import { loginForDev } from '@/api/System/System';
import { ref } from 'vue';
import md5 from 'md5';
import Cookies from 'js-cookie';
import { message } from '@fk/faicomponent';
const staffLogin = ref(false);
const form = ref({
  cacct: '',
  sacct: '',
  pwd: '',
});
const formRef = ref();
const rules = {
  cacct: [{ required: true, message: '请输入账号', trigger: 'blur' }],
  sacct: [{ required: true, message: '请输入成员账号', trigger: 'blur' }],
  pwd: [{ required: true, message: '请输入密码', trigger: 'blur' }],
};

const login = async () => {
  const { cacct, sacct, pwd } = form.value;
  formRef.value.validate((valid: boolean) => {
    if (valid) {
      loginForDev(cacct, sacct || 'boss', md5(pwd), staffLogin.value).then(
        ([err, res]) => {
          if (err) {
            message.error(JSON.stringify(err));
            return;
          }
          const resdata = JSON.parse(JSON.stringify(res));
          if (!resdata.success) {
            message.error(resdata.msg);
            return;
          }
          Cookies.set('_FSESSIONID', resdata.sessionId, { expires: 1 });
          window.location.href = '/';
        },
      );
    }
  });
};
</script>

<style></style>
