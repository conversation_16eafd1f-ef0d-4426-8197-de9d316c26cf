<template>
  <FaFileManagerTable
    :sort-by="sortBy"
    :sort-mode="sortMode"
    :columns="columns"
    :column-width="48"
    :disabled="isDisabled"
    :tr-event="trEvent"
    @sort-change="handleSortChange"
    @select="changeSelect"
  >
    <template #[SORT_BY_KEY.NAME]="{ text, record, index }">
      <div class="file-manager-demo--table-name flex ai-center">
        <div
          v-if="checkOwnerFilePresetPreview(record)"
          class="file-manager-demo--table-icon relative"
        >
          <FileManagerNewIcon :info="record" class="size-[24px]" />
          <div
            v-if="checkFileUploading(record)"
            class="absolute size-[24px] top-[-9px]"
          >
            <fa-progress
              strokeWidth="5"
              :percent="record.percent"
              :show-info="false"
            />
          </div>
        </div>
        <div
          v-else-if="checkVideoCover(record)"
          class="file-manager-demo--table-img"
        >
          <div
            :style="{ 'background-image': `url(${getVideoSnapUrl(record)})` }"
          />
        </div>
        <div v-else class="file-manager-demo--table-img">
          <div :style="{ 'background-image': `url(${getImgUrl(record)})` }" />
        </div>
        <div class="w100 flex ai-center" @dblclick.stop @click.stop>
          <FaFileManagerInput
            :ref="`input${record[INFO_KEYS.ID]}`"
            class="flex-1"
            :value="text"
            :line="1"
            :disabled="checkFileUploading(record)"
            :maxlength="isFolder(record) ? folderNameMaxLen : inputNameMaxLen"
            @focus="focusId = INFO_KEYS.ID"
            @blur="inputBlur(record, $event.value)"
          />
          <div
            class="file-manager-demo--table-control"
            :style="{ opacity: controlId === record[INFO_KEYS.ID] ? 1 : 0 }"
          >
            <span
              class="file-manager-demo--table-btn"
              @click="handleClick('move', index, record)"
              >移动</span
            >
            <span
              class="file-manager-demo--table-btn"
              @click="handleClick('delete', index, record)"
              >删除</span
            >
          </div>
        </div>
      </div>
    </template>
    <template #[SORT_BY_KEY.FILE_SIZE]="{ text, record }">
      <div :title="calcSize(record[INFO_KEYS.FILE_SIZE])">
        {{ calcSize(record[INFO_KEYS.FILE_SIZE]) }}
      </div>
    </template>
    <template #[SORT_BY_KEY.CREATE_TIME]="{ text, record }">
      <div :title="formatTime(record[INFO_KEYS.CREATE_TIME])">
        {{ formatTime(record[INFO_KEYS.CREATE_TIME]) }}
      </div>
    </template>
  </FaFileManagerTable>
</template>

<script>
import {
  FileManager as FaFileManager,
  DesignerIcon as FaDesignerIcon,
} from '@fk/fa-component-cus';
import { SORT_BY_KEY } from '@/components/MaterialManager/constants/index.ts';
import {
  INFO_KEYS,
  INPUT_NAME_MAX_LENGTH,
  FOLDER_NAME_MAX_LENGTH,
} from '@/constants/material';
import {
  iconType,
  checkOwnerFilePresetPreview,
  getSuffix,
  calcSize,
  formatTime,
  getImgUrl,
  getVideoSnapUrl,
  changeFmName,
} from '@/components/MaterialManager/utils/index.ts';
import store from '@/store';
import { defineComponent } from 'vue';
import { isFolder } from '@/utils/resource';
import {
  checkVideoCover,
  checkFileUploading,
} from '@/components/MaterialBasicUpload/utils/index.ts';
import FileManagerNewIcon from '@/components/MaterialManager/components/FileManagerNewIcon.vue';

export default defineComponent({
  name: 'FileManagerTable',
  components: {
    FaDesignerIcon,
    FaFileManagerTable: FaFileManager.FileManagerTable,
    FaFileManagerInput: FaFileManager.FileManagerInput,
    FileManagerNewIcon,
  },

  data() {
    return {
      INFO_KEYS,
      SORT_BY_KEY,

      controlId: '',

      focusId: '',

      columns: [
        {
          title: '文件名称',
          width: 410,
          dataIndex: SORT_BY_KEY.NAME,
          scopedSlots: { customRender: SORT_BY_KEY.NAME },
        },
        {
          title: '文件大小',
          width: 225,
          dataIndex: SORT_BY_KEY.FILE_SIZE,
          scopedSlots: { customRender: SORT_BY_KEY.FILE_SIZE },
        },
        {
          title: '上传时间',
          width: 225,
          dataIndex: SORT_BY_KEY.CREATE_TIME,
          scopedSlots: { customRender: SORT_BY_KEY.CREATE_TIME },
        },
      ],

      trEvent: {
        click: this.changeSelect,
        dblclick: this.handleDblclick,
        mouseenter: this.handleMouseenter,
        mouseleave: this.handleMouseleave,
      },

      inputNameMaxLen: INPUT_NAME_MAX_LENGTH,
      folderNameMaxLen: FOLDER_NAME_MAX_LENGTH,
    };
  },

  computed: {
    nowFmInfoList() {
      return store.getters.nowFmInfoList;
    },
    sortBy() {
      return store.state.meta.sortBy;
    },
    sortMode() {
      return store.state.meta.sortMode;
    },
    folderAddInfo() {
      return store.state.meta.folderAddInfo;
    },
  },

  watch: {
    folderAddInfo: {
      handler(info) {
        this.$nextTick(() => {
          const { id, form } = info;
          if (form !== 'all' && form !== 'table') return;
          this.$refs[`input${id}`] && this.$refs[`input${id}`].focus();
        });
      },
      deep: true,
    },
  },

  methods: {
    isFolder,
    getSuffix,
    getImgUrl,
    getVideoSnapUrl,
    iconType,
    checkOwnerFilePresetPreview,
    calcSize,
    formatTime,
    checkFileUploading,
    isDisabled() {
      // true 就 disabled
      return false;
    },

    async inputBlur(info, newName) {
      await changeFmName(info, newName);
    },

    changeSelect(record) {
      store.commit('changeSelectById', record);
    },

    handleDblclick(record) {
      if (isFolder(record)) {
        store.commit('changeDir', record[INFO_KEYS.ID]);
        store.dispatch('updateFolderContent');
      }
    },

    handleSortChange(info) {
      store.commit('setSort', info);
    },

    /**
     * 点击操作
     * @param type 操作类型
     * @param index 操作行索引
     * @param record 操作行数据
     */
    handleClick(type, _, record) {
      switch (type) {
        case 'delete':
          store.dispatch('deleteFile', record);
          break;
        case 'move':
          store.commit('setMoveOne', true);
          store.commit('setMoveOneData', record);
          store.commit('setMoveModal', true);
          break;
        default:
      }
    },

    handleMouseenter(e) {
      this.controlId = e[INFO_KEYS.ID];
    },

    handleMouseleave() {
      this.controlId = '';
    },

    checkVideoCover,
  },
});
</script>

<style lang="scss" scoped>
.fa-file-manager--table {
  margin-top: 10px;
  padding: 0 40px 0 30px;
  height: calc(100% - 10px);
}

.file-manager-demo--table {
  &-name {
    cursor: default;

    .w100 {
      width: 100%;
    }
  }

  &-icon {
    font-size: 24px;
  }

  &-control {
    transition: 0.3s opacity;
  }

  &-btn {
    margin-left: 16px;
    color: #40a9ff;
    cursor: pointer;
    transition: color 0.3s;

    &:hover {
      color: #3a84fe;
    }

    &:active {
      color: #096dd9;
    }
  }

  &-img {
    width: 24px;
    height: 24px;
    padding: 1px;
    border: 1px solid #d9d9d9;
    border-radius: 2px;
    box-sizing: border-box;

    > div {
      height: 100%;
      background-size: contain;
      background-position: center;
      background-repeat: no-repeat;
    }
  }
}

.fa-file-manager-input {
  margin-left: 8px;
}

.file-manager-demo--table-img {
  @apply relative;
}
</style>
