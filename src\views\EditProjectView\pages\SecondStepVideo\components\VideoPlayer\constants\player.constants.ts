/**
 * 播放器状态常量
 */
export const PLAYER_STATE = {
  /** 未初始化 */
  UNINITIALIZED: 'uninitialized',
  /** 加载中 */
  LOADING: 'loading',
  /** 就绪 */
  READY: 'ready',
  /** 错误 */
  ERROR: 'error',
} as const;

/**
 * 播放器事件类型常量
 */
export const PLAYER_EVENT_TYPE = {
  /** 播放 */
  PLAY: 'play',
  /** 暂停 */
  PAUSE: 'pause',
  /** 结束 */
  ENDED: 'ended',
  /** 错误 */
  ERROR: 'error',
  /** 全屏 */
  FULLSCREEN: 'fullscreen',
} as const;

/**
 * 播放器默认配置常量
 */
export const PLAYER_DEFAULT_CONFIG = {
  /** 控制栏自动隐藏时间（毫秒） */
  CONTROLS_HIDE_DELAY: 3000,
  /** 默认音量 */
  DEFAULT_VOLUME: 1,
  /** 视频比例 */
  ASPECT_RATIO: '9:16',
  /** 播放器初始化防抖延迟（毫秒）- 略大于作品切换间隔，减少频繁重建 */
  INIT_DEBOUNCE_DELAY: 250,
  /** 播放器初始化超时时间（毫秒）- 防止初始化卡住的兜底保护 */
  INIT_TIMEOUT: 5000,
} as const;

/**
 * 播放器样式常量
 */
export const PLAYER_STYLE = {
  /** 最大宽度 */
  MAX_WIDTH: '360px',
  /** 小屏幕最大宽度 */
  MAX_WIDTH_SMALL: '320px',
  /** 小屏幕断点 */
  SMALL_SCREEN_BREAKPOINT: '1366px',
} as const;
