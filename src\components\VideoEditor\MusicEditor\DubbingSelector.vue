<template>
  <div class="absolute z-1000" v-show="wrapperVisible">
    <el-dialog
      :visible.sync="visible"
      width="800px"
      :show-close="false"
      :close-on-click-modal="false"
      :append-to-body="false"
      :modal-append-to-body="false"
    >
      <div class="dubbing-selector" v-click-outside="handleClickOutside">
        <!-- 头部 -->
        <div
          class="h-[55px] px-[28px] b-divider b-b-1 flex shrink-0 justify-between items-center"
        >
          <div class="text-title font-bold">更换配音</div>
          <Icon
            @click="handleCancel"
            type="guanbi-tancong"
            class="w-[12px] h-[12px] text-assist hover:text-title cursor-pointer"
          />
        </div>
        <!-- 内容 -->
        <div class="flex-grow-1 flex flex-col overflow-hidden">
          <!-- 分类筛选 -->
          <div class="px-[40px] pt-[24px] pb-[16px]">
            <div class="flex justify-between items-start">
              <!-- 分类筛选 -->
              <div class="flex flex-wrap flex-1">
                <span
                  v-for="category in categories"
                  :key="category.id"
                  class="category-item"
                  :class="{ active: selectedCategory === category.id }"
                  @click="handleChangeCategory(category.id)"
                >
                  {{ category.name }}
                </span>
              </div>
              <!-- 智能推荐 -->
              <div
                class="flex items-center flex-shrink-0"
                v-if="canUseAiRecommend"
              >
                <fa-switch
                  size="small"
                  v-model="useAiRecommend"
                  @change="handleAiRecommendChange"
                /><span
                  @click="() => (useAiRecommend = !useAiRecommend)"
                  class="ml-[8px] text-[14px] text-subText cursor-pointer"
                  >智能推荐</span
                >
              </div>
            </div>
          </div>
          <!-- 列表 -->
          <div
            class="flex-1 overflow-auto scrollbar-small"
            @scroll="handleScroll"
            ref="scrollContainer"
            v-show="dubbingList.length > 0"
          >
            <div
              class="pt-[8px] pb-[24px] grid grid-cols-2 gap-[16px] px-[40px]"
            >
              <div
                class="dubbing-item"
                v-for="dubbing in dubbingList"
                :key="dubbing.voiceId"
                :class="{
                  playing: playingDubbing?.voiceId === dubbing.voiceId,
                  selected: selectedDubbing?.voiceId === dubbing.voiceId,
                }"
                @click="handleSelectDubbingWithAiCheck(dubbing)"
              >
                <!-- 封面图 -->
                <div class="relative w-[48px] h-[48px]">
                  <ScImg
                    class="dubbing-item-cover"
                    :src="dubbing.cover.resId"
                    :type="dubbing.cover.resType"
                    belong="oss"
                    :max-width="48"
                  />
                  <div
                    class="play-ctrl-box"
                    @click.stop="handlePlayDubbing(dubbing)"
                  >
                    <Icon
                      v-show="playingDubbing?.voiceId !== dubbing.voiceId"
                      class="play-ctrl-icon"
                      type="bofang"
                    />
                    <Icon
                      v-show="playingDubbing?.voiceId === dubbing.voiceId"
                      class="play-ctrl-icon"
                      type="zanting"
                    />
                  </div>
                </div>
                <!-- 中间信息 -->
                <div class="flex-1 mx-[16px] overflow-hidden flex items-center">
                  <div class="overflow-hidden whitespace-nowrap text-ellipsis">
                    {{ dubbing.name }}
                  </div>
                </div>
                <!-- 右侧按钮 -->
                <div class="flex items-center action-buttons">
                  <fa-button
                    class="select-btn"
                    v-show="selectedDubbing?.voiceId !== dubbing.voiceId"
                    type="primary"
                    @click.stop="handleSelectDubbingWithAiCheck(dubbing)"
                    >选择使用</fa-button
                  >
                  <div
                    v-show="selectedDubbing?.voiceId === dubbing.voiceId"
                    class="rounded-[6px] flex justify-center items-center text-primary"
                  >
                    <Icon
                      type="dangqianshiyong"
                      class="w-[18px] h-[18px] mr-[4px]"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
          <!-- 列表空状态 -->
          <div
            class="text-center flex-1 flex flex-col justify-center items-center"
            v-show="dubbingList.length === 0 && !loading"
          >
            <img class="w-[159px]" src="@/assets/common/empty.webp" />
            <span class="text-assist mt-[24px]">暂无数据</span>
          </div>
        </div>
        <!-- 底部按钮 -->
        <div class="py-[12px] flex shrink-0 justify-center b-divider b-t-1">
          <fa-button
            type="primary"
            class="mr-[16px] px-[28px]"
            @click="handleConfirm"
            >确认</fa-button
          >
          <fa-button class="px-[28px]" @click="handleCancel">取消</fa-button>
        </div>
        <!-- 配音播放器 -->
        <audio
          :src="currentPlayUrl"
          class="hidden"
          ref="audioPlayer"
          preload="metadata"
          crossorigin="anonymous"
          @error="handleAudioError"
          @ended="handleAudioEnded"
        ></audio>
      </div>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { ref, watch, onMounted, computed } from 'vue';
import { Dubbing } from '@/types';
import {
  getDubbingCategory,
  getDubbingList,
  getDubbingInfo,
} from '@/api/VideoEditor/music';
import { message } from '@fk/faicomponent';
import ScImg from '@/components/comm/ScImg.vue';

/*************弹窗控制 star***************/
const props = defineProps<{
  /** 是否显示 */
  value: boolean;
  /** 配音ID */
  voiceId?: string;
  /** 是否可以使用AI推荐 */
  canUseAiRecommend?: boolean;
  /** 是否使用AI推荐 */
  useAiRecommendValue?: boolean;
}>();
const visible = ref(false);
const wrapperVisible = ref(false);
const emit = defineEmits<{
  (e: 'input', value: boolean): void;
  (
    e: 'changeDubbingInfo',
    dubbing: Dubbing | null,
    useAiRecommend: boolean,
  ): void;
  (e: 'update:useAiRecommendValue', value: boolean): void;
}>();

// 保存弹窗打开时的初始状态
const initialState = ref<{
  selectedDubbing: Dubbing | null;
  useAiRecommend: boolean;
}>({
  selectedDubbing: null,
  useAiRecommend: false,
});

watch(visible, newVal => {
  emit('input', newVal);
  wrapperVisible.value = newVal;
});
watch(
  () => props.value,
  async newVal => {
    visible.value = newVal;

    // 当弹窗打开时，保存初始状态
    if (newVal) {
      await initSelectedDubbing();
      initialState.value = {
        selectedDubbing: selectedDubbing.value,
        useAiRecommend: useAiRecommend.value,
      };
    }
  },
);

/**
 * 处理点击外部区域关闭弹窗
 */
const handleClickOutside = (event: MouseEvent) => {
  // 检查是否点击在遮罩层上
  const dialogWrapper = (event.target as Element)?.closest(
    '.el-dialog__wrapper',
  );
  const dialogContent = (event.target as Element)?.closest('.el-dialog');

  // 如果点击在遮罩层上但不在对话框内容上，则关闭弹窗
  if (dialogWrapper && !dialogContent) {
    handleCancel();
  }
};

/*************弹窗控制 end***************/

// 是否可以使用AI推荐
const canUseAiRecommend = props.canUseAiRecommend !== false;
// 是否使用AI推荐 - 使用计算属性实现双向绑定
const useAiRecommend = computed({
  get: () => props.useAiRecommendValue ?? false,
  set: value => {
    emit('update:useAiRecommendValue', value);
  },
});

// 处理智能推荐变更
const handleAiRecommendChange = (value: boolean) => {
  if (value) {
    // 清空已选配音
    selectedDubbing.value = null;
    // 暂停播放
    handlePlayDubbing();
  } else {
    // 取消智能推荐时，默认选择第一个配音
    if (dubbingList.value.length > 0) {
      selectedDubbing.value = dubbingList.value[0];
    }
  }
};

/*************搜索和分类筛选 star***************/
/** 分类列表 */
const categories = ref<
  {
    id: number;
    name: string;
  }[]
>([]);
/** 选中的分类 */
const selectedCategory = ref(0);
/** 配音列表 */
const dubbingList = ref<Dubbing[]>([]);
/** 配音列表分页 */
const page = ref(1);
/** 列表total */
const total = ref(0);
/** loading 标识 */
const loading = ref(true);

// 初始化数据
const initData = async () => {
  // 初始化分类和首个分类的配音列表
  const [catErr, catRes] = await getDubbingCategory();
  loading.value = false;
  if (catErr) {
    message.error(catErr.message);
    throw new Error(catErr.message);
  }
  categories.value = catRes.data;
  await handleChangeCategory(categories.value[0].id);

  // 初始化已选配音
  initSelectedDubbing();
};

// 初始化组件
onMounted(() => {
  initData();
});

const scrollContainer = ref<HTMLElement | null>(null);
/** 点击分类筛选 */
const handleChangeCategory = async (id: number) => {
  if (loading.value) return;
  page.value = 1;
  loading.value = true;
  selectedCategory.value = id;
  const [err, res] = await getDubbingList(id, page.value);
  loading.value = false;
  if (err) {
    message.error(err.message);
    throw err;
  }
  handlePlayDubbing();
  scrollContainer.value?.scrollTo(0, 0);
  dubbingList.value = res.data;
  total.value = res.total;
};
/** 加载下一页 */
const handleLoadMore = async () => {
  if (loading.value) return;
  if (dubbingList.value.length >= total.value) return;
  page.value += 1;
  loading.value = true;
  const [err, res] = await getDubbingList(selectedCategory.value, page.value);
  loading.value = false;
  if (err) {
    message.error(err.message);
    throw err;
  }
  dubbingList.value.push(...res.data);
  total.value = res.total;
};
const handleScroll = (event: Event) => {
  const target = event.target as HTMLElement;
  // 判断是否滚动到底部，如果到底部则触发下一页加载
  const isBottom =
    target.scrollHeight - target.scrollTop <= target.clientHeight + 20;
  if (isBottom) {
    handleLoadMore();
  }
};
/*************搜索和分类筛选 end***************/

/*************选择配音和预览配音 star***************/
/** 当前选择的配音 */
const selectedDubbing = ref<Dubbing | null>(null);

// 初始化已选配音
const initSelectedDubbing = async () => {
  if (!props.voiceId) return;

  const [err, res] = await getDubbingInfo(props.voiceId);
  if (err) {
    console.error('Failed to fetch dubbing info:', err);
    return;
  }
  selectedDubbing.value = res.data;
};

/** 选择配音 */
const handleSelectDubbing = (dubbing: Dubbing) => {
  selectedDubbing.value = dubbing;
};

/** 选择配音并处理智能推荐状态 */
const handleSelectDubbingWithAiCheck = (dubbing: Dubbing) => {
  // 如果当前开启了智能推荐，则取消智能推荐
  if (useAiRecommend.value) {
    useAiRecommend.value = false;
  }
  // 选择配音
  handleSelectDubbing(dubbing);
};
/** 当前播放配音 */
const playingDubbing = ref<Dubbing | null>(null);
/** 当前播放URL */
const currentPlayUrl = ref('');
/** 配音播放器 */
const audioPlayer = ref<HTMLAudioElement | null>(null);

/** 播放和暂停配音 */
const handlePlayDubbing = async (dubbing?: Dubbing) => {
  if (!dubbing) {
    playingDubbing.value = null;
    audioPlayer.value?.pause();
    return;
  }

  if (!audioPlayer.value) {
    console.error('音频播放器元素未找到');
    return;
  }

  // 检查链接是否有效
  if (!dubbing.link || dubbing.link.trim() === '') {
    console.error('配音链接为空');
    message.error('配音链接无效');
    return;
  }

  try {
    if (playingDubbing.value?.voiceId === dubbing.voiceId) {
      // 暂停当前播放
      playingDubbing.value = null;
      audioPlayer.value.pause();
      audioPlayer.value.currentTime = 0;
    } else {
      // 播放新音频
      // 先暂停当前播放
      if (playingDubbing.value) {
        audioPlayer.value.pause();
      }

      playingDubbing.value = dubbing;
      currentPlayUrl.value = dubbing.link;

      // 等待音频加载完成后播放
      await new Promise<void>((resolve, reject) => {
        const audio = audioPlayer.value!;

        const onLoadedData = () => {
          audio.removeEventListener('loadeddata', onLoadedData);
          audio.removeEventListener('error', onError);
          resolve();
        };

        const onError = () => {
          audio.removeEventListener('loadeddata', onLoadedData);
          audio.removeEventListener('error', onError);
          reject(new Error('音频加载失败'));
        };

        audio.addEventListener('loadeddata', onLoadedData);
        audio.addEventListener('error', onError);

        // 触发加载
        audio.load();
      });

      await audioPlayer.value.play();
    }
  } catch (error) {
    console.error('音频播放失败:', error);
    message.error('音频播放失败，请检查网络连接');
    playingDubbing.value = null;
  }
};
// 音频事件处理函数
const handleAudioError = (event: Event) => {
  if (currentPlayUrl.value === '') {
    return;
  }
  console.error('音频加载错误:', event);
  playingDubbing.value = null;
  message.error('音频加载失败');
};
// 音频播放结束事件处理函数
const handleAudioEnded = () => {
  // 播放结束后重置播放状态
  playingDubbing.value = null; // 置为未播放状态
};
/*************选择配音和预览配音 end***************/

/*************确认和取消 star***************/
/** 确认 */
const handleConfirm = () => {
  handlePlayDubbing();
  emit('changeDubbingInfo', selectedDubbing.value, useAiRecommend.value);
  emit('input', false);
};
/** 取消 */
const handleCancel = () => {
  handlePlayDubbing();

  // 恢复到弹窗打开时的初始状态
  selectedDubbing.value = initialState.value.selectedDubbing;
  emit('update:useAiRecommendValue', initialState.value.useAiRecommend);

  emit('input', false);
};
/*************确认和取消 end***************/
</script>

<style lang="scss" scoped>
.dubbing-selector {
  display: flex;
  flex-direction: column;
  height: calc(100vh - 80px);
  overflow: hidden;
  .dubbing-item {
    @apply flex p-[16px] rounded-[8px] bg-white b-1 b-divider select-none;

    /* 默认状态：未选中时显示指针 */
    cursor: pointer;

    /* 已选中状态：不显示指针 */
    &.selected {
      cursor: default;
    }
    .dubbing-item-cover {
      @apply w-[48px] h-[48px] rounded-full mr-[16px];
    }
    .play-ctrl-box {
      display: none;
      background-color: rgba(0, 0, 0, 0.5);
      overflow: hidden;
      position: absolute;
      top: 0;
      left: 0;
      width: 48px;
      height: 48px;
      border-radius: 50%;
      justify-content: center;
      align-items: center;
      cursor: pointer;
      .play-ctrl-icon {
        @apply w-[16px] h-[16px] text-white;
      }
    }
    .action-buttons {
      .select-btn {
        @apply hidden;
      }
    }
    &:hover,
    &.playing {
      @apply b-primary;
      .play-ctrl-box {
        @apply flex;
      }
      .action-buttons .select-btn {
        @apply block;
      }
    }
  }
}
.category-item {
  @apply text-[14px] text-subText cursor-pointer mr-[24px];
  &.active {
    @apply font-bold text-title;
  }
  &:hover {
    @apply text-title;
  }
}
::v-deep {
  .el-dialog {
    @apply rounded-[16px] my-[40px]!;
    .el-dialog__header {
      @apply p-0;
    }
    .el-dialog__body {
      @apply p-0;
    }
  }
}
</style>
