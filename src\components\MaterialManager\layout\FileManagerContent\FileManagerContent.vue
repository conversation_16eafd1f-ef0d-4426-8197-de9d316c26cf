<template>
  <FaFileManagerContent
    :loading="loading"
    class="file-manager-demo-content"
    @contextmenu="handleContextmenu"
  >
    <FileManagerEmpty slot="empty" />

    <template v-if="selects.find(item => item)" #menu="{ hideMenu }">
      <FileManagerMenu @click="handleMenuClick($event, hideMenu)" />
    </template>

    <template v-if="category === 'file'">
      <FileManagerViewList v-if="isViewListMode" />
      <FileManagerTable v-else />
    </template>

    <FileManagerRecycleBin v-if="category !== 'file'" />
  </FaFileManagerContent>
</template>

<script>
import { FileManager as FaFileManager } from '@fk/fa-component-cus';
import FileManagerRecycleBin from '@/components/MaterialManager/layout/FileManagerContent/components/FileManagerRecycleBin.vue';
import FileManagerMenu from '@/components/MaterialManager/components/FileManagerMenu.vue';
import FileManagerEmpty from '@/components/MaterialManager/layout/FileManagerContent/components/FileManagerEmpty.vue';
import FileManagerTable from '@/components/MaterialManager/layout/FileManagerContent/components/FileManagerTable.vue';
import FileManagerViewList from '@/components/MaterialManager/layout/FileManagerContent/components/FileManagerViewList.vue';

import store from '@/store';

export default {
  name: 'FileManagerContent',

  components: {
    FileManagerMenu,
    FileManagerEmpty,
    FileManagerTable,
    FileManagerViewList,
    FileManagerRecycleBin,
    FaFileManagerContent: FaFileManager.FileManagerContent,
  },

  data() {
    return {};
  },

  computed: {
    loading() {
      return !store.state.meta.metaLoaded;
    },

    category() {
      return store.state.meta.category;
    },
    isViewListMode() {
      return store.getters['materialUpload/isListView'];
    },
    selects() {
      return store.getters.selects;
    },
  },

  methods: {
    handleMenuClick(key, hideMenu) {
      switch (key) {
        case 'delete':
          store.dispatch('multipleDelete');
          break;
        case 'move':
          store.commit('setMoveModal', true);
          break;
        case 'restore':
          store.commit('restore');
          break;
        default:
      }

      hideMenu();
    },

    handleContextmenu() {},
  },
};
</script>

<style lang="scss" scoped></style>
