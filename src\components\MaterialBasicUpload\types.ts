/**
 * @fileoverview MaterialBasicUpload 组件相关的类型定义
 * @description 定义 MaterialBasicUpload 组件所需的数据结构和接口
 * @since 1.0.0
 */

import { FileData } from '@/types/Material';

/**
 * @description MaterialBasicUpload 组件 onConfirm 回调中 files 数组的元素类型
 * @interface MaterialUploadFile
 * 此结构不能改，因为组件内部会依赖此结构
 */
export interface MaterialUploadFile {
  type?: string; // 'owner' -> 素材库-我的文件tab内选中素材
  data: FileData;
}
