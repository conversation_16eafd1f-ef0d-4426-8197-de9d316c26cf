# 编辑项目视图模块

## 📋 模块概述

编辑项目视图（EditProjectView）是 ADI 项目的核心业务模块，负责项目的创建、编辑和管理功能。该模块支持视频和图文两种项目类型，提供完整的项目编辑流程，包括基础信息填写、资源上传、表单校验、数据保护等核心功能。

## 🎯 核心功能

### 项目编辑流程
- **两步式编辑流程**: 基础信息填写 → 作品生成预览
- **多项目类型支持**: 视频项目、图文项目
- **实时预览**: 支持编辑过程中的实时预览功能

### 表单管理
- **差异化表单校验**: 生成模式完整校验，保存模式仅长度校验
- **必填验证规则**: 动态必填验证，支持条件性校验
- **表单数据保护**: 页面离开前自动保存，防止数据丢失

### 状态控制
- **项目状态访问控制**: 基于项目状态的访问权限管理
- **开发者账号检查**: 开发环境下的账号状态检查跳过
- **保存按钮状态**: 智能保存按钮禁用/启用控制

### 路由管理
- **Vue Router 3 兼容**: 解决路由跳转问题
- **路由状态同步**: 确保路由状态与项目状态一致

## 📁 文档结构

### 功能特性文档
- [开发者账号状态检查跳过功能](./功能特性/开发者账号状态检查跳过功能.md)
- [保存按钮禁用功能](./功能特性/保存按钮禁用功能.md)
- [差异化表单校验功能](./功能特性/差异化表单校验功能.md)
- [页面离开前数据保护功能](./功能特性/页面离开前数据保护功能.md)
- [项目状态访问控制功能](./功能特性/项目状态访问控制功能.md)

### 更新日志
- [必填验证规则重构更新日志](./更新日志/必填验证规则重构更新日志.md)
- [路由跳转问题修复更新日志](./更新日志/路由跳转问题修复更新日志.md)

### 技术文档
- [Vue Router 3 路由跳转解决方案](./技术文档/Vue Router 3 路由跳转解决方案.md)

## 🔧 技术架构

### 核心组件
- **EditProjectView**: 主视图组件，管理整体编辑流程
- **FirstStep**: 第一步组件，处理基础信息和资源上传
- **SecondStepVideo/SecondStepImage**: 第二步组件，处理作品预览和管理

### 关键 Composables
- **useFirstStep**: 第一步逻辑管理
- **useSecondStep**: 第二步逻辑管理
- **useProjectData**: 项目数据状态管理
- **useForm**: 表单处理和校验

### API 接口
- **getEditProjectViewData**: 获取项目编辑数据
- **saveProjectData**: 保存项目数据
- **generatePreview**: 生成预览
- **getWorkListWithTransform**: 获取作品列表

## 🚀 主要特性

### 1. 差异化表单校验
- **生成模式**: 执行完整的表单校验，包括必填项、格式验证等
- **保存模式**: 仅执行最大长度校验，跳过必填项验证
- **动态校验规则**: 根据操作类型动态调整校验策略

### 2. 页面离开前数据保护
- **自动检测**: 监听页面离开事件（刷新、关闭、跳转）
- **数据保存**: 自动保存当前编辑状态
- **用户提示**: 提供友好的确认提示

### 3. 项目状态访问控制
- **状态检查**: 基于项目状态控制页面访问权限
- **权限管理**: 不同状态下的功能权限控制
- **状态同步**: 确保前端状态与后端状态一致

### 4. 开发者功能
- **开发环境优化**: 开发环境下跳过某些检查流程
- **调试支持**: 提供详细的调试信息和日志

## 📊 使用统计

- **支持项目类型**: 2种（视频、图文）
- **表单校验规则**: 10+种
- **状态管理**: 5+种项目状态
- **API接口**: 8+个核心接口

## 🔄 版本历史

### v3.0.0 - 差异化表单校验版本
- ✅ 实现生成和保存的差异化校验策略
- ✅ 优化用户体验，减少不必要的校验阻塞
- ✅ 完善校验错误提示和处理

### v2.0.0 - 数据保护增强版本
- ✅ 新增页面离开前数据保护功能
- ✅ 实现自动保存机制
- ✅ 优化用户操作体验

### v1.0.0 - 基础功能版本
- ✅ 实现基础的项目编辑功能
- ✅ 支持视频和图文项目类型
- ✅ 完成基础的表单校验和数据管理

## 🛠️ 开发指南

### 本地开发
1. 确保项目依赖已安装
2. 启动开发服务器
3. 访问编辑项目页面进行测试

### 功能扩展
1. 新增功能特性需要更新相应的 composables
2. 表单校验规则需要在 useForm 中配置
3. 状态管理需要在 useProjectData 中处理

### 测试建议
1. 测试不同项目类型的编辑流程
2. 验证表单校验的差异化行为
3. 测试页面离开前的数据保护功能

## 🔗 相关模块

- [智能轮询系统](../智能轮询系统/README.md): 作品生成状态监控
- [通用组件](../通用组件/README.md): 表单组件和工具函数
- [API接口](../API接口/README.md): 数据接口规范

---

**最后更新**: 2025-01-10  
**维护团队**: 前端开发团队  
**模块负责人**: EditProjectView 开发组
