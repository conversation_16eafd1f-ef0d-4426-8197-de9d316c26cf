import Vue from 'vue';
import VueRouter, { RouteConfig } from 'vue-router';
import Cookies from 'js-cookie';
import store from '@/store';
import { starGlobalPreload } from '@/utils/globalPreload';
import { createRouterErrorHandler } from '@/utils/chunkErrorHandler';

// 直接导入需要避免懒加载的组件，解决缓存问题
import MainView from '@/views/MainView.vue';
import TemplateView from '@/views/MainContainer/TemplateView.vue';
// import ProjectView from '@/views/MainContainer/ProjectView.vue';
// import WorkView from '@/views/MainContainer/WorkView.vue';
// import EditProjectView from '@/views/EditProjectView/index.vue';

const isDev = import.meta.env.DEV;

// 注册 VueRouter 插件
Vue.use(VueRouter);

// 定义路由配置
const routes: RouteConfig[] = [
  {
    path: '/',
    component: MainView,
    children: [
      {
        path: '',
        name: 'template',
        component: TemplateView,
      },
      {
        path: 'meta',
        name: 'meta',
        component: () => import('@/views/MainContainer/MetaView.vue'),
      },
      {
        path: 'acct-info',
        name: 'AcctInfo',
        component: () => import('@/views/MainContainer/AcctInfoView.vue'),
        props: (route: { query: { tab?: string } }) => ({
          tab: route.query.tab || '', // 默认显示企业信息
        }),
      },
      {
        path: 'project',
        name: 'Project',
        component: () => import('@/views/MainContainer/ProjectView.vue'),
      },
      {
        path: 'work',
        name: 'Work',
        component: () => import('@/views/MainContainer/WorkView.vue'),
        props: (route: { query: { projectId?: string } }) => ({
          projectId: Number(route.query.projectId) || undefined,
        }),
      },
      {
        path: 'prototype',
        name: 'prototype',
        component: () => import('@/views/MainContainer/PrototypeView.vue'),
      },
    ],
  },
  {
    path: '/image-project',
    name: 'imageProject',
    component: () => import('@/views/EditProjectView/index.vue'),
    props: (route: {
      query: {
        step?: string;
        projectId?: string;
        templateId: string;
        from?: string;
      };
    }) => ({
      step:
        route.query.step !== undefined ? Number(route.query.step) : undefined,
      // 必传，模板ID
      templateId: Number(route.query.templateId) || undefined,
      // 已创建的项目必传，项目ID
      projectId: Number(route.query.projectId) || undefined,
      // 来源页面标识
      from: route.query.from || undefined,
    }),
  },
  {
    path: '/video-project',
    name: 'videoProject',
    component: () => import('@/views/EditProjectView/index.vue'),
    props: (route: {
      query: {
        step?: string;
        projectId?: string;
        templateId: string;
        from?: string;
      };
    }) => ({
      step:
        route.query.step !== undefined ? Number(route.query.step) : undefined,
      // 必传，模板ID
      templateId: Number(route.query.templateId) || undefined,
      // 已创建的项目必传，项目ID
      projectId: Number(route.query.projectId) || undefined,
      // 来源页面标识
      from: route.query.from || undefined,
    }),
  },
  {
    path: '/adm-set',
    name: 'admSet',
    component: () => import('@/views/AdmSetView/index.vue'),
  },
  {
    path: '/no-permission',
    name: 'noPermission',
    component: () => import('@/views/NoPermission/index.vue'),
  },
];

if (isDev) {
  routes.push({
    path: '/dev-login',
    name: 'devLogin',
    component: () => import('@/views/DevLoginView.vue'),
  });
}

// 创建路由实例
const router = new VueRouter({
  mode: 'hash',
  routes,
});

/**
 * 路由守卫
 */
router.beforeEach(async (to, _unused, next) => {
  // 开发环境登录页绿灯过
  if (isDev && to.path === '/dev-login') {
    return next();
  }

  if (!store.state.system.isSystemDone) {
    await store.dispatch('getSystem');
  }
  // 判断是否有 _TOKEN，没有就跳转到登录页
  const token = Cookies.get('_TOKEN');
  if (!token) {
    if (isDev) {
      // 开发环境登录页
      return next({ path: '/dev-login' });
    } else {
      // 生产环境登录页
      window.location.href = '//' + store.state.system.portal;
    }
  }

  if (!store.state.system.isPreload) {
    // 这里获取全局前置信息没写await，是为了快点进入业务页面，显示业务页面的骨架屏。
    // 业务页面可以通过store.state.system.isPreload来判断全局前置信息是否加载完成。
    // 然后再结合业务本身的前置加载逻辑判断是否显示骨架屏。
    starGlobalPreload(to.path);
  } else {
    // 检查内部用户权限
    if (to.path === '/adm-set') {
      if (!store.state.user.isStaff) {
        // 非内部用户重定向到首页
        next({ path: '/' });
      }
    }
  }
  return next();
});

/**
 * 处理懒加载路由的chunk加载失败问题
 * 当检测到chunk加载失败时，自动刷新页面获取最新资源
 */
router.onError(
  createRouterErrorHandler({
    reloadInterval: 15000, // 15秒内不重复刷新
    showUserNotification: false, // 显示通知
  }),
);

// 导出路由实例
export default router;
