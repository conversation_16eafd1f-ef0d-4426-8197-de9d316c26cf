<template>
  <fa-image-viewer
    v-if="visible"
    :src-list="imgList"
    :on-close="closeViewer"
    :infinite="false"
  />
</template>

<script setup lang="ts">
import { defineProps, defineEmits } from 'vue';

defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  imgList: {
    type: Array as () => string[],
    default: () => [],
  },
});
const emit = defineEmits(['close']);

function closeViewer() {
  emit('close');
}
</script>

<style lang="scss"></style>
