<template>
  <FaFileManagerViewList
    class="file-manager-demo--view-list"
    copy-class="fa-file-manager--view-item--content"
    select-class="fa-file-manager--view-item--control-view"
    :select-area="{ top: 0, right: 0, bottom: 5, left: 0 }"
    :gap="{ w: 104, x: 32, left: 30, right: 30 }"
    multiple
    not-ctrl
    @change="handleChange"
    @move-in="handleMoveIn"
    @select-end="handleSelectEnd"
    @mousedown.native.prevent="handleMousedown"
  >
    <template #default="{ info, index }">
      <FaFileManagerViewItem
        :class="[
          `file-manager-demo--view-item-${isFolder(info) ? 'folder' : 'file'}`,
        ]"
        :not-copy="false"
        :is-select="!!selects[index]"
        @dblclick.native="handleDblclick(info, index)"
        @contextmenu.native="blur"
      >
        <!-- 文件夹等格式 -->
        <div
          v-if="checkOwnerFilePresetPreview(info)"
          class="file-manager-demo-content-icon flex ai-center jc-center"
        >
          <FileManagerNewIcon :info="info" />
        </div>
        <!-- 视频 -->
        <div v-else-if="checkVideoCover(info)">
          <CoverVideo
            :file="info"
            :maxWidth="96"
            :imgStyle="{ width: '96px', height: '96px', maxWidth: '96px' }"
          />
        </div>
        <div v-else class="file-manager-demo-content-img">
          <!-- 图片 -->
          <div
            :style="{
              'background-image': `url(${getImgUrl(info)})`,
            }"
          />
        </div>

        <div v-if="checkFileUploading(info)">
          <ProgressPending :percent="info.percent" />
        </div>

        <template #extra-control="{ visible }">
          <ImgPreviewToolbar
            v-if="checkOwnerFileImgType(info)"
            v-show="visible"
            :file="info"
          />
          <VideoPlayToolbar
            v-if="checkVideoType(info)"
            :file="info"
            :btnSize="20"
          />
        </template>

        <FileManagerMenu slot="overlay" @click="handleClick($event, info)" />
      </FaFileManagerViewItem>

      <div class="file-manager-demo-content-input notDraggableSelect">
        <FaFileManagerInput
          :ref="`input${info[INFO_KEYS.ID]}`"
          :disabled="checkFileUploading(info)"
          type="div"
          align="center"
          :line="2"
          :value="info[INFO_KEYS.NAME]"
          :maxlength="isFolder(info) ? folderNameMaxLen : inputNameMaxLen"
          @focus="handleFocus(info[INFO_KEYS.ID])"
          @blur="inputBlur(info, $event.value)"
        />
      </div>
    </template>
  </FaFileManagerViewList>
</template>

<script>
import {
  FileManager as FaFileManager,
  DesignerIcon as FaDesignerIcon,
} from '@fk/fa-component-cus';
import FileManagerMenu from '@/components/MaterialManager/components/FileManagerMenu.vue';
import {
  checkOwnerFilePresetPreview,
  getSuffix,
  getImgUrl,
  changeFmName,
} from '@/components/MaterialManager/utils/index.ts';
import store from '@/store';
import {
  INFO_KEYS,
  IMG_PREVIEW_KEY_LIST,
  INPUT_NAME_MAX_LENGTH,
  FOLDER_NAME_MAX_LENGTH,
} from '@/constants/material';
import CoverVideo from '@/components/MaterialBasicUpload/components/CoverVideo.vue';
import VideoPlayToolbar from '@/components/MaterialBasicUpload/components/VideoPlayToolbar.vue';
import ImgPreviewToolbar from '@/components/MaterialBasicUpload/components/ImgPreviewToolbar.vue';
import {
  checkVideoType,
  checkVideoCover,
  checkFileUploading,
} from '@/components/MaterialBasicUpload/utils/index.ts';
import { isFolder } from '@/utils/resource';
import FileManagerNewIcon from '@/components/MaterialManager/components/FileManagerNewIcon.vue';
import ProgressPending from '@/components/MaterialBasicUpload/components/ProgressPending.vue';

export default {
  name: 'FileManagerViewList',

  components: {
    FaDesignerIcon,
    FileManagerMenu,
    FaFileManagerViewList: FaFileManager.FileManagerViewList,
    FaFileManagerViewItem: FaFileManager.FileManagerViewItem,
    FaFileManagerInput: FaFileManager.FileManagerInput,
    CoverVideo,
    VideoPlayToolbar,
    ImgPreviewToolbar,
    FileManagerNewIcon,
    ProgressPending,
  },

  data() {
    return {
      INFO_KEYS,
      focusId: '',
      inputNameMaxLen: INPUT_NAME_MAX_LENGTH,
      folderNameMaxLen: FOLDER_NAME_MAX_LENGTH,
    };
  },

  computed: {
    folderAddInfo() {
      return store.state.meta.folderAddInfo;
    },
    selects() {
      return store.getters.selects;
    },
  },

  watch: {
    folderAddInfo: {
      handler(info) {
        this.$nextTick(() => {
          const { id, form } = info;
          if (form !== 'all' && form !== 'viewList') return;
          this.$refs[`input${id}`] && this.$refs[`input${id}`].focus();
        });
      },
      deep: true,
    },
  },

  methods: {
    isFolder,
    checkOwnerFilePresetPreview,
    getSuffix,
    getImgUrl,
    checkFileUploading,

    handleChange(info) {
      this.blur();
      typeof info.index !== 'undefined' &&
        store.commit('changeSelectByIndex', info.index);
    },

    async handleMoveIn(info) {
      await store.dispatch('moveIn', info[INFO_KEYS.ID]);
    },

    handleDblclick(info) {
      if (!isFolder(info)) return;

      store.commit('changeDir', info[INFO_KEYS.ID]);
      store.dispatch('updateFolderContent');
      this.blur();
    },

    handleClick(key, info) {
      switch (key) {
        case 'delete':
          store.dispatch('deleteFile', info);
          break;
        case 'move':
          store.commit('setMoveOne', true);
          store.commit('setMoveOneData', info);
          store.commit('setMoveModal', true);
          break;
        default:
      }
    },

    handleFocus(id) {
      this.focusId = id;
    },

    blur() {
      this.$refs[`input${this.focusId}`] &&
        this.$refs[`input${this.focusId}`].getInput().blur();
    },

    handleMousedown() {},

    handleSelectEnd(info) {
      const selects = info.selects;
      store.getters.sortedFmInfoList.forEach((info, index) => {
        this.$set(info, INFO_KEYS.SELECT, selects[index]);
      });
    },

    async inputBlur(info, newName) {
      await changeFmName(info, newName);
    },

    checkVideoType,
    checkVideoCover,

    // 检测图片是否可以预览 - 图片
    checkOwnerFileImgType(data) {
      return IMG_PREVIEW_KEY_LIST.includes(data.fileType);
    },
  },
};
</script>

<style lang="scss" scoped>
.file-manager-demo--view {
  &-list {
    .file-manager-demo-content {
      &-input {
        position: relative;
        width: 104px;
        height: 46px;
        margin-top: 4px;

        .fa-file-manager-input {
          position: absolute;

          ::v-deep {
            .focus {
              background-color: #fff;
            }
          }
        }
      }
    }

    ::v-deep {
      .selector-panel--container-item[line='1'] {
        margin-top: 10px;
      }

      // .selector-panel--container-item[last-line] {}
    }
  }

  &-item {
    &-folder {
      ::v-deep {
        .fa-file-manager--view-item--content-bg {
          border-color: transparent;
          &.select,
          .file-manager-demo--view-item-folder
            .fa-file-manager--view-item--content-bg {
            border-color: #3a84fe;
          }
        }
        &:hover {
          .fa-file-manager--view-item--content-bg {
            border-color: #3a84fe;
          }
        }
      }
    }
  }
}
</style>

<style lang="scss">
.file-manager-demo-content {
  &-icon {
    height: 100%;
    font-size: 60px;
  }

  &-img {
    height: 100%;
    padding: 5px;
    box-sizing: border-box;

    > div {
      height: 100%;
      background-size: contain;
      background-position: center;
      background-repeat: no-repeat;
    }
  }
  .fa-file-manager--view-item--control-view .fa-btn.fa-btn-icon-only.fa-btn-sm {
    @apply w-[24px] h-[24px];
  }
}
.fa-file-manager--view-item--content {
  .fa-file-manager--view-item--content-bg {
    border-radius: 8px;
  }
}
.file-manager-demo--view-item-load {
  @apply absolute size-full left-0 top-0 bg-black/70 text-white text-[13px] rounded-[4px];
  .fa-progress {
    @apply w-[80%];
  }
}
</style>
