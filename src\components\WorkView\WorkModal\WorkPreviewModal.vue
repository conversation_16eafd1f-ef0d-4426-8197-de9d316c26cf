<template>
  <WorkBaseModal :isShow.sync="isShow" :width="900" @cancel="handleClose">
    <div
      v-if="workData"
      class="flex"
      :style="{ height: isVideo || isGenerating ? '684px' : '696px' }"
    >
      <div class="preview-left">
        <div>
          <div class="preview-title">预览{{ workTypeName }}</div>
          <div
            class="relative preview-wrapper"
            :style="{ height: isVideo || isGenerating ? '576px' : '588px' }"
          >
            <!-- 生成中 -->
            <LoadingPreview
              v-if="isGenerating"
              class="preview-content"
              :imgSrc="videoData.videoCover?.id"
              :percent="workData?.progress"
              :loadingText="loadingText"
            />
            <!-- 生成失败 -->
            <FailedState
              v-else-if="showFailedState"
              class="w-[324px] h-[576px]"
              :errTitle="`重新生成作品失败${
                isVideo ? '，创作点数已自动返还' : ''
              }`"
              :errMsg="failedErrorMessage"
              :showRetryButton="isVideo"
              @retry="handleFailedStateRestore"
            />
            <!-- 生成成功：视频/图片预览 -->
            <template v-else>
              <template v-if="isVideo">
                <VideoWorkPreview
                  ref="mainVideoRef"
                  class="preview-content"
                  :src="videoData.video?.id"
                  :type="videoData.video?.type"
                  :coverId="videoData.videoCover?.id"
                />
                <div class="preview-video-title">
                  <div
                    class="m-[12px_16px_0] h-[19px] lh-[19px] text-[14px] text-white overflow-hidden whitespace-nowrap text-ellipsis"
                  >
                    {{ workData?.name }}
                  </div>
                </div>
              </template>
              <ImageWorkPreview
                v-else
                class="preview-content"
                :title="workData?.title || ''"
                :content="workData?.content || ''"
                :imgList="imgListCal || []"
                carouselMaxHeight="432px"
              />
            </template>
          </div>
        </div>
      </div>
      <div class="preview-right">
        <div class="w-full">
          <div class="preview-title">发布{{ workTypeName }}</div>
          <MaterialBox
            type="text"
            layout="header-content"
            title="标题"
            class="mb-[16px]"
            :content="workData?.title"
            :copy-text="workData?.title"
          />
        </div>
        <MaterialBox
          type="text"
          layout="header-content"
          title="作品描述"
          class="material-box--content-box overflow-hidden flex-1"
          :content="workData?.content"
          :copy-text="workData?.content"
        />
        <!-- 发布平台 -->
        <div class="mt-[32px]">
          <div class="preview-title">发布至</div>
          <div class="flex gap-[12px]">
            <a
              v-for="platform in platforms"
              :key="platform.id"
              :class="platform.id"
              class="platform-icon"
              :title="platform.name"
              target="_blank"
              :href="platform.href"
            ></a>
          </div>
          <!-- 底部按钮 -->
          <div class="footer-btn-group">
            <fa-popover
              placement="top"
              trigger="hover"
              overlayClassName="qr-code-box-popover"
            >
              <template #content>
                <div class="qrCodeBoxHover">
                  <img :src="videoData.videoCover?.id" />
                  <div class="qrCodeBoxTip">扫码下载至手机</div>
                </div>
              </template>
              <fa-button
                type="primary"
                :loading="isDownLoading"
                :class="{ disabled: disabledDownload }"
                @click="handleDownload"
              >
                下载{{ isVideo ? '视频' : '图片' }}
              </fa-button>
            </fa-popover>
            <fa-button @click="handleEdit" :class="{ disabled: disabledEdit }"
              >编辑{{ workTypeName }}</fa-button
            >
          </div>
        </div>
      </div>
    </div>
  </WorkBaseModal>
</template>

<script setup lang="ts">
import { defineProps, defineEmits, ref, computed, watch } from 'vue';
import VideoWorkPreview from '@/components/TemplateView/VideoWorkPreview.vue';
import ImageWorkPreview from '@/components/TemplateView/ImageWorkPreview.vue';
import LoadingPreview from './LoadingPreview.vue';
import WorkBaseModal from './WorkBaseModal.vue';
import MaterialBox from '@/views/EditProjectView/pages/SecondStepVideo/components/ContentPanel/components/MaterialBox.vue';
import FailedState from '@/views/EditProjectView/pages/SecondStepVideo/components/VideoPlayer/components/FailedState.vue';
import {
  getAuthenticationUrl,
  downloadFile,
  getValidResourceToken,
  isUnrelatedProject,
} from '../utils';
import { FILE_TYPES } from '@/constants/fileType';
import { message } from '@fk/faicomponent';
import { ImageWorkItem } from '@/types/Work';
import { WORK_STATUS_TIPS } from '../constants';
import { useWorkModal } from '../hook/useWorkModal';
import { PROJECT_TYPE, PROJECT_TYPE_NAME } from '@/constants/project';
import {
  getWorkStatusInfo,
  isAnyFailedWork,
  isRecompletedWork,
} from '@/constants/workStatus';
import { debounce } from 'lodash-es';

// 定义组件属性
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  workId: {
    type: Number || undefined,
  },
});

// 定义组件事件
const emit = defineEmits([
  'update:visible',
  'edit',
  'retry',
  'save-new',
  'save-all',
  'showSelectModal',
]);

const isDownLoading = ref(false); // 是否点击了下载按钮，下载中状态，禁用按钮

/** 作品类型名称 */
const workTypeName = computed(
  () => PROJECT_TYPE_NAME[workData.value?.type as PROJECT_TYPE],
);

// 替换原有公共逻辑
const {
  isShow,
  workData,
  mainVideoRef,
  handleClose,
  isVideo,
  videoData,
  isGenerating,
  getWorkDetail,
} = useWorkModal(
  props,
  emit as unknown as (event: string, ...args: unknown[]) => void,
);

// 平台数据
const platforms = ref([
  { id: 'tiktok', name: '抖音', href: 'https://www.douyin.com/' },
  { id: 'xiaohongshu', name: '小红书', href: 'https://www.xiaohongshu.com/' },
  { id: 'kwai', name: '快手', href: 'https://www.kuaishou.com/' },
]);

// 计算属性

/** 加载文本 */
const loadingText = computed(() => {
  if (isVideo.value) {
    return ['视频重新生成中', '请稍后预览'];
  } else {
    return ['图片生成中', '请稍后预览'];
  }
});

/**
 * 是否显示失败状态
 */
const showFailedState = computed(() => {
  if (!workData.value) return false;
  const statusInfo = getWorkStatusInfo(workData.value);
  return isAnyFailedWork(statusInfo);
});

/** 失败错误信息 */
const failedErrorMessage = computed(() => {
  return workData.value?.data?.errMsg || '生成失败';
});

/** 是否禁用编辑按钮 */
const disabledEdit = computed(() => {
  // 已经未关联项目，或者状态为生成中，或者生成状态为失败，则禁用编辑按钮
  return (
    isUnrelatedProject(workData.value?.projectId || 0) ||
    isGenerating.value ||
    showFailedState.value
  );
});

/** 是否禁用下载按钮 */
const disabledDownload = computed(() => {
  // 状态为生成中，或者生成状态为失败，则禁用下载按钮
  return isGenerating.value || showFailedState.value;
});

/** 图文列表 */
const imgListCal = computed(() => {
  const imageWork = workData.value as ImageWorkItem;
  const graphic = imageWork.data?.graphic || [];
  return graphic.map(item => ({
    resId: item.resId,
    resType: item.type,
  }));
});

// 方法定义

/** 下载作品 */
const handleDownload = async () => {
  // 生成中不支持下载
  if (isGenerating.value) {
    return message.error(workTypeName.value + WORK_STATUS_TIPS.REGENERATING);
  }

  if (disabledDownload.value) return;

  if (isDownLoading.value) return;
  isDownLoading.value = true;

  const token = await getValidResourceToken();

  if (isVideo.value) {
    const video = videoData.value.video;
    if (!video || !video.id || !video.type) {
      message.error('视频数据不完整');
      isDownLoading.value = false;
      return;
    }
    const { id, type } = video;
    const downloadPath = getAuthenticationUrl(id, type, token);
    downloadFile(downloadPath, `${workData.value?.name || '未命名作品'}.mp4`)
      .then(() => {
        setTimeout(() => {
          message.success('下载成功');
        });
      })
      .finally(() => {
        isDownLoading.value = false;
      });
  } else {
    const imgList = imgListCal.value || [];
    if (imgList.length === 0) {
      message.error('图片数据不完整');
      isDownLoading.value = false;
      return;
    }

    await Promise.all(
      imgList.map(
        async (
          item: {
            resId: string;
            resType: FILE_TYPES;
          },
          idx: number,
        ) => {
          const downloadPath = getAuthenticationUrl(
            item.resId,
            item.resType,
            token,
          );
          return downloadFile(
            downloadPath,
            `${workData.value?.name || '未命名作品'}_${String(idx + 1).padStart(
              3,
              '0',
            )}.png`,
          );
        },
      ),
    )
      .then(() => {
        setTimeout(() => {
          message.success('下载成功');
        });
      })
      .finally(() => {
        isDownLoading.value = false;
      });
  }
};

/** 编辑作品 */
const handleEdit = () => {
  // 生成中不支持编辑
  if (isGenerating.value) {
    return message.error(workTypeName.value + WORK_STATUS_TIPS.REGENERATING);
  }
  if (disabledEdit.value) return;
  emit('edit', workData.value);
  handleClose();
};

// 恢复旧作品
const handleFailedStateRestore = () => {
  emit('retry', workData.value);
};

const setupVisibilityListener = () => {
  const handler = debounce(async () => {
    if (document.visibilityState === 'visible') {
      await getWorkDetail();
      // 刷新时如果变成重新生成完成状态，则需要切换显示新旧选择弹窗
      if (
        isVideo.value &&
        isRecompletedWork(getWorkStatusInfo(workData.value || { status: 0 }))
      ) {
        handleClose();
        emit('showSelectModal');
      }
    }
  }, 100);

  document.addEventListener('visibilitychange', handler);

  // 返回清理函数
  return () => document.removeEventListener('visibilitychange', handler);
};

let cleanup: (() => void) | null = null;
watch(
  () => props.visible,
  (val: boolean) => {
    if (val) {
      cleanup = setupVisibilityListener();
    } else {
      cleanup?.();
    }
  },
  {
    immediate: true,
  },
);
</script>

<style lang="scss" scoped>
// icon 背景图片混入
@mixin icon-background($platform) {
  background: url('@/assets/WorkView/#{$platform}.svg') no-repeat center;
  background-size: 100% 100%;
}
.preview-left {
  @apply flex-1 flex flex-col h-full items-center justify-center;
  .preview-wrapper {
    @apply overflow-hidden rounded-[12px];
    .preview-content {
      @apply max-w-[324px] w-[324px] h-full bg-black rounded-[12px];
    }
  }
  .preview-video-title {
    @apply absolute top-[0] left-[0];
    @apply w-full h-[48px] text-center;
    background: linear-gradient(
      180deg,
      rgba(0, 0, 0, 0.8) 0%,
      rgba(0, 0, 0, 0) 100%
    );
  }
  :deep(.failed-state__title) {
    @apply text-[14px];
  }
}

.preview-right {
  @apply flex flex-col items-center justify-center w-[450px] p-[40px];
  @apply bg-white rounded-[16px];
  .platform-icon {
    @apply size-[32px] rounded-full hover:opacity-70;

    &.tiktok {
      @include icon-background(tiktok);
    }
    &.xiaohongshu {
      @include icon-background(xiaohongshu);
    }
    &.kwai {
      @include icon-background(kwai);
    }
  }
  .footer-btn-group {
    @apply flex gap-[16px] mt-[32px];
    :deep(.fa-btn) {
      @apply w-[177px];
      &.fa-btn-primary-disabled,
      &.fa-btn-primary.disabled,
      &.fa-btn-primary-disabled:hover,
      &.fa-btn-primary.disabled:hover {
        @apply bg-primary border-primary text-white opacity-40;
      }
    }
  }
}

.material-box--content-box {
  // 处理作品描述在窗口显示不全问题
  :deep(.material-box--layout-header-content__body) {
    @apply mb-[20px];
  }
}

:deep(.material-box--layout-header-content__scroll-area) {
  @apply h-full;
  .fa-scrollarea__wrap {
    max-height: fit-content !important;
  }
}
</style>
<style lang="scss">
.qr-code-box-popover {
  .fa-popover-inner-content {
    padding-bottom: 12px;
    .qrCodeBoxHover {
      img {
        width: 160px;
        height: 160px;
      }
      .qrCodeBoxTip {
        margin-top: 8px;
        line-height: 21px;
        font-size: 14px;
        font-weight: 400;
        color: #333;
        text-align: center;
      }
    }
  }
}
</style>
