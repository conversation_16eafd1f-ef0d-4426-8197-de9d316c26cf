<template>
  <fa-alert banner class="file-manager-demo-alert">
    <div slot="message" class="flex ai-center jc-space-between">
      <div>
        文件进入回收站后保留30天，30天后将自动清理（彻底删除）、无法还原；超级管理员可提前执行“彻底删除”操作。
      </div>
      <div class="deleteLog" @click="handleDeleteLog">回收站日志</div>
    </div>
  </fa-alert>
</template>

<script>
import { Alert as FaAlert } from '@fk/faicomponent';

export default {
  name: 'FileManagerDeleteLog',
  components: {
    FaAlert,
  },

  props: {
    value: {
      type: Boolean,
      default: false,
    },
  },

  data() {
    return {};
  },

  computed: {},

  methods: {
    handleDeleteLog() {
      this.$emit('handleDeleteLog');
    },
  },
};
</script>

<style lang="scss" scoped></style>
