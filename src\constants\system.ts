import store from '@/store';

/** 外部URL */
export enum EXTERNAL_URL {
  /** 充值入口网址 */
  RECHARGE = 'https://www.fkw.com/',
}

/** 动态URL类型 */
export interface DynamicURLs {
  /** 企业中心-成员管理 */
  MEMBER_MANAGE: string;
  /** 版本购买 */
  VERSION_BUY: string;
}

/**
 * 返回依赖 store 的外部 URL 配置
 */
export function getExternalDynamicUrl(): DynamicURLs {
  const portalDomain = store.state.system.portal;
  return {
    /** 企业中心-成员管理 */
    MEMBER_MANAGE: `//${portalDomain}/portal.jsp#appId=corpStaff`,
    /** 版本购买 */
    VERSION_BUY: `//${portalDomain}/marketing/sc.jsp`,
  };
}
