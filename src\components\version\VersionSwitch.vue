<template>
  <div class="version-switch">
    <fa-switch
      :checked="localValue"
      @change="handleChange"
      @click="handleSwitchClick"
    >
      <template slot="checkedChildren"
        ><div class="version-switch-content">
          <span class="version-switch-content-text">{{ switchText }}</span>
          <!-- 版本标识图标 -->
          <VersionIcon
            v-if="showVersionIcon && config"
            ref="versionIconRef"
            :version="config.requiredVersion"
            :required-version="config.requiredVersion"
            type="small"
            :custom-tooltip-message="tooltipMessage"
            :getPopupContainer="getVersionSwitchContainer"
            class="version-switch__icon"
          /></div
      ></template>
      <template slot="unCheckedChildren"
        ><div class="version-switch-content">
          <span class="version-switch-content-text">{{ switchText }}</span>
          <!-- 版本标识图标 -->
          <VersionIcon
            v-if="showVersionIcon && config"
            ref="versionIconRef"
            :version="config.requiredVersion"
            :required-version="config.requiredVersion"
            type="small"
            :custom-tooltip-message="tooltipMessage"
            :getPopupContainer="getVersionSwitchContainer"
            class="version-switch__icon"
          /></div
      ></template>
    </fa-switch>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, watch, onUnmounted } from 'vue';
import VersionController from '@/utils/versionControl';
import VersionIcon from '@/components/comm/ScVersionIcon.vue';
import { VERSION_NAME } from '@/constants/version';

/**
 * 版本控制开关组件
 * @description 集成版本校验的开关组件，自动处理权限检查和升级提示
 */

const props = defineProps({
  /** 功能标识 */
  featureKey: {
    type: String,
    required: true,
  },
  /** 开关状态 */
  value: {
    type: Boolean,
    default: false,
  },
  /** 开关显示文本 */
  label: {
    type: String,
    default: '',
  },
  /** 是否显示版本图标 */
  showIcon: {
    type: Boolean,
    default: true,
  },
});

const emit = defineEmits(['input', 'permission-denied', 'change']);

// 版本图标引用
const versionIconRef = ref();

// 全局点击监听器引用，用于清理
let globalClickListener: ((event: MouseEvent) => void) | null = null;

// 获取功能配置
const config = computed(() => {
  const featureConfig = VersionController.getFeatureConfig(props.featureKey);
  if (!featureConfig) {
    console.warn(`[VersionSwitch] 未找到功能配置: ${props.featureKey}`);
    return null;
  }
  return featureConfig;
});

// 检查用户权限
const hasPermission = computed(() => {
  if (!config.value) return true;
  return VersionController.hasPermission(props.featureKey);
});

// 本地开关状态 - Vue 2.7 v-model 实现
const localValue = computed({
  get: (): boolean => {
    return props.value;
  },
  set: (value: boolean): void => {
    // 发射 input 事件（Vue 2.7 v-model）
    emit('input', value);
  },
});

// 开关显示文本
const switchText = computed(() => {
  return props.label || config.value?.featureName || '';
});

// 是否显示版本图标
const showVersionIcon = computed(() => {
  return props.showIcon && config.value;
});

// Tooltip提示消息
const tooltipMessage = computed(() => {
  if (!config.value || hasPermission.value) return '';
  const requiredVersionName = VERSION_NAME[config.value.requiredVersion];
  return `开启该功能需要升级至${requiredVersionName}`;
});

/**
 * 统一的状态更新函数
 * @param value 新的开关状态
 * @param reason 状态变更原因（用于日志记录）
 */
const updateSwitchState = (value: boolean, reason = 'user-action'): void => {
  // 更新开关状态 - 调用 localValue.value 触发计算属性的 setter
  localValue.value = value;

  // 发射 change 事件，供父组件监听
  emit('change', value);

  // 记录状态变更日志
  if (reason === 'auto-downgrade') {
    console.log(
      `[VersionSwitch] 版本权限不足，自动关闭功能: ${props.featureKey}`,
    );
  }
};

/**
 * 处理开关状态变化
 * @param value 新的开关状态
 */
const handleChange = (value: boolean): void => {
  // 如果没有权限且尝试开启功能，阻止操作并显示提示
  if (value && !hasPermission.value) {
    // 触发tooltip显示
    handleSwitchClick();
    // 发射权限拒绝事件
    emit('permission-denied', props.featureKey);
    return;
  }

  // 使用统一的状态更新函数
  updateSwitchState(value, 'user-action');
};

/**
 * 清理全局点击监听器
 */
const cleanupGlobalListener = (): void => {
  if (globalClickListener) {
    document.removeEventListener('click', globalClickListener, true);
    globalClickListener = null;
  }
};

/**
 * 处理开关点击事件
 * @description 当用户点击被禁用的开关时，触发版本图标的tooltip显示
 */
const handleSwitchClick = (): void => {
  // 只有在没有权限时才触发tooltip
  if (!hasPermission.value && versionIconRef.value) {
    // 清理之前的监听器
    cleanupGlobalListener();

    // 触发版本图标的mouseenter事件来显示tooltip
    const versionIconElement = versionIconRef.value.$el || versionIconRef.value;

    if (versionIconElement) {
      const mouseEnterEvent = new MouseEvent('mouseenter', {
        bubbles: true,
        cancelable: true,
      });
      versionIconElement.dispatchEvent(mouseEnterEvent);

      // 创建新的全局点击监听器来隐藏tooltip
      globalClickListener = (event: MouseEvent) => {
        const target = event.target as Element;
        // 如果点击的不是tooltip相关元素，则隐藏tooltip
        if (
          !target.closest('.fa-tooltip') &&
          !target.closest('.version-switch')
        ) {
          const mouseLeaveEvent = new MouseEvent('mouseleave', {
            bubbles: true,
            cancelable: true,
          });
          versionIconElement.dispatchEvent(mouseLeaveEvent);

          // 清理监听器
          cleanupGlobalListener();
        }
      };

      // 延迟添加监听器，避免立即触发
      setTimeout(() => {
        if (globalClickListener) {
          document.addEventListener('click', globalClickListener, true);
        }
      }, 100);
    }
  }
};

// 监听用户版本变化，自动执行降级
watch(
  () => hasPermission.value,
  newPermission => {
    // 如果权限从有变无，且当前开关是开启状态，自动关闭
    if (!newPermission && props.value) {
      // 使用统一的状态更新函数，确保 input 和 change 事件都被正确发射
      updateSwitchState(false, 'auto-downgrade');
    }
  },
);

// 获取版本开关容器，用于tooltip定位
const getVersionSwitchContainer = (_triggerNode: HTMLElement): HTMLElement => {
  return (
    document.querySelector('.dynamic-form-item__upload-wrapper') ||
    document.body
  );
};

// 组件卸载时清理全局监听器
onUnmounted(() => {
  cleanupGlobalListener();
});
</script>

<style lang="scss">
.version-switch {
  /* 布局相关 */
  @apply flex items-center;

  .fa-switch {
    /* 尺寸相关 */
    @apply h-24px;
    &::after {
      top: 2.15px;
    }
  }

  .version-switch-content {
    /* 布局相关 */
    @apply flex items-center;

    .version-switch-content-text {
      /* 文字相关 */
      @apply lh-17px;
    }
  }

  .version-switch__icon {
    /* 布局相关 */
    @apply ml-4px;
  }
}
</style>
