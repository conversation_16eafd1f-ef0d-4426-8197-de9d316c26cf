<template>
  <div class="absolute z-1000">
    <el-drawer
      :visible.sync="visible"
      :with-header="false"
      :size="1190"
      :wrapperClosable="false"
      @closed="handleClosed"
      :modal-append-to-body="false"
    >
      <div class="h-full flex flex-col" v-click-outside="handleClickOutside">
        <div class="h-[65px] b-divider b-b-[1px] flex items-center pl-[30px]">
          <Icon
            type="guanbi-tancong"
            class="w-[12px] h-[12px] text-disabledText"
            cursor-pointer
            hover:text-title
            @click="visible = false"
          />
          <span class="ml-[16px] font-bold text-black">编辑视频</span>
        </div>
        <fa-tabs
          mode="horizontal"
          :defaultActiveKey="defaultTab"
          class="flex-1"
          v-show="!loading"
        >
          <fa-tab-pane tab="脚本编辑" key="script">
            <ScriptEditor />
          </fa-tab-pane>
          <fa-tab-pane tab="音乐配音" key="music">
            <MusicEditor />
          </fa-tab-pane>
          <fa-tab-pane tab="花字贴图" key="sticker">
            <StickerEditor />
          </fa-tab-pane>
        </fa-tabs>
        <!-- 底部按钮 -->
        <div
          class="b-divider b-t-1 flex justify-center items-center h-[64px]"
          v-show="!loading"
        >
          <fa-popconfirm
            :title="saveAndGenerateTips"
            @confirm="showGenerateTips"
            :visible="isGenerateTipsVisible"
            @visibleChange="handleGenerateTipsVisibleChange"
            :okText="saveAndGenerateOkText"
            cancelText="取消"
          >
            <fa-button
              class="btn-special mr-[16px]"
              :class="{
                'btn-special-disabled': isSaveAndGenerateDisabled,
              }"
            >
              <div
                class="flex items-center justify-center text-[16px] line-height-[21px]"
              >
                生成新作品
                <div
                  class="star transition-all duration-200"
                  :style="{
                    opacity: consumePoint > 0 ? 1 : 0,
                    width: consumePoint > 0 ? 'auto' : '0',
                    marginLeft: consumePoint > 0 ? '4px' : '0',
                  }"
                >
                  <img
                    class="star__icon"
                    src="@/assets/common/score.svg"
                    style="filter: brightness(0) invert(1)"
                  />
                  <span class="star__text">{{ consumePoint || 1 }}</span>
                </div>
              </div>
            </fa-button>
          </fa-popconfirm>
          <fa-button
            class="w-[96px] text-[16px]"
            @click="handleClickSaveAll"
            :disabled="isSaveAllDisabled || isSaveAndGenerateDisabled"
            >保存修改</fa-button
          >
        </div>
        <div class="flex-1 flex justify-center items-center" v-show="loading">
          <fa-spin />
        </div>
      </div>
      <!-- 点数不足弹窗 -->
      <InsufficientPointsModal
        :visible.sync="modalVisible"
        @recharge="handleRecharge"
        @cancel="modalVisible = false"
      />
    </el-drawer>
  </div>
</template>

<script lang="ts" setup>
import { computed, nextTick, onMounted, ref } from 'vue';
import ScriptEditor from './ScriptEditor/ScriptEditor.vue';
import MusicEditor from './MusicEditor/MusicEditor.vue';
import StickerEditor from './StickerEditor/StickerEditor.vue';
import InsufficientPointsModal from '@/components/InsufficientPointsModal/index.vue';
import {
  loading,
  closeStatus,
  workInfo,
  originWorkInfo,
  consumePoint,
  updateConsumePoint,
  updateConsumePointNow,
  isNeedMergeVideo,
  isDubbingInvalid,
  isBgmInvalid,
} from './hook/useWorkInfo';
import { saveWork } from '@/api/VideoEditor/save';
import { message } from '@fk/faicomponent';
import { VIDEO_EDITOR_CLOSE_STATUS } from './constants';
import store from '@/store';
import { getExternalDynamicUrl } from '@/constants/system';
import { showConfirmModal } from '../comm/ScModalConfirm';

const visible = ref(false);
const emit = defineEmits(['close']);
const handleClosed = () => {
  emit('close');
};

onMounted(() => {
  visible.value = true;
});

/** 是否包含失效图片 */
const hasInvalidImage = computed(() => {
  return workInfo.value?.setting.style.some(
    sticker => sticker.type === 'image' && sticker.resId?.length === 0,
  );
});

/** 处理点击外部区域关闭抽屉 */
const handleClickOutside = (event: MouseEvent) => {
  // 检查是否点击在遮罩层上
  const drawerWrapper = (event.target as Element)?.closest(
    '.el-drawer__wrapper',
  );
  const drawerContent = (event.target as Element)?.closest('.el-drawer__body');

  // 如果点击在遮罩层上但不在抽屉内容上，则关闭抽屉
  if (drawerWrapper && !drawerContent) {
    visible.value = false;
  }
};

const props = defineProps<{
  /** 默认tab */
  defaultTab?: 'script' | 'music' | 'sticker';
}>();
/** 默认tab是脚本编辑 */
const defaultTab = ref(props.defaultTab || 'script');

/** 是否够积分重新生成 */
const isEnoughPointToGenerate = computed(() => {
  return consumePoint.value <= store.state.user.point;
});

/** 保存并生成的ok按钮文案 */
const saveAndGenerateOkText = computed(() => {
  if (!isEnoughPointToGenerate.value) {
    return '去充值';
  }
  return '确认生成';
});

/** 保存并生成提示 */
const saveAndGenerateTips = computed(() => {
  if (!isEnoughPointToGenerate.value) {
    return `重新生成将消耗 ${consumePoint.value} 个创作点，当前创作点数不足，请先充值创作点数`;
  }
  return `重新生成将消耗 ${consumePoint.value} 个创作点。是否继续？`;
});

/** 保存并生成按钮 disable */
const isSaveAndGenerateDisabled = computed(() => {
  const isNotModify =
    JSON.stringify(workInfo.value) === JSON.stringify(originWorkInfo.value) &&
    consumePoint.value === 0 &&
    !isNeedMergeVideo.value;
  return (
    !workInfo.value || // 数据没加载
    loading.value || // 数据没加载
    workInfo.value.script.segments.some(
      // 脚本内容有空内容时
      segment => segment.content.trim().length === 0,
    ) ||
    isNotModify || // 没修改过
    hasInvalidImage.value // 有失效图片
  );
});

const isSaveAllDisabled = computed(() => {
  return (
    JSON.stringify(workInfo.value) === JSON.stringify(originWorkInfo.value) ||
    hasInvalidImage.value
  );
});

/** 点击保存所有修改 */
const handleClickSaveAll = async () => {
  if (!workInfo.value || loading.value) return;
  await updateConsumePoint();
  loading.value = true;
  const [err] = await saveWork(workInfo.value, false);
  loading.value = false;
  if (err) {
    message.error(err.message || '保存失败');
    return;
  }
  originWorkInfo.value = JSON.parse(JSON.stringify(workInfo.value));
  closeStatus.value = VIDEO_EDITOR_CLOSE_STATUS.SAVED;
  message.success('保存成功');
};

const isGenerateTipsVisible = ref(false);
/** 是否显示扣点提示 */
const handleGenerateTipsVisibleChange = async (visible: boolean) => {
  if (
    visible &&
    (isSaveAndGenerateDisabled.value || isSaveAndGenerateDisabled.value)
  ) {
    // 按钮不可用时，阻止弹出提示
    return;
  }
  if (isDubbingInvalid.value) {
    return message.error('请检查配音设置');
  }
  if (isBgmInvalid.value && workInfo.value?.setting.bgMusic.open) {
    return message.error('请检查背景音乐设置');
  }
  await updateConsumePointNow();
  await nextTick();
  // 如果本次生成不需要扣点，不显示扣点提示，直接执行保存并生成
  if (consumePoint.value === 0) {
    handleSaveAndGenerate();
    return;
  }
  isGenerateTipsVisible.value = visible;
};

const modalVisible = ref(false);
/** 充值按钮点击事件 */
const handleRecharge = () => {
  window.open(getExternalDynamicUrl().VERSION_BUY, '_blank');
};

let saveAndGenerateLock = false;
/** 点击保存并生成 */
const handleSaveAndGenerate = async () => {
  if (!workInfo.value || loading.value || isSaveAndGenerateDisabled.value)
    return;
  if (saveAndGenerateLock) return; // 防止重复点击
  if (!isEnoughPointToGenerate.value) {
    // 如果积分不足，跳转到充值页面
    window.open(getExternalDynamicUrl().VERSION_BUY, '_blank');
    return;
  }
  saveAndGenerateLock = true;
  const [err] = await saveWork(workInfo.value, true);
  loading.value = false;
  if (err) {
    if (err.rt === -9) {
      modalVisible.value = true;
      return;
    }
    message.error(err.message || '保存失败');
    saveAndGenerateLock = false;
    return;
  }
  let successMessage = '保存成功';
  // 判断脚本内容、配音速度或配音id是否有更改，修改成功提示语
  const isScriptChanged =
    JSON.stringify(workInfo.value?.script) !==
    JSON.stringify(originWorkInfo.value?.script);
  const isVoiceIdChanged =
    workInfo.value?.setting.voice?.voiceId !==
    originWorkInfo.value?.setting.voice?.voiceId;
  const isVoiceSpeedChanged =
    workInfo.value?.setting.voice?.speed !==
    originWorkInfo.value?.setting.voice?.speed;
  if (isScriptChanged || isVoiceIdChanged || isVoiceSpeedChanged) {
    successMessage = '保存成功，视频时长及配音将自动调整';
  }
  message.success(successMessage);
  closeStatus.value = VIDEO_EDITOR_CLOSE_STATUS.GENERATED;
  visible.value = false;
};
/** 重新生成提示 */
const showGenerateTips = () => {
  if (!isEnoughPointToGenerate.value) {
    // 如果积分不足，跳转到充值页面
    window.open(getExternalDynamicUrl().VERSION_BUY, '_blank');
    return;
  }
  showConfirmModal({
    content:
      '重新生成将基于当前项目中已上传的素材进行。若素材内容有变更，可能导致生成结果与之前存在较大差异。是否确认重新生成？',
    onOk: handleSaveAndGenerate,
    okType: 'primary',
    okText: '确认重新生成',
  });
};
</script>

<style lang="scss" scoped>
.star {
  @apply flex items-center ml-4px line-height-[21px] h-[21px];

  &__icon {
    @apply w-16px h-16px mr-3px text-white;
  }

  &__text {
    @apply text-15px lh-none font-400 text-left text-white;
  }
}
::v-deep {
  .fa-tabs {
    .fa-tabs-top-content {
      height: calc(100% - 57px);
    }
    .fa-tabs-bar {
      @apply mb-0;
    }
  }
  .fa-tabs-nav {
    @apply ml-[20px];
    .fa-tabs-tab {
      @apply text-[15px] line-height-[20px];
      @apply py-[20px] px-[4px] mr-[32px];
    }
    .fa-tabs-tab-active {
      @apply font-bold;
    }
  }
  .fa-tabs-ink-bar {
    @apply h-[3px];
  }
  .fa-tooltip-inner {
    @apply rounded-[6px] bg-[#000000] bg-op-80;
  }
  .el-drawer__wrapper {
    overflow-x: auto;
    .el-drawer__container {
      min-width: 1190px;
    }
  }
}
</style>
