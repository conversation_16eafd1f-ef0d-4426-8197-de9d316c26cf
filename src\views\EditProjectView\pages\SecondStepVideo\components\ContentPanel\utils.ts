/**
 * 内容面板工具函数
 */

import { getMusicInfo, getDubbingInfo } from '@/api/VideoEditor/music';
import { getMaterialFullUrl } from '@/components/MaterialBasicUpload/utils/index.ts';
import { FILE_TYPES } from '@/constants/fileType';

/**
 * 截断文本
 * @param text 原始文本
 * @param maxLength 最大长度
 * @returns 截断后的文本
 */
export function truncateText(text: string, maxLength: number): string {
  if (!text || text.length <= maxLength) return text;
  return text.substring(0, maxLength) + '...';
}

/**
 * 格式化时间（秒转为mm:ss格式）
 * @param seconds 秒数
 * @returns 格式化后的时间字符串
 */
export function formatDuration(seconds: number): string {
  if (!seconds || isNaN(seconds)) return '00:00';

  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = Math.floor(seconds % 60);

  const formattedMinutes = String(minutes).padStart(2, '0');
  const formattedSeconds = String(remainingSeconds).padStart(2, '0');

  return `${formattedMinutes}:${formattedSeconds}`;
}

/**
 * 解析脚本内容为段落数组
 * @param scriptContent 脚本内容
 * @returns 段落数组
 */
export function parseScriptToParagraphs(scriptContent: string): string[] {
  if (!scriptContent) return [];
  return scriptContent.split('\n').filter(p => p.trim() !== '');
}

/**
 * 获取默认音乐信息
 * @returns 默认音乐信息
 */
export function getDefaultMusicInfo(resId?: string) {
  return {
    name: 'Piano Music ver.',
    duration: '00:50',
    resId: resId || '',
    volume: 100,
  };
}

/**
 * 获取默认配音信息
 * @returns 默认配音信息
 */
export function getDefaultVoiceInfo(voiceId?: string) {
  return {
    name: '影视解说小帅',
    gender: '男声',
    avatar: 'https://picsum.photos/300/200?random=1',
    voiceId: voiceId || '',
    speed: 1.0,
  };
}

/**
 * 通过resId获取音乐详细信息
 * @param resId 音乐资源ID
 * @returns Promise<音乐详细信息>
 */
export async function getMusicDetailsByResId(resId: string): Promise<{
  name: string;
  duration: string;
  volume: number;
  resId: string;
}> {
  const [err, res] = await getMusicInfo(resId);

  if (err || !res?.data) {
    console.error('获取音乐详情失败:', err?.message || '未知错误');
    // 返回默认值，避免组件崩溃
    const defaultMusic = getDefaultMusicInfo(resId);
    return {
      name: defaultMusic.name,
      duration: defaultMusic.duration,
      volume: defaultMusic.volume,
      resId: defaultMusic.resId,
    };
  }

  return {
    name: res.data.name,
    duration: res.data.duration,
    volume: 100, // API中没有音量信息，使用默认值
    resId: res.data.resId,
  };
}

/**
 * 通过voiceId获取配音详细信息
 * @param voiceId 配音员ID
 * @returns Promise<配音详细信息>
 */
export async function getVoiceDetailsByVoiceId(voiceId: string): Promise<{
  name: string;
  gender: string;
  avatar: string;
  voiceId: string;
  speed: number;
}> {
  const [err, res] = await getDubbingInfo(voiceId);

  if (err || !res?.data) {
    console.error('获取配音详情失败:', err?.message || '未知错误');
    // 返回默认值，避免组件崩溃
    const defaultVoice = getDefaultVoiceInfo(voiceId);
    return {
      name: defaultVoice.name,
      gender: defaultVoice.gender,
      avatar: defaultVoice.avatar,
      voiceId: defaultVoice.voiceId,
      speed: defaultVoice.speed,
    };
  }

  return {
    name: res.data.name,
    gender: '未知', // API中没有性别信息，使用默认值
    avatar: getMaterialFullUrl(res.data.cover.resId, FILE_TYPES.WEBP, 'oss'),
    voiceId: res.data.voiceId,
    speed: 1.0, // API中没有语速信息，使用默认值
  };
}
