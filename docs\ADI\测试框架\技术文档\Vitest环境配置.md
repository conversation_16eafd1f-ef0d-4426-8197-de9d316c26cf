# Vitest 测试环境配置指南

## 概述

本项目使用 Vitest 作为测试框架，配置了完整的 Vue 2.7 + TypeScript + Element UI 测试环境。

## ✅ 配置完成状态

- ✅ Vitest 基础配置完成
- ✅ Vue 2.7 组件测试支持
- ✅ TypeScript 类型支持
- ✅ Element UI 组件模拟
- ✅ 测试覆盖率报告
- ✅ 测试工具函数库
- ✅ 示例测试文件
- ✅ 所有测试通过 (29/29)

## 配置文件

### 主要配置文件

- `vitest.config.ts` - Vitest 专用配置文件
- `src/test/setup.ts` - 测试环境设置文件
- `src/test/utils.ts` - 测试工具函数
- `tsconfig.json` - TypeScript 配置（包含测试类型）

### 依赖包

```json
{
  "devDependencies": {
    "vitest": "0.34.6",
    "@vitest/ui": "0",
    "@vitest/browser": "^0.34.6",
    "@vue/test-utils": "^2.4.6",
    "jsdom": "22.1.0",
    "@types/jsdom": "21.1.7"
  }
}
```

## 测试脚本

```bash
# 运行所有测试（单次）
pnpm test

# 监听模式运行测试
pnpm test:watch

# 运行测试并生成覆盖率报告
pnpm test:coverage

# 使用 UI 界面运行测试
pnpm test:ui

# 使用浏览器模式运行测试
pnpm test:browser
```

## 测试环境特性

### 1. 全局配置

- **测试环境**: jsdom（模拟浏览器环境）
- **全局变量**: 启用 vitest 全局 API（describe, it, expect 等）
- **超时时间**: 10 秒
- **并发**: 支持多线程并发测试

### 2. Vue 组件测试支持

- **Vue Test Utils**: 用于 Vue 组件测试
- **Element UI 模拟**: 自动模拟 Element UI 组件
- **路由模拟**: 模拟 Vue Router
- **状态管理模拟**: 模拟 Vuex Store

### 3. 浏览器 API 模拟

- localStorage / sessionStorage
- ResizeObserver / IntersectionObserver
- matchMedia
- 其他常用浏览器 API

### 4. 覆盖率报告

- **提供商**: v8
- **报告格式**: text, json, html
- **阈值**: 80% (分支、函数、行、语句)
- **排除文件**: 配置文件、类型文件、mock 文件等

## 测试文件组织

### 目录结构

```
src/
├── test/                    # 测试配置和工具
│   ├── setup.ts            # 测试环境设置
│   └── utils.ts             # 测试工具函数
├── utils/__tests__/         # 工具函数测试
│   ├── eventBus.test.ts
│   └── logger.test.ts
├── components/__tests__/    # 组件测试
│   ├── TestComponent.vue    # 测试用组件
│   └── TestComponent.test.ts
└── ...
```

### 命名规范

- 测试文件: `*.test.ts` 或 `*.spec.ts`
- 测试目录: `__tests__/`
- 组件测试: 与组件同目录或在 `__tests__/` 目录下

## 测试工具函数

### createWrapper / createShallowWrapper

用于创建 Vue 组件测试包装器，自动配置常用的 mocks 和 stubs。

```typescript
import { createWrapper } from '@/test/utils';
import MyComponent from './MyComponent.vue';

const wrapper = createWrapper(MyComponent, {
  props: { title: '测试标题' },
});
```

### 异步测试工具

```typescript
import { nextTick, sleep } from '@/test/utils';

// 等待 Vue 下一个 tick
await nextTick();

// 等待指定时间
await sleep(1000);
```

### 用户交互模拟

```typescript
import { setInputValue, clickElement } from '@/test/utils';

// 模拟输入
await setInputValue(wrapper, '.input-selector', '测试值');

// 模拟点击
await clickElement(wrapper, '.button-selector');
```

### 断言工具

```typescript
import {
  expectElementExists,
  expectElementText,
  expectElementVisible,
} from '@/test/utils';

// 检查元素存在
expectElementExists(wrapper, '.my-element');

// 检查元素文本
expectElementText(wrapper, '.title', '期望文本');

// 检查元素可见性
expectElementVisible(wrapper, '.visible-element');
```

## API 请求测试

### 模拟 API 响应

```typescript
import { mockApiResponse } from '@/test/utils';
import { vi } from 'vitest';

// 模拟成功响应
const mockGet = vi
  .fn()
  .mockResolvedValue(mockApiResponse({ id: 1, name: '测试数据' }));

// 模拟错误响应
const mockGetError = vi
  .fn()
  .mockResolvedValue(mockApiResponse(null, new Error('请求失败')));
```

### await-to 模式测试

```typescript
import { getUserInfo } from '@/api/user';

it('应该正确处理 API 响应', async () => {
  const [err, res] = await getUserInfo();

  if (err) {
    expect(err.message).toBe('请求失败');
    return;
  }

  expect(res.data.name).toBe('测试用户');
});
```

## 最佳实践

### 1. 测试组织

- 使用 `describe` 分组相关测试
- 使用 `beforeEach` / `afterEach` 设置和清理
- 测试名称应该清晰描述测试内容

### 2. 组件测试

- 测试组件的公共接口（props, events, slots）
- 测试用户交互和状态变化
- 避免测试实现细节

### 3. 异步测试

- 正确处理异步操作
- 使用 `await` 等待异步完成
- 设置合适的超时时间

### 4. Mock 使用

- 模拟外部依赖（API、第三方库）
- 避免过度模拟
- 确保 mock 的行为与真实实现一致

## 常见问题

### 1. 测试文件找不到模块

确保 `tsconfig.json` 中包含了正确的路径映射：

```json
{
  "compilerOptions": {
    "paths": {
      "@/*": ["./src/*"]
    }
  }
}
```

### 2. Element UI 组件报错

检查 `src/test/setup.ts` 中是否正确模拟了 Element UI 组件。

### 3. 异步测试超时

增加测试超时时间或检查异步操作是否正确完成：

```typescript
it('异步测试', async () => {
  // 测试代码
}, 15000); // 15秒超时
```

### 4. 覆盖率不足

- 添加更多测试用例
- 检查是否有未测试的分支
- 考虑是否需要排除某些文件

## 扩展配置

### 添加新的测试环境

如需要特殊的测试环境，可以在 `vitest.config.ts` 中添加：

```typescript
export default defineConfig({
  test: {
    // 自定义环境配置
    environmentOptions: {
      jsdom: {
        url: 'http://localhost:3000',
      },
    },
  },
});
```

### 自定义匹配器

可以在 `src/test/setup.ts` 中添加自定义匹配器：

```typescript
import { expect } from 'vitest';

expect.extend({
  toBeValidEmail(received: string) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    const pass = emailRegex.test(received);

    return {
      pass,
      message: () => `expected ${received} to be a valid email`,
    };
  },
});
```
