<template>
  <div class="content-panel">
    <!-- 脚本内容区域 -->
    <div class="content-panel__section content-panel__section--script">
      <div class="content-panel__section-header">
        <div class="content-panel__section-title">脚本详情</div>
        <fa-button
          type="default"
          size="small"
          :disabled="isEditDisabled"
          @click="handleEditScript"
          class="content-panel__edit-button"
        >
          编辑脚本
        </fa-button>
      </div>

      <!-- 脚本结构 -->
      <div class="content-panel__script-structure">
        <span class="content-panel__script-structure-label">脚本结构：</span>
        <div class="content-panel__script-structure-items">
          <template v-for="(segment, index) in workData?.script?.segments">
            <div :key="index">
              <span class="content-panel__script-structure-item">
                {{ segment.module }}
              </span>
              <span
                v-if="index < (workData?.script?.segments?.length || 0) - 1"
                class="content-panel__script-structure-plus"
                >+</span
              >
            </div>
          </template>
        </div>
      </div>

      <!-- 脚本内容 -->
      <div class="content-panel__script-container">
        <div class="content-panel__script-content">
          <MaterialBox type="text" layout="content-only">
            <template #content>
              <template v-if="scriptContent">
                <p
                  v-for="(paragraph, index) in scriptParagraphs"
                  :key="index"
                  class="content-panel__paragraph"
                >
                  {{ paragraph }}
                </p>
              </template>
              <div v-else class="content-panel__empty-content">
                暂无脚本内容
              </div>
            </template>
          </MaterialBox>
        </div>
      </div>
    </div>

    <!-- 背景音乐区域 -->
    <div class="content-panel__section">
      <div class="content-panel__section-header">
        <div class="content-panel__section-title">背景音乐</div>
      </div>

      <div class="content-panel__music-content">
        <MaterialBox
          type="music"
          layout="content-actions"
          :mediaInfo="bgMusicMediaInfo"
          :disabled="isEditDisabled"
          @click="handleChangeMusic"
        >
        </MaterialBox>
      </div>
    </div>

    <!-- 配音区域 -->
    <div class="content-panel__section">
      <div class="content-panel__section-header">
        <div class="content-panel__section-title">配音</div>
      </div>

      <div class="content-panel__voice-content">
        <MaterialBox
          type="voice"
          layout="content-actions"
          :mediaInfo="voiceMediaInfo"
          :disabled="isEditDisabled"
          @click="handleChangeVoice"
        />
      </div>
    </div>

    <!-- 花字区域 -->
    <div class="content-panel__section">
      <div class="content-panel__section-header">
        <div class="content-panel__section-title">花字</div>
      </div>

      <div class="content-panel__tags-content">
        <div class="content-panel__tags-list">
          <template v-if="textStickers && textStickers.length > 0">
            <div
              v-for="(sticker, index) in textStickers"
              :key="index"
              class="content-panel__tag-item"
            >
              <img
                :src="
                  presetImages[`/src/assets/FontPreset/${sticker.styleId}.webp`]
                    ?.default || ''
                "
                class="content-panel__tag-image"
              />
            </div>
          </template>
          <fa-button
            type="default"
            class="content-panel__add-tag"
            :disabled="isEditDisabled"
            @click="handleAddFontTag"
            ><Icon type="jiaxiao" class="content-panel__add-icon" />
            <span>添加花字</span></fa-button
          >
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent } from 'vue';
import {
  CONTENT_PANEL_ACTION_TYPES,
  CONTENT_PANEL_ACTIONS,
  CONTENT_PANEL_CONFIG,
} from './constants';
import {
  WORK_STATUS,
  getWorkStatusInfo,
  isAnyCompletedWork,
} from '@/constants/workStatus';
import { ContentPanelActionEventParam } from './types';
import { VideoWorkItem, TextSticker, Music, Dubbing } from '@/types';
import { parseScriptToParagraphs } from './utils';
import { getMusicInfo, getDubbingInfo } from '@/api/VideoEditor/music';
import { getMaterialFullUrl } from '@/components/MaterialBasicUpload/utils/index';
import { FILE_TYPES } from '@/constants/fileType';
import MaterialBox from './components/MaterialBox.vue';
import defaultBgMusicImg from '@/assets/EditProject/bgMusicImg.webp';
import defaultVoiceImg from '@/assets/EditProject/voiceImg.webp';

export default defineComponent({
  name: 'ContentPanel',
  components: { MaterialBox },
  props: {
    loading: {
      type: Boolean,
      default: false,
    },
    workData: {
      type: Object as () => VideoWorkItem,
      default: undefined,
    },
  },
  data() {
    return {
      // 常量
      ACTION_TYPES: CONTENT_PANEL_ACTION_TYPES,
      ACTIONS: CONTENT_PANEL_ACTIONS,
      CONFIG: CONTENT_PANEL_CONFIG,
      WORK_STATUS,

      // 背景音乐信息
      bgMusicInfo: {
        resId: '',
        name: '未选择音乐',
        duration: '',
        cover: {
          resId: '',
          resType: FILE_TYPES.WEBP,
        },
        link: '',
      } as Music,
      // 背景音乐是否失效
      isBgmInvalid: false,

      // 配音信息
      dubbingInfo: {
        voiceId: '',
        typeName: '',
        categoryName: '未知',
        cover: {
          resId: '',
          resType: FILE_TYPES.WEBP,
        },
        name: '未选择配音',
        link: '',
      } as Dubbing,
      // 配音是否失效
      isDubbingInvalid: false,

      // 字体预设图片
      presetImages: import.meta.glob('@/assets/FontPreset/*.webp', {
        eager: true,
      }) as Record<string, { default: string }>,

      // 默认图片
      defaultBgMusicImg,
      defaultVoiceImg,
    };
  },
  computed: {
    /**
     * 获取脚本内容
     */
    scriptContent(): string {
      if (!this.workData?.script?.segments?.length) return '';

      return this.workData.script.segments
        .map(segment => `${segment.content}\n`)
        .join('\n\n');
    },

    /**
     * 将脚本内容分割为段落
     */
    scriptParagraphs(): string[] {
      return parseScriptToParagraphs(this.scriptContent);
    },

    /**
     * 是否配置了背景音乐
     */
    hasBgMusic(): boolean {
      const bgMusic = this.workData?.setting?.bgMusic;
      return !!(bgMusic?.open && bgMusic?.resId);
    },

    /**
     * 是否配置了配音
     */
    hasVoice(): boolean {
      const voice = this.workData?.setting?.voice;
      return !!voice?.voiceId;
    },

    /**
     * 背景音乐媒体信息
     */
    bgMusicMediaInfo() {
      return {
        name: this.isBgmInvalid
          ? '背景音乐已失效，请重新更换'
          : this.hasBgMusic
          ? this.bgMusicInfo.name
          : '未配置背景音乐',
        description:
          this.hasBgMusic && !this.isBgmInvalid
            ? this.bgMusicInfo.duration
            : '00:00',
        img:
          this.hasBgMusic && this.bgMusicInfo.cover.resId && !this.isBgmInvalid
            ? getMaterialFullUrl(
                this.bgMusicInfo.cover.resId,
                FILE_TYPES.WEBP,
                'oss',
                48,
              )
            : this.defaultBgMusicImg,
      };
    },

    /**
     * 配音媒体信息
     */
    voiceMediaInfo() {
      return {
        name: this.isDubbingInvalid
          ? '配音已失效，请重新更换'
          : this.hasVoice
          ? this.dubbingInfo.name
          : '未配置配音',
        description:
          this.hasVoice && !this.isDubbingInvalid
            ? this.dubbingInfo.categoryName
            : '',
        img:
          this.hasVoice &&
          this.dubbingInfo.cover.resId &&
          !this.isDubbingInvalid
            ? getMaterialFullUrl(
                this.dubbingInfo.cover.resId,
                FILE_TYPES.WEBP,
                'oss',
                48,
              )
            : this.defaultVoiceImg,
      };
    },

    /**
     * 是否禁用编辑功能
     * 只有已生成(COMPLETED)和重新生成完成(RECOMPLETED)状态的作品可以进行编辑操作
     * 生成失败和重新生成失败状态都不允许编辑
     */
    isEditDisabled() {
      if (
        this.workData?.status === undefined ||
        this.workData?.status === null
      ) {
        return true; // 没有状态信息时禁用编辑
      }

      // 只有已完成状态的作品可以编辑
      const statusInfo = getWorkStatusInfo(this.workData);
      return !isAnyCompletedWork(statusInfo);
    },

    /**
     * 获取文本类型的花字数据
     */
    textStickers(): TextSticker[] {
      if (!this.workData?.setting?.style) return [];

      return this.workData.setting.style.filter(
        (style): style is TextSticker => style.type === 'text',
      );
    },
  },
  watch: {
    /**
     * 监听workData变化
     */
    workData: {
      handler(newVal: VideoWorkItem | undefined) {
        if (newVal) {
          this.initializeData();
        }
      },
      immediate: true,
      deep: true,
    },
  },
  methods: {
    /**
     * 初始化数据
     */
    async initializeData() {
      // 初始化时直接调用更新面板数据方法
      await this.updatePanelData();
    },

    /**
     * 更新面板数据
     */
    async updatePanelData() {
      if (!this.workData) return;

      // 并行更新音乐和配音信息
      await Promise.allSettled([
        this.updateMusicInfo(),
        this.updateVoiceInfo(),
      ]);
    },

    /**
     * 更新音乐信息
     */
    async updateMusicInfo() {
      if (!this.hasBgMusic) return;

      const bgMusic = this.workData.setting.bgMusic;
      const [err, res] = await getMusicInfo(bgMusic.resId!);

      if (err) {
        if (!(err instanceof Error) && err.rt === -3) {
          this.isBgmInvalid = true;
          return;
        }
        console.log('获取背景音乐失败:', err);
        return;
      }

      if (res?.data) {
        this.bgMusicInfo = res.data;
      }
    },

    /**
     * 更新配音信息
     */
    async updateVoiceInfo() {
      if (!this.hasVoice) return;

      const voice = this.workData.setting.voice;
      const [err, res] = await getDubbingInfo(voice.voiceId!);

      if (err) {
        if (!(err instanceof Error) && err.rt === -3) {
          this.isDubbingInvalid = true;
          return;
        }
        console.log('获取配音信息失败:', err);
        return;
      }

      if (res?.data) {
        this.dubbingInfo = res.data;
      }
    },

    /**
     * 处理编辑脚本
     */
    handleEditScript() {
      // 如果编辑功能被禁用，则不执行操作
      if (this.isEditDisabled) {
        return;
      }
      this.emitAction(this.ACTION_TYPES.SCRIPT, this.ACTIONS.EDIT);
    },

    /**
     * 处理更换音乐
     */
    handleChangeMusic() {
      // 如果编辑功能被禁用，则不执行操作
      if (this.isEditDisabled) {
        return;
      }
      this.emitAction(this.ACTION_TYPES.MUSIC, this.ACTIONS.CHANGE);
    },

    /**
     * 处理更换配音
     */
    handleChangeVoice() {
      // 如果编辑功能被禁用，则不执行操作
      if (this.isEditDisabled) {
        return;
      }
      this.emitAction(this.ACTION_TYPES.VOICE, this.ACTIONS.CHANGE);
    },

    /**
     * 处理添加花字
     */
    handleAddFontTag() {
      // 如果编辑功能被禁用，则不执行操作
      if (this.isEditDisabled) {
        return;
      }
      this.emitAction(this.ACTION_TYPES.FONT_TAG, this.ACTIONS.ADD);
    },

    /**
     * 发送操作事件
     */
    emitAction(type: string, action: string, data?: Record<string, unknown>) {
      const eventParam: ContentPanelActionEventParam = {
        type,
        action,
        data,
      };
      this.$emit('action', eventParam);
    },
  },
});
</script>

<style lang="scss" scoped>
@import '@/style/mixins/scrollbar.scss';

/*
 * 内容面板主容器
 * ----------------------------------------
 * 定义整体布局结构和基础样式
 */
.content-panel {
  /* 布局相关 */
  @apply flex flex-col;
  /* 尺寸相关 */
  @apply w-full h-full px-40px;
  /* 外观相关 */
  @apply overflow-y-auto;
  /* 滚动条样式 */
  @include scrollbar-style;
}

/*
 * 内容区块通用样式
 * ----------------------------------------
 * 定义各个内容区块的基础样式和间距
 */
.content-panel__section {
  /* 尺寸相关 */
  @apply mt-24px;

  &:first-child {
    /* 尺寸相关 */
    @apply mt-0;
  }

  &:last-child {
    /* 尺寸相关 */
    @apply mb-24px;
  }
}

.content-panel__section--script {
  /* 布局相关 */
  @apply relative;
  /* 外观相关 */
  @apply bg-transparent;
}

.content-panel__section-header {
  /* 布局相关 */
  @apply flex justify-between items-center;
}

.content-panel__edit-button {
  /* 文字相关 */
  @apply text-14px;
}

.content-panel__section-title {
  /* 文字相关 */
  @apply font-700 text-15px text-left text-[#111];
}

/*
 * 脚本结构样式
 * ----------------------------------------
 * 定义脚本结构展示区域的布局和样式
 */
.content-panel__script-structure {
  /* 布局相关 */
  @apply flex items-center;
  /* 尺寸相关 */
  @apply mt-18px;
}

.content-panel__script-structure-label {
  /* 布局相关 */
  @apply whitespace-nowrap self-start;
  /* 尺寸相关 */
  @apply mr-8px;
  /* 文字相关 */
  @apply font-400 text-14px text-left text-[#333] lh-24px;
}

.content-panel__script-structure-items {
  /* 布局相关 */
  @apply flex items-center flex-wrap;
  /* 布局相关 */
  @apply flex flex-wrap gap-x-0 gap-y-8px;
}

.content-panel__script-structure-item {
  /* 尺寸相关 */
  @apply inline-block px-8px border-rd-4px bg-[#f3f3f5];
  /* 文字相关 */
  @apply font-400 text-13px lh-24px text-left;
  /* 外观相关 */
  @apply text-[#333];
}

.content-panel__script-structure-plus {
  /* 尺寸相关 */
  @apply mx-8px;
  /* 文字相关 */
  @apply font-400 text-14px text-left;
  /* 外观相关 */
  @apply text-[#333];
}

/*
 * 脚本内容样式
 * ----------------------------------------
 * 定义脚本内容展示区域的布局和样式
 */
.content-panel__script-container {
  /* 布局相关 */
  @apply relative;
}

.content-panel__script-content {
  /* 布局相关 */
  @apply relative;
  /* 尺寸相关 */
  @apply mt-16px;
}

.content-panel__paragraph {
  /* 尺寸相关 */
  @apply mb-16px;
  /* 文字相关 */
  @apply text-14px text-[#333] leading-[1.8] text-text;

  &:last-child {
    /* 尺寸相关 */
    @apply mb-0;
  }
}

.content-panel__empty-content {
  /* 布局相关 */
  @apply text-center;
  /* 尺寸相关 */
  @apply py-20px;
  /* 文字相关 */
  @apply text-14px text-assist;
}

/*
 * 媒体内容样式
 * ----------------------------------------
 * 定义音乐和配音区域的布局和样式
 */
.content-panel__music-content,
.content-panel__voice-content {
  /* 尺寸相关 */
  @apply mt-8px;
}

.content-panel__media-item {
  /* 布局相关 */
  @apply flex justify-between items-center;
}

.content-panel__media-info {
  /* 布局相关 */
  @apply flex items-center;
}

.content-panel__media-icon {
  /* 布局相关 */
  @apply flex items-center justify-center;
  /* 尺寸相关 */
  @apply w-40px h-40px mr-12px;
  /* 外观相关 */
  @apply rounded-full bg-disabledBackground;
}

.content-panel__icon {
  /* 尺寸相关 */
  @apply w-20px h-20px;
  /* 文字相关 */
  @apply text-subText;
}

.content-panel__media-avatar {
  /* 尺寸相关 */
  @apply w-40px h-40px mr-12px;
  /* 外观相关 */
  @apply rounded-full overflow-hidden;
}

.content-panel__avatar-img {
  /* 尺寸相关 */
  @apply w-full h-full;
  /* 外观相关 */
  @apply object-cover;
}

.content-panel__media-details {
  /* 布局相关 */
  @apply flex flex-col;
}

.content-panel__media-name {
  /* 尺寸相关 */
  @apply mb-4px;
  /* 文字相关 */
  @apply text-14px text-text;
}

.content-panel__media-duration,
.content-panel__media-type {
  /* 文字相关 */
  @apply text-12px text-assist;
}

.content-panel__action-button {
  /* 尺寸相关 */
  @apply p-0;
  /* 文字相关 */
  @apply text-14px text-primary;

  &:hover {
    /* 文字相关 */
    @apply text-secondary;
  }
}

/*
 * 花字标签样式
 * ----------------------------------------
 * 定义花字标签展示和添加按钮的样式
 */
.content-panel__tags-content {
  /* 尺寸相关 */
  @apply pt-8px;
}

.content-panel__tags-list {
  /* 布局相关 */
  @apply flex flex-wrap;
  /* 尺寸相关 */
  @apply gap-16px;
}

.content-panel__tag-item {
  /* 布局相关 */
  @apply flex items-center justify-center;
  /* 尺寸相关 */
  @apply h-40px w-40px;
  /* 外观相关 */
  @apply border-rd-8px bg-[#f3f3f5] overflow-hidden;
  /* 文字相关 */
  @apply text-14px text-text;
}

.content-panel__tag-image {
  /* 尺寸相关 */
  @apply w-full h-full;
  /* 外观相关 */
  @apply rounded-[4px] object-cover;
}

.content-panel__add-tag {
  /* 布局相关 */
  @apply flex items-center justify-center;

  &:not(:disabled):hover {
    /* 文字相关 */
    @apply text-primary;
    .content-panel__add-icon {
      /* 文字相关 */
      @apply text-primary;
    }
  }

  &:disabled {
    /* 交互相关 */
    @apply cursor-not-allowed;
  }
}

.content-panel__add-icon {
  /* 尺寸相关 */
  @apply w-20px h-20px mr-4px;
  /* 文字相关 */
  @apply text-[#999];
}
</style>
