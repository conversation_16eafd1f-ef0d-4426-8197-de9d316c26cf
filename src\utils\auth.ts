import Cookies from 'js-cookie';
import { logoutReq } from '@/api/user/user';
import router from '@/router';
import store from '@/store';

const isDev = import.meta.env.MODE === 'development';
/**
 * 退出登录公共方法
 */
export const logout = async () => {
  // 调用退出登录接口
  await logoutReq();

  Cookies.remove('_TOKEN');
  Cookies.remove('_FSESSIONID');

  if (isDev) {
    router.replace('/dev-login');
  } else {
    let redirectUrl = window.location.href;
    if (window.location.hash.includes('no-permission')) {
      redirectUrl = window.location.origin;
    }
    Cookies.set('callBackUrl', redirectUrl, {
      expires: 1,
      domain: store.state.system.homeDomain,
      path: '/',
    });

    window.location.href =
      '//' +
      store.state.system.portal +
      `?url=${encodeURIComponent(redirectUrl)}`;
  }
};
