import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

/**
 * 检查 .env.development.local 文件是否存在
 * 如果不存在，提示开发者创建该文件
 */
function checkEnvLocal() {
  // 获取项目根目录
  const __filename = fileURLToPath(import.meta.url);
  const __dirname = path.dirname(__filename);
  const rootDir = path.resolve(__dirname, '..');

  const envLocalPath = path.join(rootDir, '.env.development.local');

  if (!fs.existsSync(envLocalPath)) {
    // 如果 .env.development.local 文件不存在，则从 .env.development 复制内容
    const envDevelopmentPath = path.join(rootDir, '.env.development');
    const content = fs.readFileSync(envDevelopmentPath, 'utf8');
    fs.writeFileSync(envLocalPath, content);
    console.log(
      '\x1b[32m%s\x1b[0m',
      '.env.development.local 文件自动创建，并从 .env.development 复制了内容。请根据需要修改该文件。\n',
    );
  }
}

checkEnvLocal();
