import {
  ref,
  onMounted,
  onBeforeUnmount,
  onActivated,
  onDeactivated,
  type Ref,
} from 'vue';
import { debounce } from 'lodash-es';

/**
 * @description 页面离开前的数据保护功能 composable
 * @description 监听表单变更，在用户尝试离开页面时显示确认提示
 */
export function useUnsavedChanges() {
  // ============= 响应式数据 =============

  /**
   * 是否有未保存的更改
   * @description 标识当前表单是否有未保存的修改内容
   */
  const hasUnsavedChanges = ref<boolean>(false);

  /**
   * 是否已初始化事件监听器
   * @description 防止重复初始化事件监听器
   */
  const isInitialized = ref<boolean>(false);

  /**
   * 是否处于静默期
   * @description 在保存操作后的短暂时间内忽略表单变更事件，防止误触发
   */
  const isSilentPeriod = ref<boolean>(false);

  // ============= 内部变量 =============

  /**
   * beforeunload 事件处理函数引用
   * @description 保存事件处理函数的引用，用于清理时移除监听器
   */
  let beforeUnloadHandler:
    | ((event: BeforeUnloadEvent) => string | void)
    | null = null;

  /**
   * 静默期定时器引用
   * @description 保存静默期定时器的引用，用于清理时清除定时器
   */
  let silentPeriodTimer: ReturnType<typeof setTimeout> | null = null;

  /**
   * 防抖处理的变更检测函数
   * @description 使用防抖机制避免频繁的变更检测，提升性能
   */
  const debouncedChangeHandler = debounce(() => {
    // 在静默期内忽略表单变更事件
    if (isSilentPeriod.value) {
      console.log('🚀 ADI-LOG ~ 静默期内忽略表单变更事件');
      return;
    }

    if (!hasUnsavedChanges.value) {
      hasUnsavedChanges.value = true;
      console.log('🚀 ADI-LOG ~ 检测到表单变更，设置未保存状态为 true');
    }
  }, 300); // 300ms 防抖延迟

  // ============= 核心方法 =============

  /**
   * beforeunload 事件处理函数
   * @description 处理页面离开前的确认提示
   * @param event - beforeunload 事件对象
   * @returns 确认提示信息或 undefined
   */
  const handleBeforeUnload = (event: BeforeUnloadEvent): string | void => {
    if (hasUnsavedChanges.value) {
      // 标准的确认提示信息
      const message = '系统可能不会保存您所做的更改';

      // 现代浏览器会忽略自定义消息，但仍需要设置 returnValue
      event.preventDefault();
      event.returnValue = message;

      console.log('🚀 ADI-LOG ~ 页面离开前确认提示已显示');
      return message;
    }
  };

  /**
   * 表单变更事件处理函数
   * @description 监听表单变更事件，使用事件代理方式
   * @param event - DOM 事件对象
   */
  const handleFormChange = (event: Event): void => {
    const target = event.target as HTMLElement;

    // 检查是否是表单相关元素
    if (
      target.tagName === 'INPUT' ||
      target.tagName === 'TEXTAREA' ||
      target.tagName === 'SELECT' ||
      target.classList.contains('fa-input') ||
      target.classList.contains('fa-textarea') ||
      target.classList.contains('fa-select') ||
      target.closest('.dynamic-form-item') ||
      target.closest('.fa-form-model-item') ||
      target.closest('.fa-select-selection__choice__remove') || // 标签删除按钮
      target.closest('.fa-select-search__field') || // 选择器搜索框
      target.closest('.dynamic-form-item__container') || // 动态表单项容器
      target.closest('.first-step__form') // 第一步表单容器
    ) {
      // 使用防抖处理变更检测
      debouncedChangeHandler();
    }
  };

  /**
   * 文件上传变更事件处理函数
   * @description 监听文件上传组件的变更
   * @param event - DOM 事件对象
   */
  const handleFileChange = (event: Event): void => {
    const target = event.target as HTMLElement;

    // 检查是否是文件上传相关元素
    if (
      target.closest('.custom-upload') ||
      target.closest('.fa-upload') ||
      target.closest('.custom-upload__button') || // 上传按钮
      target.closest('.custom-upload__delete-button') || // 删除按钮
      target.closest('.custom-upload__file-list') || // 文件列表
      target.closest('.dynamic-form-item__container--upload') || // 动态表单上传容器
      target.closest('.dynamic-form-item__upload-wrapper') || // 上传包装器
      target.closest('[class*="upload"]') // 通用上传相关元素
    ) {
      debouncedChangeHandler();
    }
  };

  /**
   * 音乐和配音组件变更事件处理函数
   * @description 监听音乐和配音组件的交互
   * @param event - DOM 事件对象
   */
  const handleMediaChange = (event: Event): void => {
    const target = event.target as HTMLElement;

    // 检查是否是音乐或配音相关元素
    if (
      target.closest('.bg-music-selector') ||
      target.closest('.dubbing-selector') ||
      target.closest('.bgm-card') || // 背景音乐卡片容器
      target.closest('.fa-switch') || // fa-switch 开关组件
      target.classList.contains('fa-switch') || // 直接点击开关
      target.classList.contains('fa-switch-inner') || // 点击开关内部元素
      target.closest('[class*="music"]') ||
      target.closest('[class*="voice"]') ||
      target.closest('[class*="audio"]') ||
      target.closest('[class*="bgm"]') // 背景音乐相关元素
    ) {
      debouncedChangeHandler();
    }
  };

  // ============= 公共方法 =============

  /**
   * 初始化数据保护功能
   * @description 注册事件监听器，开始监听表单变更
   */
  const initializeUnsavedChanges = (): void => {
    if (isInitialized.value) {
      console.log('🚀 ADI-LOG ~ 数据保护功能已初始化，跳过重复初始化');
      return;
    }

    try {
      // 创建 beforeunload 事件处理函数
      beforeUnloadHandler = handleBeforeUnload;

      // 注册 beforeunload 事件监听器
      window.addEventListener('beforeunload', beforeUnloadHandler);

      // 使用事件代理监听表单变更
      // 监听输入框、文本域、选择器等表单元素的变更
      document.addEventListener('input', handleFormChange, true);
      document.addEventListener('change', handleFormChange, true);

      // 监听文件上传组件的变更
      document.addEventListener('click', handleFileChange, true);

      // 监听音乐和配音组件的变更
      document.addEventListener('click', handleMediaChange, true);

      isInitialized.value = true;
    } catch (error) {
      console.error('🚀 ADI-LOG ~ 数据保护功能初始化失败:', error);
    }
  };

  /**
   * 清理数据保护功能
   * @description 移除事件监听器，清理资源
   */
  const cleanupUnsavedChanges = (): void => {
    try {
      // 移除 beforeunload 事件监听器
      if (beforeUnloadHandler) {
        window.removeEventListener('beforeunload', beforeUnloadHandler);
        beforeUnloadHandler = null;
      }

      // 移除表单变更事件监听器
      document.removeEventListener('input', handleFormChange, true);
      document.removeEventListener('change', handleFormChange, true);
      document.removeEventListener('click', handleFileChange, true);
      document.removeEventListener('click', handleMediaChange, true);

      // 取消防抖函数
      debouncedChangeHandler.cancel();

      // 清理静默期定时器
      if (silentPeriodTimer) {
        clearTimeout(silentPeriodTimer);
        silentPeriodTimer = null;
      }

      // 重置状态
      isSilentPeriod.value = false;
      isInitialized.value = false;
      console.log('🚀 ADI-LOG ~ 数据保护功能清理完成');
    } catch (error) {
      console.error('🚀 ADI-LOG ~ 数据保护功能清理失败:', error);
    }
  };

  /**
   * 重置未保存状态
   * @description 当用户保存数据后调用，重置未保存状态
   */
  const resetUnsavedChanges = (): void => {
    hasUnsavedChanges.value = false;

    // 清除之前的静默期定时器（如果存在）
    if (silentPeriodTimer) {
      clearTimeout(silentPeriodTimer);
    }

    // 设置静默期，防止保存操作后的 UI 更新触发误报
    isSilentPeriod.value = true;

    // 1秒后结束静默期
    silentPeriodTimer = setTimeout(() => {
      isSilentPeriod.value = false;
      silentPeriodTimer = null;
      console.log('🚀 ADI-LOG ~ 静默期结束，恢复表单变更监听');
    }, 1000);

    console.log('🚀 ADI-LOG ~ 未保存状态已重置，进入1秒静默期');
  };

  /**
   * 手动设置未保存状态
   * @description 允许外部手动设置未保存状态
   * @param value - 是否有未保存的更改
   */
  const setUnsavedChanges = (value: boolean): void => {
    hasUnsavedChanges.value = value;
    console.log(`🚀 ADI-LOG ~ 手动设置未保存状态为: ${value}`);
  };

  /**
   * 重新激活数据保护功能
   * @description 专门用于 KeepAlive 场景，当组件从缓存中激活时调用
   */
  const reactivateUnsavedChanges = (): void => {
    // 如果已经初始化，先清理再重新初始化
    if (isInitialized.value) {
      cleanupUnsavedChanges();
    }
    initializeUnsavedChanges();
    console.log('🚀 ADI-LOG ~ 数据保护功能已重新激活（KeepAlive 场景）');
  };

  // ============= 生命周期 =============

  /**
   * 组件挂载时自动初始化
   */
  onMounted(() => {
    initializeUnsavedChanges();
  });

  /**
   * 组件销毁前自动清理
   */
  onBeforeUnmount(() => {
    cleanupUnsavedChanges();
  });

  /**
   * KeepAlive 组件激活时重新初始化
   * @description 当组件从缓存中激活时，重新注册事件监听器
   */
  onActivated(() => {
    // 如果组件已经初始化过，重新激活时需要重新注册事件监听器
    if (!isInitialized.value) {
      initializeUnsavedChanges();
    }
  });

  /**
   * KeepAlive 组件停用时清理
   * @description 当组件被缓存时，清理事件监听器以避免内存泄漏
   */
  onDeactivated(() => {
    cleanupUnsavedChanges();
    console.log('🚀 ADI-LOG ~ KeepAlive 组件停用，数据保护功能已清理');
  });

  // ============= 返回接口 =============

  return {
    // 响应式状态
    hasUnsavedChanges,
    isInitialized,

    // 公共方法
    initializeUnsavedChanges,
    cleanupUnsavedChanges,
    resetUnsavedChanges,
    setUnsavedChanges,
    reactivateUnsavedChanges,
  };
}

/**
 * @description useUnsavedChanges 返回值类型定义
 */
export interface UseUnsavedChangesReturn {
  /** 是否有未保存的更改 */
  hasUnsavedChanges: Ref<boolean>;
  /** 是否已初始化事件监听器 */
  isInitialized: Ref<boolean>;
  /** 初始化数据保护功能 */
  initializeUnsavedChanges: () => void;
  /** 清理数据保护功能 */
  cleanupUnsavedChanges: () => void;
  /** 重置未保存状态 */
  resetUnsavedChanges: () => void;
  /** 手动设置未保存状态 */
  setUnsavedChanges: (value: boolean) => void;
  /** 重新激活数据保护功能（KeepAlive 场景） */
  reactivateUnsavedChanges: () => void;
}
