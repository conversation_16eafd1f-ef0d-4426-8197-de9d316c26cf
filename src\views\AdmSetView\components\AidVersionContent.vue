<template>
  <div class="aid-version">
    <div class="aid-version__form">
      <fa-form layout="vertical">
        <fa-form-item label="输入aid（可选，不输入则默认设置当前aid）">
          <fa-input
            v-model:value="aidValue"
            :allowClear="true"
            placeholder="请输入aid"
          />
        </fa-form-item>
        <fa-form-item label="选择版本">
          <fa-select v-model:value="aidVersionValue" placeholder="请选择版本">
            <fa-select-option
              v-for="option in versionOptions"
              :key="option.value"
              :value="option.value"
            >
              {{ option.label }}
            </fa-select-option>
          </fa-select>
        </fa-form-item>
        <fa-form-item>
          <fa-button type="primary" @click="handleSubmit">确认设置</fa-button>
        </fa-form-item>
      </fa-form>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import { message } from '@fk/faicomponent';
import { VERSION, VERSION_NAME } from '@/constants/version';
import { setVersion, setVersionByAid } from '@/api/AdmSetView';

// 获取实际的版本选项
const versionOptions = [
  { label: VERSION_NAME[VERSION.FREE], value: VERSION.FREE },
  { label: VERSION_NAME[VERSION.BASIC], value: VERSION.BASIC },
  { label: VERSION_NAME[VERSION.PRO], value: VERSION.PRO },
];

/**
 * @description aid输入值
 */
const aidValue = ref<string>('');

/**
 * @description aid版本设置的值
 */
const aidVersionValue = ref<VERSION>(VERSION.FREE);

/**
 * @description 提交表单
 */
const handleSubmit = async () => {
  if (aidValue.value) {
    const [err] = await setVersionByAid(
      Number(aidValue.value),
      aidVersionValue.value,
    );
    if (err) {
      console.error(err);
      message.error(err?.message || '设置失败');
      return;
    }
  } else {
    const [err] = await setVersion(aidVersionValue.value);
    if (err) {
      console.error(err);
      message.error(err?.message || '设置失败');
      return;
    }
  }

  message.success('设置成功');
};
</script>

<style scoped>
.aid-version {
  /* 布局相关 */
  @apply flex flex-col;
}

.aid-version__form {
  /* 尺寸相关 */
  @apply max-w-400px;
}

.aid-version__form-item-tip {
  /* 文字相关 */
  @apply text-12px text-#999 mb-8px;
}
</style>
