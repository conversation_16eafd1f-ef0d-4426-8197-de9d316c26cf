<?xml version="1.0" encoding="utf-8"?>
<!-- Generator: Adobe Illustrator 26.0.0, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->
<svg version="1.1" id="eOWreKEMpjI1" shape-rendering="geometricPrecision" text-rendering="geometricPrecision"
	 xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" viewBox="0 0 672 672"
	 enable-background="new 0 0 672 672" xml:space="preserve">
<style><![CDATA[
/* 动画样式 */
#eOWreKEMpjI3_to {
  animation: shape1_move 2s ease-in-out infinite alternate;
}

#eOWreKEMpjI3_to path {
  filter: blur(60px);
  animation: shape1_blur 2s ease-in-out infinite alternate;
}

#eOWreKEMpjI4_to {
  animation: shape2_move 2s ease-in-out infinite alternate;
}

#eOWreKEMpjI4_tr {
  animation: shape2_rotate 2s ease-in-out infinite alternate;
}

#eOWreKEMpjI4_ts {
  animation: shape2_scale 2s ease-in-out infinite alternate;
}

#eOWreKEMpjI4 {
  filter: blur(42px);
  animation: shape2_blur 2s ease-in-out infinite alternate;
}

#eOWreKEMpjI5_to {
  animation: shape3_move 2s ease-in-out infinite alternate;
}

#eOWreKEMpjI5_ts {
  animation: shape3_scale 2s ease-in-out infinite alternate;
}

#eOWreKEMpjI5 {
  filter: blur(70px);
  animation: shape3_morph 2s ease-in-out infinite alternate, shape3_blur 2s ease-in-out infinite alternate;
}

#eOWreKEMpjI6_to {
  animation: shape4_move 2s ease-in-out infinite alternate;
}

#eOWreKEMpjI6 {
  filter: blur(50px);
  animation: shape4_opacity 2s ease-in-out infinite alternate;
}

#eOWreKEMpjI7_to {
  animation: shape5_move 2s ease-in-out infinite alternate;
}

#eOWreKEMpjI7 {
  filter: blur(70px);
  animation: shape5_morph 2s ease-in-out infinite alternate, shape5_opacity 2s ease-in-out infinite alternate, shape5_blur 2s ease-in-out infinite alternate;
}

#eOWreKEMpjI8_to {
  animation: shape6_move 2s ease-in-out infinite alternate;
}

#eOWreKEMpjI8 {
  filter: blur(60px);
  animation: shape6_opacity 2s ease-in-out infinite alternate;
}

/* 关键帧动画 */
@keyframes shape1_move {
  0% { transform: translate(-16.319523px, 572.42553px); }
  100% { transform: translate(246.973878px, 313.049097px); }
}

@keyframes shape1_blur {
  0% { filter: blur(60px); }
  100% { filter: blur(85px); }
}

@keyframes shape2_move {
  0% { transform: translate(596.061953px, 491.881433px); }
  100% { transform: translate(723.752142px, 345.668228px); }
}

@keyframes shape2_rotate {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(-20deg); }
}

@keyframes shape2_scale {
  0% { transform: scale(1.469612, 0.933168); }
  100% { transform: scale(2.408517, 0.745995); }
}

@keyframes shape2_blur {
  0% { filter: blur(42px); }
  100% { filter: blur(85px); }
}

@keyframes shape3_move {
  0% { transform: translate(635.724594px, 513.717239px); }
  100% { transform: translate(332.637262px, 837.423027px); }
}

@keyframes shape3_scale {
  0% { transform: scale(1, 1); }
  100% { transform: scale(1.179228, 1.179228); }
}

@keyframes shape3_morph {
  0% {
    d: path('M120,103.7c-81.4,98.6-229.3,195.6-296.1,140.4c-22.7-18.8-42.2-86.1-18-151.9c23.6-64.4,4.8-165.5,106-289.9c80.7-99.2,191.4-63.6,258.3-8.4S201.5,5.1,120,103.7z');
  }
  100% {
    d: path('M180,140c-100,120-270,210-340,160c-28-22-48-95-22-170c28-70,8-180,120-320c90-110,210-75,280-12S240,35,180,140z');
  }
}

@keyframes shape3_blur {
  0% { filter: blur(70px); }
  100% { filter: blur(113px); }
}

@keyframes shape4_move {
  0% { transform: translate(122.815871px, -59.678658px); }
  100% { transform: translate(105.808081px, -319.853465px); }
}

@keyframes shape4_opacity {
  0% { opacity: 0.77; }
  100% { opacity: 0.45; }
}

@keyframes shape5_move {
  0% { transform: translate(455.394663px, 735.18885px); }
  100% { transform: translate(641.88915px, 431.307445px); }
}

@keyframes shape5_morph {
  0% {
    d: path('M143.5-129.6c116.6,41,143,108.6,98.4,188.1c-44.6,79.5-242.4,124.7-359,83.7S-292,3.7-247.5-75.7c18.3-32.7,51.2-57.2,91.7-71.8C-97.8-168.6,74.9-153.7,143.5-129.6z');
  }
  100% {
    d: path('M180-90c135,50,170,135,120,220c-55,90-280,145-400,95S-330,40-275-45c22-38,60-65,110-80C-105-190,105-115,180-90z');
  }
}

@keyframes shape5_opacity {
  0% { opacity: 0.89; }
  100% { opacity: 0.37; }
}

@keyframes shape5_blur {
  0% { filter: blur(70px); }
  100% { filter: blur(113px); }
}

@keyframes shape6_move {
  0% { transform: translate(58.889769px, 313.049097px); }
  100% { transform: translate(21.155229px, 14.095977px); }
}

@keyframes shape6_opacity {
  0% { opacity: 0.72; }
  100% { opacity: 0.45; }
}
]]></style>
<defs>
  <!-- 滤镜定义 -->
  <filter id="blur-filter-42" x="-150%" y="-150%" width="400%" height="400%">
    <feGaussianBlur stdDeviation="42,42" result="result"/>
  </filter>
  <filter id="blur-filter-50" x="-150%" y="-150%" width="400%" height="400%">
    <feGaussianBlur stdDeviation="50,50" result="result"/>
  </filter>
  <filter id="blur-filter-60" x="-150%" y="-150%" width="400%" height="400%">
    <feGaussianBlur stdDeviation="60,60" result="result"/>
  </filter>
  <filter id="blur-filter-70" x="-150%" y="-150%" width="400%" height="400%">
    <feGaussianBlur stdDeviation="70,70" result="result"/>
  </filter>
</defs>
<polygon fill="#3B88FF" points="672,0 672,672 0,672 0,0 "/>
<g id="eOWreKEMpjI3_to" transform="translate(-16.319523,572.42553)">

		<radialGradient id="SVGID_1_" cx="-232.1547" cy="-174.8551" r="201.1689" gradientTransform="matrix(1.1331 0.5942 -1.1892 0.9142 19.5484 299.3073)" gradientUnits="userSpaceOnUse">
		<stop  offset="0" style="stop-color:#5BDAFF"/>
		<stop  offset="1" style="stop-color:#C8F1FF"/>
	</radialGradient>
	<path opacity="0.9" fill="url(#SVGID_1_)" enable-background="new    " d="M108.6,100.8c-119.6,92-269.2,178.6-367.4,127
		c-33.4-17.5-62-80.3-26.5-141.7c34.7-60.1,7.1-154.4,155.8-270.5C-10.9-277,142.5-572,240.8-520.5
		C339.2-469.1,228.4,8.8,108.6,100.8z"/>
</g>
<g id="eOWreKEMpjI4_to" transform="translate(596.061953,491.881433)">
	<g id="eOWreKEMpjI4_tr" transform="rotate(0)">
		<g id="eOWreKEMpjI4_ts" transform="scale(1.469612,0.933168)">

				<radialGradient id="eOWreKEMpjI4_gradient" cx="-879.4792" cy="-218.6268" r="201.1677" gradientTransform="matrix(1.6652 1.3753 -0.7046 0.8531 1286.2366 1397.722)" gradientUnits="userSpaceOnUse">
				<stop  offset="0" style="stop-color:#5BDAFF"/>
				<stop  offset="1" style="stop-color:#C8F1FF"/>
			</radialGradient>

				<path id="eOWreKEMpjI4" opacity="0.59" fill="url(#eOWreKEMpjI4_gradient)" enable-background="new    " d="
				M64.8,103.8c-81.4,98.6-174.1,195.6-240.9,140.4c-22.7-18.8-42.2-86.1-18-151.9c23.6-64.4,4.8-165.5,106-289.9
				C-7.4-296.9,71.1-648.1,138-592.9C204.9-537.7,146.3,5.2,64.8,103.8L64.8,103.8z"/>
		</g>
	</g>
</g>
<g id="eOWreKEMpjI5_to" transform="translate(635.724594,513.717239)">
	<g id="eOWreKEMpjI5_ts" transform="scale(1,1)">

			<radialGradient id="eOWreKEMpjI5_gradient" cx="-1370.0385" cy="-289.8613" r="201.1678" gradientTransform="matrix(0.771 0.6368 -0.8092 0.9797 797.6205 1157.9296)" gradientUnits="userSpaceOnUse">
			<stop  offset="0" style="stop-color:#66D7BC"/>
			<stop  offset="1" style="stop-color:#EAEEF2"/>
		</radialGradient>

			<path id="eOWreKEMpjI5" opacity="0.87" fill="url(#eOWreKEMpjI5_gradient)" enable-background="new    " d="
			M120,103.7c-81.4,98.6-229.3,195.6-296.1,140.4c-22.7-18.8-42.2-86.1-18-151.9c23.6-64.4,4.8-165.5,106-289.9
			c80.7-99.2,191.4-63.6,258.3-8.4S201.5,5.1,120,103.7z"/>
	</g>
</g>
<g id="eOWreKEMpjI6_to" transform="translate(122.815871,-59.678658)">

		<radialGradient id="eOWreKEMpjI6_gradient" cx="-435.4844" cy="68.2109" r="216.0643" gradientTransform="matrix(1.3781 0 0 1.3781 600.1537 -93.9821)" gradientUnits="userSpaceOnUse">
		<stop  offset="0" style="stop-color:#CF5BE3"/>
		<stop  offset="0.6173" style="stop-color:#7746CF"/>
		<stop  offset="1" style="stop-color:#6144CC"/>
	</radialGradient>
	<path id="eOWreKEMpjI6" opacity="0.77" fill="url(#eOWreKEMpjI6_gradient)" enable-background="new    " d="
		M525.5-43.2C461.7,115.2,223.4,250.6,47.7,312.5c-78.8,27.8-211.7,110-267.3,56.1c-58.2-56.1,21-210,21-297.3
		c0-170.8,329.6-139,480.9-167.6C390.6-96.4,559.6-127.6,525.5-43.2z"/>
</g>
<g id="eOWreKEMpjI7_to" transform="translate(455.394663,735.18885)">

		<radialGradient id="eOWreKEMpjI7_gradient" cx="-1430.5607" cy="680.5358" r="235.1854" gradientTransform="matrix(0.4058 -0.7232 0.914 0.3211 -48.6375 -1253.9506)" gradientUnits="userSpaceOnUse">
		<stop  offset="0" style="stop-color:#E3BDFF"/>
		<stop  offset="0.4162" style="stop-color:#D6A4FD"/>
		<stop  offset="1" style="stop-color:#9595FE"/>
	</radialGradient>

		<path id="eOWreKEMpjI7" opacity="0.89" fill="url(#eOWreKEMpjI7_gradient)" enable-background="new    " d="
		M143.5-129.6c116.6,41,143,108.6,98.4,188.1c-44.6,79.5-242.4,124.7-359,83.7S-292,3.7-247.5-75.7c18.3-32.7,51.2-57.2,91.7-71.8
		C-97.8-168.6,74.9-153.7,143.5-129.6z"/>
</g>
<g id="eOWreKEMpjI8_to" transform="translate(58.889769,313.049097)">

		<radialGradient id="eOWreKEMpjI8_gradient" cx="-807.8259" cy="0.1979" r="0.5" gradientTransform="matrix(354.2813 0 0 475.017 286198.2188 -93.9872)" gradientUnits="userSpaceOnUse">
		<stop  offset="0" style="stop-color:#C590FB"/>
		<stop  offset="1" style="stop-color:#F2DDFF"/>
	</radialGradient>

		<path id="eOWreKEMpjI8" opacity="0.72" fill="url(#eOWreKEMpjI8_gradient)" enable-background="new    " d="
		M-176.5-78.2c56.8-109.2,135.1-162.3,234.9-159.2c74.9,2.3,168.3,76.1,89.3,174.2s-35.1,179.5-89.3,244.7s-131.7,83.8-183.3,0
		S-176.5,81-176.5-78.2L-176.5-78.2z"/>
</g>
<!-- 浅暗色遮罩层 -->
<rect width="672" height="672" fill="rgba(0, 0, 0, 0.3)" opacity="1"/>
</svg>
