/**
 * @description useFileUpload composable 测试
 */
import { describe, it, expect, beforeEach, vi } from 'vitest';
import { ref, type Ref } from 'vue';
import { useFileUpload } from '../useFileUpload';
import type {
  FormItemInternal,
  FileInfo,
  DynamicFileFormInstance,
} from '@/views/EditProjectView/types/index';
import { MEDIA_TYPES, FILE_STATUS } from '@/views/EditProjectView/types/index';
import { FILE_TYPES } from '@/constants/fileType';

// Mock dependencies
vi.mock('@fk/faicomponent', () => ({
  message: {
    success: vi.fn(),
    error: vi.fn(),
    warning: vi.fn(),
  },
}));

vi.mock('@/components/MaterialBasicUpload/index', () => ({
  showMaterialBasicUpload: vi.fn(),
}));

vi.mock('@/components/MaterialBasicUpload/utils/index.ts', () => ({
  getMaterialFullUrl: vi.fn((url: string) => `full-${url}`),
}));

vi.mock('@/constants/material', () => ({
  INFO_KEYS: {
    ID: 'id',
    RES_ID: 'resId',
    NAME: 'name',
    URL: 'url',
  },
}));

describe('useFileUpload', () => {
  let fileFormItems: Ref<FormItemInternal[]>;
  let mockDynamicFormFiles: DynamicFileFormInstance;
  let mockFormData: Record<string, unknown>;

  beforeEach(() => {
    // 重置所有 mock
    vi.clearAllMocks();

    // 设置测试数据
    fileFormItems = ref<FormItemInternal[]>([
      {
        prop: 'testFiles',
        maxLength: 3,
        type: 'upload',
        label: '测试文件',
      } as FormItemInternal,
    ]);

    mockFormData = {
      testFiles: [],
    };

    mockDynamicFormFiles = {
      getFormData: vi.fn(() => mockFormData),
      setFormData: vi.fn(data => {
        Object.assign(mockFormData, data);
      }),
      validateField: vi.fn(),
      hasFieldBeenValidated: vi.fn(() => false), // 默认返回false，表示字段未被验证过
      validate: vi.fn(() => Promise.resolve(true)),
      handleReset: vi.fn(),
      clearValidate: vi.fn(),
    } as unknown as DynamicFileFormInstance;
  });

  describe('handleReplace', () => {
    it('应该在用户取消选择时保持原来的失效文件不变', async () => {
      const { handleReplace } = useFileUpload(fileFormItems);

      // 设置初始文件数据，包含一个失效的文件
      const invalidFile: FileInfo = {
        resId: 'res-1',
        name: '失效文件.mp4',
        url: 'invalid-url',
        mediaType: MEDIA_TYPES.VIDEO,
        status: FILE_STATUS.DELETE, // 失效状态
        resType: FILE_TYPES.MP4,
      };

      mockFormData.testFiles = [invalidFile];

      // 模拟 showMaterialBasicUpload 被调用时用户取消选择（不传入文件）
      const { showMaterialBasicUpload } = await import(
        '@/components/MaterialBasicUpload/index'
      );
      (showMaterialBasicUpload as any).mockImplementation(
        ({ onConfirm }: any) => {
          // 模拟用户取消选择，调用 onConfirm 但不传入文件
          onConfirm();
        },
      );

      // 执行替换操作
      handleReplace(
        {
          index: 0,
          prop: 'testFiles',
          mediaType: MEDIA_TYPES.VIDEO,
          formItem: fileFormItems.value![0],
        },
        mockDynamicFormFiles,
      );

      // 验证 setFormData 没有被调用（因为用户取消了选择）
      expect(mockDynamicFormFiles.setFormData).not.toHaveBeenCalled();
    });

    it('应该在用户选择新文件时正确替换失效文件', async () => {
      const { handleReplace } = useFileUpload(fileFormItems);

      // 设置初始文件数据，包含一个失效的文件
      const invalidFile: FileInfo = {
        resId: 'res-1',
        name: '失效文件.mp4',
        url: 'invalid-url',
        mediaType: MEDIA_TYPES.VIDEO,
        status: FILE_STATUS.DELETE,
        resType: FILE_TYPES.MP4,
      };

      mockFormData.testFiles = [invalidFile];

      // 模拟用户选择新文件
      const newFile = {
        data: {
          name: '新文件.mp4',
          url: 'new-url',
          resId: 'res-2',
          fileType: 'mp4',
          extra: {
            cover: 'cover-id',
            coverType: 'webp',
            duration: 120,
          },
        },
      };

      const { showMaterialBasicUpload } = await import(
        '@/components/MaterialBasicUpload/index'
      );
      (showMaterialBasicUpload as any).mockImplementation(
        ({ onConfirm }: any) => {
          // 模拟用户选择新文件
          onConfirm([newFile]);
        },
      );

      // 执行替换操作
      handleReplace(
        {
          index: 0,
          prop: 'testFiles',
          mediaType: MEDIA_TYPES.VIDEO,
          formItem: fileFormItems.value![0],
        },
        mockDynamicFormFiles,
      );

      // 验证新文件被正确替换到原位置
      expect(mockDynamicFormFiles.setFormData).toHaveBeenCalledWith({
        testFiles: [
          {
            name: '新文件.mp4',
            url: 'new-url',
            status: FILE_STATUS.NORMAL,
            mediaType: MEDIA_TYPES.VIDEO,
            resId: 'res-2',
            resType: 'mp4',
            thumbUrl: 'full-cover-id',
            duration: 120,
            coverId: 'cover-id',
            coverType: 'webp',
          },
        ],
      });
    });

    it('应该处理无效索引的情况', async () => {
      const { handleReplace } = useFileUpload(fileFormItems);
      const { message } = await import('@fk/faicomponent');

      mockFormData.testFiles = [];

      // 尝试替换不存在的文件
      handleReplace(
        {
          index: 0,
          prop: 'testFiles',
          mediaType: MEDIA_TYPES.VIDEO,
          formItem: fileFormItems.value![0],
        },
        mockDynamicFormFiles,
      );

      // 验证错误消息
      expect(message.error).toHaveBeenCalledWith(
        '未找到需要替换的素材或索引无效',
      );
    });
  });

  describe('triggerAuto校验机制', () => {
    it('应该在字段未被验证过时不触发验证', async () => {
      const { handleUploadClick } = useFileUpload(fileFormItems);

      // 设置hasFieldBeenValidated返回false（字段未被验证过）
      (mockDynamicFormFiles.hasFieldBeenValidated as any).mockReturnValue(
        false,
      );

      // 模拟文件上传成功
      const { showMaterialBasicUpload } = await import(
        '@/components/MaterialBasicUpload/index'
      );
      (showMaterialBasicUpload as any).mockImplementation(
        ({ onConfirm }: any) => {
          onConfirm([
            {
              data: {
                name: '测试文件.jpg',
                url: 'test-url',
                resId: 'res-1',
                fileType: 'jpg',
              },
            },
          ]);
        },
      );

      handleUploadClick(
        {
          prop: 'testFiles',
          mediaType: MEDIA_TYPES.IMAGE,
          formItem: fileFormItems.value[0],
        },
        mockDynamicFormFiles,
      );

      // 验证validateField没有被调用（因为字段未被验证过）
      expect(mockDynamicFormFiles.validateField).not.toHaveBeenCalled();
    });

    it('应该在字段已被验证过时触发验证', async () => {
      const { handleUploadClick } = useFileUpload(fileFormItems);

      // 设置hasFieldBeenValidated返回true（字段已被验证过）
      (mockDynamicFormFiles.hasFieldBeenValidated as any).mockReturnValue(true);

      // 模拟文件上传成功
      const { showMaterialBasicUpload } = await import(
        '@/components/MaterialBasicUpload/index'
      );
      (showMaterialBasicUpload as any).mockImplementation(
        ({ onConfirm }: any) => {
          onConfirm([
            {
              data: {
                name: '测试文件.jpg',
                url: 'test-url',
                resId: 'res-1',
                fileType: 'jpg',
              },
            },
          ]);
        },
      );

      handleUploadClick(
        {
          prop: 'testFiles',
          mediaType: MEDIA_TYPES.IMAGE,
          formItem: fileFormItems.value[0],
        },
        mockDynamicFormFiles,
      );

      // 验证validateField被调用了（因为字段已被验证过）
      expect(mockDynamicFormFiles.validateField).toHaveBeenCalledWith(
        'testFiles',
      );
    });
  });
});
