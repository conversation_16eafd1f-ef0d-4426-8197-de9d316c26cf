/**
 * @file API 请求参数类型定义
 * @description 该文件集中定义了 EditProjectView 模块后端接口的请求体类型。
 */

import type { WorkTypeValue } from '@/components/WorkListBar/types';

/**
 * 获取模板信息接口 - 请求参数
 */
export interface GetTemplateInfoParams {
  /** 模板ID */
  templateId: number;
}

/**
 * 获取项目编辑信息接口 - 请求参数
 */
export interface GetEditInfoParams {
  /** 项目ID */
  id?: number;
  /** 模板ID */
  templateId?: number;
}

/**
 * 项目更新接口 - 资源上传项
 */
export interface UpdateProjectResUploadItem {
  /** 上传ID */
  uploadId: string;
  /** 上传类型 */
  uploadType: number;
  /** 封面ID */
  coverId: string;
  /** 封面类型 */
  coverType: number;
  /** 时长-视频才有 */
  duration?: number;
}

/**
 * 项目更新接口 - 资源表单项
 */
export interface UpdateProjectResFormItem {
  /** 表单项ID */
  id: number;
  /** 资源上传项列表 */
  resIds: UpdateProjectResUploadItem[];
  /** 是否修改嘴型 */
  modifyMouth?: boolean;
}

/**
 * 项目更新接口 - 背景音乐设置
 */
export interface UpdateProjectBgmSetting {
  /** 是否开启 */
  open: boolean;
  /** 是否自动 */
  auto: boolean;
  /** 资源ID列表 */
  resIds: string[];
}

/**
 * 项目更新接口 - 配音设置
 */
export interface UpdateProjectVoiceSetting {
  /** 是否自动 */
  auto: boolean;
  /** 配音类型 */
  voiceType: string;
  /** 额外类型 */
  extraType?: string;
}

/**
 * 项目更新接口 - 项目设置
 */
export interface UpdateProjectSetting {
  /** 背景音乐设置 */
  bgm: UpdateProjectBgmSetting;
  /** 配音设置 */
  voice: UpdateProjectVoiceSetting;
}

/**
 * 项目更新接口 - 请求参数
 */
export interface UpdateProjectParams {
  /** 项目ID */
  id: number;
  /** 模板ID，新建项目时必传 */
  templateId?: number;
  /** 输入表单数据（JSON字符串格式） */
  inputForm: string;
  /** 资源表单数据 */
  resFormList: UpdateProjectResFormItem[];
  /** 项目设置 */
  setting: UpdateProjectSetting;
  /** 幂等性唯一标识，用于防重复提交 */
  uniqueIdForModify?: string;
}

/**
 * 生成项目预览参数接口
 */
export interface GeneratePreviewParams {
  /** 项目ID，新项目可能没有 */
  projectId?: number;
  /** 项目类型 */
  projectType?: WorkTypeValue;
  /** 表单数据 */
  formData: Record<string, unknown>;
  /** 生成数量 */
  count: number;
}

// ============= 作品相关请求参数 =============

/**
 * 获取作品列表请求参数
 */
export interface GetWorkListParams {
  /** 项目ID */
  projectId: number;
  /** 分页大小 */
  limit: number;
  /** 当前页码 */
  pageNow: number;
  /** 可选：指定作品ID列表，用于轮询更新特定作品 */
  idList?: number[];
  /** 排序字段 */
  sortKey?: string;
  /** 是否降序 */
  desc?: boolean;
  /** 查看模式：1-编辑器弹窗，2-预览 */
  viewMode?: number;
}

/**
 * 轮询专用接口请求参数
 * @description 用于 /api/work/getListById 接口的参数定义
 */
export interface GetWorkListByIdParams {
  /** 项目ID（必填） */
  projectId: number;
  /** 作品ID列表（必填，数组格式） */
  idList: number[];
}

/**
 * 获取作品详情请求参数
 */
export interface GetWorkInfoParams {
  /** 作品ID */
  id: number;
  /** 查看模式：1-编辑器弹窗，2-预览 */
  viewMode?: number;
}

/**
 * 保存作品到作品库请求参数
 */
export interface SaveWorksParams {
  /** 作品ID列表 */
  workIds: number[];
  /** 项目ID（必填） */
  projectId: number;
  /** 是否全选（用于分页场景下的全选操作） */
  isAll?: boolean;
}

/**
 * 删除作品请求参数
 */
export interface DeleteWorksParams {
  /** 作品ID列表 */
  workIds: number[];
}

/**
 * 更新作品标识请求参数
 * @description 用于调用作品标识更新接口的参数定义，告知后端该作品已被查看
 * @see {@link updateWorkFlag} - 对应的API接口函数
 */
export interface UpdateWorkFlagParams {
  /**
   * 作品ID
   * @description 需要更新标识的作品唯一标识，必须为正整数
   * @example 123
   */
  id: number;
}

/**
 * 获取消耗点数请求参数
 * @description 用于调用消耗点数查询接口的参数定义
 */
export interface GetConsumePointParams {
  /**
   * 消费点类型列表
   * @description 消费点类型列表，多个类型用逗号分隔
   * @example "1,3" 表示AI生成视频和AI改嘴型
   */
  typeList: string;
  /**
   * 创建数量
   * @description 需要创建的作品数量
   * @example 1
   */
  createNum: number;
}

/**
 * 获取唯一ID请求参数
 * @description 用于获取幂等性唯一标识的接口参数定义
 */
export interface GetUniqueIdParams {
  // 该接口无需参数
}
