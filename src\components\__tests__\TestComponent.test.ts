/**
 * TestComponent 组件测试
 * 验证 Vue 组件测试环境是否正常工作
 */

import { describe, it, expect, beforeEach } from 'vitest';
import { createWrapper, nextTick, sleep } from '@/test/utils';
import TestComponent from './TestComponent.vue';
import type { Wrapper } from '@vue/test-utils';
import type Vue from 'vue';

describe('TestComponent', () => {
  let wrapper: Wrapper<Vue>;

  beforeEach(() => {
    wrapper = createWrapper(TestComponent);
  });

  describe('组件渲染', () => {
    it('应该正确渲染默认内容', () => {
      expect(wrapper.find('.test-component__title').text()).toBe('默认标题');
      expect(wrapper.find('.test-component__content').text()).toBe('默认内容');
      expect(wrapper.find('.test-component__button').text()).toBe('点击我');
    });

    it('应该正确渲染传入的 props', () => {
      wrapper = createWrapper(TestComponent, {
        propsData: {
          title: '自定义标题',
          content: '自定义内容',
        },
      });

      expect(wrapper.find('.test-component__title').text()).toBe('自定义标题');
      expect(wrapper.find('.test-component__content').text()).toBe(
        '自定义内容',
      );
    });

    it('初始状态下不应该显示消息', () => {
      expect(wrapper.find('.test-component__message').exists()).toBe(false);
    });
  });

  describe('用户交互', () => {
    it('点击按钮应该触发加载状态', async () => {
      const button = wrapper.find('.test-component__button');

      await button.trigger('click');

      expect(wrapper.vm.$data.loading).toBe(true);
      expect(button.text()).toBe('加载中...');
      expect(button.attributes('disabled')).toBeDefined();
    });

    it('点击按钮应该在完成后显示成功消息', async () => {
      const button = wrapper.find('.test-component__button');

      await button.trigger('click');

      // 等待异步操作完成
      await sleep(1100);
      await nextTick();

      expect(wrapper.vm.$data.loading).toBe(false);
      expect(wrapper.vm.$data.showMessage).toBe(true);
      expect(wrapper.vm.$data.message).toBe('操作成功！');
      expect(wrapper.find('.test-component__message').text()).toBe(
        '操作成功！',
      );
    });

    it('点击按钮应该触发 success 事件', async () => {
      const button = wrapper.find('.test-component__button');

      await button.trigger('click');
      await sleep(1100);
      await nextTick();

      const emittedEvents = wrapper.emitted('success');
      expect(emittedEvents).toBeTruthy();
      if (emittedEvents) {
        expect(emittedEvents[0][0]).toEqual({ message: '操作成功！' });
      }
    });
  });

  describe('组件方法', () => {
    it('reset 方法应该重置组件状态', async () => {
      // 先设置一些状态
      await wrapper.setData({
        loading: true,
        showMessage: true,
        message: '测试消息',
      });

      await nextTick();

      // 调用 reset 方法
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      (wrapper.vm as any).reset();

      await nextTick();

      expect(wrapper.vm.$data.loading).toBe(false);
      expect(wrapper.vm.$data.showMessage).toBe(false);
      expect(wrapper.vm.$data.message).toBe('');
    });
  });

  describe('组件状态', () => {
    it('loading 状态应该正确控制按钮状态', async () => {
      const button = wrapper.find('.test-component__button');

      // 初始状态
      expect(button.attributes('disabled')).toBeUndefined();
      expect(button.text()).toBe('点击我');

      // 设置 loading 状态
      await wrapper.setData({ loading: true });
      await nextTick();

      expect(button.attributes('disabled')).toBeDefined();
      expect(button.text()).toBe('加载中...');
    });

    it('showMessage 状态应该正确控制消息显示', async () => {
      // 初始状态
      expect(wrapper.find('.test-component__message').exists()).toBe(false);

      // 显示消息
      await wrapper.setData({
        showMessage: true,
        message: '测试消息',
      });
      await nextTick();

      expect(wrapper.find('.test-component__message').exists()).toBe(true);
      expect(wrapper.find('.test-component__message').text()).toBe('测试消息');
    });
  });

  describe('组件样式', () => {
    it('应该包含正确的 CSS 类', () => {
      expect(wrapper.find('.test-component').exists()).toBe(true);
      expect(wrapper.find('.test-component__title').exists()).toBe(true);
      expect(wrapper.find('.test-component__content').exists()).toBe(true);
      expect(wrapper.find('.test-component__button').exists()).toBe(true);
    });
  });

  describe('错误处理', () => {
    it('应该能够处理异步操作错误', async () => {
      // 使用 setData 方法设置组件状态来模拟错误情况
      await wrapper.setData({
        loading: true,
        message: '操作失败！',
        showMessage: true,
      });

      await nextTick();

      expect(wrapper.vm.$data.message).toBe('操作失败！');
      expect(wrapper.vm.$data.showMessage).toBe(true);
      expect(wrapper.vm.$data.loading).toBe(true);

      // 模拟触发错误事件
      wrapper.vm.$emit('error', { error: new Error('模拟错误') });
      expect(wrapper.emitted('error')).toBeTruthy();
    });
  });
});
