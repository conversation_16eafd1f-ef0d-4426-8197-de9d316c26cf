<!-- 两种情况会跳转到无权限页面 -->
<!-- 1. 员工被boss账号停用了 -->
<!-- 2. 员工没有停用，但是没速创权限（即在成员权限页面中关掉权限） -->
<template>
  <div class="no-permission">
    <div class="no-permission__header">
      <img :src="warningIcon" class="no-permission__header-icon" />
      <div class="no-permission__header-title">系统提示</div>
    </div>
    <div class="no-permission__content">
      <img :src="lockIcon" class="no-permission__content-icon" />
      <p class="no-permission__content-title">该账号没有配置速创权限</p>
      <p class="no-permission__content-subtitle">
        请登录boss账号或联系boss添加速创权限
      </p>
      <fa-button
        class="mt-16px"
        type="primary"
        @click="backToLogin"
        size="small"
        >重新登录</fa-button
      >
    </div>
  </div>
</template>

<script lang="ts" setup>
import warningIcon from '@/assets/NoPermission/warning.svg';
import lockIcon from '@/assets/NoPermission/lock.svg';
import store from '@/store';
import { logout } from '@/utils/auth';
import router from '@/router';
import { watch } from 'vue';

// 监听 aid 变化
watch(
  () => store.state.user.aid,
  newAid => {
    if (newAid) {
      router.push('/');
    }
  },
  { immediate: true }, // 立即执行一次
);

const backToLogin = () => {
  logout();
};
</script>

<style scoped>
.no-permission__header {
  /* 尺寸相关 */
  @apply w-full h-70px;
  /* 外观相关 */
  @apply bg-white shadow-[0_1px_6px_#0000000f];
  /* 布局相关 */
  @apply flex items-center justify-center;
}

.no-permission__header-icon {
  /* 尺寸相关 */
  @apply w-20px h-20px;
  /* 布局相关 */
  @apply mr-8px;
}

.no-permission__header-title {
  /* 文字相关 */
  @apply text-20px text-center text-#333 lh-70px;
  /* 布局相关 */
  /* @apply flex-1; */
}

.no-permission__content {
  /* 尺寸相关 */
  @apply w-full;
  /* 布局相关 */
  @apply flex flex-col items-center mt-29vh;
}

.no-permission__content-icon {
  /* 尺寸相关 */
  @apply w-104px h-auto;
  /* 外观相关 */
  @apply mb-24px;
}

.no-permission__content-title {
  /* 文字相关 */
  @apply text-16px text-center text-#333 mb-8px;
}

.no-permission__content-subtitle {
  /* 文字相关 */
  @apply text-12px text-center text-#999;
}
</style>
