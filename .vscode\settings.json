{"editor.formatOnSave": true, "[javascript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[typescript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[vue]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "css.lint.unknownAtRules": "ignore", "scss.lint.unknownAtRules": "ignore", "cssPeek.peekFromLanguages": ["html", "scss", "vue"], "github.copilot.chat.codeGeneration.instructions": [{"text": "Always use Chinese for code comments"}, {"text": "Use Chinese for answering questions"}, {"text": "Use TypeScript for all code generation"}, {"text": "Use Vue2 for all Vue code generation"}, {"text": "Use Unocss for all CSS code generation"}], "github.copilot.chat.testGeneration.instructions": [{"text": "Always use Chinese for code comments"}, {"text": "Always use Chinese for test case names"}, {"text": "Always use vitest in version 0."}], "github.copilot.chat.commitMessageGeneration.instructions": [{"text": "Use Chinese for commit messages"}], "element-helper.language": "zh-CN", "unot.switchMagic": true, "search.exclude": {"docs/ADI/**": true}, "files.exclude": {"docs/ADI/**": false}}