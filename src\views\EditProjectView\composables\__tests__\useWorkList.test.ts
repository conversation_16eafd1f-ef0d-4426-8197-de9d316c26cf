/**
 * @fileoverview useWorkList composable 单元测试
 * @description 测试作品列表管理的可复用 Composable 功能
 */

import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { useVideoWorkList, useImageWorkList } from '../useWorkList';
import { PROJECT_TYPE_VIDEO, PROJECT_TYPE_IMAGE } from '../../constants/index';
import type { VideoWorkItem, ImageWorkItem } from '@/types';

// Mock 依赖模块
vi.mock('@/api/EditProjectView/work', () => ({
  getWorkListWithTransform: vi.fn(),
}));

vi.mock('../usePagination', () => ({
  usePagination: vi.fn(() => ({
    currentPage: { value: 1 },
    pageSize: { value: 5 },
    totalItems: { value: 0 },
    setTotalItems: vi.fn(),
    getPaginationParams: vi.fn(() => ({ pageNow: 1, limit: 5 })),
  })),
}));

vi.mock('../useWorkDetail', () => ({
  useWorkDetail: vi.fn(() => ({
    currentWorkId: { value: -1 },
    currentWork: { value: undefined },
    isLoadingDetail: { value: false },
    fetchWorkDetail: vi.fn(),
    setCurrentWorkId: vi.fn(),
    clearWorkDetailCacheEntry: vi.fn(),
    clearAllWorkDetailCache: vi.fn(),
    updateCurrentWorkProgress: vi.fn(),
  })),
}));

// 导入 mock 后的模块
import { getWorkListWithTransform } from '@/api/EditProjectView/work';
import { usePagination } from '../usePagination';
import { useWorkDetail } from '../useWorkDetail';

// 创建测试数据
const createMockVideoWork = (id: number, progress = 50): VideoWorkItem => ({
  id,
  aid: 1,
  projectId: 123,
  type: PROJECT_TYPE_VIDEO,
  name: `视频作品${id}`,
  coverImg: `cover${id}`,
  duration: 30,
  progress,
  status: 1,
  createTime: '2023-01-01',
  script: {
    title: `测试作品 ${id} 的脚本`,
    hashtags: ['测试', '作品'],
    segments: [],
  },
  setting: {
    bgMusic: { open: false, vol: 50 },
    voice: { open: false, speed: 1 },
    style: [],
  },
});

const createMockImageWork = (id: number): ImageWorkItem => ({
  id,
  aid: 1,
  projectId: 123,
  type: PROJECT_TYPE_IMAGE,
  name: `图文作品${id}`,
  coverImg: `cover${id}`,
  duration: 0,
  progress: 100,
  status: 1,
  createTime: '2023-01-01',
  setting: {
    graphicList: [],
    curMaxId: 0,
  },
});

describe('useWorkList', () => {
  // Mock 函数
  const mockGetWorkListWithTransform = getWorkListWithTransform as ReturnType<
    typeof vi.fn
  >;
  const mockUsePagination = usePagination as ReturnType<typeof vi.fn>;
  const mockUseWorkDetail = useWorkDetail as ReturnType<typeof vi.fn>;

  // Mock 返回值
  let mockPaginationReturn: any;
  let mockWorkDetailReturn: any;

  beforeEach(() => {
    vi.clearAllMocks();

    // 设置 pagination mock 返回值
    mockPaginationReturn = {
      currentPage: { value: 1 },
      pageSize: { value: 5 },
      totalItems: { value: 0 },
      setTotalItems: vi.fn(),
      getPaginationParams: vi.fn(() => ({ pageNow: 1, limit: 5 })),
    };

    // 设置 work detail mock 返回值
    mockWorkDetailReturn = {
      currentWorkId: { value: -1 },
      currentWork: { value: undefined },
      isLoadingDetail: { value: false },
      fetchWorkDetail: vi.fn(),
      setCurrentWorkId: vi.fn(),
      clearWorkDetailCacheEntry: vi.fn(),
      clearAllWorkDetailCache: vi.fn(),
      updateCurrentWorkProgress: vi.fn(),
    };

    mockUsePagination.mockReturnValue(mockPaginationReturn);
    mockUseWorkDetail.mockReturnValue(mockWorkDetailReturn);
  });

  afterEach(() => {
    vi.resetAllMocks();
  });

  describe('useVideoWorkList', () => {
    it('应该正确初始化视频作品列表状态', () => {
      const projectId = 123;
      const result = useVideoWorkList(projectId);

      // 验证返回的接口
      expect(result).toHaveProperty('currentWorkId');
      expect(result).toHaveProperty('isLoading');
      expect(result).toHaveProperty('workList');
      expect(result).toHaveProperty('currentWork');
      expect(result).toHaveProperty('currentProgress');
      expect(result).toHaveProperty('fetchWorkList');
      expect(result).toHaveProperty('selectedWorkIds');
      expect(result).toHaveProperty('handleSelectChange');
      expect(result).toHaveProperty('pageSize');
      expect(result).toHaveProperty('currentPage');
      expect(result).toHaveProperty('totalItems');
      expect(result).toHaveProperty('handlePageChange');
      expect(result).toHaveProperty('isLoadingDetail');
      expect(result).toHaveProperty('switchToWork');
      expect(result).toHaveProperty('clearWorkDetailCacheEntry');
      expect(result).toHaveProperty('clearAllWorkDetailCache');
      expect(result).toHaveProperty('updateCurrentWorkProgress');

      // 验证初始状态
      expect(result.isLoading.value).toBe(false);
      expect(result.workList.value).toEqual([]);
      expect(result.selectedWorkIds.value).toEqual([]);

      // 验证调用了依赖的 composables
      expect(mockUsePagination).toHaveBeenCalledWith(5);
      expect(mockUseWorkDetail).toHaveBeenCalled();
    });
  });

  describe('useImageWorkList', () => {
    it('应该正确初始化图文作品列表状态', () => {
      const projectId = 123;
      const result = useImageWorkList(projectId);

      // 验证返回的接口与视频作品列表相同
      expect(result).toHaveProperty('currentWorkId');
      expect(result).toHaveProperty('fetchWorkList');
      expect(result).toHaveProperty('selectedWorkIds');

      // 验证初始状态
      expect(result.isLoading.value).toBe(false);
      expect(result.workList.value).toEqual([]);
      expect(result.selectedWorkIds.value).toEqual([]);
    });
  });

  describe('fetchWorkList', () => {
    it('应该成功获取作品列表数据', async () => {
      const projectId = 123;
      const mockWorkList = [createMockVideoWork(1), createMockVideoWork(2)];

      // Mock API 成功响应
      mockGetWorkListWithTransform.mockResolvedValue([
        null,
        {
          workList: mockWorkList,
          totalSize: 2,
          total: 2,
          projectStatus: 1,
          sucNum: 2,
        },
      ]);

      const result = useVideoWorkList(projectId);
      const [error, response] = await result.fetchWorkList();

      // 验证 API 调用
      expect(mockGetWorkListWithTransform).toHaveBeenCalledWith({
        projectId,
        limit: 5,
        pageNow: 1,
      });

      // 验证返回结果
      expect(error).toBeNull();
      expect(response).toBeNull();

      // 验证状态更新
      expect(result.workList.value).toEqual(mockWorkList);
      expect(mockPaginationReturn.setTotalItems).toHaveBeenCalledWith(2);
      expect(result.isLoading.value).toBe(false);
    });

    it('应该在有作品时自动选中第一个作品', async () => {
      const projectId = 123;
      const mockWorkList = [createMockVideoWork(1), createMockVideoWork(2)];

      mockGetWorkListWithTransform.mockResolvedValue([
        null,
        {
          workList: mockWorkList,
          totalSize: 2,
          total: 2,
          projectStatus: 1,
          sucNum: 2,
        },
      ]);

      const result = useVideoWorkList(projectId);
      await result.fetchWorkList();

      // 验证自动选中第一个作品
      expect(result.selectedWorkIds.value).toEqual([1]);
      expect(mockWorkDetailReturn.setCurrentWorkId).toHaveBeenCalledWith(1);
      expect(mockWorkDetailReturn.fetchWorkDetail).toHaveBeenCalledWith(1);
    });

    it('应该处理 API 错误', async () => {
      const projectId = 123;
      const mockError = new Error('获取作品列表失败');

      mockGetWorkListWithTransform.mockResolvedValue([mockError, null]);

      const result = useVideoWorkList(projectId);
      const [error, response] = await result.fetchWorkList();

      // 验证错误处理
      expect(error).toBe(mockError);
      expect(response).toBeNull();
      expect(result.isLoading.value).toBe(false);
    });

    it('应该处理空数据响应', async () => {
      const projectId = 123;

      mockGetWorkListWithTransform.mockResolvedValue([null, null]);

      const result = useVideoWorkList(projectId);
      const [error, response] = await result.fetchWorkList();

      // 验证错误处理
      expect(error).toBeInstanceOf(Error);
      expect(error?.message).toBe('获取作品列表失败');
      expect(response).toBeNull();
      expect(result.isLoading.value).toBe(false);
    });

    it('应该在加载过程中设置 loading 状态', async () => {
      const projectId = 123;
      let resolvePromise: (value: any) => void;
      const promise = new Promise(resolve => {
        resolvePromise = resolve;
      });

      mockGetWorkListWithTransform.mockReturnValue(promise);

      const result = useVideoWorkList(projectId);
      const fetchPromise = result.fetchWorkList();

      // 验证加载状态
      expect(result.isLoading.value).toBe(true);

      // 完成请求
      resolvePromise!([
        null,
        { workList: [], totalSize: 0, total: 0, projectStatus: 1, sucNum: 0 },
      ]);
      await fetchPromise;

      // 验证加载状态重置
      expect(result.isLoading.value).toBe(false);
    });
  });

  describe('handlePageChange', () => {
    it('应该更新页码并重新获取数据', async () => {
      const projectId = 123;
      const mockWorkList = [createMockVideoWork(1)];

      mockGetWorkListWithTransform.mockResolvedValue([
        null,
        {
          workList: mockWorkList,
          totalSize: 1,
          total: 1,
          projectStatus: 1,
          sucNum: 1,
        },
      ]);

      const result = useVideoWorkList(projectId);
      const [error, response] = await result.handlePageChange(2);

      // 验证页码更新
      expect(mockPaginationReturn.currentPage.value).toBe(2);

      // 验证重新获取数据
      expect(mockGetWorkListWithTransform).toHaveBeenCalled();
      expect(error).toBeNull();
      expect(response).toBeNull();
    });
  });

  describe('handleSelectChange', () => {
    it('应该更新选中的作品ID列表', async () => {
      const projectId = 123;
      const result = useVideoWorkList(projectId);

      await result.handleSelectChange([1, 2, 3]);

      // 验证选中状态更新
      expect(result.selectedWorkIds.value).toEqual([1, 2, 3]);
    });

    it('应该在有选中作品时切换到第一个作品', async () => {
      const projectId = 123;
      const result = useVideoWorkList(projectId);

      await result.handleSelectChange([2, 3]);

      // 验证切换到第一个选中的作品
      expect(mockWorkDetailReturn.setCurrentWorkId).toHaveBeenCalledWith(2);
      expect(mockWorkDetailReturn.fetchWorkDetail).toHaveBeenCalledWith(2);
    });

    it('应该处理空选中列表', async () => {
      const projectId = 123;
      const result = useVideoWorkList(projectId);

      await result.handleSelectChange([]);

      // 验证选中状态清空
      expect(result.selectedWorkIds.value).toEqual([]);
      // 不应该调用切换作品的方法
      expect(mockWorkDetailReturn.setCurrentWorkId).not.toHaveBeenCalled();
    });
  });

  describe('switchToWork', () => {
    it('应该成功切换到指定作品', async () => {
      const projectId = 123;
      const result = useVideoWorkList(projectId);

      const [error, response] = await result.switchToWork(5);

      // 验证切换作品
      expect(mockWorkDetailReturn.setCurrentWorkId).toHaveBeenCalledWith(5);
      expect(mockWorkDetailReturn.fetchWorkDetail).toHaveBeenCalledWith(5);
      expect(error).toBeNull();
      expect(response).toBeNull();
    });

    it('应该处理无效的作品ID', async () => {
      const projectId = 123;
      const result = useVideoWorkList(projectId);

      const [error, response] = await result.switchToWork(0);

      // 验证不处理无效ID
      expect(error).toBeNull();
      expect(response).toBeNull();
      expect(mockWorkDetailReturn.setCurrentWorkId).not.toHaveBeenCalled();
    });

    it('应该处理切换作品时的错误', async () => {
      const projectId = 123;
      const mockError = new Error('获取作品详情失败');

      mockWorkDetailReturn.fetchWorkDetail.mockRejectedValue(mockError);

      const result = useVideoWorkList(projectId);
      const [error, response] = await result.switchToWork(5);

      // 验证错误处理
      expect(error).toBe(mockError);
      expect(response).toBeNull();
    });
  });

  describe('currentProgress computed', () => {
    it('应该为视频作品返回正确的进度', () => {
      const projectId = 123;
      const mockVideoWork = createMockVideoWork(1, 75);

      // 设置当前作品为视频作品
      mockWorkDetailReturn.currentWork.value = mockVideoWork;

      const result = useVideoWorkList(projectId);

      // 验证进度计算
      expect(result.currentProgress.value).toBe(75);
    });

    it('应该为图文作品返回 0 进度', () => {
      const projectId = 123;
      const mockImageWork = createMockImageWork(1);

      // 设置当前作品为图文作品
      mockWorkDetailReturn.currentWork.value = mockImageWork;

      const result = useImageWorkList(projectId);

      // 验证图文作品进度为 0
      expect(result.currentProgress.value).toBe(0);
    });

    it('应该在没有当前作品时返回 0 进度', () => {
      const projectId = 123;

      // 设置当前作品为 undefined
      mockWorkDetailReturn.currentWork.value = undefined;

      const result = useVideoWorkList(projectId);

      // 验证无作品时进度为 0
      expect(result.currentProgress.value).toBe(0);
    });
  });

  describe('缓存管理方法', () => {
    it('应该正确代理 clearWorkDetailCacheEntry 方法', () => {
      const projectId = 123;
      const result = useVideoWorkList(projectId);

      result.clearWorkDetailCacheEntry(5);

      // 验证调用了底层方法
      expect(
        mockWorkDetailReturn.clearWorkDetailCacheEntry,
      ).toHaveBeenCalledWith(5);
    });

    it('应该正确代理 clearAllWorkDetailCache 方法', () => {
      const projectId = 123;
      const result = useVideoWorkList(projectId);

      result.clearAllWorkDetailCache();

      // 验证调用了底层方法
      expect(mockWorkDetailReturn.clearAllWorkDetailCache).toHaveBeenCalled();
    });

    it('应该正确代理 updateCurrentWorkProgress 方法', () => {
      const projectId = 123;
      const result = useVideoWorkList(projectId);

      result.updateCurrentWorkProgress(80);

      // 验证调用了底层方法
      expect(
        mockWorkDetailReturn.updateCurrentWorkProgress,
      ).toHaveBeenCalledWith(80);
    });
  });

  describe('边界情况测试', () => {
    it('应该处理已有选中作品时的列表获取', async () => {
      const projectId = 123;
      const mockWorkList = [createMockVideoWork(1), createMockVideoWork(2)];

      mockGetWorkListWithTransform.mockResolvedValue([
        null,
        {
          workList: mockWorkList,
          totalSize: 2,
          total: 2,
          projectStatus: 1,
          sucNum: 2,
        },
      ]);

      const result = useVideoWorkList(projectId);

      // 预先设置选中的作品
      result.selectedWorkIds.value = [2];

      await result.fetchWorkList();

      // 验证不会重置已有的选中状态
      expect(result.selectedWorkIds.value).toEqual([2]);
      // 不应该自动选中第一个作品
      expect(mockWorkDetailReturn.setCurrentWorkId).not.toHaveBeenCalledWith(1);
    });

    it('应该处理空作品列表', async () => {
      const projectId = 123;

      mockGetWorkListWithTransform.mockResolvedValue([
        null,
        { workList: [], totalSize: 0, total: 0, projectStatus: 1, sucNum: 0 },
      ]);

      const result = useVideoWorkList(projectId);
      await result.fetchWorkList();

      // 验证空列表处理
      expect(result.workList.value).toEqual([]);
      expect(result.selectedWorkIds.value).toEqual([]);
      expect(mockPaginationReturn.setTotalItems).toHaveBeenCalledWith(0);
    });

    it('应该处理 API 返回的错误对象而非 Error 实例', async () => {
      const projectId = 123;
      const mockErrorObj = { message: '网络错误' };

      mockGetWorkListWithTransform.mockResolvedValue([mockErrorObj, null]);

      const result = useVideoWorkList(projectId);
      const [error, response] = await result.fetchWorkList();

      // 验证错误对象转换为 Error 实例
      expect(error).toBeInstanceOf(Error);
      expect(error?.message).toBe('网络错误');
      expect(response).toBeNull();
    });
  });
});
