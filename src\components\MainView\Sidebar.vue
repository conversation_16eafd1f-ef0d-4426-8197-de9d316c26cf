<template>
  <div class="sidebar">
    <fa-menu
      v-show="!isShowSkeleton"
      class="w-[200px] h-full pt-[16px]"
      theme="lighter"
      mode="inline"
      :selectedKeys="selectedKeys"
    >
      <fa-menu-item
        v-for="item in menuItems"
        :key="item.id"
        class="menuItem !pl-[57px]"
        @click="handleClickLink(item)"
      >
        <RouterLink :to="item.link" class="!flex items-center">
          <Icon
            class="text-[#888] mr-[12px] w-[18px] h-[18px]"
            :type="item.iconType"
          />
          <span class="text-subText">{{ item.name }}</span>
        </RouterLink>
      </fa-menu-item>
    </fa-menu>
    <fa-skeleton
      v-show="isShowSkeleton"
      :loading="isShowSkeleton"
      :title="false"
      active
      :paragraph="{ rows: menuItems.length }"
      class="px-[32px] pt-[24px]"
    >
    </fa-skeleton>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import router from '@/router';
import { useSkeleton } from '@/hook/useSkeleton';
import {
  currentIndustry,
  currentScene,
  currentType,
  DEFAULT_INDUSTRY,
  DEFAULT_SCENE,
  isSearch,
  searchTemplate,
  searchValue,
} from '@/hook/useTemplateSearch';
import { PROJECT_TYPE } from '@/constants/project';

const { isShowSkeleton } = useSkeleton();

interface MenuItem {
  id: number;
  name: string;
  iconType: string;
  link: string;
}

const menuItems = ref<MenuItem[]>([
  { id: 1, name: '模板中心', iconType: 'mubanzhongxin', link: '/' },
  { id: 2, name: '我的项目', iconType: 'wodixiangmu', link: '/project' },
  { id: 3, name: '我的作品', iconType: 'wodizuopin', link: '/work' },
  { id: 4, name: '素材库', iconType: 'sucaiku', link: '/meta' },
  { id: 5, name: '企业信息', iconType: 'qiyexinxi', link: '/acct-info' },
]);

const getSelectedKey = (): number | null => {
  const path = router.app.$route.path;
  const selectedItem = menuItems.value.find(
    (item: MenuItem) => item.link === path,
  );
  return selectedItem ? selectedItem.id : null;
};

const selectedKeys = computed(() => {
  const selectedKey = getSelectedKey();
  return selectedKey !== null ? [selectedKey] : [];
});

const handleClickLink = (item: MenuItem) => {
  // 特殊处理模板页逻辑，在模板页且在搜索状态下，再次点模板页链接，重置模板页
  if (item.id === 1 && isSearch.value && router.app.$route.path === item.link) {
    searchValue.value = '';
    currentScene.value = DEFAULT_SCENE;
    currentIndustry.value = DEFAULT_INDUSTRY;
    currentType.value = PROJECT_TYPE.VIDEO;
    searchTemplate();
  }
};
</script>

<style scoped lang="scss">
.menuItem a {
  @apply no-underline;
}
::v-deep {
  .fa-skeleton-content {
    .fa-skeleton-paragraph {
      li {
        width: 136px;
        height: 32px;
      }
    }
  }
  .fa-menu-lighter.fa-menu-inline .fa-menu-item-selected {
    svg,
    span {
      @apply text-primary;
    }
  }
}
</style>
