/**
 * @fileoverview 智能轮询事件相关类型定义
 * @description 定义事件系统中使用的所有类型接口
 */

import { WorkItem } from '@/types';
import { WorkStatusInfo } from '@/constants/workStatus';

/**
 * 基础事件接口
 */
export interface BaseWorkEvent {
  /** 事件类型 */
  type: string;
  /** 作品ID */
  workId: number;
  /** 事件时间戳 */
  timestamp: number;
  /** 触发上下文 */
  context: string;
}

/**
 * 进度变化事件
 */
export interface ProgressChangedEvent extends BaseWorkEvent {
  type: 'progress_changed';
  /** 之前的进度 */
  previousProgress: number;
  /** 当前进度 */
  currentProgress: number;
}

/**
 * 状态变化事件
 */
export interface StatusChangedEvent extends BaseWorkEvent {
  type: 'status_changed';
  /** 之前的完整状态信息 */
  previousStatusInfo: WorkStatusInfo;
  /** 当前的完整状态信息 */
  currentStatusInfo: WorkStatusInfo;
}

/**
 * 数据更新事件
 */
export interface DataUpdatedEvent extends BaseWorkEvent {
  type: 'data_updated';
  /** 更新的作品数据 */
  updatedWorks: WorkItem[];
  /** 更新数量 */
  updateCount: number;
}

/**
 * 联合事件类型
 */
export type WorkEvent =
  | ProgressChangedEvent
  | StatusChangedEvent
  | DataUpdatedEvent;

/**
 * 事件处理器类型
 */
export type EventHandler<T extends WorkEvent = WorkEvent> = (
  event: T,
) => void | Promise<void>;
