# 使用 Unocss 的说明

## 1. 安装 Unocss 插件

为了提高开发效率，建议安装 VS Code 的 Unocss 插件。  
该插件可以提示缩写的样式内容，还能自动补全。  
插件地址：[Unocss - Visual Studio Marketplace](https://marketplace.visualstudio.com/items?itemName=antfu.unocss)

## 2. 颜色配置

项目设计稿规范中的颜色已经在 Unocss 的配置文件 `/uno.config.ts` 中进行了配置。  
如果需要使用规范中的颜色，尽量使用 Unocss 提供的颜色类名。  
这样可以在后续支持深色主题时，通过统一修改配置文件实现全局更新。

示例：

```html
<div class="text-primary">使用主颜色#3261FD的示例文本</div>
```

## 3. 使用方式

Unocss 支持以下两种写法：

### 行内写法

直接在元素的 `class` 属性中添加 Unocss 的类名：

```html
<div class="p-4 text-center text-lg text-gray-700 hover:text-blue-500">
  行内写法示例
</div>
```

### 在 `<style>` 标签中写

在组件的 `<style>` 标签中使用 Unocss 的类名：

```vue
<template>
  <div class="custom-class">Style 标签写法示例</div>
</template>

<style>
/* 使用 Unocss 的类名 */
.custom-class {
  @apply p-4 text-center text-lg text-gray-700 hover:text-blue-500;
}
</style>
```
