import { Module } from 'vuex';
import { RootState } from '@/store';
import { VERSION_NAME, VERSION } from '@/constants/version';
import { getPoint, getUserInfo } from '@/api/user/user';
import { day } from '@/utils/dayjs';

export interface UserState {
  /** 是否为内部用户 */
  isStaff: boolean;
  /** 剩余点数 */
  point: number;
  /** 企业账号（凡科网账号） */
  aAcct: string;
  /** 企业名 */
  acctName: string;
  /** 用户名 */
  staffName: string;
  /** 头像URL */
  avatarUrl: string;
  /** 版本标识 */
  version: VERSION;
  /** 过期时间戳 */
  expireTime: number;
  /** 用户AID标识 */
  aid: number;
  /** 是否有购买权限 */
  buyAuth: boolean;
  /** 是否为boss账号 */
  isBoss: boolean;
}

export const state: UserState = {
  isStaff: false,
  point: 0,
  aAcct: '',
  acctName: '',
  staffName: '',
  avatarUrl: '',
  version: 0,
  expireTime: 0,
  aid: 0,
  buyAuth: false,
  isBoss: false,
};

const mutations = {
  setPoints(state: UserState, point: number) {
    state.point = point;
  },
  setUserInfo(state: UserState, userInfo: Omit<UserState, 'point'>) {
    state.isStaff = userInfo.isStaff;
    state.aAcct = userInfo.aAcct;
    state.acctName = userInfo.acctName;
    state.staffName = userInfo.staffName;
    state.avatarUrl = userInfo.avatarUrl;
    state.version = userInfo.version;
    state.expireTime = userInfo.expireTime;
    state.aid = userInfo.aid;
    state.buyAuth = userInfo.buyAuth;
    state.isBoss = userInfo.isBoss;

    // 保存用户AID到localStorage。作为vuex的缓存key
    localStorage.setItem('user-aid', userInfo.aid?.toString() || '');
  },
};

const actions = {
  /** 更新点数 */
  async updatePoints({
    commit,
  }: {
    commit: (mutation: string, payload: number) => void;
  }) {
    const [err, res] = await getPoint();
    if (err) {
      console.error('获取点数失败:', err);
      return;
    }
    commit('setPoints', res.data.point);
  },
  /** 更新用户信息 */
  async updateUserInfo({
    commit,
    dispatch,
  }: {
    commit: (mutation: string, payload: Omit<UserState, 'point'>) => void;
    dispatch: (action: string) => Promise<void>;
  }) {
    const [err, res] = await getUserInfo();
    if (err) {
      console.error('获取用户信息失败:', err);
      return;
    }
    if (res.data.isFirstOpen) {
      // 如果是第一次开通速创，进行一些初始化操作
      dispatch('updatePoints'); // 更新点数
    }
    commit('setUserInfo', res.data);
  },
};

const getters = {
  /** 当前版本名称 */
  currVerName: (state: UserState) => VERSION_NAME[state.version],
  /** 过期日期 */
  expireDate: (state: UserState) => {
    return day(state.expireTime).format('YYYY-MM-DD');
  },
};

const user: Module<UserState, RootState> = {
  state,
  mutations,
  actions,
  getters,
};

export default user;
