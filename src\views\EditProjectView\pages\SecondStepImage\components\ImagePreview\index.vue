/** * 图片预览组件 * @description 用于预览和展示图片 */
<template>
  <div class="image-preview">
    <div class="image-preview__container">
      <ImageWorkPreview
        :title="workInfo?.script?.title || '标题'"
        :content="contentWithHashtags"
        :imgList="imgListCal"
        :carousel-max-height="dynamicCarouselMaxHeight"
        :enableCarouselControl="true"
        :disableAutoplay="true"
        :showLoading="showLoadingState"
        :loadingPercent="currentProgress"
        :loadingText="loadingText"
        :coverImg="workInfo?.coverImg"
        :showFailed="showFailedState"
        :failedErrorMessage="failedErrorMessage"
        :pinchPoint="workInfo?.pinchPoint"
        titleClass="font-bold text-16px mb-5px"
        contentClass="text-14px"
        class="image-preview__work-preview"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import ImageWorkPreview from '@/components/TemplateView/ImageWorkPreview.vue';
import { ImageWorkItem } from '@/types';
import { computed, ref, watch, nextTick } from 'vue';
import { FILE_TYPES } from '@/constants/fileType';
import {
  getWorkStatusInfo,
  isAnyGeneratingWork,
  getWorkStatusDisplayName,
  isAnyFailedWork,
} from '@/constants/workStatus';
import { getMaterialFullUrl } from '@/components/MaterialBasicUpload/utils/index';
import { formatImageWorkContent } from '@/views/EditProjectView/utils';

interface Props {
  workInfo: ImageWorkItem | undefined;
}

const props = defineProps<Props>();

const imgListCal = computed(() => {
  const graphic = props.workInfo?.data?.graphic || [];
  return graphic.map(item => ({
    resId: item.resId,
    resType: FILE_TYPES.WEBP,
  }));
});

/**
 * 计算拼接后的内容（content + hashtags）
 */
const contentWithHashtags = computed(() => {
  return formatImageWorkContent(props.workInfo?.script, '内容');
});

/**
 * 是否显示加载状态
 */
const showLoadingState = computed(() => {
  if (!props.workInfo) return false;
  const statusInfo = getWorkStatusInfo(props.workInfo);
  return isAnyGeneratingWork(statusInfo);
});

/**
 * 当前进度值
 */
const currentProgress = computed(() => {
  if (!props.workInfo) return 0;
  return Math.max(0, Math.min(100, props.workInfo.progress || 0));
});

/**
 * 加载文本
 */
const loadingText = computed(() => {
  if (!props.workInfo) return ['图片生成中', '请稍后预览'];

  const statusInfo = getWorkStatusInfo(props.workInfo);
  const statusName = getWorkStatusDisplayName(statusInfo);

  if (statusName === '生成中') {
    return ['图片生成中', '请稍后预览'];
  } else if (statusName === '重新生成中') {
    return ['图片重新生成中', '请稍后预览'];
  }

  return ['图片生成中', '请稍后预览'];
});

/**
 * 是否显示失败状态
 */
const showFailedState = computed(() => {
  if (!props.workInfo) return false;
  const statusInfo = getWorkStatusInfo(props.workInfo);
  return isAnyFailedWork(statusInfo);
});

/**
 * 失败错误信息
 */
const failedErrorMessage = computed(() => {
  if (!props.workInfo) return '生成失败';
  return props.workInfo?.errMsg || '生成失败';
});

// 轮播图容器固定宽度（根据当前样式设置）
const CAROUSEL_CONTAINER_WIDTH = 378;

// 第一张图片的尺寸信息
const firstImageSize = ref<{ width: number; height: number } | null>(null);

// 动态计算的轮播图最大高度
const dynamicCarouselMaxHeight = computed(() => {
  // 如果没有图片或第一张图片尺寸信息，使用默认高度
  if (!firstImageSize.value || !imgListCal.value.length) {
    return '100%'; // 保持原有的默认高度
  }

  const { width, height } = firstImageSize.value;

  // 防止除零错误
  if (width <= 0) {
    return '100%';
  }

  // 计算动态高度：固定宽度 × (第一张图片高度 / 第一张图片宽度)
  const aspectRatio = height / width;
  const dynamicHeight = CAROUSEL_CONTAINER_WIDTH * aspectRatio;

  // 设置合理的高度范围限制（最小212px，最大504px）
  const minHeight = 212;
  const maxHeight = 504;
  const clampedHeight = Math.max(minHeight, Math.min(maxHeight, dynamicHeight));

  return `${clampedHeight}px`;
});

/**
 * 加载第一张图片并获取其尺寸
 */
const loadFirstImageSize = async () => {
  if (!imgListCal.value.length) {
    firstImageSize.value = null;
    return;
  }

  const firstImg = imgListCal.value[0];
  if (!firstImg.resId) {
    firstImageSize.value = null;
    return;
  }

  try {
    // 创建图片对象来获取自然尺寸
    const img = new Image();
    const imageUrl = getMaterialFullUrl(
      firstImg.resId,
      FILE_TYPES.WEBP,
      'user',
      400,
    );

    // 使用 Promise 包装图片加载
    await new Promise<void>((resolve, reject) => {
      img.onload = () => {
        firstImageSize.value = {
          width: img.naturalWidth,
          height: img.naturalHeight,
        };
        resolve();
      };

      img.onerror = () => {
        console.warn('第一张图片加载失败，使用默认高度');
        firstImageSize.value = null;
        reject(new Error('图片加载失败'));
      };

      img.src = imageUrl;
    });
  } catch (error) {
    console.warn('获取第一张图片尺寸失败:', error);
    firstImageSize.value = null;
  }
};

// 监听图片列表变化，重新计算高度
watch(
  () => imgListCal.value,
  async newImgList => {
    if (newImgList.length > 0) {
      await nextTick(); // 等待 DOM 更新
      await loadFirstImageSize();
    } else {
      firstImageSize.value = null;
    }
  },
  { immediate: true, deep: true },
);
</script>

<style lang="scss" scoped>
.image-preview {
  /* 布局相关 */
  @apply relative flex flex-col;
  /* 尺寸相关 */
  @apply w-full h-full;
}

.image-preview__container {
  /* 布局相关 */
  @apply relative overflow-hidden flex justify-center items-center;
  /* 尺寸相关 */
  @apply w-full h-full py-0;
  /* 外观相关 */
  @apply bg-[#f3f3f5] rounded-lg border-rd-8px;

  :deep(.el-carousel) {
    @apply max-h-[70.92%];
  }

  :deep(.loading-wrapper) {
    max-width: unset;
    width: 100%;
    height: 100%;
    border-radius: 0;
  }

  .image-preview__work-preview {
    @apply w-378px h-full! rounded-[0] overflow-hidden;

    :deep(.el-carousel__container) {
      @apply h-full;
    }
  }
}
</style>
