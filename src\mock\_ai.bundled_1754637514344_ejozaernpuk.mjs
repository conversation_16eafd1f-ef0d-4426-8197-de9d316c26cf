// src/mock/ai.ts
var mocks = [
  {
    url: "/api/scProf/getPoint2",
    method: "get",
    response: () => {
      return {
        msg: "\u64CD\u4F5C\u6210\u529F",
        rt: 0,
        data: { value: 4399 },
        success: true,
        flow: 1092566038
      };
    }
  }
];
var ai_default = mocks;
export {
  ai_default as default
};
//# sourceMappingURL=data:application/json;base64,ewogICJ2ZXJzaW9uIjogMywKICAic291cmNlcyI6IFsic3JjL21vY2svYWkudHMiXSwKICAic291cmNlc0NvbnRlbnQiOiBbImNvbnN0IF9faW5qZWN0ZWRfZmlsZW5hbWVfXyA9IFwiQzpcXFxcRGVtb25cXFxccHJvamVjdFxcXFxzY1xcXFxzY3BvcnRhbC13ZWJcXFxcc3JjXFxcXG1vY2tcXFxcYWkudHNcIjtjb25zdCBfX2luamVjdGVkX2Rpcm5hbWVfXyA9IFwiQzpcXFxcRGVtb25cXFxccHJvamVjdFxcXFxzY1xcXFxzY3BvcnRhbC13ZWJcXFxcc3JjXFxcXG1vY2tcIjtjb25zdCBfX2luamVjdGVkX2ltcG9ydF9tZXRhX3VybF9fID0gXCJmaWxlOi8vL0M6L0RlbW9uL3Byb2plY3Qvc2Mvc2Nwb3J0YWwtd2ViL3NyYy9tb2NrL2FpLnRzXCI7aW1wb3J0IHsgTW9ja01ldGhvZCB9IGZyb20gJ3ZpdGUtcGx1Z2luLW1vY2snO1xyXG5cclxuY29uc3QgbW9ja3M6IE1vY2tNZXRob2RbXSA9IFtcclxuICB7XHJcbiAgICB1cmw6ICcvYXBpL3NjUHJvZi9nZXRQb2ludDInLFxyXG4gICAgbWV0aG9kOiAnZ2V0JyxcclxuICAgIHJlc3BvbnNlOiAoKSA9PiB7XHJcbiAgICAgIHJldHVybiB7XHJcbiAgICAgICAgbXNnOiAnXHU2NENEXHU0RjVDXHU2MjEwXHU1MjlGJyxcclxuICAgICAgICBydDogMCxcclxuICAgICAgICBkYXRhOiB7IHZhbHVlOiA0Mzk5IH0sXHJcbiAgICAgICAgc3VjY2VzczogdHJ1ZSxcclxuICAgICAgICBmbG93OiAxMDkyNTY2MDM4LFxyXG4gICAgICB9O1xyXG4gICAgfSxcclxuICB9LFxyXG5dO1xyXG5cclxuZXhwb3J0IGRlZmF1bHQgbW9ja3M7XHJcbiJdLAogICJtYXBwaW5ncyI6ICI7QUFFQSxJQUFNLFFBQXNCO0FBQUEsRUFDMUI7QUFBQSxJQUNFLEtBQUs7QUFBQSxJQUNMLFFBQVE7QUFBQSxJQUNSLFVBQVUsTUFBTTtBQUNkLGFBQU87QUFBQSxRQUNMLEtBQUs7QUFBQSxRQUNMLElBQUk7QUFBQSxRQUNKLE1BQU0sRUFBRSxPQUFPLEtBQUs7QUFBQSxRQUNwQixTQUFTO0FBQUEsUUFDVCxNQUFNO0FBQUEsTUFDUjtBQUFBLElBQ0Y7QUFBQSxFQUNGO0FBQ0Y7QUFFQSxJQUFPLGFBQVE7IiwKICAibmFtZXMiOiBbXQp9Cg==
