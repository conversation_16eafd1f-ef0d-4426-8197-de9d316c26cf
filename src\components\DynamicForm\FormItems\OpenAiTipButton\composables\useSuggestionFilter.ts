/**
 * @file 推荐内容过滤逻辑 Composable
 * @description 提供推荐内容的过滤、验证和处理功能
 */

import { computed, type Ref } from 'vue';
import { FIELD_TYPES, COMPONENT_TYPES } from '@/constants/fieldTypes';
import { MAX_TAGS_LIMIT } from '@/components/DynamicForm/constants/index';
import type { FormItemInternal } from '@/views/EditProjectView/types/index';

/**
 * 推荐项数据类型
 */
export type SuggestionData = { value: string } | string;

/**
 * 字段长度信息接口
 */
export interface FieldLengthInfo {
  /** 最大长度限制 */
  maxLength: number;
  /** 当前已使用长度 */
  currentLength: number;
  /** 剩余可用长度 */
  remainingLength: number;
  /** 已选择的项目（仅标签类型） */
  selectedItems: string[];
  /** 当前标签数量（仅标签类型） */
  currentTagCount: number;
  /** 最大标签数量限制（仅标签类型） */
  maxTagCount: number;
}

/**
 * 长度验证结果接口
 */
export interface LengthValidationResult {
  /** 是否通过验证 */
  isValid: boolean;
  /** 当前长度 */
  currentLength: number;
  /** 最大长度 */
  maxLength: number;
  /** 新值长度 */
  newValueLength: number;
  /** 剩余长度 */
  remainingLength: number;
  /** 是否会超出限制 */
  wouldExceedLimit: boolean;
}

/**
 * 推荐内容过滤 Composable
 * @description 提供推荐内容的过滤、验证和处理功能
 * @param suggestions 推荐内容列表
 * @param fieldName 字段名称
 * @param contextData 上下文数据（响应式）
 * @param fieldsConfig 字段配置
 * @returns 过滤和验证相关的方法和计算属性
 */
export function useSuggestionFilter(
  suggestions: Ref<SuggestionData[]>,
  fieldName: string,
  contextData: Ref<Record<string, unknown>>,
  fieldsConfig: FormItemInternal,
) {
  /**
   * 获取推荐项的值
   * @description 从推荐项中提取实际的值内容
   * @param suggestion 推荐项
   * @returns 推荐值
   */
  const getSuggestionValue = (suggestion: SuggestionData): string => {
    if (!suggestion) return '';
    return typeof suggestion === 'object' ? suggestion.value : suggestion;
  };

  /**
   * 获取字段的当前长度信息
   * @description 根据字段类型计算当前已使用的字符长度和剩余可用长度
   * @returns 长度信息对象
   */
  const getFieldLengthInfo = (): FieldLengthInfo => {
    const maxLength = (fieldsConfig.attrs?.maxLength as number) || 1000;

    if (fieldsConfig.type === COMPONENT_TYPES.SELECT_TAGS) {
      const selectedTags = (contextData.value[fieldName] as string[]) || [];
      const currentLength = selectedTags.join('').length;
      const currentTagCount = selectedTags.length;
      return {
        maxLength,
        currentLength,
        remainingLength: maxLength - currentLength,
        selectedItems: selectedTags,
        currentTagCount,
        maxTagCount: MAX_TAGS_LIMIT,
      };
    }

    if (
      fieldsConfig.type === COMPONENT_TYPES.INPUT ||
      fieldsConfig.type === FIELD_TYPES.TEXT
    ) {
      const currentText = (contextData.value[fieldName] as string) || '';
      const currentLength = currentText.length;
      return {
        maxLength,
        currentLength,
        remainingLength: maxLength - currentLength,
        selectedItems: [],
        currentTagCount: 0,
        maxTagCount: 0,
      };
    }

    return {
      maxLength,
      currentLength: 0,
      remainingLength: maxLength,
      selectedItems: [],
      currentTagCount: 0,
      maxTagCount: 0,
    };
  };

  /**
   * 检查推荐项是否应该被过滤
   * @description 根据字段类型和当前状态判断推荐项是否应该显示
   * @param suggestion 推荐项
   * @param lengthInfo 长度信息
   * @returns 是否应该过滤（true表示过滤掉）
   */
  const shouldFilterSuggestion = (
    suggestion: SuggestionData,
    lengthInfo: FieldLengthInfo,
  ): boolean => {
    const value = getSuggestionValue(suggestion);

    // 检查是否已被选择（对标签类型）
    if (fieldsConfig.type === COMPONENT_TYPES.SELECT_TAGS) {
      // 检查标签数量是否已达到限制
      if (lengthInfo.currentTagCount >= lengthInfo.maxTagCount) {
        return true;
      }

      if (lengthInfo.selectedItems.includes(value)) {
        return true;
      }
    }

    // 检查input类型是否已填入相同的标签值
    if (fieldsConfig.type === COMPONENT_TYPES.INPUT) {
      const currentText = (contextData.value[fieldName] as string) || '';
      // 检查当前文本中是否已经包含了这个推荐值
      if (currentText.includes(value)) {
        return true;
      }
    }

    // 移除长度限制过滤：允许显示超过剩余字数的AI推荐词
    // 注释掉原有的长度限制检查，让所有推荐词都能显示
    // if (value.length > lengthInfo.remainingLength) {
    //   return true;
    // }

    return false;
  };

  /**
   * 验证添加新内容是否会超出长度限制
   * @description 统一的长度验证逻辑，支持标签和文本类型
   * @param newValue 要添加的新值
   * @returns 验证结果对象
   */
  const validateLengthLimit = (newValue: string): LengthValidationResult => {
    const lengthInfo = getFieldLengthInfo();
    const newValueLength = newValue.length;
    const wouldExceedLimit =
      lengthInfo.currentLength + newValueLength > lengthInfo.maxLength;

    return {
      isValid: !wouldExceedLimit,
      currentLength: lengthInfo.currentLength,
      maxLength: lengthInfo.maxLength,
      newValueLength,
      remainingLength: lengthInfo.remainingLength,
      wouldExceedLimit,
    };
  };

  /**
   * 过滤后的推荐列表
   * @description 根据字段类型和长度限制过滤推荐内容
   * @returns 过滤后的推荐内容数组
   */
  const filteredSuggestions = computed<SuggestionData[]>(() => {
    if (!suggestions.value.length) return [];

    const lengthInfo = getFieldLengthInfo();

    return suggestions.value.filter(
      suggestion => !shouldFilterSuggestion(suggestion, lengthInfo),
    );
  });

  /**
   * 检查是否应该隐藏推荐按钮
   * @description 根据字段类型判断是否应该隐藏推荐按钮，移除剩余字数限制
   * @returns 是否隐藏推荐按钮
   */
  const shouldHideButton = computed<boolean>(() => {
    const lengthInfo = getFieldLengthInfo();

    // 对于标签类型，检查标签数量是否已达到限制
    if (fieldsConfig.type === COMPONENT_TYPES.SELECT_TAGS) {
      if (lengthInfo.currentTagCount >= lengthInfo.maxTagCount) {
        return true;
      }
    }

    // 移除剩余字数限制：允许在任何情况下显示AI推荐按钮
    // 注释掉原有的剩余长度检查，让推荐按钮始终显示（除非达到标签数量限制）
    // return lengthInfo.remainingLength < 4;

    return false;
  });

  return {
    // 计算属性
    filteredSuggestions,
    shouldHideButton,

    // 方法
    getSuggestionValue,
    getFieldLengthInfo,
    shouldFilterSuggestion,
    validateLengthLimit,
  };
}
