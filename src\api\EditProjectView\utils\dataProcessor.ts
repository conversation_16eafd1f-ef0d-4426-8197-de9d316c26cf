/**
 * @fileoverview 数据处理工具函数
 * @description 提供数据过滤、转换和合并功能
 */

import { transformEditProjectApiDataToProjectData } from './inputDataTransform';
import { FILTER_CONFIG } from '../config';
import VersionController from '@/utils/versionControl';
import type {
  GetTemplateListResponse,
  GetEditInfoResponse,
} from '../types/response';
import type { EditProjectViewData } from '@/views/EditProjectView/types/index';

/**
 * 过滤模板数据
 * @description 过滤掉不需要的输入表单项
 * @param templateData 原始模板数据
 * @returns 过滤后的模板数据
 */
export function filterTemplateData(
  templateData: GetTemplateListResponse,
): GetTemplateListResponse {
  // 创建模板数据的副本，避免修改原始数据
  const filteredData = { ...templateData };

  // 过滤掉inputFormList中不需要的项
  if (filteredData.inputFormList) {
    filteredData.inputFormList = filteredData.inputFormList.filter(
      item => !FILTER_CONFIG.EXCLUDED_INPUT_VARIABLES.includes(item.variable),
    );
  }

  return filteredData;
}

/**
 * 合并项目数据
 * @description 将模板数据和项目数据合并转换为前端格式，并应用版本校验降级处理
 * @param templateData 过滤后的模板数据
 * @param projectData 项目数据（可选）
 * @returns 转换后的完整项目数据
 */
export function mergeProjectData(
  templateData: GetTemplateListResponse,
  projectData?: GetEditInfoResponse,
): EditProjectViewData {
  // 使用现有的转换函数进行数据转换
  const transformedData = transformEditProjectApiDataToProjectData(
    templateData,
    projectData,
  );

  // 如果是从模板新建，则使用接口返回的项目ID
  if (projectData?.id) {
    transformedData.projectId = projectData.id;
  }

  // 应用版本校验降级处理
  transformedData.resForm = VersionController.applyDowngradeToList(
    transformedData.resForm,
    'AI_MOUTH_SHAPE',
  );

  return transformedData;
}
