import { Module } from 'vuex';
import { RootState } from '@/store';
import { File_View_Type_KEY_LIST } from '@/constants/material';
import { FolderData } from '@/types/Material';
import { getSpaceUsage } from '@/api/Material/index.ts';
import { message as FaMessage } from '@fk/faicomponent';

export interface MaterialUploadState {
  // 视图类型
  ownerFileViewType: string;
  // 当前访问的文件夹id
  currentFolder: number;
  // 当前路径列表
  currentPathList: FolderData[];

  /** 内存空间信息 */
  capacityUsedSize: number; // 已使用空间大小 byte
  capacityTotalSize: number; // 总容量大小
}

export const state: MaterialUploadState = {
  ownerFileViewType: File_View_Type_KEY_LIST.LIST,
  currentFolder: 0,
  currentPathList: [] as FolderData[],

  capacityUsedSize: 0, // 已使用空间大小 byte
  capacityTotalSize: 0, // 总容量大小
};

const mutations = {
  /** 设置视图类型 */
  setOwnerFileViewType(state: MaterialUploadState, viewType: string) {
    state.ownerFileViewType = viewType;
  },
  /** 设置当前文件夹 */
  setCurrentFolder(state: MaterialUploadState, folderId: number) {
    state.currentFolder = folderId;
  },
  /** 设置当前路径列表 */
  setCurrentPathList(state: MaterialUploadState, pathList: FolderData[]) {
    state.currentPathList = pathList;
  },
  /** 重置为默认状态 */
  resetToDefault(state: MaterialUploadState) {
    state.ownerFileViewType = File_View_Type_KEY_LIST.LIST;
    state.currentPathList = [];
    state.currentFolder = 0;
  },

  setCapacityUsedSize(state: MaterialUploadState, capacityUsedSize: number) {
    state.capacityUsedSize = capacityUsedSize;
  },
  setCapacityTotalSize(state: MaterialUploadState, capacityTotalSize: number) {
    state.capacityTotalSize = capacityTotalSize;
  },
};

const actions = {
  /** 切换视图类型 */
  toggleViewType({
    commit,
    state,
  }: {
    commit: (mutation: string, payload: string) => void;
    state: MaterialUploadState;
  }) {
    const newViewType =
      state.ownerFileViewType === File_View_Type_KEY_LIST.LIST
        ? File_View_Type_KEY_LIST.TABLE
        : File_View_Type_KEY_LIST.LIST;
    commit('setOwnerFileViewType', newViewType);
  },
  /** 导航到指定文件夹 */
  navigateToFolder(
    { commit }: { commit: (mutation: string, payload: number) => void },
    folderId: number,
  ) {
    commit('setCurrentFolder', folderId);
  },
  /** 设置当前路径列表 */
  setCurrentPathList(
    { commit }: { commit: (mutation: string, payload: FolderData[]) => void },
    pathList: FolderData[],
  ) {
    commit('setCurrentPathList', pathList);
  },
  /** 重置为默认状态 */
  resetToDefault({ commit }: { commit: (mutation: string) => void }) {
    commit('resetToDefault');
  },
  /** 设置已使用空间大小 */
  setCapacityUsedSize(
    { commit }: { commit: (mutation: string, payload: number) => void },
    capacityUsedSize: number,
  ) {
    commit('setCapacityUsedSize', capacityUsedSize);
  },
  /** 设置总容量大小 */
  setCapacityTotalSize(
    { commit }: { commit: (mutation: string, payload: number) => void },
    capacityTotalSize: number,
  ) {
    commit('setCapacityTotalSize', capacityTotalSize);
  },
  /** 更新已用内存空间 */
  async updateSpaceUsage({
    commit,
  }: {
    commit: (mutation: string, payload: number) => void;
  }) {
    const [err, res] = await getSpaceUsage();
    if (err) {
      FaMessage.error(err.message || '更新已用内存空间失败');
      return;
    }
    commit('setCapacityUsedSize', res?.data?.usage);
    commit('setCapacityTotalSize', res?.data?.total);
  },
};

const getters = {
  /** 是否为列表视图 */
  isListView: (state: MaterialUploadState) =>
    state.ownerFileViewType === File_View_Type_KEY_LIST.LIST,
  /** 是否为表格视图 */
  isTableView: (state: MaterialUploadState) =>
    state.ownerFileViewType === File_View_Type_KEY_LIST.TABLE,
};

const materialUpload: Module<MaterialUploadState, RootState> = {
  namespaced: true, // 启用命名空间，支持持久化
  state,
  mutations,
  actions,
  getters,
};

export default materialUpload;
