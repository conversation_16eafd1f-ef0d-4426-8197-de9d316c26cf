/**
 * OpenAI 推荐词组件工具函数
 * @description 提供组件所需的各种工具函数
 */

import { FIELD_TYPES } from '../config/index';

/**
 * 从提示模板中提取字段名
 * @description 解析提示模板字符串，提取所有字段占位符
 * @param template 提示模板字符串
 * @returns 字段名数组
 */
export const extractFieldNamesFromTemplate = (template: string): string[] => {
  if (!template.includes('#')) return [];
  const fieldMatches = template.match(/#([^#]+)#/g) || [];
  return fieldMatches.map(match => match.replace(/#/g, ''));
};

/**
 * 检查是否为提示模板格式
 * @description 判断依赖字段配置是否为提示模板格式
 * @param dependencyFields 依赖字段配置
 * @returns 是否为提示模板格式
 */
export const isTemplateFormat = (dependencyFields: string[]): boolean => {
  return (
    dependencyFields.length === 1 &&
    typeof dependencyFields[0] === 'string'
  );
};

/**
 * 检查字段值是否有效
 * @description 统一的字段值检查逻辑
 * @param value 字段值
 * @returns 是否有效
 */
export const isFieldValueValid = (value: unknown): boolean => {
  return Array.isArray(value)
    ? value.length > 0
    : value !== undefined && value !== null && value !== '';
};

/**
 * 去重推荐词
 * @description 对推荐词数组进行去重处理
 * @param suggestions 原始推荐词数组
 * @param getSuggestionValue 获取推荐词值的函数
 * @returns 去重后的推荐词数组
 */
export const deduplicateSuggestions = (
  suggestions: Array<{ value: string } | string>,
  getSuggestionValue: (suggestion: { value: string } | string) => string,
): Array<{ value: string } | string> => {
  const seen = new Set<string>();
  return suggestions.filter(suggestion => {
    const value = getSuggestionValue(suggestion);
    if (seen.has(value)) return false;
    seen.add(value);
    return true;
  });
};

/**
 * 获取推荐项的显示文本
 * @description 从推荐项中提取用于显示的文本内容
 * @param suggestion 推荐项（字符串或包含value属性的对象）
 * @returns 显示文本
 */
export const getSuggestionText = (
  suggestion: { value: string } | string,
): string => {
  if (!suggestion) return '';
  return typeof suggestion === 'object' ? suggestion.value : suggestion;
};

/**
 * 截断文本
 * @description 根据指定长度截断文本并添加省略号
 * @param text 原始文本
 * @param maxLength 最大长度
 * @returns 截断后的文本
 */
export const truncateText = (text: string, maxLength: number): string => {
  if (!text || text.length <= maxLength) return text;
  return text.slice(0, maxLength) + '...';
};

/**
 * 更新已选择推荐词集合
 * @description 根据当前表单字段的值，更新已选择的推荐词集合，用于过滤显示
 * @param currentValue 当前字段值
 * @param fieldType 字段类型
 * @param cachedSuggestions 缓存的推荐词
 * @param getSuggestionValue 获取推荐词值的函数
 * @returns 已选择的推荐词集合
 */
export const getSelectedSuggestions = (
  currentValue: unknown,
  fieldType: string,
  cachedSuggestions: Array<{ value: string } | string>,
  getSuggestionValue: (suggestion: { value: string } | string) => string,
): Set<string> => {
  const selectedSet = new Set<string>();

  if (fieldType === FIELD_TYPES.SELECT_TAGS) {
    // 标签选择类型：当前值是数组
    if (Array.isArray(currentValue)) {
      currentValue.forEach(tag => {
        if (tag && typeof tag === 'string') {
          selectedSet.add(tag);
        }
      });
    }
  } else if (
    fieldType === FIELD_TYPES.INPUT ||
    fieldType === FIELD_TYPES.TEXTAREA
  ) {
    // 文本输入类型：检查当前文本中是否包含推荐词
    if (currentValue && typeof currentValue === 'string') {
      // 对于文本类型，检查缓存推荐词是否在当前文本中
      cachedSuggestions.forEach(suggestion => {
        const suggestionValue = getSuggestionValue(suggestion);
        if (currentValue.includes(suggestionValue)) {
          selectedSet.add(suggestionValue);
        }
      });
    }
  } else {
    // 其他类型（如 select）：直接比较值
    if (currentValue && typeof currentValue === 'string') {
      selectedSet.add(currentValue);
    }
  }

  return selectedSet;
};

/**
 * 检测依赖字段是否发生变化
 * @description 比较当前依赖字段值与缓存值，检测是否有变化
 * @param otherDependencyFields 其他依赖字段名数组
 * @param contextData 上下文数据
 * @param dependencyValuesCache 依赖字段值缓存
 * @returns 是否有依赖字段发生变化
 */
export const detectDependencyFieldsChange = (
  otherDependencyFields: string[],
  contextData: Record<string, unknown>,
  dependencyValuesCache: Record<string, unknown>,
): boolean => {
  return otherDependencyFields.some(field => {
    const oldValue = dependencyValuesCache[field];
    const newValue = contextData[field];
    return oldValue !== newValue;
  });
};

/**
 * 更新依赖字段值缓存
 * @description 将当前依赖字段的值保存到缓存中，用于后续变化检测
 * @param otherDependencyFields 其他依赖字段名数组
 * @param contextData 上下文数据
 * @param dependencyValuesCache 依赖字段值缓存（引用类型，会被修改）
 */
export const updateDependencyCache = (
  otherDependencyFields: string[],
  contextData: Record<string, unknown>,
  dependencyValuesCache: Record<string, unknown>,
): void => {
  otherDependencyFields.forEach(field => {
    dependencyValuesCache[field] = contextData[field];
  });
};

/**
 * 计算标签动画的延迟时间
 * @description 用于错开动画效果
 * @param index 标签在列表中的索引
 * @param incrementDelay 延迟增量
 * @returns 延迟时间（毫秒）
 */
export const calculateTagDelay = (index: number, incrementDelay: number): number => {
  return index * incrementDelay;
};
