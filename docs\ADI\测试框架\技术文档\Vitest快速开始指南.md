# Vitest 快速开始指南

## 🎉 配置已完成

Vitest 测试环境已经完全配置好，可以直接使用！

## 快速验证

运行以下命令验证配置是否正常：

```bash
# 运行所有测试
pnpm test

# 运行测试并生成覆盖率报告
pnpm test:coverage

# 监听模式运行测试
pnpm test:watch

# 使用 UI 界面运行测试
pnpm test:ui
```

## 当前测试状态

- ✅ **29个测试全部通过**
- ✅ **97.36%** 代码覆盖率
- ✅ **3个测试文件** 正常运行

## 创建新测试

### 1. 工具函数测试

在 `src/utils/__tests__/` 目录下创建测试文件：

```typescript
// src/utils/__tests__/myUtil.test.ts
import { describe, it, expect } from 'vitest';
import { myFunction } from '../myUtil';

describe('MyUtil', () => {
  it('应该正确处理输入', () => {
    const result = myFunction('test');
    expect(result).toBe('expected');
  });
});
```

### 2. Vue 组件测试

在组件目录下创建测试文件：

```typescript
// src/components/MyComponent/__tests__/MyComponent.test.ts
import { describe, it, expect, beforeEach } from 'vitest';
import { createWrapper } from '@/test/utils';
import MyComponent from '../MyComponent.vue';
import type { Wrapper } from '@vue/test-utils';
import type Vue from 'vue';

describe('MyComponent', () => {
  let wrapper: Wrapper<Vue>;

  beforeEach(() => {
    wrapper = createWrapper(MyComponent, {
      propsData: {
        title: '测试标题',
      },
    });
  });

  it('应该正确渲染标题', () => {
    expect(wrapper.find('.title').text()).toBe('测试标题');
  });

  it('应该响应用户点击', async () => {
    await wrapper.find('.button').trigger('click');
    expect(wrapper.emitted('click')).toBeTruthy();
  });
});
```

## 测试工具函数

项目提供了丰富的测试工具函数，位于 `src/test/utils.ts`：

### 组件创建

```typescript
import { createWrapper, createShallowWrapper } from '@/test/utils';

// 完整渲染
const wrapper = createWrapper(MyComponent, {
  propsData: { prop: 'value' },
});

// 浅层渲染
const shallowWrapper = createShallowWrapper(MyComponent);
```

### 用户交互模拟

```typescript
import { setInputValue, clickElement } from '@/test/utils';

// 模拟输入
await setInputValue(wrapper, '.input', '测试值');

// 模拟点击
await clickElement(wrapper, '.button');
```

### 断言工具

```typescript
import { 
  expectElementExists,
  expectElementText,
  expectElementVisible 
} from '@/test/utils';

// 检查元素存在
expectElementExists(wrapper, '.my-element');

// 检查文本内容
expectElementText(wrapper, '.title', '期望文本');

// 检查可见性
expectElementVisible(wrapper, '.visible-element');
```

### API 模拟

```typescript
import { mockApiResponse } from '@/test/utils';

// 模拟成功响应
const mockData = mockApiResponse({ id: 1, name: '测试' });

// 模拟错误响应
const mockError = mockApiResponse(null, new Error('请求失败'));
```

## 测试最佳实践

### 1. 测试文件命名

- 工具函数: `src/utils/__tests__/functionName.test.ts`
- Vue 组件: `src/components/ComponentName/__tests__/ComponentName.test.ts`
- API 模块: `src/api/ModuleName/__tests__/api.test.ts`

### 2. 测试结构

```typescript
describe('功能模块名', () => {
  describe('子功能1', () => {
    it('应该做什么', () => {
      // 测试代码
    });
  });

  describe('子功能2', () => {
    it('应该做什么', () => {
      // 测试代码
    });
  });
});
```

### 3. 组件测试要点

- 测试组件的公共接口（props, events, slots）
- 测试用户交互和状态变化
- 避免测试实现细节
- 使用 `propsData` 传递 props（Vue 2）
- 使用 `setData()` 设置组件状态

### 4. 异步测试

```typescript
it('应该处理异步操作', async () => {
  await wrapper.find('.button').trigger('click');
  await nextTick(); // 等待 Vue 更新
  
  expect(wrapper.vm.$data.loading).toBe(false);
});
```

## 覆盖率报告

运行 `pnpm test:coverage` 后，可以查看：

- **控制台报告**: 直接在终端显示覆盖率统计
- **HTML 报告**: 在 `test-results/index.html` 查看详细报告
- **JSON 报告**: 在 `test-results/results.json` 查看机器可读格式

## 故障排除

### 常见问题

1. **测试文件找不到模块**
   - 检查 `tsconfig.json` 中的路径映射
   - 确保导入路径正确

2. **Vue 组件渲染失败**
   - 检查是否使用了正确的 Vue 2 语法
   - 确保 props 使用 `propsData` 传递

3. **Element UI 组件报错**
   - 检查 `src/test/setup.ts` 中的模拟配置
   - 确保组件已在 stubs 中声明

4. **异步测试超时**
   - 增加测试超时时间
   - 确保正确等待异步操作完成

### 获取帮助

- 查看 `docs/ADI/vitest-setup.md` 了解详细配置
- 参考现有测试文件作为示例
- 查看 Vitest 官方文档: https://vitest.dev/

## 下一步

现在你可以：

1. 为现有代码编写测试
2. 在开发新功能时同步编写测试
3. 设置 CI/CD 流程运行测试
4. 配置代码覆盖率阈值

开始编写测试，提高代码质量！🚀
