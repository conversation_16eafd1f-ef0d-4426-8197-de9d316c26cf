/**
 * @fileoverview 数据转换工具函数
 * @description 将后端接口数据转换为前端业务数据结构
 */

// 导入类型定义
import type {
  TemplateInputFormItem,
  TemplateResFormItem,
  ProjectResFormItem,
  GetTemplateListResponse,
  GetEditInfoResponse,
  ApiTextStyle,
  ApiImageStyle,
} from '@/api/EditProjectView/types';
import type {
  InputFormItem,
  ResFormItem,
  EditProjectViewData,
  ProjectType,
  FileStatus,
} from '@/views/EditProjectView/types/index';
import { FieldType } from '@/views/EditProjectView/types/index';
import {
  isApiVideoScript,
  isApiImageScript,
  isApiImageTextSetting,
  isApiVideoData,
  isApiImageTextData,
  isApiVideoSetting,
} from '@/api/EditProjectView/types/utils';
import { getMaterialFullUrl } from '@/components/MaterialBasicUpload/utils/index.ts';
import { FILE_STATUS } from '@/views/EditProjectView/types/base';
import type {
  ApiWorkInfo,
  ApiWorkListItem,
  ApiVideoSegment,
  ApiVideoData,
  ApiImageTextData,
} from '@/api/EditProjectView/types/response';
import type {
  WorkItem,
  VideoWorkItem,
  ImageWorkItem,
  StickerSetting,
} from '@/types/Work';
import type { ScriptSegment } from '@/types';
import {
  PROJECT_TYPE_VIDEO,
  PROJECT_TYPE_IMAGE,
} from '@/views/EditProjectView/constants';
import { PROJECT_STATUS } from '@/constants/project';
import { FILE_TYPES } from '@/constants/fileType';

/**
 * 错误类型枚举
 */
export enum TransformErrorType {
  /** 模板数据错误 */
  TEMPLATE_ERROR = 'TEMPLATE_ERROR',
  /** 项目数据错误 */
  PROJECT_ERROR = 'PROJECT_ERROR',
  /** 数据转换错误 */
  TRANSFORM_ERROR = 'TRANSFORM_ERROR',
  /** 数据验证错误 */
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  /** 网络错误 */
  NETWORK_ERROR = 'NETWORK_ERROR',
  /** 超时错误 */
  TIMEOUT_ERROR = 'TIMEOUT_ERROR',
}

/**
 * 自定义错误类
 */
export class TransformError extends Error {
  public readonly type: TransformErrorType;
  public readonly originalError?: Error;
  public readonly context?: Record<string, unknown>;

  constructor(
    type: TransformErrorType,
    message: string,
    originalError?: Error,
    context?: Record<string, unknown>,
  ) {
    super(message);
    this.name = 'TransformError';
    this.type = type;
    this.originalError = originalError;
    this.context = context;
  }
}

/**
 * 常量配置
 */
const CONFIG = {
  /** 默认表单配置 */
  DEFAULT_FORM: {
    minLength: 0,
    maxLength: 500,
    maxResLength: 50,
    description: '请上传相关素材',
  } as const,
} as const;

/**
 * 创建统一的错误对象
 * @param type 错误类型
 * @param message 错误信息
 * @param originalError 原始错误对象
 * @param context 错误上下文
 * @returns 统一的错误对象
 */
export function createTransformError(
  type: TransformErrorType,
  message: string,
  originalError?: Error,
  context?: Record<string, unknown>,
): TransformError {
  return new TransformError(type, message, originalError, context);
}

/**
 * 转换输入表单数据
 * @description 将模板的 inputFormList 和项目的 inputForm 数据进行组合转换
 * @param templateInputFormList 模板表单项配置列表
 * @param projectInputForm 项目表单数据（key-value格式）
 * @returns 转换后的前端表单项数组
 */
export function transformEditProjectInputFormFromApiToProject(
  templateInputFormList: TemplateInputFormItem[],
  projectInputForm: Record<string, string> = {},
): InputFormItem[] {
  try {
    return templateInputFormList.map(templateItem => {
      const value =
        projectInputForm[templateItem.variable] ||
        templateItem.defaultContent ||
        '';

      // 处理标签类型的值（字符串转数组）
      const transformedValue =
        templateItem.filedType === FieldType.TAGS && typeof value === 'string'
          ? value.split(',').filter(Boolean)
          : value;

      const transformedItem: InputFormItem = {
        variable: templateItem.variable,
        label: templateItem.label,
        minLength: CONFIG.DEFAULT_FORM.minLength,
        maxLength: templateItem.maxLength,
        required: templateItem.required,
        filedType: templateItem.filedType as FieldType,
        placeholder: templateItem.desc || `请输入${templateItem.label}`,
        value: transformedValue,
        openAiTip: templateItem.openAiTip,
      };

      // 如果有提示语，转换为数组格式
      if (templateItem.prompt) {
        transformedItem.prompt = [templateItem.prompt];
      }

      // 为标签类型添加默认选项
      if (templateItem.filedType === FieldType.TAGS) {
        transformedItem.options = [];
      }

      return transformedItem;
    });
  } catch (error) {
    console.error('转换输入表单数据时发生错误:', error);
    return [];
  }
}

/**
 * 获取文件状态
 * @param status - 文件当前状态
 * @param uploadId - 上传ID
 * @returns 处理后的文件状态
 */
const getFileStatus = (
  status: FileStatus,
  uploadId: string | number | undefined,
) => {
  // 如果状态是 NORMAL，直接返回
  if (status === FILE_STATUS.NORMAL) {
    return FILE_STATUS.NORMAL;
  }

  // 如果 uploadId 为空字符串、-1 或 undefined，返回 DELETE 状态
  if (['', -1, '-1', undefined].includes(uploadId)) {
    return FILE_STATUS.DELETE;
  }

  // 默认返回原始状态
  return status ?? FILE_STATUS.NORMAL;
};

/**
 * 转换资源表单数据
 * @description 将模板的 resFormList 和项目的 resForm 数据进行组合转换
 * @param templateResFormList 模板资源表单项配置列表
 * @param projectResForm 项目资源表单数据数组
 * @returns 转换后的前端资源表单项数组
 */
export function transformEditProjectResFormFromApiToProject(
  templateResFormList: TemplateResFormItem[],
  projectResForm: ProjectResFormItem[] = [],
): ResFormItem[] {
  try {
    return templateResFormList.map(templateItem => {
      // 通过 id 查找对应的项目数据
      const projectItem = projectResForm.find(
        item => item.id === templateItem.id,
      );

      // 转换上传的文件数据
      const transformedValue =
        projectItem?.resIds?.map((resItem, index) => ({
          name: `file_${index + 1}`,
          status: getFileStatus(resItem.status, resItem.uploadId),
          url: getMaterialFullUrl(
            resItem.uploadId || '',
            FILE_TYPES.WEBP || resItem.uploadType,
            'user',
            100,
          ), // 实际应该是完整的URL
          thumbUrl: getMaterialFullUrl(
            resItem?.coverId || '',
            FILE_TYPES.WEBP || resItem?.coverType,
            'user',
            100,
          ), // 缩略图URL
          resId: resItem.uploadId,
          resType: resItem.uploadType,
          coverId: resItem.coverId,
          coverType: resItem.coverType,
          duration: resItem.duration ?? 0,
        })) || [];

      const transformedItem: ResFormItem = {
        id: templateItem.id,
        type: templateItem.type as ProjectType,
        label: templateItem.label,
        value: transformedValue,
        required: templateItem.required,
        minLength: templateItem.required ? 1 : 0,
        maxLength: CONFIG.DEFAULT_FORM.maxResLength, // 默认最大上传数量
        hasModifyMouth: templateItem.modifyMouth,
        openModifyMouth: projectItem?.modifyMouth,
        description:
          templateItem.type === PROJECT_TYPE_VIDEO
            ? '建议每段视频时长在15秒至5分钟之间，上传多段视频素材效果更佳。'
            : '建议上传多个图片素材',
        variable: String(templateItem.id),
        // 示例资源与封面资源
        resId: templateItem.resId,
        resType: templateItem.resType,
        coverId: templateItem.coverId,
        coverType: templateItem.coverType,
      };

      return transformedItem;
    });
  } catch (error) {
    console.error('转换资源表单数据时发生错误:', error);
    return [];
  }
}

/**
 * 错误处理包装函数
 * @description 为转换函数提供统一的错误处理和日志记录
 * @param fn 要执行的函数
 * @param fallbackValue 失败时的默认返回值
 * @param errorType 错误类型
 * @param errorMessage 错误消息
 * @param context 错误上下文
 * @returns 执行结果或默认值
 */
export function safeExecuteWithErrorHandling<T>(
  fn: () => T,
  fallbackValue: T,
  errorType: TransformErrorType,
  errorMessage = '操作失败',
  context?: Record<string, unknown>,
): T {
  try {
    return fn();
  } catch (error) {
    const transformError = createTransformError(
      errorType,
      errorMessage,
      error instanceof Error ? error : new Error(String(error)),
      context,
    );

    console.error('[数据转换错误]', {
      type: errorType,
      message: errorMessage,
      originalError: error,
      context,
      stack: transformError.stack,
    });

    return fallbackValue;
  }
}

/**
 * 网络错误检测函数
 * @param error 错误对象
 * @returns 是否为网络错误
 */
export function isNetworkError(error: Error): boolean {
  const networkErrorPatterns = [
    'NetworkError',
    'ERR_NETWORK',
    'ERR_INTERNET_DISCONNECTED',
    'ERR_CONNECTION_REFUSED',
    'fetch',
    'network',
  ];

  const errorMessage = error.message?.toLowerCase() || '';
  return networkErrorPatterns.some(pattern =>
    errorMessage.includes(pattern.toLowerCase()),
  );
}

/**
 * 超时错误检测函数
 * @param error 错误对象
 * @returns 是否为超时错误
 */
export function isTimeoutError(error: Error): boolean {
  const timeoutErrorPatterns = [
    'timeout',
    'ERR_TIMEOUT',
    'ETIMEDOUT',
    'Request timeout',
  ];

  const errorMessage = error.message?.toLowerCase() || '';
  return timeoutErrorPatterns.some(pattern =>
    errorMessage.includes(pattern.toLowerCase()),
  );
}

/**
 * 合并模板和项目数据
 * @description 将 getTemplateInfo 和 getEditInfo 两个接口的数据合并转换
 * @param templateData 模板数据
 * @param projectData 项目数据（可选，新建项目时为空）
 * @returns 转换后的完整项目数据
 */
export function transformEditProjectApiDataToProjectData(
  templateData: GetTemplateListResponse,
  projectData?: GetEditInfoResponse,
): EditProjectViewData {
  // 转换输入表单数据
  const inputForm = transformEditProjectInputFormFromApiToProject(
    templateData?.inputFormList || [],
    projectData?.inputForm,
  );

  // 转换资源表单数据
  const resForm = transformEditProjectResFormFromApiToProject(
    templateData?.resFormList || [],
    projectData?.resForm,
  );

  // 转换背景音乐设置
  const bgMusic = projectData?.setting?.bgm
    ? {
        open: projectData.setting.bgm.open,
        useAiRecommend: projectData.setting.bgm.auto,
        resIds: projectData.setting.bgm.resIds || [],
        name: projectData.setting.bgm.name || '',
        vol: 75, // 默认音量
      }
    : {
        open: false,
        useAiRecommend: false,
        resIds: [],
        name: '',
        vol: 75,
      };

  // 转换配音设置
  const voice = projectData?.setting?.voice
    ? {
        open: true,
        useAiRecommend: projectData.setting.voice.auto,
        voiceId: projectData.setting.voice.voiceType
          ? projectData.setting.voice.voiceType
          : '',
        voiceName: projectData.setting.voice.name || '',
        speed: 1.0, // 默认语速
        extraType: projectData.setting.voice.extraType,
      }
    : {
        open: false,
        useAiRecommend: true,
        voiceId: '',
        voiceName: '',
        speed: 1.0,
        extraType: undefined,
      };

  // 构建并返回数据结构
  return {
    projectId: projectData?.id,
    templateId: templateData?.id || 0,
    type: (templateData?.type || 0) as ProjectType,
    status: projectData?.status ?? PROJECT_STATUS.DRAFT, // 默认为草稿状态
    inputForm,
    resForm,
    script: {
      title: templateData?.script?.title || '',
      content: templateData?.script?.content || '',
      segments: [], // 视频脚本片段
    },
    bgMusic,
    voice,
    flowerTexts: [],
  };
}

/**
 * 将API返回的作品列表项转换为前端作品项
 * @description 转换作品列表中的单个作品项，包括基础信息和作品数据
 * - 对于图文作品：转换 graphic 数组，包含图片的 id 和 type
 * - 对于视频作品：转换视频相关数据，包含 video、videoCover 等信息
 * - 使用类型保护确保数据转换的安全性
 * @param apiWorkListItem API返回的作品列表项
 * @returns 转换后的前端作品项
 */
export function transformWorkListItemFromApiToProject(
  apiWorkListItem: ApiWorkListItem,
): WorkItem {
  const baseItem: Partial<WorkItem> = {
    id: apiWorkListItem.id,
    aid: apiWorkListItem.aid,
    projectId: apiWorkListItem.projectId,
    type:
      apiWorkListItem.type === PROJECT_TYPE_VIDEO
        ? PROJECT_TYPE_VIDEO
        : PROJECT_TYPE_IMAGE,
    name: apiWorkListItem.name,
    coverImg: apiWorkListItem.coverImg,
    size: apiWorkListItem.size,
    status: apiWorkListItem.status,
    progress: apiWorkListItem.progress,
    createTime: String(apiWorkListItem.createTime),
    updateTime: String(apiWorkListItem.updateTime),
    // 保存时间字段处理
    saveTime: apiWorkListItem.saveTime
      ? String(apiWorkListItem.saveTime)
      : undefined,
    // 关联作品ID（从顶层字段获取）
    relWorkId: apiWorkListItem.relWorkId,
    // 扩展属性
    sizeName: apiWorkListItem.sizeName,
    typeName: apiWorkListItem.typeName,
    statusName: apiWorkListItem.statusName,
    projectName: apiWorkListItem.projectName,
    coverImgType: apiWorkListItem.coverImgType,
    duration: apiWorkListItem.duration || 0,
    // 错误信息
    errMsg: apiWorkListItem.errMsg,
    // 图文是否二次编辑（仅当作品状态为已生成时使用此字段判断是否显示新生成标签）
    editAgainGraphic: apiWorkListItem.editAgainGraphic,
    // 是否重新编辑（用于统一状态处理系统）
    editAgain: apiWorkListItem.editAgain,
    // 是否需要扣除点数（true=需要扣点，false=不需要扣点）
    pinchPoint: apiWorkListItem.pinchPoint,
  };

  // 处理作品数据字段
  if (apiWorkListItem.data) {
    if (
      apiWorkListItem.type === PROJECT_TYPE_IMAGE &&
      isApiImageTextData(apiWorkListItem.data)
    ) {
      // 图文作品 - 转换图文数据
      (baseItem as Partial<ImageWorkItem>).data = {
        graphic:
          apiWorkListItem.data.graphic?.map(item => ({
            resId: item.resId,
            type: item.type,
          })) || [],
        relWorkId: apiWorkListItem.data.relWorkId,
        errMsg: apiWorkListItem.data.errMsg,
      };
    } else if (
      apiWorkListItem.type === PROJECT_TYPE_VIDEO &&
      isApiVideoData(apiWorkListItem.data)
    ) {
      // 视频作品 - 转换视频数据
      (baseItem as Partial<VideoWorkItem>).data = {
        relWorkId: apiWorkListItem.data.relWorkId,
        video: apiWorkListItem.data.fullVideo
          ? {
              fullId: apiWorkListItem.data.fullVideo.id,
              fullType: apiWorkListItem.data.fullVideo.type,
            }
          : undefined,
        videoCover: apiWorkListItem.data.fullCover
          ? {
              fullId: apiWorkListItem.data.fullCover.id,
              fullType: apiWorkListItem.data.fullCover.type,
            }
          : undefined,
        voiceId: apiWorkListItem.data.voiceType,
        errMsg: apiWorkListItem.data.errMsg,
      };
    }
  }

  return baseItem as WorkItem;
}

/**
 * 将API返回的视频段落转换为前端脚本段落
 * @param apiVideoSegment API返回的视频段落
 * @returns 转换后的前端脚本段落
 */
export function transformVideoSegmentFromApiToProject(
  apiVideoSegment: ApiVideoSegment,
): ScriptSegment {
  return {
    module: apiVideoSegment.module,
    content: apiVideoSegment.content,
    highlighted: apiVideoSegment.highlighted,
    beginTime: apiVideoSegment.beginTime,
    length: apiVideoSegment.endTime - apiVideoSegment.beginTime,
  };
}

/**
 * 将API返回的作品详情转换为前端作品项
 * @param apiWorkInfo API返回的作品详情
 * @returns 转换后的前端作品项
 */
export function transformWorkInfoFromApiToProject(
  apiWorkInfo: ApiWorkInfo,
): WorkItem {
  // 基础属性
  const baseItem: Partial<WorkItem> = {
    id: apiWorkInfo.id,
    aid: apiWorkInfo.aid,
    projectId: apiWorkInfo.projectId,
    type:
      apiWorkInfo.type === PROJECT_TYPE_VIDEO
        ? PROJECT_TYPE_VIDEO
        : PROJECT_TYPE_IMAGE,
    name: apiWorkInfo.name,
    size: apiWorkInfo.size,
    status: apiWorkInfo.status,
    progress: apiWorkInfo.progress,
    createTime: String(apiWorkInfo.createTime),
    updateTime: String(apiWorkInfo.updateTime),
    // 保存时间字段处理
    saveTime: apiWorkInfo.saveTime ? String(apiWorkInfo.saveTime) : undefined,
    // 扩展属性
    flag: apiWorkInfo.flag,
    subtitle: apiWorkInfo.subtitle,
    duration: 0, // 添加必需的duration属性
    title: apiWorkInfo.title || '',
    content: apiWorkInfo.content || '',
    // 错误信息（从data字段中提取）
    errMsg: (apiWorkInfo.data as ApiVideoData | ApiImageTextData)?.errMsg,
    // 图文是否二次编辑（仅当作品状态为已生成时使用此字段判断是否显示新生成标签）
    editAgainGraphic: apiWorkInfo.editAgainGraphic,
    // 是否重新编辑（用于统一状态处理系统）
    editAgain: apiWorkInfo.editAgain,
    // 是否需要扣除点数（true=需要扣点，false=不需要扣点）
    pinchPoint: apiWorkInfo.pinchPoint,
  };

  // 根据类型处理不同的属性
  if (apiWorkInfo.type === PROJECT_TYPE_VIDEO) {
    // 视频
    const videoItem = baseItem as Partial<VideoWorkItem>;

    if (isApiVideoScript(apiWorkInfo.script)) {
      videoItem.script = {
        segments: apiWorkInfo.script.segments.map(
          transformVideoSegmentFromApiToProject,
        ),
        hashtags: apiWorkInfo.script.hashtags,
        title: apiWorkInfo.script.title,
      };
    }

    if (isApiVideoData(apiWorkInfo.data)) {
      videoItem.coverImg = apiWorkInfo.data.fullCover.id;
      videoItem.coverImgType = apiWorkInfo.data.fullCover.type;
      videoItem.duration = apiWorkInfo.data.baseVideo.duration || 0;

      videoItem.contentUrl = getMaterialFullUrl(
        String(apiWorkInfo.data.fullVideo.id),
        apiWorkInfo.data.fullVideo.type,
      );

      videoItem.relWorkId = apiWorkInfo.data.relWorkId;

      // 设置data字段
      videoItem.data = {
        relWorkId: apiWorkInfo.data.relWorkId,
        video: apiWorkInfo.data.fullVideo
          ? {
              fullId: apiWorkInfo.data.fullVideo.id,
              fullType: apiWorkInfo.data.fullVideo.type,
              baseId: apiWorkInfo.data.baseVideo.id,
              baseType: apiWorkInfo.data.baseVideo.type,
            }
          : undefined,
        videoCover: apiWorkInfo.data.fullCover
          ? {
              fullId: apiWorkInfo.data.fullCover.id,
              fullType: apiWorkInfo.data.fullCover.type,
              baseId: apiWorkInfo.data.baseCover.id,
              baseType: apiWorkInfo.data.baseCover.type,
            }
          : undefined,
        voiceId: apiWorkInfo.data.voiceType,
        errMsg: apiWorkInfo.data.errMsg,
      };
    }

    // 转换视频设置
    const setting = isApiVideoSetting(apiWorkInfo.setting)
      ? apiWorkInfo.setting
      : null;
    videoItem.setting = {
      bgMusic: {
        open: setting?.bgm?.open ?? false,
        resId: setting?.bgm?.resId || '',
        vol: setting?.bgm?.volume ?? 75,
      },
      voice: {
        open: setting?.voice?.open ?? false,
        voiceId: setting?.voice?.voiceType ? setting.voice.voiceType : '',
        speed: setting?.voice?.speed ?? 1.0,
        extraType: setting?.voice?.extraType,
      },
      style: (setting?.style || []).map(style => {
        if (style.type === 'text') {
          return {
            ...style,
            styleId: style.id || style.styleId,
            fontName: style.fontName || '', // TODO 测试数据少了个fontName字段导致响应式依赖断裂，这里补个空默认值
          };
        }
        return style;
      }) as unknown as StickerSetting[],
    };

    return videoItem as VideoWorkItem;
  } else {
    // 图文
    const imageItem = baseItem as Partial<ImageWorkItem>;

    if (isApiImageScript(apiWorkInfo.script)) {
      imageItem.script = {
        title: apiWorkInfo.script.title,
        content: apiWorkInfo.script.content,
        hashtags: apiWorkInfo.script.hashtags,
      };
    }

    if (isApiImageTextData(apiWorkInfo.data)) {
      imageItem.relWorkId = apiWorkInfo.data.relWorkId;

      // 转换图文数据
      imageItem.data = {
        graphic:
          apiWorkInfo.data.graphic?.map(item => ({
            resId: item.resId,
            type: item.type,
          })) || [],
        relWorkId: apiWorkInfo.data.relWorkId,
        // 保留错误信息
        errMsg: apiWorkInfo.data.errMsg,
      };

      if (isApiImageTextSetting(apiWorkInfo.setting)) {
        const maxId = Math.max(
          ...(apiWorkInfo.setting.graphicList.map(item => item.id) ?? [-1]),
        );
        imageItem.setting = {
          graphicList: apiWorkInfo.setting.graphicList.map(item => ({
            id: item.id,
            puzzleType: item.puzzleType,
            ratio: item.ratio,
            puzzleStyle: (item.puzzleStyle || []).map(style => ({
              ...style,
              isNewCreate: item.puzzleType == 1, // 这个拼图是否用户新增的（前端临时变量，用户编辑新增的拼图需要居中渲染，单图也需要每次都居中渲染）
            })),
            style: item.style?.map(style => {
              const baseStyle = {
                type:
                  style.type === 'text'
                    ? ('text' as const)
                    : ('image' as const),
                x: Number(style.x),
                y: Number(style.y),
              };

              if (style.type === 'text') {
                return {
                  ...baseStyle,
                  styleId: (style as ApiTextStyle).id || 0,
                  align: (style as ApiTextStyle).align || 'center',
                  content: (style as ApiTextStyle).text || '',
                  fontSize: Number((style as ApiTextStyle).fontSize || 0),
                  color: (style as ApiTextStyle).color || '#000000',
                  text: (style as ApiTextStyle).text || '',
                  fontName: (style as ApiTextStyle).fontName || '',
                  fileName: (style as ApiTextStyle).fileName || '',
                  strokeColor: (style as ApiTextStyle).strokeColor,
                  strokeWidth: (style as ApiTextStyle).strokeWidth,
                  shadowColor: (style as ApiTextStyle).shadowColor,
                  shadowX: (style as ApiTextStyle).shadowX,
                  shadowY: (style as ApiTextStyle).shadowY,
                  shadowStrokeColor: (style as ApiTextStyle).shadowStrokeColor,
                  shadowStrokeWidth: (style as ApiTextStyle).shadowStrokeWidth,
                  boxColor: (style as ApiTextStyle).boxColor,
                };
              } else {
                return {
                  ...baseStyle,
                  styleId: 0,
                  align: 'center',
                  resId: (style as ApiImageStyle).resId,
                  resType: (style as ApiImageStyle).resType,
                  width: (style as ApiImageStyle).width,
                  height: (style as ApiImageStyle).height,
                };
              }
            }) as unknown as StickerSetting[],
          })),
          curMaxId: maxId,
        };
      }

      imageItem.relWorkId = apiWorkInfo.data.relWorkId;
    }

    return imageItem as ImageWorkItem;
  }
}
