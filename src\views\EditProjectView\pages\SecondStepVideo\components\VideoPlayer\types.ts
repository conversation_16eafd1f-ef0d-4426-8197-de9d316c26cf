import { Ref, ComputedRef } from 'vue';
import { WORK_STATUS } from '@/constants/workStatus';
import type Player from 'video.js/dist/types/player';

/**
 * 浏览器特定的全屏 API 类型定义
 */
export interface BrowserDocument extends Document {
  webkitFullscreenEnabled?: boolean;
  mozFullScreenEnabled?: boolean;
  msFullscreenEnabled?: boolean;
  webkitFullscreenElement?: Element | null;
  mozFullScreenElement?: Element | null;
  msFullscreenElement?: Element | null;
  webkitExitFullscreen?: () => Promise<void>;
  mozCancelFullScreen?: () => Promise<void>;
  msExitFullscreen?: () => Promise<void>;
}

/**
 * 浏览器特定的全屏元素类型定义
 */
export interface BrowserElement extends HTMLElement {
  webkitRequestFullscreen?: () => Promise<void>;
  mozRequestFullScreen?: () => Promise<void>;
  msRequestFullscreen?: () => Promise<void>;
}

/**
 * 视频播放器状态枚举
 */
export enum PlayerState {
  /** 未初始化 */
  UNINITIALIZED = 'UNINITIALIZED',
  /** 加载中 */
  LOADING = 'LOADING',
  /** 就绪 */
  READY = 'READY',
  /** 错误 */
  ERROR = 'ERROR',
}

/**
 * 视频播放器Props接口
 */
export interface VideoPlayerProps {
  /** 视频URL */
  videoUrl: string;
  /** 视频状态 */
  videoStatus: number;
  /** 进度百分比 */
  progress: number;
  /** 封面图资源ID */
  coverImg?: string;
  /** 封面图类型 */
  coverImgType?: number;
}

/**
 * 视频播放器事件参数接口
 */
export interface VideoPlayerEventParams {
  /** 操作类型 */
  actionType:
    | 'play'
    | 'pause'
    | 'ended'
    | 'error'
    | 'fullscreen'
    | 'refresh-full-list';
}

/**
 * 视频控制器Props接口
 */
export interface VideoControlsProps {
  /** 是否正在播放 */
  isPlaying: boolean;
  /** 是否禁用控制 */
  isDisabled: boolean;
  /** 缓冲百分比 */
  bufferedPercent: number;
  /** 已播放百分比 */
  playedPercent: number;
  /** 当前时间(秒) */
  currentTime: number;
  /** 总时长(秒) */
  duration: number;
}

/**
 * 视频控制器事件处理函数类型
 */
export type VideoControlsEventHandlers = {
  /** 切换播放/暂停 */
  onTogglePlay: () => void;
  /** 跳转到指定时间 */
  onSeek: (time: number) => void;
  /** 跳转中 */
  onSeeking: (percent: number, time: number) => void;
};

/**
 * 视频播放器Ref接口
 */
export interface VideoPlayerRef {
  /** 视频容器元素 */
  videoContainer: Ref<HTMLElement | null>;
  /** 视频播放器元素 */
  videoPlayer: Ref<HTMLVideoElement | null>;
  /** 是否正在加载 */
  isLoading: Ref<boolean>;
  /** 是否发生错误 */
  isError: Ref<boolean>;
  /** 是否正在播放 */
  isPlaying: Ref<boolean>;
  /** 加载文本 */
  loadingText: ComputedRef<string | string[]>;
  /** 当前播放时间 */
  currentTime: Ref<number>;
  /** 视频总时长 */
  duration: Ref<number>;
  /** 缓冲进度百分比 */
  bufferedPercent: Ref<number>;
  /** 播放进度百分比 */
  playedPercent: Ref<number>;
  /** 进度条值 */
  progressValue: ComputedRef<number>;
  /** 视频状态 */
  videoStatus: ComputedRef<number>;
  /** 视频URL */
  videoUrlValue: ComputedRef<string>;
  /** 是否可以显示视频 */
  canShowVideo: ComputedRef<boolean>;
  /** 播放器是否已初始化 */
  playerInitialized: Ref<boolean>;
  /** 视频状态常量 */
  WORK_STATUS: typeof WORK_STATUS;
  /** 切换播放/暂停 */
  togglePlay: () => void;
  /** 设置播放时间 */
  setPlayerTime: (time: number) => void;
  /** 处理进度条拖动 */
  handleSeeking: (time: number) => void;
  /** 切换全屏 */
  toggleFullScreen: () => void;
  /** 初始化播放器 */
  initializePlayer: () => void;
  /** 是否全屏 */
  isFullscreen: Ref<boolean>;
  /** 是否禁用控制 */
  isControlDisabled: ComputedRef<boolean>;
  /** 处理视频预览请求 */
  handlePreviewVideo: () => void;
}

/**
 * 播放器事件处理器参数接口
 */
export interface PlayerEventsHandlerParams {
  /** 事件发射器 */
  emit: (actionType: string) => void;
  /** 播放器实例 */
  player: Ref<Player | null>;
  /** 是否正在播放 */
  isPlaying: Ref<boolean>;
  /** 当前播放时间 */
  currentTime: Ref<number>;
  /** 视频总时长 */
  duration: Ref<number>;
  /** 缓冲进度 */
  bufferedPercent: Ref<number>;
  /** 播放进度 */
  playedPercent: Ref<number>;
  /** 播放器状态 */
  playerState: Ref<PlayerState>;
}

/**
 * 播放器事件处理器返回值接口
 */
export interface PlayerEventsHandlerResult {
  /** 设置播放器事件 */
  setupPlayerEvents: () => void;
}

/**
 * 播放器配置接口
 */
export interface VideoPlayerConfig {
  /** 自动播放 */
  autoplay?: boolean;
  /** 显示控制栏 */
  controls?: boolean;
  /** 预加载策略 */
  preload?: 'auto' | 'metadata' | 'none';
  /** 行内播放 */
  playsinline?: boolean;
  /** 静音播放 */
  muted?: boolean;
  /** 循环播放 */
  loop?: boolean;
  /** 封面图URL */
  poster?: string;
  /** 控制列表 - 用于禁用特定功能 */
  controlsList?: string;
  /** 用户交互配置 */
  userActions?: {
    /** 是否允许点击切换播放 */
    click?: boolean;
  };
}

/**
 * 错误处理配置接口
 */
export interface ErrorHandlingConfig {
  /** 最大重试次数 */
  maxRetries?: number;
  /** 重试间隔（毫秒） */
  retryDelay?: number;
  /** 是否显示错误信息 */
  showErrorMessage?: boolean;
  /** 自定义错误处理函数 */
  onError?: (error: Error) => void;
}
