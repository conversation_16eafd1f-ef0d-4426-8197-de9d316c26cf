import { WorkItem } from '@/types';
import { POST_JSON } from '../request';
import { transformWorkInfoToApiWorkInfo } from './utils/outputTransform';

export function saveWork(workInfo: WorkItem, isSubmit: boolean) {
  const transformData = transformWorkInfoToApiWorkInfo(workInfo);
  return POST_JSON<string>('/api/work/edit', {
    ...transformData,
    isSubmit: isSubmit,
  });
}

/**
 * 查询作品重新生成所需信息
 * @param workInfo 作品信息
 * @returns
 * {
 *  pointCnt: 扣点数
 *  mergeVideo: 是否需要重新生成
 * }
 */
export function checkWorkRegenerateInfo(workInfo: WorkItem) {
  const transformData = transformWorkInfoToApiWorkInfo(workInfo);
  return POST_JSON<{ pointCnt: number; mergeVideo: boolean }>(
    '/api/work/checkWorkInfo',
    {
      ...transformData,
      isSubmit: false,
    },
  );
}

/**
 * 获取花字调试结果
 * @returns base64
 */
export function debugTextSticker(workInfo: WorkItem) {
  const transformData = transformWorkInfoToApiWorkInfo(workInfo);
  return POST_JSON<string>('/api/adm/getImageTextDraw', {
    ...transformData,
    isSubmit: true,
  });
}
