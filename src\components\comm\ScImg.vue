<template>
  <!-- 图片元素 -->
  <el-image :src="currSrc" v-bind="$attrs" v-on="$listeners">
    <!-- 占位内容插槽 - 优先使用外部传入的内容 -->
    <template #placeholder>
      <slot name="placeholder">
        <div class="loading">
          <fa-spin />
        </div>
      </slot>
    </template>

    <!-- 错误内容插槽 - 支持外部传入自定义错误内容 -->
    <template #error>
      <slot name="error">
        <div class="error">
          <i class="el-icon-picture-outline"></i>
        </div>
      </slot>
    </template>
  </el-image>
</template>

<script lang="ts" setup>
// 引入组合式 API
import { ref, watch } from 'vue';
import { getMaterialFullUrl } from '../MaterialBasicUpload/utils/index.ts';
import { FILE_TYPES } from '@/constants/fileType';

// 定义组件的 props
const props = defineProps<{
  /** 图片地址或资源id */
  src?: string;
  /** 图片资源类型，后端接口会和资源id一起返回，没有就找后端要（不传默认使用webp格式） */
  type?: number;
  /** 资源归属，一般不用传值，默认是用户资源。*/
  belong?: 'user' | 'system' | 'oss';
  /** 图片最大宽度，仅控制加载图片的分辨率，不控制图片的显示宽度 */
  maxWidth?: number;
}>();

const currSrc = ref('');
function setSrc() {
  if (!props.src) return;
  if (props.src.startsWith('http') || props.src.startsWith('//')) {
    return (currSrc.value = props.src);
  }
  const belong = props.belong || 'user';
  currSrc.value = getMaterialFullUrl(
    props.src,
    props.type || FILE_TYPES.WEBP,
    belong,
    props.maxWidth,
  );
}
setSrc();
watch(
  () => [props.src, props.type, props.belong],
  () => {
    setSrc();
  },
);
</script>

<style lang="scss" scoped>
.loading {
  @apply flex justify-center items-center w-full h-full;
}

.error {
  @apply flex flex-col justify-center items-center w-full h-full;
  @apply text-gray-400 text-14px;

  i {
    @apply text-24px;
  }
}
</style>
