/**
 * @fileoverview 图文作品新生成标签功能测试
 * @description 测试图文作品新生成标签的核心逻辑
 */

import { describe, it, expect, vi } from 'vitest';
import { ref, computed } from 'vue';
import {
  WORK_STATUS,
  getWorkStatusInfo,
  isRecompletedWork,
} from '@/constants/workStatus';

// Mock updateWorkFlag API
const mockUpdateWorkFlag = vi.fn((_params: { id: number }) =>
  Promise.resolve([null, { success: true }]),
);

vi.mock('@/api/EditProjectView/work', () => ({
  updateWorkFlag: mockUpdateWorkFlag,
}));

describe('图文作品新生成标签功能 - 核心逻辑测试', () => {
  describe('本地状态管理逻辑', () => {
    it('应该正确管理已标识作品的本地状态', () => {
      // 模拟组件中的本地状态
      const flaggedWorkIds = ref<Record<number, boolean>>({});

      // 初始状态：对象为空
      expect(Object.keys(flaggedWorkIds.value).length).toBe(0);
      expect(!!flaggedWorkIds.value[1]).toBe(false);

      // 添加已标识的作品ID
      flaggedWorkIds.value[1] = true;

      // 验证状态更新
      expect(Object.keys(flaggedWorkIds.value).length).toBe(1);
      expect(!!flaggedWorkIds.value[1]).toBe(true);
      expect(!!flaggedWorkIds.value[2]).toBe(false);
    });

    it('应该正确计算是否显示新生成标签', () => {
      // 模拟组件中的状态和计算属性
      const flaggedWorkIds = ref<Record<number, boolean>>({});

      // 模拟图文作品数据
      const mockWorkItem = {
        id: 1,
        type: 1, // IMAGE
        status: WORK_STATUS.COMPLETED,
        editAgainGraphic: true,
      };

      // 模拟 isRegenerated 计算属性的逻辑
      const isRegenerated = computed(() => {
        if (mockWorkItem.type === 1) {
          // IMAGE
          // 如果已被本地标识，不显示标签
          if (flaggedWorkIds.value[mockWorkItem.id]) {
            return false;
          }

          return (
            mockWorkItem.status === WORK_STATUS.COMPLETED &&
            mockWorkItem.editAgainGraphic === true
          );
        }
        return false;
      });

      // 初始状态：应该显示新生成标签
      expect(isRegenerated.value).toBe(true);

      // 标识作品后：创建新对象确保响应式更新
      flaggedWorkIds.value = {
        ...flaggedWorkIds.value,
        [mockWorkItem.id]: true,
      };
      expect(isRegenerated.value).toBe(false);
    });
  });

  describe('API调用逻辑', () => {
    beforeEach(() => {
      vi.clearAllMocks();
    });

    it('应该在满足条件时调用updateWorkFlag接口', async () => {
      const workItem = {
        id: 1,
        type: 1, // IMAGE
        status: WORK_STATUS.COMPLETED,
        editAgainGraphic: true,
      };

      const flaggedWorkIds = ref<Record<number, boolean>>({});

      // 模拟点击处理逻辑
      const handleClick = async () => {
        if (
          workItem.type === 1 && // IMAGE
          workItem.status === WORK_STATUS.COMPLETED &&
          workItem.editAgainGraphic === true &&
          !flaggedWorkIds.value[workItem.id]
        ) {
          const [err] = await mockUpdateWorkFlag({ id: workItem.id });

          if (!err) {
            flaggedWorkIds.value = {
              ...flaggedWorkIds.value,
              [workItem.id]: true,
            };
          }
        }
      };

      // 执行点击处理
      await handleClick();

      // 验证API被调用
      expect(mockUpdateWorkFlag).toHaveBeenCalledWith({ id: 1 });
      expect(mockUpdateWorkFlag).toHaveBeenCalledTimes(1);

      // 验证本地状态更新
      expect(!!flaggedWorkIds.value[1]).toBe(true);
    });

    it('不应该对已标识的作品重复调用接口', async () => {
      const workItem = {
        id: 1,
        type: 1, // IMAGE
        status: WORK_STATUS.COMPLETED,
        editAgainGraphic: true,
      };

      const flaggedWorkIds = ref<Record<number, boolean>>({ 1: true }); // 已标识

      // 模拟点击处理逻辑
      const handleClick = async () => {
        if (
          workItem.type === 1 && // IMAGE
          workItem.status === WORK_STATUS.COMPLETED &&
          workItem.editAgainGraphic === true &&
          !flaggedWorkIds.value[workItem.id]
        ) {
          await mockUpdateWorkFlag({ id: workItem.id });
        }
      };

      // 执行点击处理
      await handleClick();

      // 验证API未被调用
      expect(mockUpdateWorkFlag).not.toHaveBeenCalled();
    });

    it('不应该对非图文作品调用接口', async () => {
      const workItem = {
        id: 1,
        type: 0, // VIDEO
        status: WORK_STATUS.COMPLETED, // 重新生成完成状态
        editAgain: true, // 使用新的字段名
        editAgainGraphic: false,
      };

      const flaggedWorkIds = ref<Record<number, boolean>>({});

      // 模拟点击处理逻辑
      const handleClick = async () => {
        if (
          workItem.type === 1 && // IMAGE
          workItem.status === WORK_STATUS.COMPLETED &&
          workItem.editAgainGraphic === true &&
          !flaggedWorkIds.value[workItem.id]
        ) {
          await mockUpdateWorkFlag({ id: workItem.id });
        }
      };

      // 执行点击处理
      await handleClick();

      // 验证API未被调用
      expect(mockUpdateWorkFlag).not.toHaveBeenCalled();
    });
  });

  describe('错误处理', () => {
    it('应该正确处理API调用失败的情况', async () => {
      // Mock API 返回错误
      const mockUpdateWorkFlagError = vi.fn((_params: { id: number }) =>
        Promise.resolve([new Error('网络错误'), null]),
      );

      const workItem = {
        id: 1,
        type: 1, // IMAGE
        status: WORK_STATUS.COMPLETED,
        editAgainGraphic: true,
      };

      const flaggedWorkIds = ref<Record<number, boolean>>({});

      // 模拟点击处理逻辑（包含错误处理）
      const handleClick = async () => {
        if (
          workItem.type === 1 && // IMAGE
          workItem.status === WORK_STATUS.COMPLETED &&
          workItem.editAgainGraphic === true &&
          !flaggedWorkIds.value[workItem.id]
        ) {
          const [err] = await mockUpdateWorkFlagError({ id: workItem.id });

          if (err) {
            console.error('更新作品标识失败:', err.message);
            // 不更新本地状态
          } else {
            flaggedWorkIds.value = {
              ...flaggedWorkIds.value,
              [workItem.id]: true,
            };
          }
        }
      };

      // 执行点击处理
      await handleClick();

      // 验证API被调用
      expect(mockUpdateWorkFlagError).toHaveBeenCalledWith({ id: 1 });

      // 验证本地状态未更新（因为API调用失败）
      expect(!!flaggedWorkIds.value[1]).toBe(false);
    });
  });

  describe('保存作品逻辑', () => {
    it('图文作品不应该触发新旧选择弹窗', () => {
      const imageWorkItem = {
        id: 1,
        type: 1, // IMAGE
        status: WORK_STATUS.COMPLETED,
        editAgainGraphic: true,
      };

      // 模拟保存逻辑
      const shouldShowModal = (workItem: typeof imageWorkItem) => {
        // 只有视频作品的重新生成完成状态才显示新旧选择弹窗
        if (workItem.type === 0) {
          // VIDEO
          const statusInfo = getWorkStatusInfo(workItem);
          return isRecompletedWork(statusInfo);
        }
        return false;
      };

      // 验证图文作品不触发弹窗
      expect(shouldShowModal(imageWorkItem)).toBe(false);
    });

    it('视频作品重新生成完成状态应该触发新旧选择弹窗', () => {
      const videoWorkItem = {
        id: 1,
        type: 0, // VIDEO
        status: WORK_STATUS.COMPLETED,
        editAgain: true, // 重新生成完成状态
      };

      // 模拟保存逻辑
      const shouldShowModal = (workItem: typeof videoWorkItem) => {
        // 只有视频作品的重新生成完成状态才显示新旧选择弹窗
        if (workItem.type === 0) {
          // VIDEO
          const statusInfo = getWorkStatusInfo(workItem);
          return isRecompletedWork(statusInfo);
        }
        return false;
      };

      // 验证视频作品触发弹窗
      expect(shouldShowModal(videoWorkItem)).toBe(true);
    });

    it('批量保存时图文作品不应该触发新旧选择弹窗', () => {
      const workList = [
        {
          id: 1,
          type: 1,
          status: WORK_STATUS.COMPLETED,
          editAgainGraphic: true,
        }, // 图文作品
        { id: 2, type: 0, status: WORK_STATUS.COMPLETED, editAgain: true }, // 视频作品重新生成完成
        {
          id: 3,
          type: 1,
          status: WORK_STATUS.COMPLETED,
          editAgainGraphic: false,
        }, // 图文作品无标识
      ];

      // 模拟批量保存逻辑
      const findRegeneratedWork = (works: typeof workList) => {
        return works.find(work => {
          // 只有视频作品（type === 0）才检查重新生成完成状态
          if (work.type === 0) {
            // VIDEO
            const statusInfo = getWorkStatusInfo(work);
            return isRecompletedWork(statusInfo);
          }
          return false;
        });
      };

      const regeneratedWork = findRegeneratedWork(workList);

      // 验证只找到视频作品
      expect(regeneratedWork).toBeDefined();
      expect(regeneratedWork?.type).toBe(0); // VIDEO
      expect(regeneratedWork?.id).toBe(2);
    });
  });
});
