:root {
  --font-family: Microsoft YaHei, 微软雅黑, Arial, Helvetica, sans-serif, -apple-system, BlinkMacSystemFont, Segoe UI, PingFang SC, Hiragino Sans GB, Helvetica Neue, Apple Color Emoji, Segoe UI Emoji, Segoe UI Symbol;
}

html {
  overflow-x: auto;
  overflow-y: hidden;
}

body {
  margin: 0;
  font-family: var(--font-family);
}

/* 覆盖等宽字体标签 */
code,
kbd,
samp,
pre,
var {
  font-family: var(--font-family) !important;
}

::-webkit-scrollbar {
  width: 6px;
  height: 6px;
  background-color: transparent;
}

::-webkit-scrollbar:hover {
  background-color: #e8e8e8;
}

::-webkit-scrollbar-button {
  display: none;
}

::-webkit-scrollbar-thumb {
  background-color: #BBBBBB;
  width: 6px;
  height: 6px;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background-color: #999;
}

::-webkit-scrollbar-thumb:active {
  background-color: #666666;
}