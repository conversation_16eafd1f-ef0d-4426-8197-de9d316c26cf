#!/bin/bash

# 检查是否提供了页面名称参数
if [ -z "$1" ]; then
  echo "错误: 请提供页面名称作为参数"
  echo "用法: npm run add-view <页面名称>"
  exit 1
fi

# 设置页面名称变量
PAGE_NAME=$1
VIEW_DIR="src/views/$PAGE_NAME"

# 创建目录结构
mkdir -p "$VIEW_DIR/components"

# 创建 index.vue 文件
cat >"$VIEW_DIR/index.vue" <<EOL
<template>
  <div class="$PAGE_NAME"></div>
</template>

<script>
export default {
  name: '$PAGE_NAME',
  components: {},
  props: {},
  data() {
    return {};
  },
  computed: {},
  watch: {},
  created() {},
  mounted() {},
  destroyed() {},
  methods: {},
};
</script>

<style lang="scss" scoped>
.$PAGE_NAME {
}
</style>

EOL

echo "✨ 成功创建页面 $PAGE_NAME"
echo "📁 目录结构："
echo "   $VIEW_DIR/"
echo "   ├── components/"
echo "   └── index.vue"
