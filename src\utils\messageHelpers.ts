/**
 * @fileoverview 消息提示工具函数
 * @description 提供统一的消息提示功能，包括带交互功能的复杂提示
 */

import { message } from '@fk/faicomponent';
import type { CreateElement } from 'vue';
import router from '@/router';

/**
 * 显示保存成功提示，包含跳转到"我的作品"的链接
 * @description 统一的保存成功提示，提供一致的用户体验
 */
export function showSaveSuccessMessage(): void {
  message.success((h: CreateElement) => {
    return h('span', [
      '保存成功，可前往',
      h(
        'span',
        {
          style: {
            color: '#1890ff',
            cursor: 'pointer',
            textDecoration: 'underline',
            marginLeft: '2px',
            marginRight: '2px',
          },
          on: {
            click: () => {
              const resolvedPath = router.resolve({ path: '/work' });
              window.open(resolvedPath.href, '_blank');
            },
          },
        },
        '我的作品',
      ),
      '预览与下载',
    ]);
  });
}

/**
 * 显示带自定义链接的成功提示
 * @param text 提示文本
 * @param linkText 链接文本
 * @param linkPath 链接路径
 * @param openInNewTab 是否在新标签页打开，默认为true
 */
export function showSuccessMessageWithLink(
  text: string,
  linkText: string,
  linkPath: string,
  openInNewTab: boolean = true,
): void {
  message.success((h: CreateElement) => {
    return h('span', [
      text,
      h(
        'span',
        {
          style: {
            color: '#1890ff',
            cursor: 'pointer',
            textDecoration: 'underline',
            marginLeft: '2px',
            marginRight: '2px',
          },
          on: {
            click: () => {
              const resolvedPath = router.resolve({ path: linkPath });
              if (openInNewTab) {
                window.open(resolvedPath.href, '_blank');
              } else {
                router.push(linkPath);
              }
            },
          },
        },
        linkText,
      ),
    ]);
  });
}
