/**
 * @fileoverview 轮询间隔计算工具函数
 * @description 提供智能轮询间隔计算功能
 */

import { PROJECT_TYPE_VIDEO, PROJECT_TYPE_IMAGE } from '@/views/EditProjectView/constants';
import { POLLING_INTERVALS, PROGRESS_THRESHOLDS } from '../config';
import { IntervalCalculationParams } from '../types';
import { logger } from '@/utils/logger';

/**
 * 计算智能轮询间隔
 * 基于项目类型和所有进行中作品的最大进度来动态调整轮询频率
 * @param params 计算参数
 * @returns 轮询间隔（毫秒）
 */
export const calculatePollingInterval = (params: IntervalCalculationParams): number => {
  const { projectType, maxProgress, pollingWorksCount } = params;

  if (pollingWorksCount === 0) {
    // 没有轮询作品时，根据项目类型返回默认间隔
    return projectType === PROJECT_TYPE_IMAGE
      ? POLLING_INTERVALS.IMAGE_LOW_PROGRESS
      : POLLING_INTERVALS.VIDEO_LOW_PROGRESS;
  }

  // 根据项目类型和最大进度返回对应的轮询间隔
  if (projectType === PROJECT_TYPE_IMAGE) {
    // 图文项目：基于50%进度分界线
    // 边界情况：进度恰好等于50%时使用高频间隔
    if (maxProgress < PROGRESS_THRESHOLDS.IMAGE_THRESHOLD) {
      return POLLING_INTERVALS.IMAGE_LOW_PROGRESS; // 50%以下：4秒
    }
    return POLLING_INTERVALS.IMAGE_HIGH_PROGRESS; // 50%及以上：2秒
  } else if (projectType === PROJECT_TYPE_VIDEO) {
    // 视频项目：保持原有逻辑
    if (maxProgress < PROGRESS_THRESHOLDS.VIDEO_LOW) {
      return POLLING_INTERVALS.VIDEO_LOW_PROGRESS; // 20%以下：5秒
    }
    if (maxProgress >= PROGRESS_THRESHOLDS.VIDEO_HIGH) {
      return POLLING_INTERVALS.VIDEO_HIGH_PROGRESS; // 90%及以上：3秒
    }
    return POLLING_INTERVALS.VIDEO_MEDIUM_PROGRESS; // 20~90%：10秒
  } else {
    // 未知项目类型：使用视频项目的默认逻辑作为兜底
    logger.debug('⚠️ 未知项目类型，使用视频项目轮询间隔', {
      项目类型: projectType,
      最大进度: `${maxProgress.toFixed(1)}%`,
    });

    if (maxProgress < PROGRESS_THRESHOLDS.VIDEO_LOW) {
      return POLLING_INTERVALS.VIDEO_LOW_PROGRESS;
    }
    if (maxProgress >= PROGRESS_THRESHOLDS.VIDEO_HIGH) {
      return POLLING_INTERVALS.VIDEO_HIGH_PROGRESS;
    }
    return POLLING_INTERVALS.VIDEO_MEDIUM_PROGRESS;
  }
};
