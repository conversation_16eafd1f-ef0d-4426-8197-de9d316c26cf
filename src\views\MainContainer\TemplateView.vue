<template>
  <div
    class="template-view pb-0 bg-background overflow-y-scroll h-[calc(100vh-70px)] relative"
    @scroll.passive="handleScroll($event)"
  >
    <!-- 搜索结果统计 -->
    <div
      v-show="isSearch && currSearchWord"
      class="text-[14px] mt-[20px] ml-[20px]"
    >
      <span
        class="text-assist hover:text-primary cursor-pointer"
        @click="handleClickBackToAll"
        >模板中心</span
      >
      <span class="text-assist"> /</span>
      <span class="text-title">
        共找到与
        <span class="font-bold">{{ `"${currSearchWord}"` }}</span>
        相关的模板
        <span>{{ totalTemplates }}</span>
        个
      </span>
    </div>
    <div class="mt-[20px]">
      <!-- 筛选项 -->
      <div
        class="top-[-8px] z-10 bg-white p-[32px_32px_24px] rounded-t-[16px] mx-[20px] transition-all"
        :class="{
          'border-b border-b-[#ededed]': !isSearch && isScrollTop === false,
          'position-sticky': !isSearch,
        }"
      >
        <!-- 行业筛选 -->
        <fa-skeleton
          v-if="isShowSkeleton"
          :loading="isShowSkeleton"
          :paragraph="false"
          active
          class="filter-skeleton"
        >
        </fa-skeleton>
        <div class="filter-item flex-justify-between" v-else>
          <div class="flex flex-none">
            <div class="filter-item-title">行业</div>
            <div
              class="filter-item-content"
              v-for="industry in industryList"
              @click="switchIndustry(industry.id)"
              :class="{ active: currentIndustry == industry.id }"
            >
              {{ industry.name }}
            </div>
          </div>
          <div v-if="isInteralStaff">
            <el-checkbox v-model="filterInteralTemplate"
              >仅查看内部上架模板</el-checkbox
            >
          </div>
        </div>
        <!-- 场景筛选 -->
        <fa-skeleton
          v-if="isShowSkeleton"
          :loading="isShowSkeleton"
          :paragraph="false"
          active
          class="filter-skeleton"
        >
        </fa-skeleton>
        <div class="filter-item" v-else>
          <div class="filter-item-title">场景</div>
          <div
            class="filter-item-content"
            v-for="scene in sceneList"
            @click="switchScene(scene.id)"
            :class="{ active: currentScene === scene.id }"
          >
            {{ scene.name }}
          </div>
        </div>
        <!-- 类型筛选 -->
        <fa-skeleton
          v-if="isShowSkeleton"
          :loading="isShowSkeleton"
          :paragraph="false"
          active
          class="type-skeleton"
        >
        </fa-skeleton>
        <div class="plans-diff-tab-container" v-else>
          <div class="plans-diff-slider" :style="plansDiffSliderCal"></div>
          <div class="plans-diff-tabs">
            <button
              v-for="(type, index) in typeList"
              :key="type.id"
              class="reset-button plans-diff-tab"
              :class="{
                'plans-diff-tab-free': true,
                'plans-diff-tab-free-active': currentType === index,
              }"
              @click="switchType(Number(type.id))"
            >
              {{ type.name }}
            </button>
          </div>
        </div>
      </div>
      <!-- 模板列表 -->
      <div class="p-[20px] pt-0">
        <div class="template-list" v-show="templateList.length > 0">
          <fa-skeleton
            v-if="isShowSkeleton"
            :paragraph="false"
            active
            class="template-skeleton"
            v-for="i in isShowSkeleton ? 20 : 0"
            :key="`skeleton-${i}`"
          ></fa-skeleton>
          <template v-show="templateList.length > 0 && !loadingTemplate">
            <TemplateItem
              v-for="template in isShowSkeleton ? [] : templateList"
              :key="template.id"
              :template-info="template"
              @preview="showTemplatePreview"
            />
          </template>
        </div>
        <!-- 空状态 -->
        <div
          class="flex flex-col items-center justify-center bg-white rounded-b-[16px]"
          v-show="
            templateList.length === 0 && !isShowSkeleton && !loadingTemplate
          "
        >
          <div
            class="min-h-[200px] h-[calc(100vh-295px)] flex flex-col items-center justify-center w-full"
            :class="{ 'h-[calc(100vh-335px)]': isSearch }"
          >
            <img
              src="@/assets/common/empty.webp"
              class="w-[159px] h-auto mb-[24px]"
              v-show="!isSearch"
            />
            <div class="text-assist text-[15px]" v-show="!isSearch">
              暂无相关模板
            </div>
            <img
              src="@/assets/common/searchEmpty.webp"
              class="w-[159px] h-auto mb-[24px]"
              v-show="isSearch"
            />
            <div class="text-assist text-[15px]" v-show="isSearch">
              找不到搜索的模板
            </div>
          </div>
        </div>
        <!-- loading -->
        <div
          class="flex flex-col items-center justify-center bg-white rounded-b-[16px] h-[calc(100vh-335px)]"
          v-show="loadingTemplate && !isShowSkeleton"
        >
          <fa-spin />
        </div>
      </div>
    </div>
    <TemplatePreview
      v-model="isShowPreview"
      :template-info="previewTemplateInfo"
    />
  </div>
</template>

<script lang="ts" setup>
import { PROJECT_TYPE, PROJECT_TYPE_NAME } from '@/constants/project';
import { computed, onMounted, ref, watch } from 'vue';
import TemplateItem from '@/components/TemplateView/TemplateItem.vue';
import TemplatePreview from '@/components/TemplateView/TemplatePreview.vue';
import store from '@/store';
import {
  currentIndustry,
  currentScene,
  currentType,
  filterTemplate,
  nextPage,
  searchTemplate,
  isSearch,
  currSearchWord,
  totalTemplates,
  templateList,
  loadingTemplate,
  DEFAULT_INDUSTRY,
  DEFAULT_SCENE,
  searchValue,
  filterInteralTemplate,
} from '@/hook/useTemplateSearch';
import { Template } from '@/types';
import { debounce } from 'lodash-es';
import { getTemplatePreviewInfo } from '@/api/TemplateView';
import { useSkeleton } from '@/hook/useSkeleton';
import { message } from '@fk/faicomponent';

const { isShowSkeleton } = useSkeleton();

/****************查看内部模板 start******************/
const isInteralStaff = computed(() => store.state.user.isStaff); // 是否为内部用户

watch(
  () => filterInteralTemplate.value,
  async () => {
    filterTemplate();
  },
);
/****************查看内部模板 end******************/

/****************筛选功能 start******************/

/** 行业列表 */
const industryList = computed(() => {
  return store.state.system.industryMap
    ? Object.entries(store.state.system.industryMap)
        .map(([id, name]) => ({
          id: Number(id),
          name: name as string,
        }))
        .sort((a, b) => a.id - b.id)
    : [];
});
/** 切换行业 */
const switchIndustry = (industryId: number) => {
  currentIndustry.value = industryId;
  filterTemplate();
};

/** 场景列表 */
const sceneList = computed(() => {
  return store.state.system.sceneMap
    ? Object.entries(store.state.system.sceneMap)
        .map(([id, name]) => ({
          id: Number(id),
          name: name as string,
        }))
        .sort((a, b) => a.id - b.id)
    : [];
});
/** 切换场景 */
const switchScene = (sceneId: number) => {
  currentScene.value = sceneId;
  filterTemplate();
};

/** 模板类型 */
const typeList = ref(
  Object.keys(PROJECT_TYPE_NAME).map(key => ({
    id: key,
    name: PROJECT_TYPE_NAME[key as unknown as PROJECT_TYPE],
  })),
);
// 切换模板类型
const switchType = (typeId: number) => {
  currentType.value = typeId;
  filterTemplate();
};
const plansDiffSliderCal = computed(() => ({
  transform: `translateX(${currentType.value * 100}%)`,
}));
/** 返回全部模板 */
const handleClickBackToAll = () => {
  searchValue.value = '';
  currentIndustry.value = DEFAULT_INDUSTRY;
  currentScene.value = DEFAULT_SCENE;
  filterTemplate();
};

/****************筛选功能 end******************/

/****************初始化模板列表 start******************/
onMounted(() => {
  searchValue.value = '';
  currentIndustry.value = DEFAULT_INDUSTRY;
  currentScene.value = DEFAULT_SCENE;
  currentType.value = PROJECT_TYPE.VIDEO;
  searchTemplate();
});
/****************模板列表 end******************/

/****************模板预览窗 start******************/
const isShowPreview = ref(false);
const previewTemplateInfo = ref<Template | null>(null);
const showTemplatePreview = async (templateInfo: Template) => {
  const [err, res] = await getTemplatePreviewInfo(templateInfo.id);
  if (err) {
    message.error(err.message);
    throw new Error(err.message);
  }
  isShowPreview.value = true;
  previewTemplateInfo.value = res.data;
};
/****************模板预览窗 end******************/

/****************模板列表滚动监听 start******************/
const isScrollTop = ref(true);
const setIsScrollTop = debounce((val: boolean) => {
  isScrollTop.value = val;
}, 50);
function handleScroll(event: Event) {
  const target = event.target as HTMLElement;
  // 判断是否滚动到底部，如果到底部则触发下一页加载
  const isBottom =
    target.scrollHeight - target.scrollTop <= target.clientHeight + 50; // 50px是一个容错值，可以根据需要调整
  if (isBottom) {
    nextPage();
  }
  setIsScrollTop(target.scrollTop === 0);
}
/****************模板列表滚动监听 end******************/
</script>

<style scoped lang="scss">
.template-view {
  // 骨架屏
  .filter-skeleton {
    ::v-deep .fa-skeleton-content {
      .fa-skeleton-title {
        @apply w-full h-32px mt-10px;
      }
    }
    &:first-child {
      ::v-deep .fa-skeleton-content {
        .fa-skeleton-title {
          @apply mt-0;
        }
      }
    }
  }
  .type-skeleton {
    ::v-deep .fa-skeleton-content {
      .fa-skeleton-title {
        @apply w-164px h-40px rounded-20px mt-26px;
      }
    }
  }
  .template-skeleton {
    ::v-deep .fa-skeleton-content {
      .fa-skeleton-title {
        @apply w-full h-full aspect-[210/373];
      }
    }
  }
  // 筛选项
  .filter-item {
    @apply flex mb-[20px];
    .filter-item-title {
      @apply font-bold text-[15px] text-title mr-[40px];
    }
    .filter-item-content {
      @apply text-[14px] text-text mr[24px] cursor-pointer;
      &.active {
        @apply text-primary font-bold;
      }
      &:hover {
        @apply text-primary;
      }
    }
  }
  // 模板类型切换
  .reset-button {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    border: none;
    background: none;
    padding: 0;
    margin: 0;
    outline: none;
    border-radius: 0;
    cursor: pointer;
  }
  .plans-diff-tab-container {
    visibility: visible;
    position: relative;
    border-radius: 9999px;
    width: 164px;
    background: #f3f3f5;
    border: 4px solid #f3f3f5;
    .plans-diff-tabs {
      display: flex;
      position: relative;
      .plans-diff-tab {
        flex: 1;
        padding: 0.41rem 0;
        text-align: center;
        cursor: pointer;
        position: relative;
        transition: all 0.3s ease;
        font-weight: 700;
        font-size: 15px;
        line-height: 19px;
        user-select: none;
        @apply text-assist;
      }
      & .plans-diff-tab-free-active {
        @apply text-title;
      }
    }
    .plans-diff-slider {
      position: absolute;
      top: 0;
      bottom: 0;
      left: 0;
      width: 50%;
      width: calc(100% / 2);
      background: #ffffff;
      border-radius: 9999px;
      transition: transform 0.3s ease;
      box-shadow: 0 0.042667rem 0.17rem rgba(0, 0, 0, 0.1);
    }
  }

  // 模板列表
  .template-list {
    @apply bg-white grid px-[32px] pb-[32px] gap-[24px] rounded-b-[16px];
    @media screen and (max-width: 1430px) {
      @apply grid-cols-5;
    }
    @media screen and (min-width: 1431px) and (max-width: 1664px) {
      @apply grid-cols-6;
    }
    @media screen and (min-width: 1665px) {
      @apply grid-cols-7;
    }
  }
}
</style>
