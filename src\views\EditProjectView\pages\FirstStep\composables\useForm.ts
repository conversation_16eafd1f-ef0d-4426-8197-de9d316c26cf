/**
 * @description 表单处理相关的composable
 * 整合了表单配置、数据管理和验证功能
 */
import { ref, type Ref } from 'vue';
import { mapComponentTypeToFieldType } from '@/constants/fieldTypes';
import { mapFieldTypeToComponentType } from '@/constants/fieldTypes';
import {
  PROJECT_TYPE_VIDEO,
  PROJECT_TYPE_IMAGE,
} from '@/views/EditProjectView/constants/index';
import { FieldType } from '@/views/EditProjectView/types/index';
import type {
  EditProjectViewData,
  ValidationResult,
  InputFormItem,
  ResFormItem,
  ProjectType,
  FormItemInternal,
  DynamicFormRef,
  FileFormRef,
  ResettableFormRef,
  DynamicFileFormInstance,
  FormConfig,
  FormRule,
  MediaType,
  FileInfo,
} from '@/views/EditProjectView/types/index';
import {
  MEDIA_TYPES,
  FORM_LAYOUT,
  VALIDATION_TRIGGER,
} from '@/views/EditProjectView/types/index';
import { isInvalidFileStatus } from '@/views/EditProjectView/types/utils';

/**
 * 表单处理Hook的返回值类型
 * @interface UseFormReturn
 */
interface UseFormReturn {
  /** 表单配置 */
  formConfig: Ref<FormConfig>;
  /** 基础表单项配置 */
  formItems: Ref<FormItemInternal[]>;
  /** 文件上传表单项配置 */
  fileFormItems: Ref<FormItemInternal[]>;
  /** 基础表单初始值 */
  initialValues: Ref<Record<string, unknown>>;
  /** 文件表单初始值 */
  fileInitialValues: Ref<Record<string, unknown>>;
  /** 数据加载状态 */
  loading: Ref<boolean>;
  /** 获取所有表单数据 */
  getAllFormData: (
    dynamicForm: DynamicFormRef,
    dynamicFormFiles: FileFormRef,
  ) => Record<string, unknown>;
  /** 重置所有表单 */
  resetAllForms: (
    dynamicForm: ResettableFormRef,
    dynamicFormFiles: ResettableFormRef,
  ) => void;
  /** 验证所有表单（生成模式：完整校验） */
  validateAllForms: (
    dynamicForm: DynamicFormRef | null,
    dynamicFormFiles: DynamicFileFormInstance | null,
  ) => Promise<ValidationResult>;
  /** 验证所有表单（保存模式：仅最大长度校验） */
  validateAllFormsForSave: (
    dynamicForm: DynamicFormRef | null,
    dynamicFormFiles: DynamicFileFormInstance | null,
  ) => Promise<ValidationResult>;
  /** 处理表单配置 */
  processFormConfig: (data: EditProjectViewData) => void;
}

/**
 * 表单处理Hook
 * @param projectType 项目类型
 * @returns 表单配置、数据和验证方法
 */
export function useForm(projectType: ProjectType): UseFormReturn {
  // ============= 表单配置相关 =============

  // 表单配置，使用严格的类型约束
  const formConfig = ref<FormConfig>({
    layout: FORM_LAYOUT.VERTICAL,
    labelCol: { span: 24 },
    wrapperCol: { span: 24 },
    defaultMediaType: MEDIA_TYPES.VIDEO,
    validateOnChange: false,
    validateOnBlur: false,
    scrollToError: false,
    scrollOffset: 100,
  });

  // 基础表单项配置
  const formItems = ref<FormItemInternal[]>([]);
  // 文件上传表单配置
  const fileFormItems = ref<FormItemInternal[]>([]);

  // 校验模式枚举
  const VALIDATION_MODE = {
    GENERATE: 'generate', // 生成模式：完整校验
    SAVE: 'save', // 保存模式：仅最大长度校验
  } as const;

  // 当前校验模式
  let currentValidationMode: string = VALIDATION_MODE.GENERATE;

  /**
   * 计算视频文件总时长
   * @param files 文件列表
   * @returns 总时长（秒）
   */
  const calculateVideoTotalDuration = (files: unknown[]): number => {
    let totalDuration = 0;

    for (const file of files) {
      if (file && typeof file === 'object' && 'duration' in file) {
        const duration = Number(file.duration) || 0;
        totalDuration += duration;
      }
    }

    return totalDuration;
  };

  /**
   * 检查视频文件时长是否满足要求
   * @param files 文件列表
   * @returns 校验结果 { valid: boolean, totalDuration: number, message?: string }
   */
  const validateVideoFileDuration = (
    files: unknown[],
  ): {
    valid: boolean;
    totalDuration: number;
    message?: string;
  } => {
    const totalDuration = calculateVideoTotalDuration(files);
    const MIN_DURATION = 15; // 15秒
    const MAX_DURATION = 600; // 10分钟

    if (totalDuration < MIN_DURATION) {
      return {
        valid: false,
        totalDuration,
        message: '视频素材总时长需超过15s，建议上传多段视频素材',
      };
    } else if (totalDuration > MAX_DURATION) {
      return {
        valid: false,
        totalDuration,
        message:
          '视频素材总时长不能超过10min，建议减少视频素材或上传更短的视频素材',
      };
    }

    return {
      valid: true,
      totalDuration,
    };
  };

  /**
   * 设置校验模式
   * @param mode 校验模式
   */
  const setValidationMode = (mode: string): void => {
    currentValidationMode = mode;
  };

  /**
   * 创建必填验证规则
   * @param _label 表单项标签（暂未使用，保留用于未来扩展）
   * @param _componentType 组件类型（暂未使用，保留用于未来扩展）
   * @returns 验证规则配置
   */
  const createRequiredRule = (
    _label: string,
    _componentType: string,
  ): FormRule => {
    return {
      trigger: VALIDATION_TRIGGER.MAGIC,
      triggerAuto: true,
      validator: (
        _rule: FormRule,
        value: unknown,
        callback: (error?: Error) => void,
      ) => {
        // 保存模式跳过必填校验
        if (currentValidationMode === VALIDATION_MODE.SAVE) {
          console.log('保存模式：跳过必填校验');
          return callback();
        }

        // 统一的空值检查逻辑
        const isEmpty = Array.isArray(value)
          ? value.length === 0
          : !value || String(value).trim() === '';

        if (isEmpty) {
          callback(new Error('输入不能为空'));
        } else {
          callback();
        }
      },
    };
  };

  /**
   * 创建长度验证规则
   * @param item 表单项数据
   * @param componentType 组件类型
   * @returns 验证规则配置
   */
  const createLengthRule = (
    item: InputFormItem,
    componentType: string,
  ): FormRule => {
    const { label, minLength, maxLength } = item;
    const isSelectTags = componentType === 'selectTags';

    return {
      trigger: VALIDATION_TRIGGER.MAGIC,
      triggerAuto: true,
      min: minLength,
      max: maxLength,
      validator: (
        _rule: FormRule,
        value: unknown,
        callback: (error?: Error) => void,
      ) => {
        let valueStr = Array.isArray(value)
          ? value.join('')
          : String(value || '');
        if (isSelectTags) {
          // 特殊处理selectTags组件，将数组转为字符串后，去掉逗号
          valueStr = valueStr.replace(/,/g, '');
        }
        const length = valueStr ? valueStr.length : 0;

        // 保存模式下跳过最小长度校验
        const shouldCheckMinLength =
          currentValidationMode !== VALIDATION_MODE.SAVE;

        if (shouldCheckMinLength && minLength && length < minLength) {
          callback(new Error(`${label}不能少于${minLength}个字符`));
        } else if (maxLength && length > maxLength) {
          callback(new Error(`输入超出${maxLength}个字符限制`));
        } else {
          callback();
        }
      },
    };
  };

  /**
   * 创建基础表单项配置
   * @param item 表单项原始数据
   * @returns 处理后的表单项配置
   */
  const createBasicFormItem = (item: InputFormItem): FormItemInternal => {
    const componentType = mapFieldTypeToComponentType(item.filedType);
    const formItem: FormItemInternal = {
      type: componentType,
      label: item.label,
      prop: item.variable,
      placeholder: item.placeholder || '',
      rules: [],
      attrs: {},
    };

    // 添加必填验证规则
    if (item.required) {
      formItem.rules.push(createRequiredRule(item.label, componentType));
      formItem.required = true;
    }

    // 添加长度限制验证规则
    if (item.minLength || item.maxLength) {
      // 添加长度相关属性
      let adjustedMaxLength = item.maxLength;

      // 特殊处理 FieldType.TAGS 标签选择字段
      if (item.filedType === FieldType.TAGS && item.maxLength) {
        // 标签规定了最多10个，因此用户端的输入限制需要在dify接口返回的表单字段限制基础上-10
        adjustedMaxLength = item.maxLength - 10;

        // 边界情况处理：如果出现负数，输出警告并使用原始值
        if (adjustedMaxLength < 0) {
          console.warn(
            `原型长度配置错误: TAGS字段 "${item.label}" 的maxLength(${item.maxLength})减去10后为负数，使用原始值`,
          );
          adjustedMaxLength = item.maxLength;
        }
      }

      formItem.attrs.maxLength = adjustedMaxLength;
      formItem.attrs.showCount = true;

      // 创建调整后的item副本用于验证规则
      const adjustedItem = { ...item, maxLength: adjustedMaxLength };
      formItem.rules.push(createLengthRule(adjustedItem, componentType));
    }

    // 添加选项（如果有）
    if (item.options && item.options.length > 0) {
      formItem.options = item.options;
    }

    // 添加AI相关配置
    if (item.openAiTip) {
      formItem.openAiTip = true;
    }

    if (item.prompt && item.prompt.length > 0) {
      formItem.prompt = item.prompt;
    }

    return formItem;
  };

  /**
   * 创建文件上传表单项配置
   * @param item 文件上传表单项原始数据
   * @returns 处理后的表单项配置
   */
  const createFileFormItem = (item: ResFormItem): FormItemInternal => {
    // 使用严格的MediaType类型
    const mediaType: MediaType =
      item.type === PROJECT_TYPE_VIDEO ? MEDIA_TYPES.VIDEO : MEDIA_TYPES.IMAGE;

    const formItem: FormItemInternal = {
      type: 'upload',
      label: item.label,
      prop: item.variable,
      mediaType,
      maxLength: item.maxLength || 5,
      description: item.description || '',
      rules: [],
      attrs: {},
      hasModifyMouth: item.hasModifyMouth, // 后端功能还没完成，前端暂时设置为false / hasModifyMouth: item.hasModifyMouth || false,
      openModifyMouth: item.openModifyMouth || false,
      // 添加资源ID和类型，用于获取示例资源
      resId: item.resId || '',
      resType: item.resType || 0,
      coverId: item.coverId || '',
      coverType: item.coverType || 0,
    };

    if (item.required) {
      formItem.rules.push({
        trigger: VALIDATION_TRIGGER.MAGIC,
        triggerAuto: true,
        validator: (
          _rule: FormRule,
          value: unknown,
          callback: (error?: Error) => void,
        ) => {
          // 保存模式跳过文件必填校验
          if (currentValidationMode === VALIDATION_MODE.SAVE) {
            console.log('保存模式：跳过文件必填校验');
            return callback();
          }

          // 检查文件列表是否为空
          const isEmpty =
            !value || (Array.isArray(value) && value.length === 0);

          if (isEmpty) {
            // 根据媒体类型显示不同的错误信息
            const errorMessage =
              mediaType === MEDIA_TYPES.VIDEO
                ? '视频素材总时长需超过15s，建议上传多段视频素材'
                : '请上传相关素材';
            callback(new Error(errorMessage));
          } else {
            callback();
          }
        },
      });
      formItem.required = true;
    }

    // 为视频类型文件添加时长校验规则
    if (mediaType === MEDIA_TYPES.VIDEO) {
      formItem.rules.push({
        trigger: VALIDATION_TRIGGER.MAGIC,
        triggerAuto: true,
        validator: (
          _rule: FormRule,
          value: unknown,
          callback: (error?: Error) => void,
        ) => {
          // 保存模式跳过时长校验
          if (currentValidationMode === VALIDATION_MODE.SAVE) {
            console.log('保存模式：跳过视频时长校验');
            return callback();
          }

          // 检查文件列表是否为空
          if (!value || (Array.isArray(value) && value.length === 0)) {
            // 如果没有文件，跳过时长校验（由必填校验处理）
            return callback();
          }

          // 使用公用方法进行视频时长校验
          const files = Array.isArray(value) ? value : [];
          const validationResult = validateVideoFileDuration(files);

          if (!validationResult.valid) {
            callback(new Error(validationResult.message));
          } else {
            callback();
          }
        },
      });

      // 为开启AI改嘴型功能的视频添加单个视频时长校验规则
      formItem.rules.push({
        trigger: VALIDATION_TRIGGER.MAGIC,
        triggerAuto: true,
        validator: (
          _rule: FormRule,
          value: unknown,
          callback: (error?: Error) => void,
        ) => {
          // 保存模式跳过AI改嘴型校验
          if (currentValidationMode === VALIDATION_MODE.SAVE) {
            console.log('保存模式：跳过AI改嘴型视频时长校验');
            return callback();
          }

          // 检查是否开启了AI改嘴型功能
          if (!formItem.hasModifyMouth || !formItem.openModifyMouth) {
            // 未开启AI改嘴型功能，跳过此校验
            return callback();
          }

          // 检查文件列表是否为空
          if (!value || (Array.isArray(value) && value.length === 0)) {
            // 如果没有文件，跳过校验（由必填校验处理）
            return callback();
          }

          // 检查每个视频文件的时长
          const files = Array.isArray(value) ? value : [];
          for (const file of files) {
            if (file && typeof file === 'object' && 'duration' in file) {
              const duration = Number(file.duration) || 0;
              // 如果单个视频时长低于5秒，返回校验失败
              if (duration < 5) {
                return callback(
                  new Error(
                    '开启Ai改嘴型的单个视频时长需超过5s，请重新上传视频素材',
                  ),
                );
              }
            }
          }

          // 所有视频时长都符合要求
          callback();
        },
      });
    }

    return formItem;
  };

  // ============= 表单数据相关 =============

  // 基础表单的初始值
  const initialValues = ref<Record<string, unknown>>({});
  // 文件上传表单的初始值
  const fileInitialValues = ref<Record<string, unknown>>({});
  // 数据加载状态
  const loading = ref(false);

  /**
   * 更新表单值
   * @param data API返回的数据
   */
  const updateFormValues = (data: EditProjectViewData) => {
    // 提取并更新基础信息表单数据
    initialValues.value = data.inputForm.reduce((acc, item) => {
      acc[item.variable] = item.value;
      return acc;
    }, {} as Record<string, unknown>);

    // 提取并更新文件上传表单数据
    fileInitialValues.value = data.resForm.reduce((acc, item) => {
      acc[item.variable] = item.value || [];
      return acc;
    }, {} as Record<string, unknown>);
  };

  /**
   * 获取所有表单数据
   * @param dynamicForm 基础表单引用
   * @param dynamicFormFiles 文件表单引用
   * @returns 合并后的表单数据
   */
  const getAllFormData = (
    dynamicForm: DynamicFormRef,
    dynamicFormFiles: FileFormRef,
  ): Record<string, unknown> => {
    // 检查表单组件是否存在
    if (!dynamicForm || !dynamicFormFiles) {
      console.warn('表单组件未找到，无法获取数据');
      return {};
    }

    // 获取基础信息表单数据
    const basicFormData = dynamicForm.getFormData();

    // 获取文件上传表单数据
    const filesFormData = dynamicFormFiles.getFormData();

    // 合并两个表单的数据
    return {
      ...basicFormData,
      ...filesFormData,
    };
  };

  /**
   * 重置所有表单
   * @param dynamicForm 基础表单引用
   * @param dynamicFormFiles 文件表单引用
   */
  const resetAllForms = (
    dynamicForm: ResettableFormRef,
    dynamicFormFiles: ResettableFormRef,
  ) => {
    // 检查表单组件是否存在
    if (dynamicForm) {
      dynamicForm.handleReset();
    }

    if (dynamicFormFiles) {
      dynamicFormFiles.handleReset();
    }
  };

  // ============= 表单验证相关 =============

  /**
   * 转换基础表单数据为API格式
   * @param formData 基础表单数据
   * @returns 转换后的表单数据
   */
  const transformInputForm = (formData: Record<string, unknown>) => {
    return formItems.value.map((item: FormItemInternal) => ({
      variable: item.prop,
      label: item.label,
      required:
        item.rules?.some((rule: Record<string, unknown>) => rule.required) ||
        false,
      filedType: mapComponentTypeToFieldType(item.type as string),
      placeholder: item.placeholder || '',
      options: item.options || [],
      openAiTip: item.openAiTip || false,
      prompt: item.prompt || [],
      value: formData[item.prop] || '',
    }));
  };

  /**
   * 转换文件上传表单数据为API格式
   * @param formData 文件上传表单数据
   * @returns 转换后的表单数据
   */
  const transformResForm = (formData: Record<string, unknown>) => {
    return fileFormItems.value.map((item: FormItemInternal, index: number) => ({
      id: index + 1,
      type:
        item.mediaType === MEDIA_TYPES.VIDEO
          ? PROJECT_TYPE_VIDEO
          : PROJECT_TYPE_IMAGE,
      variable: item.prop,
      label: item.label,
      required:
        item.rules?.some((rule: Record<string, unknown>) => rule.required) ||
        false,
      value: formData[item.prop] || [],
      minLength: 0, // 默认最小长度为0
      maxLength: item.maxLength || 5,
      description: item.description || '',
      hasModifyMouth: item.hasModifyMouth || false,
      openModifyMouth: item.openModifyMouth || false,
    }));
  };

  /**
   * 检查表单数据中的失效文件
   * @param filesData 文件表单数据
   * @returns 失效文件列表
   */
  const checkInvalidFiles = (
    filesData: Record<string, unknown>,
  ): FileInfo[] => {
    const invalidFiles: FileInfo[] = [];

    // 遍历所有文件字段
    Object.values(filesData).forEach(fieldValue => {
      if (Array.isArray(fieldValue)) {
        // 检查每个文件的状态
        fieldValue.forEach((file: unknown) => {
          if (file && typeof file === 'object' && 'status' in file) {
            const fileInfo = file as FileInfo;
            if (isInvalidFileStatus(fileInfo.status)) {
              invalidFiles.push(fileInfo);
            }
          }
        });
      }
    });

    return invalidFiles;
  };

  /**
   * 验证所有表单并收集数据（生成模式：完整校验）
   * @param dynamicForm 基础表单引用
   * @param dynamicFormFiles 文件表单引用
   * @returns 验证结果和数据
   */
  const validateAllForms = (
    dynamicForm: DynamicFormRef | null,
    dynamicFormFiles: DynamicFileFormInstance | null,
  ): Promise<ValidationResult> => {
    // 设置为生成模式
    setValidationMode(VALIDATION_MODE.GENERATE);
    return performValidation(dynamicForm, dynamicFormFiles);
  };

  /**
   * 验证所有表单并收集数据（保存模式：仅最大长度校验）
   * @param dynamicForm 基础表单引用
   * @param dynamicFormFiles 文件表单引用
   * @returns 验证结果和数据
   */
  const validateAllFormsForSave = (
    dynamicForm: DynamicFormRef | null,
    dynamicFormFiles: DynamicFileFormInstance | null,
  ): Promise<ValidationResult> => {
    // 设置为保存模式
    setValidationMode(VALIDATION_MODE.SAVE);

    // 注意：clearValidate 方法需要在 FirstStep 组件中调用
    // 因为这里的 DynamicFormRef 类型没有 clearValidate 方法
    // 实际的 clearValidate 调用会在组件层面处理

    return performValidation(dynamicForm, dynamicFormFiles);
  };

  /**
   * 执行表单校验的核心逻辑
   * @param dynamicForm 基础表单引用
   * @param dynamicFormFiles 文件表单引用
   * @returns 验证结果和数据
   */
  const performValidation = (
    dynamicForm: DynamicFormRef | null,
    dynamicFormFiles: DynamicFileFormInstance | null,
  ): Promise<ValidationResult> => {
    // 验证表单组件是否存在
    if (!dynamicForm || !dynamicFormFiles) {
      return Promise.resolve({
        valid: false,
        message: !dynamicForm
          ? '基础信息表单组件未找到'
          : '文件上传表单组件未找到',
        data: {} as Record<string, unknown>,
      });
    }

    return Promise.resolve()
      .then(() => dynamicForm.validate())
      .then(basicFormValid => {
        if (!basicFormValid) {
          const errorMessage =
            currentValidationMode === VALIDATION_MODE.SAVE
              ? '请检查表单内容'
              : '请检查表单内容';
          return Promise.reject({
            type: 'validation',
            message: errorMessage,
          });
        }
        return dynamicFormFiles.validate();
      })
      .then(filesFormValid => {
        if (!filesFormValid) {
          let errorMessage: string;
          if (currentValidationMode === VALIDATION_MODE.SAVE) {
            // 根据项目类型显示不同的错误信息
            errorMessage =
              projectType === PROJECT_TYPE_VIDEO
                ? '请检查视频素材，完善后再保存项目'
                : '请检查图片素材，完善后再保存项目';
          } else {
            // 根据项目类型显示不同的错误信息
            errorMessage =
              projectType === PROJECT_TYPE_VIDEO
                ? '请检查视频素材，完善后再生成预览'
                : '请检查图片素材，完善后再生成预览';
          }
          return Promise.reject({
            type: 'validation',
            message: errorMessage,
          });
        }

        // 收集表单数据
        const formData = {
          basic: dynamicForm.getFormData(),
          files: dynamicFormFiles.getFormData(),
        };

        // 检查文件失效状态
        const invalidFiles = checkInvalidFiles(formData.files);
        if (invalidFiles.length > 0) {
          return Promise.reject({
            type: 'validation',
            message: '存在失效的文件，请更换后重试',
          });
        }

        // 转换为API格式
        const apiData = {
          id: 0,
          type: projectType,
          name: '',
          inputForm: transformInputForm(formData.basic),
          resForm: transformResForm(formData.files),
        };

        return {
          valid: true,
          message: '验证通过',
          data: apiData,
        } as ValidationResult;
      })
      .catch(error => {
        return {
          valid: false,
          message:
            error.type === 'validation' ? error.message : '表单验证过程中出错',
          data: {} as Record<string, unknown>,
        };
      });
  };

  /**
   * 处理表单配置和数据
   * @param data API返回的数据
   */
  const processFormConfig = (data: EditProjectViewData) => {
    // 检查数据结构是否完整
    if (!data.inputForm || !data.resForm) {
      console.error('表单配置数据结构不完整');
      return;
    }

    // 转换基础表单配置
    formItems.value = data.inputForm.map(item => createBasicFormItem(item));

    // 转换文件上传表单配置
    fileFormItems.value = data.resForm.map(item => createFileFormItem(item));

    // 提取并更新表单数据
    updateFormValues(data);
  };

  return {
    // 状态
    loading,
    formConfig,
    formItems,
    fileFormItems,
    initialValues,
    fileInitialValues,

    // 表单数据方法
    getAllFormData,
    resetAllForms,

    // 表单验证方法
    validateAllForms,
    validateAllFormsForSave,

    // 处理表单配置和数据
    processFormConfig,
  };
}
