/**
 * @fileoverview 数据获取工具函数
 * @description 封装API调用逻辑，提供统一的数据获取接口
 */

import { GET } from '@/api/request';
import { ERROR_MESSAGES } from '../config';
import type {
  GetTemplateInfoParams,
  GetEditInfoParams,
} from '../types/request';
import type {
  GetTemplateListResponse,
  GetEditInfoResponse,
} from '../types/response';

/**
 * 获取模板数据
 * @description 调用模板信息接口，获取模板配置数据
 * @param templateId 模板ID
 * @returns Promise<[Error | null, GetTemplateListResponse | null]> 错误优先的元组格式
 */
export async function fetchTemplateData(
  templateId: number,
): Promise<[Error | null, GetTemplateListResponse | null]> {
  const params: GetTemplateInfoParams = { templateId };

  const [templateErr, templateRes] = await GET<GetTemplateListResponse>(
    '/api/template/getTemplateInfo',
    { id: params.templateId },
  );

  if (templateErr) {
    const errorMessage = `${ERROR_MESSAGES.TEMPLATE_FETCH_FAILED}: ${templateErr.message}`;
    console.error(errorMessage);
    return [new Error(errorMessage), null];
  }

  if (!templateRes?.data) {
    const errorMessage = ERROR_MESSAGES.TEMPLATE_DATA_EMPTY;
    console.error(errorMessage);
    return [new Error(errorMessage), null];
  }

  return [null, templateRes.data];
}

/**
 * 获取项目数据
 * @description 调用项目编辑信息接口，获取项目数据（可选）
 * @param templateId 模板ID
 * @param projectId 项目ID（可选，新建项目时为空）
 * @returns Promise<[Error | null, GetEditInfoResponse | null]> 错误优先的元组格式
 */
export async function fetchProjectData(
  templateId: number,
  projectId?: number,
): Promise<[Error | null, GetEditInfoResponse | null]> {
  const params: GetEditInfoParams = projectId
    ? { id: projectId }
    : { templateId };

  const requestParams: Record<string, string | number> = {};

  // 优先使用项目ID，如果没有则使用模板ID
  if (params.id) {
    requestParams.id = params.id;
  } else if (params.templateId) {
    requestParams.templateId = params.templateId;
  }

  const [projectErr, projectRes] = await GET<GetEditInfoResponse>(
    '/api/project/getEditInfo',
    requestParams,
  );

  if (projectErr) {
    const errorMessage = `${ERROR_MESSAGES.PROJECT_FETCH_FAILED}: ${projectErr.message}`;
    console.warn(errorMessage);
    return [new Error(errorMessage), null];
  }

  if (!projectRes?.data) {
    return [null, null];
  }

  return [null, projectRes.data];
}
