/**
 * 花字文本坐标计算工具
 * 集中管理所有文本坐标计算逻辑，提供可复用的坐标计算函数
 */

/**
 * 坐标计算所需的参数接口
 */
export interface CoordinateCalculationParams {
  /** SVG宽度 */
  svgWidth: number;
  /** 每行文字宽度数组 */
  widthPerLine: number[];
  /** 文本内边距宽度 */
  textPaddingWidth: number;
  /** 文本对齐方式 */
  textAnchor: string;
  /** SVG文本样式 */
  svgTextStyle: Record<string, string>;
  /** 行高 */
  lineHeight: number;
}

/**
 * 根据对齐方式计算文本X坐标
 * @param idx 文本行索引
 * @param anchor 对齐方式
 * @param params 坐标计算参数
 * @returns 计算后的X坐标
 */
export const getTextXByAnchor = (
  idx: number,
  anchor: string,
  params: Pick<CoordinateCalculationParams, 'svgWidth' | 'widthPerLine'>,
): number => {
  const { svgWidth, widthPerLine } = params;

  switch (anchor) {
    case 'middle':
      return (svgWidth - widthPerLine[idx]) / 2;
    case 'end':
      return svgWidth - widthPerLine[idx];
    case 'start':
    default:
      return 0;
  }
};

/**
 * 计算背景色X坐标
 * @param idx 文本行索引
 * @param anchor 对齐方式
 * @param params 坐标计算参数
 * @returns 背景色X坐标
 */
export const getBackgroundXByAnchor = (
  idx: number,
  anchor: string,
  params: Pick<
    CoordinateCalculationParams,
    'svgWidth' | 'widthPerLine' | 'textPaddingWidth'
  >,
): number => {
  const { svgWidth, widthPerLine, textPaddingWidth } = params;
  const baseX = getTextXByAnchor(idx, anchor, { svgWidth, widthPerLine });
  const paddingOffset = textPaddingWidth / 2;

  switch (anchor) {
    case 'middle':
      return baseX - paddingOffset;
    case 'end':
      return baseX - textPaddingWidth;
    case 'start':
    default:
      return 0;
  }
};

/**
 * 计算阴影层X坐标
 * @param idx 文本行索引
 * @param params 坐标计算参数
 * @returns 阴影层X坐标
 */
export const getTextShadowX = (
  idx: number,
  params: Pick<
    CoordinateCalculationParams,
    | 'svgWidth'
    | 'widthPerLine'
    | 'textAnchor'
    | 'textPaddingWidth'
    | 'svgTextStyle'
  >,
): number => {
  // 先计算主文本层X坐标，再加上阴影偏移量
  const result =
    getTextContentX(idx, params) + (Number(params.svgTextStyle.shadowX) || 0);
  return result;
};

/**
 * 计算阴影层Y坐标
 * @param idx 文本行索引
 * @param params 坐标计算参数
 * @returns 阴影层Y坐标
 */
export const getTextShadowY = (
  idx: number,
  params: Pick<CoordinateCalculationParams, 'lineHeight' | 'svgTextStyle'>,
): number => {
  const { lineHeight, svgTextStyle } = params;

  const halfLineHeight = lineHeight / 2;

  return halfLineHeight * (2 * idx + 1) + (Number(svgTextStyle.shadowY) || 0);
};

/**
 * 计算背景层X坐标
 * @param idx 文本行索引
 * @param params 坐标计算参数
 * @returns 背景层X坐标
 */
export const getTextBgX = (
  idx: number,
  params: Pick<
    CoordinateCalculationParams,
    'svgWidth' | 'widthPerLine' | 'textPaddingWidth' | 'textAnchor'
  >,
): number => {
  const { svgWidth, widthPerLine, textPaddingWidth, textAnchor } = params;
  return (
    getBackgroundXByAnchor(idx, textAnchor, {
      svgWidth,
      widthPerLine,
      textPaddingWidth,
    }) || 0
  );
};

/**
 * 计算背景矩形Y坐标，确保与文字位置匹配
 * @param idx 文本行索引
 * @param params 坐标计算参数
 * @returns 背景矩形Y坐标
 */
export const getTextBgY = (
  idx: number,
  params: Pick<CoordinateCalculationParams, 'lineHeight'>,
): number => {
  const { lineHeight } = params;

  // 计算当前行的起始Y坐标（背景矩形从行起始位置开始）
  const lineStartY = idx * lineHeight;

  return lineStartY;
};

/**
 * 计算主文本层X坐标
 * @param idx 文本行索引
 * @param params 坐标计算参数
 * @returns 主文本层X坐标
 */
export const getTextContentX = (
  idx: number,
  params: Pick<
    CoordinateCalculationParams,
    'svgWidth' | 'widthPerLine' | 'textAnchor' | 'textPaddingWidth'
  >,
): number => {
  const { svgWidth, widthPerLine, textAnchor, textPaddingWidth } = params;
  let baseX = getTextXByAnchor(idx, textAnchor, { svgWidth, widthPerLine });

  // 调整偏移量，左右留出间距
  const paddingOffset = textPaddingWidth / 2;
  if (textAnchor === 'end') {
    baseX -= paddingOffset;
  } else if (textAnchor === 'start') {
    baseX += paddingOffset;
  }

  return baseX;
};

/**
 * 计算文本Y坐标，实现垂直居中
 * @param idx 文本行索引
 * @param params 坐标计算参数
 * @returns 文本Y坐标
 */
export const getTextContentY = (
  idx: number,
  params: Pick<CoordinateCalculationParams, 'lineHeight'>,
): number => {
  const { lineHeight } = params;

  // 计算当前行的起始Y坐标
  const lineStartY = idx * lineHeight;

  // 计算当前行的中心Y坐标（文字垂直居中）
  // 如果使用了 dominant-baseline="central"，Y坐标就是文字的中心点
  const lineCenterY = lineStartY + lineHeight / 2;

  return lineCenterY;
};
