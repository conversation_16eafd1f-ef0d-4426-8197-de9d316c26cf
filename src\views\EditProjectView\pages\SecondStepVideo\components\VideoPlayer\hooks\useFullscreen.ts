import { ref, Ref, onMounted, onUnmounted } from 'vue';
import { BrowserDocument, BrowserElement } from '../types';
import type Player from 'video.js/dist/types/player';

/**
 * 全屏管理 hook
 * @param container - 播放器容器元素引用
 * @param player - VideoJS播放器实例引用（可选）
 */
export function useFullscreen(
  container: Ref<HTMLElement | null>,
  player?: Ref<Player | null>,
) {
  const isFullscreen = ref(false);

  /**
   * 检查是否支持全屏
   */
  const isFullscreenEnabled = (): boolean => {
    const doc = document as BrowserDocument;
    return !!(
      doc.fullscreenEnabled ||
      doc.webkitFullscreenEnabled ||
      doc.mozFullScreenEnabled ||
      doc.msFullscreenEnabled
    );
  };

  /**
   * 检查是否处于全屏状态
   */
  const checkFullscreenState = (): boolean => {
    const doc = document as BrowserDocument;
    return !!(
      doc.fullscreenElement ||
      doc.webkitFullscreenElement ||
      doc.mozFullScreenElement ||
      doc.msFullscreenElement
    );
  };

  /**
   * 请求全屏
   */
  const requestFullscreen = async () => {
    if (!container.value || !isFullscreenEnabled()) return;

    try {
      const element = container.value as BrowserElement;
      if (element.requestFullscreen) {
        await element.requestFullscreen();
      } else if (element.webkitRequestFullscreen) {
        await element.webkitRequestFullscreen();
      } else if (element.mozRequestFullScreen) {
        await element.mozRequestFullScreen();
      } else if (element.msRequestFullscreen) {
        await element.msRequestFullscreen();
      }
      isFullscreen.value = true;
    } catch (error) {
      console.error('请求全屏失败:', error);
    }
  };

  /**
   * 退出全屏
   */
  const exitFullscreen = async () => {
    const doc = document as BrowserDocument;
    try {
      if (doc.exitFullscreen) {
        await doc.exitFullscreen();
      } else if (doc.webkitExitFullscreen) {
        await doc.webkitExitFullscreen();
      } else if (doc.mozCancelFullScreen) {
        await doc.mozCancelFullScreen();
      } else if (doc.msExitFullscreen) {
        await doc.msExitFullscreen();
      }
      isFullscreen.value = false;
    } catch (error) {
      console.error('退出全屏失败:', error);
    }
  };

  /**
   * 切换全屏状态
   */
  const toggleFullscreen = async () => {
    if (checkFullscreenState()) {
      await exitFullscreen();
    } else {
      await requestFullscreen();
    }
  };

  /**
   * 处理全屏状态变化事件
   */
  const handleFullscreenChange = () => {
    const newFullscreenState = checkFullscreenState();
    isFullscreen.value = newFullscreenState;

    // 动态控制video元素的controls属性
    if (player?.value) {
      try {
        // 全屏时显示浏览器原生控件，退出全屏时隐藏
        player.value.controls(newFullscreenState);

        // 对于Firefox浏览器，还需要直接设置video元素的controls属性
        const videoElement = player.value
          .el()
          ?.querySelector('video') as HTMLVideoElement;
        if (videoElement) {
          if (newFullscreenState) {
            videoElement.setAttribute('controls', 'controls');
          } else {
            videoElement.removeAttribute('controls');
          }
        }
      } catch (error) {
        console.warn('设置video controls属性失败:', error);
      }
    }
  };

  // 组件挂载时添加全屏状态变化监听
  onMounted(() => {
    const doc = document as BrowserDocument;

    // 添加各种浏览器的全屏状态变化事件监听
    doc.addEventListener('fullscreenchange', handleFullscreenChange);
    doc.addEventListener('webkitfullscreenchange', handleFullscreenChange);
    doc.addEventListener('mozfullscreenchange', handleFullscreenChange);
    doc.addEventListener('MSFullscreenChange', handleFullscreenChange);
  });

  // 组件卸载时清理全屏状态变化监听
  onUnmounted(() => {
    const doc = document as BrowserDocument;

    // 移除各种浏览器的全屏状态变化事件监听
    doc.removeEventListener('fullscreenchange', handleFullscreenChange);
    doc.removeEventListener('webkitfullscreenchange', handleFullscreenChange);
    doc.removeEventListener('mozfullscreenchange', handleFullscreenChange);
    doc.removeEventListener('MSFullscreenChange', handleFullscreenChange);
  });

  return {
    isFullscreen,
    toggleFullscreen,
  };
}
