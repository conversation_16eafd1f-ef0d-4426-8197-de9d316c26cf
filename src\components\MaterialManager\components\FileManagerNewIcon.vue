<template>
  <div class="file-manager-icon">
    <img v-if="isFolder(info)" src="@/assets/Material/icon_folder.svg" />
    <img
      v-if="iconType(info) === 'video'"
      src="@/assets/Material/icon_video.svg"
    />
    <img
      v-if="iconType(info) === 'image'"
      src="@/assets/Material/icon_image.svg"
    />
  </div>
</template>

<script>
import { isFolder } from '@/utils/resource';
import { iconType } from '@/components/MaterialManager/utils/index.ts';

export default {
  name: 'FileManagerNewIcon',
  props: {
    info: {
      type: Object,
      default: () => {},
    },
  },
  methods: {
    isFolder,
    iconType,
  },
};
</script>

<style lang="scss" scoped>
.file-manager-icon {
}
</style>
