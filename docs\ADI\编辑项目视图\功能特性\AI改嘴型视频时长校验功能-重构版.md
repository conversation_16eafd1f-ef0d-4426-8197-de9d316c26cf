# AI改嘴型视频时长校验功能实现说明（重构版）

## 功能概述

在生成按钮点击时增加判断逻辑：检查开启了AI改嘴型功能的素材组中是否存在单个视频时长低于5秒的视频，如果存在则显示全局提示阻止生成。

**重构亮点**：使用表单验证规则（rule）实现，统一走 `validateAllForms` 流程，更加优雅和符合架构设计。

## 校验规则

### 触发条件
- 用户点击【生成并预览】按钮时
- 表单验证过程中自动触发
- 存在开启了AI改嘴型功能的素材组
- 该素材组中存在单个视频时长低于5秒的视频

### 校验逻辑
1. 检查表单项是否同时满足：
   - `hasModifyMouth: true` (支持AI改嘴型功能)
   - `openModifyMouth: true` (用户已开启AI改嘴型)
2. 对于开启AI改嘴型的素材组，检查其中每个视频文件的时长
3. 如果发现任何视频时长 < 5秒，立即返回校验失败

### 错误提示
- 提示内容：`"开启Ai改嘴型的单个视频时长需超过5s，请重新上传视频素材"`
- 显示方式：表单验证错误提示
- 阻止行为：阻止生成流程继续执行

## 技术实现

### 1. 核心实现位置

文件：`src/views/EditProjectView/pages/FirstStep/composables/useForm.ts`

### 2. 表单规则实现方式

AI改嘴型视频时长校验现在通过表单验证规则实现，而不是单独的校验函数。这种方式更加优雅和统一，符合现有的表单验证架构。

### 3. 新增校验规则

在 `createFileFormItem` 函数中，为视频类型文件添加AI改嘴型校验规则：

```typescript
// 为开启AI改嘴型功能的视频添加单个视频时长校验规则
formItem.rules.push({
  trigger: VALIDATION_TRIGGER.MAGIC,
  triggerAuto: true,
  validator: (
    _rule: FormRule,
    value: unknown,
    callback: (error?: Error) => void,
  ) => {
    // 保存模式跳过AI改嘴型校验
    if (currentValidationMode === VALIDATION_MODE.SAVE) {
      console.log('保存模式：跳过AI改嘴型视频时长校验');
      return callback();
    }

    // 检查是否开启了AI改嘴型功能
    if (!formItem.hasModifyMouth || !formItem.openModifyMouth) {
      // 未开启AI改嘴型功能，跳过此校验
      return callback();
    }

    // 检查文件列表是否为空
    if (!value || (Array.isArray(value) && value.length === 0)) {
      // 如果没有文件，跳过校验（由必填校验处理）
      return callback();
    }

    // 检查每个视频文件的时长
    const files = Array.isArray(value) ? value : [];
    for (const file of files) {
      if (file && typeof file === 'object' && 'duration' in file) {
        const duration = Number(file.duration) || 0;
        // 如果单个视频时长低于5秒，返回校验失败
        if (duration < 5) {
          return callback(
            new Error(
              '开启Ai改嘴型的单个视频时长需超过5s，请重新上传视频素材',
            ),
          );
        }
      }
    }

    // 所有视频时长都符合要求
    callback();
  },
});
```

### 4. 集成到现有流程

校验规则自动集成到现有的表单验证流程中：

```typescript
// 在生成按钮点击时，统一调用表单验证
const validationResult = await formComponent.validateAllForms();
if (!validationResult.valid) {
  message.error(validationResult.message || '表单验证失败');
  return { shouldProceed: false, validationPassed: false };
}
```

## 架构优势

### 1. 统一的验证流程
- 所有验证都通过 `validateAllForms` 统一处理
- 不需要单独的校验函数和调用逻辑
- 符合现有的表单验证架构

### 2. 差异化校验支持
- 生成模式：完整校验（包括AI改嘴型）
- 保存模式：跳过AI改嘴型校验，仅最大长度校验

### 3. 更好的用户体验
- 错误提示与其他表单验证错误一致
- 支持表单级别的错误处理和显示

### 4. 易于维护
- 校验逻辑与表单项定义在同一位置
- 遵循单一职责原则
- 便于测试和调试

## 数据结构依赖

### FormItemInternal 接口
```typescript
interface FormItemInternal {
  // ... 其他属性
  hasModifyMouth?: boolean;    // 是否支持AI改嘴型功能
  openModifyMouth?: boolean;   // AI改嘴型开关的当前状态
}
```

### 视频文件数据结构
```typescript
interface VideoFile {
  duration: number;  // 视频时长，单位为秒
  name: string;      // 文件名
  // ... 其他属性
}
```

## 测试用例

### 1. 正常情况测试
- ✅ 没有开启AI改嘴型功能 → 校验通过
- ✅ 开启AI改嘴型但所有视频时长 ≥ 5秒 → 校验通过
- ✅ 视频时长正好等于5秒 → 校验通过

### 2. 异常情况测试
- ❌ 开启AI改嘴型且存在时长 < 5秒的视频 → 校验失败
- ❌ 多个素材组中有一个开启AI改嘴型且存在短视频 → 校验失败

### 3. 模式测试
- ✅ 保存模式：跳过AI改嘴型校验
- ✅ 生成模式：执行AI改嘴型校验

### 4. 测试文件位置
`src/views/EditProjectView/pages/FirstStep/composables/__tests__/useForm-aiMouthShape.test.ts`

## 与现有架构的集成

### 1. 校验时机
- 在现有表单校验流程中自动执行
- 不需要额外的调用逻辑

### 2. 错误处理
- 使用统一的表单验证错误处理机制
- 返回标准的校验结果格式

### 3. 性能考虑
- 仅在开启AI改嘴型时进行检查，避免不必要的计算
- 发现第一个不符合条件的视频即返回，提高效率

## 使用示例

### 用户操作流程
1. 用户在第一步中上传视频素材
2. 用户开启某个素材组的AI改嘴型功能
3. 用户点击【生成并预览】按钮
4. 系统自动执行表单验证（包括AI改嘴型校验）
5. 如果发现短视频，显示错误提示并阻止生成
6. 用户根据提示重新上传符合要求的视频素材

### 开发者使用
无需额外配置，功能会自动应用到表单验证流程中。

## 注意事项

1. **仅检查开启AI改嘴型的素材组**：未开启AI改嘴型的素材组不受此限制
2. **依赖视频时长数据**：需要确保视频文件包含有效的 `duration` 字段
3. **单个视频时长限制**：检查的是每个视频文件的单独时长，不是总时长
4. **差异化校验**：生成模式执行完整校验，保存模式跳过AI改嘴型校验

## 后续扩展

如需调整功能，可以考虑：
1. 将5秒的时长限制提取为可配置常量
2. 支持不同类型素材的不同时长限制
3. 提供更详细的错误信息，指出具体哪个文件不符合要求
4. 添加批量检查和修复建议

## 重构总结

相比之前的单独校验函数实现，新的表单规则实现方式具有以下优势：

1. **更优雅的架构**：遵循现有的表单验证模式
2. **统一的处理流程**：所有验证都通过 `validateAllForms` 处理
3. **更好的可维护性**：校验逻辑与表单项定义在同一位置
4. **完整的测试覆盖**：包含各种场景的单元测试
5. **符合用户规范要求**：使用 rule 实现，统一走 validateAllForms
