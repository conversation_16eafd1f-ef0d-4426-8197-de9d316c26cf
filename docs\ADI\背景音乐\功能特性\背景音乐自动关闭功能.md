# 背景音乐自动关闭功能实现

## 功能概述

当用户开启背景音乐时，如果音乐资源弹窗未开启智能推荐且没有选中音乐的情况下，**只有在多选模式下**系统会自动关闭背景音乐。在单选模式下，背景音乐保持开启状态。

## 实现原理

### 核心逻辑

1. **用户开启背景音乐** → 显示音乐资源弹窗
2. **用户在弹窗中的操作**：
   - 选择具体音乐 + 未开启智能推荐 → 保存音乐信息，保持背景音乐开启
   - 开启智能推荐 → 清空具体音乐选择，保持背景音乐开启
   - 未开启智能推荐 + 没有选择音乐：
     - **多选模式（maxSelectCount > 1）** → 自动关闭背景音乐
     - **单选模式（maxSelectCount = 1）** → 保持背景音乐开启

### 涉及的文件修改

#### 1. BgMusicSelector.vue 组件修改

**文件路径**: `src/components/VideoEditor/MusicEditor/BgMusicSelector.vue`

**修改内容**:

- 移除了 `handleCancel` 方法中的音乐选择校验逻辑
- 只有在多选模式下取消时才会触发 `changeMusicInfo` 事件，传递当前的选择状态

```javascript
/** 取消 */
const handleCancel = () => {
  currPlayUrl.value = '';
  playingMusic.value = undefined;
  audioPlayer.value?.pause();

  // 只有在多选模式下才发送取消事件，传递当前的选择状态
  if (maxSelectCount > 1) {
    emit('changeMusicInfo', selectedMusicList.value, useAiRecommend.value);
  }

  emit('input', false);
};
```

#### 2. useBgMusic.ts 组合式函数修改

**文件路径**: `src/views/EditProjectView/pages/FirstStep/composables/useBgMusic.ts`

**修改内容**:

1. **初始化逻辑优化**:

   ```javascript
   const bgMusicSettings =
     ref <
     BgMusicSetting >
     {
       open: initialData?.bgMusic?.open || false,
       useAiRecommend: initialData?.bgMusic?.useAiRecommend || false,
       resIds: initialData?.bgMusic?.resIds || [],
     };
   ```

2. **音乐选择处理逻辑**:

   ```javascript
   const handleChangeMusicInfo = async (
     selectedMusics: Music[],
     useAiRecommend: boolean,
     maxSelectCount?: number, // 新增参数：最大选择数量
   ) => {
     bgMusicSettings.value.useAiRecommend = useAiRecommend;

     if (selectedMusics.length > 0 && !useAiRecommend) {
       // 保存所有选中的音乐
       bgMusicInfo.value = selectedMusics;
       bgMusicSettings.value.resIds = selectedMusics.map(music => music.resId);
       if (selectedMusics[0]) {
         bgMusicSettings.value.name = selectedMusics[0].name;
       }
     } else if (useAiRecommend) {
       // 如果使用智能推荐，则清空具体音乐选择
       bgMusicInfo.value = [];
       bgMusicSettings.value.resIds = [];
     } else {
       // 如果未开启智能推荐且没有选择音乐
       bgMusicInfo.value = [];
       bgMusicSettings.value.resIds = [];

       // 只有在多选模式下才自动关闭背景音乐
       const isMultiSelectMode = maxSelectCount && maxSelectCount > 1;
       if (isMultiSelectMode) {
         bgMusicSettings.value.open = false; // 关键：自动关闭背景音乐
       }
     }

     // 触发数据变更回调
     if (onDataChangeCallback) {
       onDataChangeCallback(true);
     }
   };
   ```

## 用户交互流程

### 场景 1：用户选择具体音乐

1. 用户开启背景音乐开关
2. 弹出音乐资源选择器
3. 用户选择一首或多首音乐
4. 点击确认
5. **结果**: 背景音乐保持开启，显示选中的音乐

### 场景 2：用户开启智能推荐

1. 用户开启背景音乐开关
2. 弹出音乐资源选择器
3. 用户开启智能推荐开关
4. 点击确认
5. **结果**: 背景音乐保持开启，显示"智能推荐"

### 场景 3：用户未选择音乐且未开启智能推荐（多选模式）

1. 用户开启背景音乐开关
2. 弹出音乐资源选择器（maxSelectCount > 1）
3. 用户既没有选择音乐，也没有开启智能推荐
4. 点击确认或取消
5. **结果**: 背景音乐自动关闭，回到初始状态

### 场景 4：用户未选择音乐且未开启智能推荐（单选模式）

1. 用户开启背景音乐开关
2. 弹出音乐资源选择器（maxSelectCount = 1）
3. 用户既没有选择音乐，也没有开启智能推荐
4. 点击确认或取消
5. **结果**: 背景音乐保持开启，显示"未选择"

## 测试覆盖

已创建完整的单元测试文件：`src/views/EditProjectView/pages/FirstStep/composables/useBgMusic.test.ts`

测试用例包括：

- ✅ 当用户选择音乐且未开启智能推荐时，应保存音乐信息
- ✅ 当用户开启智能推荐时，应清空具体音乐选择但保持背景音乐开启
- ✅ 当用户未开启智能推荐且没有选择音乐时，在多选模式下应关闭背景音乐
- ✅ 当用户未开启智能推荐且没有选择音乐时，在单选模式下应保持背景音乐开启
- ✅ 当数据变更时，应触发回调函数
- ✅ 当开启背景音乐时，应显示音乐选择器
- ✅ 当切换背景音乐开关时，应触发数据变更回调
- ✅ 应正确初始化背景音乐设置
- ✅ 当没有初始数据时，应使用默认设置

## 注意事项

1. **模式区分**: 只有在多选模式下（maxSelectCount > 1）才会自动关闭背景音乐，单选模式保持原有行为
2. **数据一致性**: 修改确保了背景音乐状态与用户选择的一致性
3. **用户体验**: 在多选模式下避免了用户在没有选择音乐的情况下背景音乐仍然开启的困惑
4. **向后兼容**: 修改不影响现有的音乐选择和智能推荐功能，单选模式行为保持不变
5. **事件触发**: 所有状态变更都会触发相应的数据变更回调，确保表单状态同步
6. **取消事件**: 只有在多选模式下取消操作才会触发 changeMusicInfo 事件

## 相关文件

- `src/components/VideoEditor/MusicEditor/BgMusicSelector.vue` - 音乐选择器组件
- `src/views/EditProjectView/pages/FirstStep/composables/useBgMusic.ts` - 背景音乐管理逻辑
- `src/views/EditProjectView/pages/FirstStep/index.vue` - 第一步页面组件
- `src/views/EditProjectView/pages/FirstStep/composables/useBgMusic.test.ts` - 单元测试文件
