/**
 * 作品状态常量
 * 统一的作品状态定义，用于整个应用程序
 *
 * 状态值说明：
 * - GENERATING=0: 生成中
 * - COMPLETED=1: 已生成
 * - HIDDEN=2: 隐藏
 * - EDIT=3: 编辑中
 * - FAILED=4: 生成失败
 * - 重新生成状态通过 editAgain 字段区分，不再使用独立状态值
 */
export const WORK_STATUS = {
  /** 生成中 */
  GENERATING: 0,
  /** 已生成 */
  COMPLETED: 1,
  /** 隐藏 */
  HIDDEN: 2,
  /** 编辑中 */
  EDIT: 3,
  /** 生成失败 */
  FAILED: 4,
} as const;

/**
 * 作品状态类型
 */
export type WorkStatusType = (typeof WORK_STATUS)[keyof typeof WORK_STATUS];

/**
 * 需要轮询的状态列表
 * 这些状态表示作品正在处理中，需要定期检查状态更新
 * 注意：新逻辑下，重新生成状态通过 editAgain 字段区分
 */
export const POLLING_STATUSES = [WORK_STATUS.GENERATING] as const;

/**
 * 已完成的状态列表
 * 这些状态表示作品已经完成生成
 * 注意：新逻辑下，重新生成完成状态通过 editAgain 字段区分
 */
export const COMPLETED_STATUSES = [WORK_STATUS.COMPLETED] as const;

/**
 * 作品状态名称映射
 * 重新生成状态通过 editAgain 字段区分
 */
export const WORK_STATUS_NAMES = {
  [WORK_STATUS.GENERATING]: '生成中',
  [WORK_STATUS.COMPLETED]: '已完成',
  [WORK_STATUS.HIDDEN]: '隐藏',
  [WORK_STATUS.EDIT]: '编辑中',
  [WORK_STATUS.FAILED]: '生成失败',
} as const;

// ==================== 统一状态处理系统 ====================

/**
 * 作品状态信息接口
 * 用于统一状态处理系统的数据结构
 */
export interface WorkStatusInfo {
  /** 主状态：0=生成中, 1=已生成, 2=隐藏, 3=编辑中, 4=生成失败 */
  status: number;
  /** 是否为重新生成 */
  editAgain?: boolean;
}

/**
 * 统一的作品状态信息获取入口
 * 所有状态判断必须通过此函数获取状态信息
 * @param workItem 作品数据
 * @returns 状态信息对象
 */
export const getWorkStatusInfo = (workItem: {
  status: number;
  editAgain?: boolean;
}): WorkStatusInfo => {
  return {
    status: workItem.status,
    editAgain: workItem.editAgain,
  };
};

// ==================== 专用状态判断函数 ====================
/**
 * 专用状态判断函数说明：
 *
 * 所有状态判断函数都必须通过 getWorkStatusInfo() 统一入口获取状态信息，
 * 然后使用相应的判断函数进行状态检查。
 *
 * 使用示例：
 * ```typescript
 * const statusInfo = getWorkStatusInfo(workItem);
 * if (isGeneratingWork(statusInfo)) {
 *   // 处理生成中状态
 * }
 * ```
 *
 * 函数分类：
 * 1. 基础状态判断函数：判断单一具体状态
 * 2. 组合状态判断函数：判断多个相关状态的组合
 * 3. 业务扩展判断函数：基于状态的业务逻辑判断
 */

// ==================== 1. 基础状态判断函数 ====================
/**
 * 基础状态判断函数用于判断作品的具体单一状态。
 * 这些函数提供最基础的状态检查能力，是其他组合判断函数的基础。
 */

/**
 * 判断是否为生成中状态（普通生成，非重新生成）
 * @param statusInfo - 通过 getWorkStatusInfo() 获取的状态信息对象
 * @returns 如果是普通生成中状态返回 true，否则返回 false
 */
export const isGeneratingWork = (statusInfo: WorkStatusInfo): boolean => {
  return (
    statusInfo.status === WORK_STATUS.GENERATING &&
    statusInfo.editAgain !== true
  );
};

/**
 * 判断是否为普通已生成状态（普通完成，非重新生成完成）
 * @param statusInfo - 通过 getWorkStatusInfo() 获取的状态信息对象
 * @returns 如果是普通已生成状态返回 true，否则返回 false
 */
export const isNormalCompletedWork = (statusInfo: WorkStatusInfo): boolean => {
  return (
    (statusInfo.status === WORK_STATUS.COMPLETED &&
      statusInfo.editAgain !== true) ||
    statusInfo.status === WORK_STATUS.EDIT
  );
};

/**
 * 判断是否为普通生成失败状态（普通失败，非重新生成失败）
 * @param statusInfo - 通过 getWorkStatusInfo() 获取的状态信息对象
 * @returns 如果是普通生成失败状态返回 true，否则返回 false
 */
export const isNormalFailedWork = (statusInfo: WorkStatusInfo): boolean => {
  return (
    statusInfo.status === WORK_STATUS.FAILED && statusInfo.editAgain !== true
  );
};

/**
 * 判断是否为重新生成中状态
 * @param statusInfo - 通过 getWorkStatusInfo() 获取的状态信息对象
 * @returns 如果是重新生成中状态返回 true，否则返回 false
 */
export const isRegeneratingWork = (statusInfo: WorkStatusInfo): boolean => {
  return (
    statusInfo.status === WORK_STATUS.GENERATING &&
    statusInfo.editAgain === true
  );
};

/**
 * 判断是否为重新生成完成状态
 * @param statusInfo - 通过 getWorkStatusInfo() 获取的状态信息对象
 * @returns 如果是重新生成完成状态返回 true，否则返回 false
 */
export const isRecompletedWork = (statusInfo: WorkStatusInfo): boolean => {
  return (
    statusInfo.status === WORK_STATUS.COMPLETED && statusInfo.editAgain === true
  );
};

/**
 * 判断是否为重新生成失败状态
 * @param statusInfo - 通过 getWorkStatusInfo() 获取的状态信息对象
 * @returns 如果是重新生成失败状态返回 true，否则返回 false
 */
export const isRetryFailedWork = (statusInfo: WorkStatusInfo): boolean => {
  return (
    statusInfo.status === WORK_STATUS.FAILED && statusInfo.editAgain === true
  );
};

// ==================== 2. 组合状态判断函数 ====================
/**
 * 组合状态判断函数用于判断多个相关状态的组合情况。
 * 这些函数简化了复杂的状态判断逻辑，提供更高层次的状态抽象。
 */

/**
 * 判断是否为生成中或重新生成中状态
 * @param statusInfo - 通过 getWorkStatusInfo() 获取的状态信息对象
 * @returns 如果是生成中或重新生成中状态返回 true，否则返回 false
 */
export const isAnyGeneratingWork = (statusInfo: WorkStatusInfo): boolean => {
  // 复用基础状态判断函数，保持逻辑一致性
  return isGeneratingWork(statusInfo) || isRegeneratingWork(statusInfo);
};

/**
 * 判断是否为已生成或重新生成完成状态
 * @param statusInfo - 通过 getWorkStatusInfo() 获取的状态信息对象
 * @returns 如果是已生成或重新生成完成状态返回 true，否则返回 false
 */
export const isAnyCompletedWork = (statusInfo: WorkStatusInfo): boolean => {
  // 复用基础状态判断函数，保持逻辑一致性
  return isNormalCompletedWork(statusInfo) || isRecompletedWork(statusInfo);
};

/**
 * 判断是否为生成失败或重新生成失败状态
 * @param statusInfo - 通过 getWorkStatusInfo() 获取的状态信息对象
 * @returns 如果是生成失败或重新生成失败状态返回 true，否则返回 false
 */
export const isAnyFailedWork = (statusInfo: WorkStatusInfo): boolean => {
  // 复用基础状态判断函数，保持逻辑一致性
  return isNormalFailedWork(statusInfo) || isRetryFailedWork(statusInfo);
};

// ==================== 3. 业务扩展判断函数 ====================
/**
 * 业务扩展判断函数基于基础状态提供特定业务场景的判断逻辑。
 * 这些函数封装了具体的业务规则，使业务代码更加清晰易懂。
 */

/**
 * 判断是否需要轮询的状态（生成中或重新生成中）
 * 用于确定是否需要定期检查作品状态更新
 * @param statusInfo - 通过 getWorkStatusInfo() 获取的状态信息对象
 * @returns 如果需要轮询返回 true，否则返回 false
 */
export const isPollingWork = (statusInfo: WorkStatusInfo): boolean => {
  // 复用组合状态判断函数，保持逻辑一致性
  return isAnyGeneratingWork(statusInfo);
};

// ==================== 状态比较函数 ====================

/**
 * 比较两个作品状态信息是否相等
 * 统一的状态比较入口，确保比较的完整性和一致性
 * @param statusInfo1 第一个状态信息对象
 * @param statusInfo2 第二个状态信息对象
 * @returns 如果两个状态信息相等返回 true，否则返回 false
 */
export const isWorkStatusEqual = (
  statusInfo1: WorkStatusInfo,
  statusInfo2: WorkStatusInfo,
): boolean => {
  // 比较主状态和 editAgain 字段
  return (
    statusInfo1.status === statusInfo2.status &&
    statusInfo1.editAgain === statusInfo2.editAgain
  );
};

/**
 * 检查作品状态是否发生了变化
 * @param previousStatusInfo 之前的状态信息
 * @param currentStatusInfo 当前的状态信息
 * @returns 如果状态发生变化返回 true，否则返回 false
 */
export const hasWorkStatusChanged = (
  previousStatusInfo: WorkStatusInfo,
  currentStatusInfo: WorkStatusInfo,
): boolean => {
  return !isWorkStatusEqual(previousStatusInfo, currentStatusInfo);
};

// ==================== 状态名称获取函数 ====================

/**
 * 获取作品状态的显示名称（统一入口）
 * @param statusInfo 状态信息对象
 * @returns 状态显示名称
 */
export const getWorkStatusDisplayName = (
  statusInfo: WorkStatusInfo,
): string => {
  // 根据主状态和 editAgain 组合判断
  if (statusInfo.status === WORK_STATUS.GENERATING) {
    return statusInfo.editAgain === true ? '重新生成中' : '生成中';
  } else if (statusInfo.status === WORK_STATUS.COMPLETED) {
    return statusInfo.editAgain === true ? '重新生成完成' : '已完成';
  } else if (statusInfo.status === WORK_STATUS.FAILED) {
    return statusInfo.editAgain === true ? '重新生成失败' : '生成失败';
  } else if (statusInfo.status === WORK_STATUS.HIDDEN) {
    return '隐藏';
  } else if (statusInfo.status === WORK_STATUS.EDIT) {
    return '编辑中';
  } else {
    return '未知状态';
  }
};
