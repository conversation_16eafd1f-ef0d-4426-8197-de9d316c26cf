<template>
  <div class="material-container" :class="`material-container--${type}`">
    <!-- 标题/音乐/配音类型 -->
    <template v-if="['text', 'music', 'voiceover'].includes(type)">
      <div class="material-container__content">
        <!-- 音乐类型 -->
        <template v-if="type === 'music'">
          <div class="material-container__icon">
            <img
              src="@/assets/common/icons/music-icon.png"
              alt="音乐"
              class="w-10 h-10"
            />
          </div>
          <div class="material-container__info">
            <div class="material-container__title">{{ title }}</div>
            <div class="material-container__duration">{{ duration }}</div>
          </div>
        </template>

        <!-- 配音类型 -->
        <template v-else-if="type === 'voiceover'">
          <div class="material-container__avatar">
            <img :src="avatar" alt="配音" class="w-10 h-10 rounded-full" />
          </div>
          <div class="material-container__info">
            <div class="material-container__title">{{ title }}</div>
            <div class="material-container__voiceover-type">
              {{ voiceType }}
            </div>
          </div>
        </template>

        <!-- 文本类型 -->
        <template v-else>
          <div class="material-container__text">
            {{ content }}
          </div>
        </template>
      </div>

      <!-- 操作按钮 -->
      <div class="material-container__actions" v-if="showActions">
        <el-button
          class="material-container__button"
          type="text"
          size="small"
          @click="$emit('action', 'edit')"
        >
          {{ actionText || '更换' }}
        </el-button>
      </div>
    </template>

    <!-- 卡片类型 -->
    <template v-else-if="type === 'card'">
      <div class="material-container__card">
        {{ content }}
      </div>
    </template>
  </div>
</template>

<script lang="ts">
import { defineComponent, PropType } from 'vue';
import { MaterialType } from './types';

export default defineComponent({
  name: 'MaterialContainer',
  props: {
    type: {
      type: String as PropType<MaterialType>,
      required: true,
      validator: (value: string) =>
        ['text', 'music', 'voiceover', 'card'].includes(value),
    },
    title: {
      type: String,
      default: '',
    },
    content: {
      type: String,
      default: '',
    },
    duration: {
      type: String,
      default: '00:00',
    },
    avatar: {
      type: String,
      default: '',
    },
    voiceType: {
      type: String,
      default: '男声',
    },
    showActions: {
      type: Boolean,
      default: true,
    },
    actionText: {
      type: String,
      default: '',
    },
  },
  emits: ['action'],
});
</script>

<style lang="less" scoped>
.material-container {
  width: 100%;
  background-color: #f7f7f7;
  border-radius: 4px;
  padding: 12px 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;

  &--text {
    padding: 16px;
    background-color: #f7f7f7;
  }

  &--music,
  &--voiceover {
    height: 60px;
  }

  &--card {
    padding: 20px;
    font-size: 14px;
    line-height: 1.6;
    color: #333;
    background-color: #f5f5f5;
  }

  &__content {
    display: flex;
    align-items: center;
    flex: 1;
  }

  &__icon,
  &__avatar {
    margin-right: 12px;
    flex-shrink: 0;
  }

  &__info {
    display: flex;
    flex-direction: column;
    justify-content: center;
    flex: 1;
    overflow: hidden;
  }

  &__title {
    font-size: 14px;
    font-weight: 500;
    color: #333;
    margin-bottom: 4px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  &__duration,
  &__voiceover-type {
    font-size: 12px;
    color: #999;
  }

  &__text {
    font-size: 14px;
    line-height: 1.6;
    color: #333;
    width: 100%;
  }

  &__card {
    width: 100%;
  }

  &__actions {
    margin-left: 12px;
  }

  &__button {
    color: #3370ff;
    padding: 0;

    &:hover {
      color: #598bff;
    }
  }
}
</style>
