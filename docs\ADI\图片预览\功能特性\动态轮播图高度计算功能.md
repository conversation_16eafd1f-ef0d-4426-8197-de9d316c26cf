# ImagePreview 动态轮播图高度计算功能

## 功能概述

在 ImagePreview 组件中实现了动态的轮播图高度计算功能，根据第一张图片的宽高比例来动态调整轮播图的最大高度，提供更好的用户体验。

## 实现方案

### 1. 核心功能

- **动态高度计算**：基于第一张图片的宽高比例计算轮播图高度
- **响应式更新**：图片列表变化时自动重新计算高度
- **错误处理**：图片加载失败时使用默认高度
- **合理范围限制**：设置最小和最大高度限制，避免极端情况

### 2. 计算公式

```typescript
动态高度 = 固定宽度 × (第一张图片高度 / 第一张图片宽度)
```

其中：

- 固定宽度：378px（轮播图容器宽度）
- 高度范围限制：378px - 504px
- 默认高度：78%（当无法计算时使用）

### 3. 技术实现

#### 3.1 响应式数据

```typescript
// 第一张图片的尺寸信息
const firstImageSize = ref<{ width: number; height: number } | null>(null);

// 动态计算的轮播图最大高度
const dynamicCarouselMaxHeight = computed(() => {
  // 如果没有图片或第一张图片尺寸信息，使用默认高度
  if (!firstImageSize.value || !imgListCal.value.length) {
    return '78%'; // 保持原有的默认高度
  }

  const { width, height } = firstImageSize.value;

  // 防止除零错误
  if (width <= 0) {
    return '78%';
  }

  // 计算动态高度：固定宽度 × (第一张图片高度 / 第一张图片宽度)
  const aspectRatio = height / width;
  const dynamicHeight = CAROUSEL_CONTAINER_WIDTH * aspectRatio;

  // 设置合理的高度范围限制（最小378px，最大504px）
  const minHeight = 378;
  const maxHeight = 504;
  const clampedHeight = Math.max(minHeight, Math.min(maxHeight, dynamicHeight));

  return `${clampedHeight}px`;
});
```

#### 3.2 图片尺寸获取

```typescript
/**
 * 加载第一张图片并获取其尺寸
 */
const loadFirstImageSize = async () => {
  if (!imgListCal.value.length) {
    firstImageSize.value = null;
    return;
  }

  const firstImg = imgListCal.value[0];
  if (!firstImg.resId) {
    firstImageSize.value = null;
    return;
  }

  try {
    // 创建图片对象来获取自然尺寸
    const img = new Image();
    const imageUrl = getMaterialFullUrl(
      firstImg.resId,
      firstImg.resType || FILE_TYPES.WEBP,
      'user',
    );

    // 使用 Promise 包装图片加载
    await new Promise<void>((resolve, reject) => {
      img.onload = () => {
        firstImageSize.value = {
          width: img.naturalWidth,
          height: img.naturalHeight,
        };
        resolve();
      };

      img.onerror = () => {
        console.warn('第一张图片加载失败，使用默认高度');
        firstImageSize.value = null;
        reject(new Error('图片加载失败'));
      };

      img.src = imageUrl;
    });
  } catch (error) {
    console.warn('获取第一张图片尺寸失败:', error);
    firstImageSize.value = null;
  }
};
```

#### 3.3 监听图片列表变化

```typescript
// 监听图片列表变化，重新计算高度
watch(
  () => imgListCal.value,
  async newImgList => {
    if (newImgList.length > 0) {
      await nextTick(); // 等待 DOM 更新
      await loadFirstImageSize();
    } else {
      firstImageSize.value = null;
    }
  },
  { immediate: true, deep: true },
);
```

#### 3.4 模板使用

```vue
<ImageWorkPreview
  :title="workInfo?.script?.title || '标题'"
  :content="workInfo?.script?.content || '内容'"
  :imgList="imgListCal"
  height="full"
  :carousel-max-height="dynamicCarouselMaxHeight"
  :enableCarouselControl="true"
  :disableAutoplay="true"
  :showLoading="showLoadingState"
  :loadingPercent="currentProgress"
  :loadingText="loadingText"
  :coverImg="workInfo?.coverImg"
  :showFailed="showFailedState"
  :failedErrorMessage="failedErrorMessage"
  class="image-preview__work-preview"
/>
```

### 4. 特性说明

#### 4.1 自适应高度

- 根据第一张图片的实际宽高比计算最适合的轮播图高度
- 保持图片的原始比例，避免变形

#### 4.2 性能优化

- 使用 Vue 的计算属性，只在依赖变化时重新计算
- 异步加载图片尺寸，不阻塞界面渲染

#### 4.3 错误处理

- 图片加载失败时自动回退到默认高度
- 处理除零错误和异常情况
- 提供友好的错误日志

#### 4.4 合理限制

- 设置最小高度（378px）和最大高度（504px）
- 避免极端宽高比导致的不合理显示

### 5. 使用场景

- **图文预览页面**：根据生成的图片比例动态调整预览区域
- **作品展示**：为不同比例的图片提供最佳的显示效果
- **响应式设计**：适应不同尺寸的图片内容

### 6. 兼容性

- 保持向后兼容，当无法获取图片尺寸时使用原有的默认高度
- 不影响现有的轮播图功能和交互
- 支持所有现有的图片格式和来源

### 7. 测试建议

1. **不同比例图片测试**：

   - 正方形图片（1:1）
   - 横向图片（16:9, 4:3）
   - 纵向图片（9:16, 3:4）

2. **边界情况测试**：

   - 空图片列表
   - 图片加载失败
   - 极端宽高比图片

3. **性能测试**：
   - 图片列表频繁变化
   - 大量图片的处理

### 8. 后续优化方向

- 支持多张图片的平均比例计算
- 添加动画过渡效果
- 支持用户自定义高度范围
- 缓存图片尺寸信息以提升性能
