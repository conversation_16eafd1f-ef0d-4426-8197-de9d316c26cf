import {
  ref,
  readonly,
  onMounted,
  onBeforeUnmount,
  onActivated,
  onDeactivated,
  type Ref,
} from 'vue';
import { debounce } from 'lodash-es';
import {
  eventBus,
  EVENT_NAMES,
  type CheckUnsavedChangesData,
  type UnsavedChangesResponseData,
} from '@/utils/eventBus';

/**
 * 数据保护配置选项
 */
export interface DataProtectionOptions {
  /** 当前步骤索引，用于 EventBus 通信 */
  stepIndex?: number;
  /** 是否启用浏览器刷新/关闭保护 */
  enableBrowserProtection?: boolean;
  /** 是否启用退出确认保护（EventBus） */
  enableExitProtection?: boolean;
  /** 防抖延迟时间（毫秒） */
  debounceDelay?: number;
  /** 静默期时长（毫秒） */
  silentPeriodDuration?: number;
}

/**
 * 数据保护返回接口
 */
export interface DataProtectionReturn {
  /** 是否有未保存的更改 */
  hasUnsavedChanges: Readonly<Ref<boolean>>;
  /** 是否已初始化 */
  isInitialized: Readonly<Ref<boolean>>;
  /** 是否处于静默期 */
  isSilentPeriod: Readonly<Ref<boolean>>;
  /** 手动设置未保存状态 */
  setUnsavedChanges: (value: boolean) => void;
  /** 重置未保存状态（保存后调用） */
  resetUnsavedChanges: () => void;
  /** 初始化数据保护功能 */
  initialize: () => void;
  /** 清理数据保护功能 */
  cleanup: () => void;
  /** 重新激活（KeepAlive 场景） */
  reactivate: () => void;
}

/**
 * 统一的数据保护功能 composable
 * 整合了浏览器保护、EventBus 通信、表单变更检测等功能
 *
 * @param options 配置选项
 * @returns 数据保护相关状态和方法
 */
export function useDataProtection(
  options: DataProtectionOptions = {},
): DataProtectionReturn {
  const {
    stepIndex,
    enableBrowserProtection = true,
    enableExitProtection = true,
    debounceDelay = 300,
    silentPeriodDuration = 1000,
  } = options;

  // ============= 响应式状态 =============
  const hasUnsavedChanges = ref<boolean>(false);
  const isInitialized = ref<boolean>(false);
  const isSilentPeriod = ref<boolean>(false);

  // ============= 内部变量 =============
  let beforeUnloadHandler:
    | ((event: BeforeUnloadEvent) => string | void)
    | null = null;
  let silentPeriodTimer: ReturnType<typeof setTimeout> | null = null;

  // ============= 防抖处理 =============
  const debouncedChangeHandler = debounce(() => {
    if (isSilentPeriod.value) {
      console.log('🚀 ADI-LOG ~ 静默期内忽略表单变更事件');
      return;
    }

    if (!hasUnsavedChanges.value) {
      hasUnsavedChanges.value = true;
      console.log('🚀 ADI-LOG ~ 检测到表单变更，设置未保存状态为 true');
    }
  }, debounceDelay);

  // ============= 浏览器保护相关 =============

  /**
   * beforeunload 事件处理函数
   */
  const handleBeforeUnload = (event: BeforeUnloadEvent): string | void => {
    if (hasUnsavedChanges.value) {
      const message = '系统可能不会保存您所做的更改';
      event.preventDefault();
      event.returnValue = message;
      console.log('🚀 ADI-LOG ~ 页面离开前确认提示已显示');
      return message;
    }
  };

  /**
   * 表单变更事件处理函数
   */
  const handleFormChange = (event: Event): void => {
    const target = event.target as HTMLElement;

    if (
      target.tagName === 'INPUT' ||
      target.tagName === 'TEXTAREA' ||
      target.tagName === 'SELECT' ||
      target.classList.contains('fa-input') ||
      target.classList.contains('fa-textarea') ||
      target.classList.contains('fa-select') ||
      target.closest('.dynamic-form-item') ||
      target.closest('.fa-form-model-item') ||
      target.closest('.fa-select-selection__choice__remove') ||
      target.closest('.fa-select-search__field') ||
      target.closest('.dynamic-form-item__container') ||
      target.closest('.first-step__form')
    ) {
      debouncedChangeHandler();
    }
  };

  /**
   * 标签删除操作处理函数
   * 监听用户点击标签删除按钮的操作
   */
  const handleTagDeleteChange = (event: Event): void => {
    const target = event.target as HTMLElement;

    // 检查是否是标签删除操作
    if (target.closest('.fa-select-selection__choice__remove')) {
      const tagElement = target.closest('.fa-select-selection__choice');
      const formItem = target.closest('.dynamic-form-item');

      if (tagElement && formItem) {
        // 获取被删除的标签内容
        const tagContent = tagElement
          .querySelector('.fa-select-selection__choice__content')
          ?.textContent?.trim();
        const formItemLabel = formItem
          .querySelector('.fa-form-model-item-label')
          ?.textContent?.trim();

        console.log('🚀 ADI-LOG ~ 标签删除操作记录:', {
          操作类型: '删除操作',
          表单项: formItemLabel || '未知字段',
          删除内容: tagContent || '未知标签',
          时间戳: new Date().toISOString(),
        });
      }

      debouncedChangeHandler();
    }
  };

  /**
   * AI建议标签添加操作处理函数
   * 监听用户点击AI推荐标签的操作
   */
  const handleAiTagAddChange = (event: Event): void => {
    const target = event.target as HTMLElement;

    // 检查是否是AI建议标签点击操作
    if (
      target.classList.contains('ai-suggestion__tag') ||
      target.closest('.ai-suggestion__tag')
    ) {
      const tagElement = target.classList.contains('ai-suggestion__tag')
        ? target
        : target.closest('.ai-suggestion__tag');
      const formItem = target.closest('.dynamic-form-item');

      if (tagElement && formItem) {
        // 获取被添加的标签内容
        const tagContent = tagElement.textContent?.trim();
        const formItemLabel = formItem
          .querySelector('.fa-form-model-item-label')
          ?.textContent?.trim();

        console.log('🚀 ADI-LOG ~ AI建议标签添加操作记录:', {
          操作类型: '新增操作',
          表单项: formItemLabel || '未知字段',
          添加内容: tagContent || '未知标签',
          来源: 'AI建议',
          时间戳: new Date().toISOString(),
        });
      }

      debouncedChangeHandler();
    }
  };

  /**
   * 文件上传变更处理函数
   */
  const handleFileChange = (event: Event): void => {
    const target = event.target as HTMLElement;

    if (
      target.closest('.upload-area') ||
      target.closest('.file-upload') ||
      target.closest('.upload-button') ||
      target.closest('.file-item') ||
      target.classList.contains('upload-delete-btn') ||
      target.classList.contains('upload-replace-btn')
    ) {
      debouncedChangeHandler();
    }
  };

  /**
   * 媒体组件变更处理函数
   */
  const handleMediaChange = (event: Event): void => {
    const target = event.target as HTMLElement;

    if (
      target.closest('.music-selector') ||
      target.closest('.dubbing-selector') ||
      target.closest('.media-item') ||
      target.classList.contains('media-select-btn') ||
      target.classList.contains('media-remove-btn')
    ) {
      debouncedChangeHandler();
    }
  };

  // ============= EventBus 通信相关 =============

  /**
   * 处理数据保护检查事件
   */
  const handleCheckUnsavedChanges = (...args: unknown[]) => {
    try {
      const data = args[0] as CheckUnsavedChangesData;

      if (!data || typeof data.requestId !== 'string') {
        console.warn('Invalid CheckUnsavedChangesData received:', data);
        return;
      }

      // 仅在匹配的步骤索引时响应
      if (stepIndex !== undefined && data.currentStep === stepIndex) {
        const responseData: UnsavedChangesResponseData = {
          requestId: data.requestId,
          hasUnsavedChanges: hasUnsavedChanges.value,
        };
        eventBus.emit(EVENT_NAMES.UNSAVED_CHANGES_RESPONSE, responseData);
        console.log(
          `🚀 ADI-LOG ~ 步骤 ${stepIndex} 响应数据保护检查:`,
          responseData,
        );
      }
    } catch (error) {
      console.error('Error handling unsaved changes check:', error);
    }
  };

  // ============= 核心方法 =============

  /**
   * 初始化数据保护功能
   */
  const initialize = (): void => {
    if (isInitialized.value) {
      console.log('🚀 ADI-LOG ~ 数据保护功能已初始化，跳过重复初始化');
      return;
    }

    try {
      // 初始化浏览器保护
      if (enableBrowserProtection) {
        beforeUnloadHandler = handleBeforeUnload;
        window.addEventListener('beforeunload', beforeUnloadHandler);

        // 注册表单变更监听器
        document.addEventListener('input', handleFormChange, true);
        document.addEventListener('change', handleFormChange, true);
        document.addEventListener('click', handleFileChange, true);
        document.addEventListener('click', handleMediaChange, true);

        // 注册标签删除操作监听器
        document.addEventListener('click', handleTagDeleteChange, true);

        // 注册AI建议标签添加操作监听器
        document.addEventListener('click', handleAiTagAddChange, true);
      }

      // 初始化 EventBus 通信
      if (enableExitProtection && stepIndex !== undefined) {
        eventBus.on(
          EVENT_NAMES.CHECK_UNSAVED_CHANGES,
          handleCheckUnsavedChanges,
        );
      }

      isInitialized.value = true;
    } catch (error) {
      console.error('🚀 ADI-LOG ~ 数据保护功能初始化失败:', error);
    }
  };

  /**
   * 清理数据保护功能
   */
  const cleanup = (): void => {
    try {
      // 清理浏览器保护
      if (enableBrowserProtection && beforeUnloadHandler) {
        window.removeEventListener('beforeunload', beforeUnloadHandler);
        document.removeEventListener('input', handleFormChange, true);
        document.removeEventListener('change', handleFormChange, true);
        document.removeEventListener('click', handleFileChange, true);
        document.removeEventListener('click', handleMediaChange, true);

        // 清理标签删除操作监听器
        document.removeEventListener('click', handleTagDeleteChange, true);

        // 清理AI建议标签添加操作监听器
        document.removeEventListener('click', handleAiTagAddChange, true);

        beforeUnloadHandler = null;
      }

      // 清理 EventBus 通信
      if (enableExitProtection && stepIndex !== undefined) {
        eventBus.off(
          EVENT_NAMES.CHECK_UNSAVED_CHANGES,
          handleCheckUnsavedChanges,
        );
      }

      // 清理定时器
      if (silentPeriodTimer) {
        clearTimeout(silentPeriodTimer);
        silentPeriodTimer = null;
      }

      // 重置状态
      isSilentPeriod.value = false;
      isInitialized.value = false;
      console.log('🚀 ADI-LOG ~ 数据保护功能清理完成');
    } catch (error) {
      console.error('🚀 ADI-LOG ~ 数据保护功能清理失败:', error);
    }
  };

  /**
   * 重置未保存状态
   */
  const resetUnsavedChanges = (): void => {
    hasUnsavedChanges.value = false;

    // 清除之前的静默期定时器
    if (silentPeriodTimer) {
      clearTimeout(silentPeriodTimer);
    }

    // 设置静默期
    isSilentPeriod.value = true;
    silentPeriodTimer = setTimeout(() => {
      isSilentPeriod.value = false;
      silentPeriodTimer = null;
      console.log('🚀 ADI-LOG ~ 静默期结束，恢复表单变更监听');
    }, silentPeriodDuration);

    console.log(
      `🚀 ADI-LOG ~ 未保存状态已重置，进入${silentPeriodDuration}ms静默期`,
    );
  };

  /**
   * 手动设置未保存状态
   */
  const setUnsavedChanges = (value: boolean): void => {
    hasUnsavedChanges.value = value;
    console.log(`🚀 ADI-LOG ~ 手动设置未保存状态为: ${value}`);
  };

  /**
   * 重新激活数据保护功能（KeepAlive 场景）
   */
  const reactivate = (): void => {
    if (isInitialized.value) {
      cleanup();
    }
    initialize();
    console.log('🚀 ADI-LOG ~ 数据保护功能已重新激活（KeepAlive 场景）');
  };

  // ============= 生命周期管理 =============
  onMounted(() => {
    initialize();
  });

  onBeforeUnmount(() => {
    cleanup();
  });

  onActivated(() => {
    if (!isInitialized.value) {
      initialize();
    }
  });

  onDeactivated(() => {
    cleanup();
    console.log('🚀 ADI-LOG ~ KeepAlive 组件停用，数据保护功能已清理');
  });

  // ============= 返回接口 =============
  return {
    hasUnsavedChanges: readonly(hasUnsavedChanges),
    isInitialized: readonly(isInitialized),
    isSilentPeriod: readonly(isSilentPeriod),
    setUnsavedChanges,
    resetUnsavedChanges,
    initialize,
    cleanup,
    reactivate,
  };
}

/**
 * 检查指定步骤是否有未保存的更改
 * 用于 ProjectHeader 等组件的退出确认
 *
 * @param currentStep 当前步骤索引
 * @param timeout 超时时间（毫秒）
 * @returns Promise<boolean> 是否有未保存的更改
 */
export async function checkUnsavedChangesBeforeLeave(
  currentStep: number,
  timeout: number = 1000,
): Promise<boolean> {
  return new Promise(resolve => {
    const requestId = `check-unsaved-${Date.now()}-${Math.random()}`;

    const handleResponse = (...args: unknown[]) => {
      const data = args[0] as UnsavedChangesResponseData;
      if (data && data.requestId === requestId) {
        eventBus.off(EVENT_NAMES.UNSAVED_CHANGES_RESPONSE, handleResponse);
        resolve(data.hasUnsavedChanges);
      }
    };

    eventBus.on(EVENT_NAMES.UNSAVED_CHANGES_RESPONSE, handleResponse);

    const checkData: CheckUnsavedChangesData = {
      requestId,
      currentStep,
    };
    eventBus.emit(EVENT_NAMES.CHECK_UNSAVED_CHANGES, checkData);

    // 设置超时，避免无限等待
    setTimeout(() => {
      eventBus.off(EVENT_NAMES.UNSAVED_CHANGES_RESPONSE, handleResponse);
      resolve(false); // 超时默认为无未保存更改
    }, timeout);
  });
}
