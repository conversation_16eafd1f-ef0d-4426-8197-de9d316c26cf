<template>
  <div class="top-tools flex items-center">
    <!-- 点数 -->
    <fa-skeleton
      :loading="isShowSkeleton"
      :paragraph="false"
      active
      class="point-skeleton"
    >
      <fa-dropdown placement="bottomCenter">
        <div class="h-[63px] flex items-center justify-center mr-[32px]">
          <div class="point-box">
            <div class="flex items-center select-none">
              <img
                class="mr-[4px] w-[16px] h-[16px]"
                src="@/assets/common/score.svg"
              />
              <span class="font-bold text-title">{{ point }}</span>
            </div>
            <div class="w-[1px] h-[18px] bg-divider mx-[12px]"></div>
            <div>
              <a
                :href="rechargeUrl"
                target="_blank"
                class="text-primary hover:text-secondary"
              >
                充值
              </a>
            </div>
          </div>
        </div>
        <div
          slot="overlay"
          class="select-none min-w-[168px] bg-white shadow-[0_2px_6px_#00000029] rounded-[8px] py-[12px] pl-[11px] pr-[13px]"
        >
          <div class="text-text">剩余创作点数</div>
          <div class="flex items-center justify-between mt-[8px]">
            <div flex items-center>
              <img
                class="mr-[4px] w-[16px] h-[16px]"
                src="@/assets/common/score.svg"
              />
              <span class="font-bold ml-[4px] text-title">{{ point }}</span>
            </div>
            <div>
              <a
                :href="pointDetailUrl"
                target="_blank"
                class="text-primary hover:text-secondary"
              >
                明细
              </a>
            </div>
          </div>
        </div>
      </fa-dropdown>
    </fa-skeleton>

    <!-- 工具箱 -->
    <fa-skeleton
      :loading="isShowSkeleton"
      :paragraph="false"
      active
      class="tool-skeleton"
    >
      <fa-dropdown
        placement="bottomCenter"
        @visibleChange="handleToolsVisibleChange"
      >
        <span class="h-[63px] flex items-center justify-center">
          <Icon
            type="mobaoxiang"
            class="w-[24px] h-[24px] text-assist hover:text-primary cursor-pointer"
            :class="{ 'text-primary!': isToolsVisible }"
          />
        </span>
        <fa-menu slot="overlay" @click="isToolsVisible = false">
          <fa-menu-item>
            <Icon type="bangchuzhongxin" w-18px h-18px mr-19px />
            <span>帮助中心</span>
          </fa-menu-item>
          <fa-menu-item>
            <Icon type="gongtaigengxin" w-18px h-18px mr-19px />
            <span>功能更新</span>
          </fa-menu-item>
        </fa-menu>
      </fa-dropdown>
    </fa-skeleton>

    <!-- 个人中心 -->
    <fa-skeleton
      :loading="isShowSkeleton"
      :paragraph="false"
      active
      class="user-skeleton"
    >
      <fa-dropdown
        class="ml-[32px]"
        placement="bottomCenter"
        @visibleChange="handleUserInfoVisibleChange"
      >
        <div class="h-[63px] flex items-center justify-center">
          <span
            class="flex items-center cursor-pointer text-assist hover:text-primary"
            :class="{ 'text-primary!': isUserInfoVisible }"
          >
            <fa-icon type="user" class="text-[24px]" />
            <Icon
              type="jiantou_xia"
              class="w-10px h-10px ml-8px transition-all text-[#BFBFBF]"
              :class="{ 'rotate-180': isUserInfoVisible }"
            />
          </span>
        </div>
        <div
          slot="overlay"
          class="select-none min-w-[220px] bg-white shadow-[0_2px_6px_#00000029] rounded-[8px]"
          @click.stop="handleUserInfoVisibleChange(false)"
        >
          <div
            class="py-[22px] px-[24px] flex items-center justify-between b-divider b-b"
          >
            <div
              class="w-[116px] overflow-hidden text-ellipsis whitespace-nowrap"
            >
              <!-- 企业名 -->
              <div
                class="overflow-hidden text-text font-bold mb-[7px] text-[15px] line-height-[20px] text-ellipsis"
              >
                {{ acctName }}
              </div>
              <!-- 用户名 -->
              <div
                class="overflow-hidden text-assist text-[12px] line-height-[16px] text-ellipsis"
              >
                {{ staffName }}
              </div>
            </div>
            <!-- 头像 -->
            <div
              class="avatar-wrap relative w-[40px] h-[40px] rounded-full overflow-hidden"
              v-show="!isAvatarError"
            >
              <ScImg
                class="w-full h-full rounded-full"
                :src="avatarUrl"
                alt="avatar"
                :max-width="40"
                @error="handleAvatarError"
              />
              <div
                class="avatar-mask"
                @click="
                  openNewTab(`//${portalDomain}/portal.jsp#appId=setCorpInfo`)
                "
              >
                <Icon
                  type="bianjitouxiang"
                  class="w-[14px] h-[14px] text-white"
                />
              </div>
            </div>
          </div>
          <fa-menu :selectable="false">
            <fa-menu-item @click="openNewTab(`//${portalDomain}/`)">
              <Icon
                type="jinruqiyezhongxin"
                class="w-[18px] h-[18px] mr-[19px]"
              />
              <span>进入企业中心</span>
            </fa-menu-item>
            <fa-menu-item
              v-if="store.state.user.buyAuth || store.state.user.isBoss"
              @click="openNewTab(`//${portalDomain}/portal.jsp?#appId=order`)"
            >
              <Icon type="wodidingshan" class="w-[18px] h-[18px] mr-[19px]" />
              <span>我的订单</span>
            </fa-menu-item>
          </fa-menu>
          <div
            class="b-divider b-t flex items-center h-[47px] pr-[24px] justify-end"
          >
            <span
              class="text-text cursor-pointer hover:text-primary"
              @click="handleLogout"
              >退出</span
            >
          </div>
        </div>
      </fa-dropdown>
    </fa-skeleton>

    <!-- 版本信息 -->
    <fa-skeleton
      :loading="isShowSkeleton"
      :paragraph="false"
      active
      class="version-skeleton"
    >
      <fa-dropdown class="ml-[32px]">
        <div class="h-[63px] flex items-center justify-center cursor-pointer">
          <img :src="versionIconUrl" class="h-[24px]" height="24" width="66" />
        </div>
        <div
          slot="overlay"
          class="select-none min-w-[292px] bg-white shadow-[0_2px_6px_#00000029] rounded-[4px] overflow-hidden"
        >
          <!-- 免费下拉窗 -->
          <template v-if="store.state.user.version === VERSION.FREE">
            <div class="py-[16px] px-[28px] bg-[#f8f8f8]">
              <div
                class="flex items-center justify-between height-[21px] line-height-[21px]"
              >
                <span class="text-title text-[16px] font-bold">{{
                  currVerName
                }}</span>
                <a
                  :href="rechargeUrl"
                  target="_blank"
                  class="text-assist text-[12px] hover:text-[#DEA967] cursor-pointer"
                  >更多版本详情<Icon
                    type="jiantou_you"
                    class="ml-[6px] w-6px h-6px inline"
                /></a>
              </div>
            </div>
            <div class="py-[28px] px-[28px]">
              <div class="feature-title">
                <div class="title-wrap">付费版特殊功能</div>
              </div>
              <div class="feature-list">
                <div
                  class="feature-item"
                  v-for="(item, index) in paidFeatures"
                  :key="`feature-item-${index}`"
                >
                  <img
                    class="feature-icon w-[26px] h-[26px]"
                    :src="item.icon"
                  />
                  <span class="feature-text">{{ item.text }}</span>
                </div>
              </div>
              <fa-button class="upgrade-btn mt-[28px]" @click="handleUpgrade">{{
                upgradeBtnText
              }}</fa-button>
            </div>
          </template>
          <!-- 付费版下拉窗 -->
          <template v-else>
            <div
              class="py-[16px] px-[28px] bg-gradient-to-r from-[#fef7f0] to-[#fcf6ef]"
            >
              <div
                class="flex items-center justify-between mb-[8px] line-height-[21px]"
              >
                <span class="text-title text-[16px] font-bold">{{
                  currVerName
                }}</span>
                <a
                  :href="rechargeUrl"
                  target="_blank"
                  class="text-assist text-[12px] hover:text-[#DEA967] cursor-pointer"
                  >更多版本详情<Icon
                    type="jiantou_you"
                    class="ml-[6px] w-6px h-6px inline"
                /></a>
              </div>
              <div class="text-assist line-height-[16px] text-[12px]">
                <span>到期时间：</span><span>{{ expireDate }}</span>
              </div>
            </div>
            <div class="py-[20px] px-[28px]">
              <fa-button class="upgrade-btn" @click="handleUpgrade">{{
                upgradeBtnText
              }}</fa-button>
            </div>
          </template>
        </div>
      </fa-dropdown>
    </fa-skeleton>
  </div>
</template>

<script lang="ts" setup>
// 引入常量
import { getExternalDynamicUrl } from '@/constants/system';
import { VERSION } from '@/constants/version';
import store from '@/store';
import { computed, ref } from 'vue';
import ScImg from '@/components/comm/ScImg.vue';
import { useSkeleton } from '@/hook/useSkeleton';
import router from '@/router';

// 引入版本图标
import freeIcon from '@/assets/common/version/large/free.svg';
import basicIcon from '@/assets/common/version/large/basic.svg';
import proIcon from '@/assets/common/version/large/pro.svg';

// 引入付费功能图标
import featureIcon1 from '@/assets/common/paidFeatures/feature1.svg';
import featureIcon2 from '@/assets/common/paidFeatures/feature2.svg';
import featureIcon3 from '@/assets/common/paidFeatures/feature3.svg';
import featureIcon4 from '@/assets/common/paidFeatures/feature4.svg';
import featureIcon5 from '@/assets/common/paidFeatures/feature5.svg';
import featureIcon6 from '@/assets/common/paidFeatures/feature6.svg';

import { logout } from '@/utils/auth';

const { isShowSkeleton } = useSkeleton();

/************头像处理 star*************/
/** 头像地址 */
const avatarUrl = computed(() => store.state.user.avatarUrl);
/** 头像加载错误标志 */
const isAvatarError = ref(false);
/** 头像加载错误处理 */
const handleAvatarError = () => {
  isAvatarError.value = true;
};

/************头像处理 end*************/

/** 充值链接 */
const rechargeUrl = getExternalDynamicUrl().VERSION_BUY;
/** 明细链接 */
const pointDetailUrl = router.resolve({
  path: '/acct-info',
  query: { tab: 'PointDetail' },
}).href;
/** 工具箱下拉菜单是否可见 */
const isToolsVisible = ref(false);
/** 工具箱下拉菜单显示变化事件 */
const handleToolsVisibleChange = (visible: boolean) =>
  (isToolsVisible.value = visible);
/** 个人信息下拉菜单是否可见 */
const isUserInfoVisible = ref(false);
/** 个人信息下拉菜单显示变化事件 */
const handleUserInfoVisibleChange = (visible: boolean) =>
  (isUserInfoVisible.value = visible);
/** 剩余点数 */
const point = computed(() => store.state.user.point);
/** 升级/续费按钮文案 */
const upgradeBtnText = computed(() =>
  store.state.user.version !== VERSION.FREE ? '续费' : '立即升级',
);
/** 升级续费按钮回调 */
const handleUpgrade = () => {
  window.open(getExternalDynamicUrl().VERSION_BUY, '_blank');
};
/** 过期日期 */
const expireDate = computed<string>(() => store.getters['expireDate']);
/** 当前版本名称 */
const currVerName = computed<string>(() => store.getters['currVerName']);
/** 账号名称 */
const acctName = computed(() => store.state.user.acctName);
/** 员工名称 */
const staffName = computed(() => store.state.user.staffName);
/** 版本图标URL */
const versionIconUrl = computed(() => {
  const ver = store.state.user.version;
  return ver === VERSION.FREE
    ? freeIcon
    : ver === VERSION.BASIC
    ? basicIcon
    : ver === VERSION.PRO
    ? proIcon
    : freeIcon;
});
/** 付费功能宫格 */
const paidFeatures = [
  { icon: featureIcon1, text: '海量创作点数' },
  { icon: featureIcon2, text: '剪辑加速' },
  { icon: featureIcon3, text: 'AI改嘴型' },
  { icon: featureIcon4, text: '去水印导出' },
  { icon: featureIcon5, text: '云存储空间' },
  { icon: featureIcon6, text: '多成员协作' },
];
/** 企业中心域名 */
const portalDomain = computed(() => store.state.system.portal);
/** 打开新标签页 */
const openNewTab = (url: string) => {
  window.open(url, '_blank');
  isUserInfoVisible.value = false;
};

const handleLogout = () => {
  /* 退出前弹窗确认 */
  const confirmed = window.confirm('确定要退出系统吗？');
  if (!confirmed) return;

  logout();
};
</script>

<style lang="scss" scoped>
.top-tools {
  .point-box {
    @apply flex items-center px-[16px] py-[10px] rounded-full border-divider border-1 text-[14px] line-height-[18px];
  }
}
.avatar-wrap {
  .avatar-mask {
    @apply absolute w-full h-full top-0 left-0 items-center justify-center z-10;
    @apply bg-black bg-opacity-60 cursor-pointer hidden;
  }
  &:hover .avatar-mask {
    @apply flex!;
  }
}
.upgrade-btn {
  @apply h-[32px] w-full rounded-[2px] bg-gradient-to-r from-[#EECD9A] to-[#E8B677] border-none;
  @apply text-[#4A300E];
  &:hover {
    @apply bg-gradient-to-r from-[#F1D3A5] to-[#EBBF88] text-[#4A300E];
  }
}
.fa-dropdown-menu,
.fa-menu {
  @apply rounded-[8px] py-8px;
}
::v-deep {
  .fa-dropdown-menu-item,
  .fa-menu-item {
    @apply line-height-[19px] py-[12px] pl-[24px] min-w141px flex items-center justify-start;
    @apply text-text;
    .icon {
      @apply text-subText;
    }
    &:hover {
      @apply bg-[#eef2ff] text-primary;
      .icon {
        @apply text-primary;
      }
    }
  }
  .fa-menu-vertical > .fa-menu-item {
    @apply h-[44px] m-0 pl-[24px];
  }
  .fa-menu-vertical .fa-menu-item:not(:last-child) {
    @apply m-0;
  }
}
.point-skeleton {
  ::v-deep .fa-skeleton-content {
    .fa-skeleton-title {
      width: 140px;
      height: 39px;
      border-radius: 20px;
    }
  }
}
.tool-skeleton,
.user-skeleton {
  ::v-deep .fa-skeleton-content {
    .fa-skeleton-title {
      width: 24px;
      height: 24px;
      border-radius: 6px;
      margin-left: 32px;
    }
  }
}
.version-skeleton {
  ::v-deep .fa-skeleton-content {
    .fa-skeleton-title {
      width: 66px;
      height: 24px;
      margin-left: 32px;
    }
  }
}
.feature-title {
  @apply relative text-[14px] text-title text-center mb-[20px];
  .title-wrap {
    @apply relative z-1 bg-white px-[8px] inline-block line-height-[19px];
  }
  &::before {
    content: '';
    display: block;
    position: absolute;
    left: 0;
    top: 50%;
    width: 100%;
    height: 1px;
    background-color: #f1f1f1;
    transform: translateY(-50%);
  }
}
// 付费功能宫格样式
.feature-list {
  @apply grid grid-cols-3 gap-y-[24px];
  .feature-item {
    @apply flex flex-col justify-center items-center text-[14px] text-title;
    .feature-icon {
      @apply w-[26px] h-[26px];
    }
    .feature-text {
      @apply min-w-[79px] text-center;
      @apply mt-[8px] text-[12px] text-[#666] line-height-[16px];
      @apply overflow-hidden whitespace-nowrap max-w-[60px] text-ellipsis;
    }
  }
}
</style>
