# WorkView 我的作品页面

作品视图（WorkView）模块，负责作品的列表展示、详情预览、生成状态、下载、编辑等核心业务流程。

## 目录结构

```
WorkView/
├── WorkList.vue                # 作品列表主组件
├── WorkDownloadRecord.vue      # 作品下载记录组件
├── constants.ts                # 作品相关常量
├── utils.ts                    # 工具函数
├── hook/
│   ├── useWorkList.ts          # 作品列表逻辑（组合式API）
│   └── useWorkModal.ts         # 作品弹窗逻辑（组合式API）
└── WorkModal/
    ├── LoadingPreview.vue      # 生成中loading预览组件
    ├── WorkBaseModal.vue       # 通用弹窗基础组件
    ├── WorkPreviewModal.vue    # 作品预览弹窗
    └── WorkSelectModal.vue     # 作品选择弹窗
```

## 核心功能

### 1. 作品列表与筛选

- 支持作品名称、所属项目、日期等多条件筛选
- 支持下拉分页加载项目列表
- 支持作品类型、状态筛选
- 支持表格分页、排序、快速跳转

### 2. 作品操作

- 预览作品（图片/视频）
- 编辑作品名称、内容
- 删除作品（含确认弹窗）
- 下载作品（支持多图/视频下载）
- 失败状态恢复（如视频生成失败可恢复旧作品）

### 3. 作品生成与状态

- 支持作品生成中 loading 状态展示
- 支持生成失败提示与重试
- 支持新生成标签展示

### 4. 弹窗与详情

- 作品预览弹窗（WorkPreviewModal）
- 作品选择弹窗（WorkSelectModal）
- 通用弹窗基础（WorkBaseModal）
- 生成中 loading 预览（LoadingPreview）

## 技术实现要点

- 采用 Vue3 组合式 API，状态与逻辑分离
- 统一常量与工具函数管理（constants.ts, utils.ts）
- 组件高度解耦，便于复用与扩展
- 支持大数据量分页与防抖处理
- 业务状态与 UI 状态分离，便于维护
- 详细的类型定义与接口注释

## 用法示例

```vue
<template>
  <WorkList :projectId="currentProjectId" />
</template>

<script setup lang="ts">
import WorkList from '@/components/WorkView/WorkList.vue';
const currentProjectId = 1;
</script>
```

## 组件说明

| 组件名                 | 说明                 |
| ---------------------- | -------------------- |
| WorkList.vue           | 作品列表主视图       |
| WorkDownloadRecord.vue | 作品下载记录展示     |
| WorkPreviewModal.vue   | 作品详情/预览弹窗    |
| WorkSelectModal.vue    | 视频新旧作品选择弹窗 |
| WorkBaseModal.vue      | 通用弹窗基础         |
| LoadingPreview.vue     | 生成中 loading 预览  |
| constants.ts           | 作品相关常量定义     |
| utils.ts               | 作品相关工具函数     |
| useWorkList.ts         | 作品列表业务逻辑     |
| useWorkModal.ts        | 作品弹窗业务逻辑     |

## 扩展与维护

- 支持自定义作品类型、状态扩展
- 支持多种业务弹窗复用
- 便于接入新业务（如批量操作、智能推荐等）
- 详细注释与类型定义，便于团队协作

## 更新日志

- 2025-07-01 v1.0.0 初始版本
- 2025-08-01 v1.1.0 修复已知问题

---

如需扩展或维护，请参考各组件/逻辑文件内注释与类型定义。
