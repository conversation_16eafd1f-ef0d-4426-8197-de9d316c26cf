<template>
  <div class="content-panel">
    <!-- 图文详情 -->
    <div v-if="showImageSection" class="content-panel__section">
      <div class="content-panel__section-title">
        <span class="content-panel__section-title-text">图文详情</span>
        <fa-button
          type="default"
          size="small"
          :disabled="isEditDisabled"
          @click="handleImageClick"
          class="content-panel__edit-btn"
        >
          编辑图文
        </fa-button>
      </div>
      <div v-if="showImageContent" class="content-panel__section-content">
        <ImageSidebar
          :images="puzzleImages"
          direction="horizontal"
          :showCover="true"
          :showReplaceBtn="false"
          :disabled="isEditDisabled"
          :selectedIndex="selectedImageIndex"
          :enableImagePreview="true"
          :enableHoverEffect="true"
          :showLoading="showLoadingState"
          :loadingPercent="currentProgress"
          @select="handleImageSelect"
        />
      </div>
    </div>

    <!-- 标题 -->
    <div class="content-panel__section">
      <div class="content-panel__section-content">
        <MaterialBox
          type="text"
          layout="header-content"
          title="标题"
          :content="workData?.script?.title || '暂无标题'"
          :copy-text="workData?.script?.title || ''"
          :disabled="isTitleCopyDisabled"
          empty-message="暂无内容可复制"
        />
      </div>
    </div>

    <!-- 内容 -->
    <div class="content-panel__section">
      <div class="content-panel__section-content">
        <MaterialBox
          type="text"
          layout="header-content"
          title="内容"
          :content="contentWithHashtags"
          :copy-text="originalContentForCopy"
          :disabled="isContentCopyDisabled"
          empty-message="暂无内容可复制"
        />
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent } from 'vue';
import { ImageWorkItem } from '@/types';
import ImageSidebar from '@/components/ImageTextEditor/components/ImageSidebar.vue';
import MaterialBox from '@/views/EditProjectView/pages/SecondStepVideo/components/ContentPanel/components/MaterialBox.vue';

import { showImageTextEditor } from '@/components/ImageTextEditor/utils';
import { IMAGE_TEXT_EDITOR_CLOSE_STATUS } from '@/components/ImageTextEditor/constants';
import { getMaterialFullUrl } from '@/components/MaterialBasicUpload/utils/index';
import { FILE_TYPES } from '@/constants/fileType';
import {
  WORK_STATUS,
  getWorkStatusInfo,
  isAnyGeneratingWork,
  isAnyFailedWork,
  isAnyCompletedWork,
} from '@/constants/workStatus';
import { eventBus, EVENT_NAMES } from '@/utils/eventBus';
import { formatImageWorkContent } from '@/views/EditProjectView/utils';

export default defineComponent({
  name: 'ContentPanel',
  components: { ImageSidebar, MaterialBox },
  props: {
    loading: {
      type: Boolean,
      default: false,
    },
    workData: {
      type: Object as () => ImageWorkItem,
      default: () => undefined,
    },
    /** 编辑后更新回调 */
    onEditUpdate: {
      type: Function,
      default: undefined,
    },
    /** 生成完成后更新回调 */
    onGenerateUpdate: {
      type: Function,
      default: undefined,
    },
  },
  data() {
    return {
      // 常量
      WORK_STATUS,
      // 当前选中的图片索引
      selectedImageIndex: 0,
    };
  },
  computed: {
    /**
     * 图片数据
     */
    graphicData() {
      return this.workData?.data?.graphic || [];
    },

    puzzleImages() {
      return this.getImageUrls(this.graphicData);
    },

    hasImages() {
      return this.graphicData.length > 0;
    },

    /**
     * 计算拼接后的内容（content + hashtags）
     */
    contentWithHashtags() {
      return formatImageWorkContent(this.workData?.script, '暂无内容');
    },

    /**
     * 用于复制的原始内容（不包含默认文案）
     */
    originalContentForCopy() {
      return formatImageWorkContent(this.workData?.script, '');
    },

    /**
     * 标题复制按钮是否禁用
     */
    isTitleCopyDisabled() {
      const title = this.workData?.script?.title || '';
      return !title.trim();
    },

    /**
     * 内容复制按钮是否禁用
     */
    isContentCopyDisabled() {
      const originalContent = this.originalContentForCopy;
      return !originalContent.trim();
    },

    /**
     * 是否显示加载状态
     */
    showLoadingState() {
      if (!this.workData) return false;
      const statusInfo = getWorkStatusInfo(this.workData);
      return isAnyGeneratingWork(statusInfo);
    },

    /**
     * 当前进度值
     */
    currentProgress() {
      if (!this.workData) return 0;
      return Math.max(0, Math.min(100, this.workData.progress || 0));
    },

    /**
     * 是否显示失败状态
     */
    showFailedState() {
      if (!this.workData) return false;
      const statusInfo = getWorkStatusInfo(this.workData);
      return isAnyFailedWork(statusInfo);
    },

    /**
     * 是否显示图文详情区块
     * 当有图片、正在加载或生成失败时显示
     */
    showImageSection() {
      return this.hasImages || this.showLoadingState || this.showFailedState;
    },

    /**
     * 是否显示图文详情内容
     * 失败状态时隐藏内容，只显示标题和按钮
     */
    showImageContent() {
      return !this.showFailedState;
    },

    /**
     * 是否禁用编辑功能
     * 图文作品已完成状态和重新生成完成状态的作品可以编辑
     */
    isEditDisabled() {
      // 没有作品数据时禁用编辑
      if (!this.workData) {
        return true;
      }

      // 只有已完成状态、重新生成完成状态的作品可以编辑
      const statusInfo = getWorkStatusInfo(this.workData);
      return !isAnyCompletedWork(statusInfo);
    },
  },
  watch: {
    /**
     * 监听workData变化
     */
    workData: {
      handler(newVal: ImageWorkItem | undefined) {
        if (newVal) {
          this.updatePanelData();
          // 重置选中的图片索引为第一张
          this.selectedImageIndex = 0;
          // 通知预览组件切换到第一张图片
          eventBus.emit(EVENT_NAMES.IMAGE_PREVIEW_CHANGE, 0);
        }
      },
      immediate: true,
      deep: true,
    },
  },
  methods: {
    /**
     * 获取图片完整URL列表
     */
    getImageUrls(graphicItems: Array<{ resId: string; type: number }>) {
      return graphicItems.map(item =>
        getMaterialFullUrl(
          item.resId,
          FILE_TYPES.WEBP || item.type,
          'user',
          120,
        ),
      );
    },
    /**
     * 更新面板数据
     */
    updatePanelData() {
      if (!this.workData) return;
    },
    /**
     * 处理图片选择
     */
    handleImageSelect(index: number) {
      this.selectedImageIndex = index;
      // 通过事件总线通知预览组件切换图片
      eventBus.emit(EVENT_NAMES.IMAGE_PREVIEW_CHANGE, index);
    },
    /**
     * 处理图片点击
     */
    handleImageClick() {
      // 如果编辑功能被禁用，则不执行操作
      if (this.isEditDisabled) {
        return;
      }

      showImageTextEditor(
        this.workData.id,
        undefined, // 默认tab
        async closeStatus => {
          // 图文编辑器关闭回调
          if (!this.workData) return;

          console.log('图文编辑器关闭，状态:', closeStatus);

          switch (closeStatus) {
            case IMAGE_TEXT_EDITOR_CLOSE_STATUS.UNMODIFIED:
              // 未修改，不需要处理
              console.log('未修改，无需处理');
              break;

            case IMAGE_TEXT_EDITOR_CLOSE_STATUS.SAVED:
              // 已保存，需要调用 onEditUpdate
              console.log('已保存，调用 onEditUpdate');
              if (this.onEditUpdate) {
                await this.onEditUpdate(this.workData.id);
              }
              break;

            case IMAGE_TEXT_EDITOR_CLOSE_STATUS.GENERATED:
              // 已生成，需要调用 onGenerateUpdate
              console.log('已生成，调用 onGenerateUpdate');
              if (this.onGenerateUpdate) {
                await this.onGenerateUpdate(this.workData.id);
              }
              break;

            default:
              console.warn('未知的关闭状态:', closeStatus);
              break;
          }
        },
      );
    },
    /**
     * 处理轮播图切换事件
     */
    handleCarouselChange(...args: unknown[]) {
      const activeIndex = args[0] as number;
      this.selectedImageIndex = activeIndex;
    },
  },
  mounted() {
    // 监听轮播图切换事件
    eventBus.on(EVENT_NAMES.CAROUSEL_CHANGE, this.handleCarouselChange);
  },
  beforeDestroy() {
    // 移除轮播图切换事件监听
    eventBus.off(EVENT_NAMES.CAROUSEL_CHANGE, this.handleCarouselChange);
  },
});
</script>

<style lang="scss" scoped>
@import '@/style/mixins/scrollbar.scss';

/*
 * 内容面板主容器
 * ----------------------------------------
 * 定义整体布局结构和基础样式
 */
.content-panel {
  /* 布局相关 */
  @apply flex flex-col;
  /* 尺寸相关 */
  @apply w-full h-full px-40px;
  /* 外观相关 */
  @apply overflow-y-auto;
  /* 滚动条样式 */
  @include scrollbar-style;
}

/*
 * 内容区块通用样式
 * ----------------------------------------
 * 定义各个内容区块的基础样式和间距
 */
.content-panel__section {
  /* 尺寸相关 */
  @apply mt-24px;

  &:first-child {
    /* 尺寸相关 */
    @apply mt-0;
  }

  &:last-child {
    /* 尺寸相关 */
    @apply mb-24px;
  }

  .content-panel__section-title {
    /* 布局相关 */
    @apply flex items-center justify-between mb-10px;
  }

  .content-panel__section-title-text {
    /* 文字相关 */
    @apply font-700 text-15px text-left text-[#111];
  }

  .content-panel__edit-btn {
    /* 布局相关 */
    @apply flex-shrink-0;
  }

  .content-panel__section-content {
    :deep(.image-text-editor-sidebar) {
      @apply py-0;
    }
  }
}
</style>
