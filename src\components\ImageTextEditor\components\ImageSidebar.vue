<template>
  <div
    class="image-text-editor-sidebar"
    :class="[`image-text-editor-sidebar--${direction}`]"
  >
    <!-- 滚动区域 -->
    <div
      v-if="images?.length || showLoading"
      class="image-text-editor-sidebar__list"
      ref="scrollContainer"
      :style="scrollContainerStyle"
    >
      <!-- 滚动按钮-上一页 -->
      <div
        v-if="showScrollButtons"
        class="image-text-editor-sidebar__scroll-btn-left"
        :class="{
          'image-text-editor-sidebar__scroll-btn--disabled': isLeftEnd,
        }"
        @click="scrollLeft"
      >
        <Icon type="jiantou_zuo" class="image-text-editor-sidebar__icon" />
      </div>
      <!-- 图片区域 -->
      <div class="image-text-editor-sidebar__scroll-container">
        <div
          class="image-text-editor-sidebar__content"
          ref="scrollContent"
          :style="contentStyle"
        >
          <!-- 生成中状态且无图片时显示占位进度条 -->
          <div
            v-if="showLoading && (!images || images.length === 0)"
            class="image-text-editor-sidebar__item"
          >
            <div class="image-text-editor-sidebar__img-wrap" :style="imgStyle">
              <!-- 生成中状态显示进度条遮罩层 -->
              <div class="image-text-editor-sidebar__progress-overlay">
                <div class="image-text-editor-sidebar__progress-circle">
                  <fa-progress
                    :type="PROGRESS_CONFIG.type"
                    :strokeLinecap="PROGRESS_CONFIG.strokeLinecap"
                    :strokeColor="PROGRESS_CONFIG.strokeColor"
                    :trailColor="PROGRESS_CONFIG.trailColor"
                    :width="PROGRESS_CONFIG.width"
                    :strokeWidth="PROGRESS_CONFIG.strokeWidth"
                    :percent="progressPercent"
                  />
                </div>
              </div>
            </div>
          </div>
          <!-- 正常图片列表 -->
          <div
            v-for="(img, idx) in images"
            :key="img + '_' + idx"
            :class="[
              'image-text-editor-sidebar__item',
              enableImagePreview && idx === selectedImgIdx
                ? 'image-text-editor-sidebar__item--active'
                : '',
              direction === 'horizontal' && enableHoverEffect
                ? 'image-text-editor-sidebar__item--hover-enabled'
                : '',
            ]"
            @click="enableImagePreview ? selectImg(idx) : undefined"
            @mouseenter="hoverIdx = idx"
            @mouseleave="hoverIdx = -1"
          >
            <div class="image-text-editor-sidebar__img-wrap" :style="imgStyle">
              <!-- 失效状态显示 -->
              <div
                v-if="isInvalidFileStatus(img)"
                class="custom-upload__invalid-mask"
              >
                <Icon
                  type="sucaiyixiao"
                  class="w-[24px] h-[24px] custom-upload__invalid-icon"
                />
                <div class="custom-upload__invalid-text">素材已失效</div>
              </div>
              <div v-else>
                <!-- 正常状态显示图片 -->
                <ScImg
                  :src="img"
                  class="image-text-editor-sidebar__img"
                  :style="imgStyle"
                  fit="contain"
                  :maxWidth="100"
                />
                <!-- 生成中状态显示进度条遮罩层 -->
                <div
                  v-if="showLoading"
                  class="image-text-editor-sidebar__progress-overlay"
                >
                  <div class="image-text-editor-sidebar__progress-circle">
                    <fa-progress
                      :type="PROGRESS_CONFIG.type"
                      :strokeLinecap="PROGRESS_CONFIG.strokeLinecap"
                      :strokeColor="PROGRESS_CONFIG.strokeColor"
                      :trailColor="PROGRESS_CONFIG.trailColor"
                      :width="PROGRESS_CONFIG.width"
                      :strokeWidth="PROGRESS_CONFIG.strokeWidth"
                      :percent="progressPercent"
                    />
                  </div>
                </div>
                <template v-if="needDelete">
                  <div
                    v-if="hoverIdx === idx && !showLoading"
                    class="image-text-editor-sidebar__mask"
                    @click.stop="selectImg(idx)"
                  ></div>
                  <fa-popconfirm
                    v-model:visible="removeConfirmIdxList[idx]"
                    title="确定将图片从该组图文中删除吗？"
                    placement="top"
                    okType="danger"
                    @confirm="deleteImg(idx)"
                    @cancel="removeConfirmIdxList[idx] = false"
                  >
                    <div
                      v-show="
                        (hoverIdx === idx && !showLoading && hoverIdx != 0) ||
                        removeConfirmIdxList[idx]
                      "
                      class="image-text-editor-sidebar__delete-btn"
                      @click.stop="removeConfirmIdxList[idx] = true"
                    >
                      <Icon
                        type="shanshu_xiao"
                        class="w-[9px] h-[9px] text-[#fff]"
                      />
                    </div>
                  </fa-popconfirm>
                </template>
              </div>
              <template v-if="showReplaceBtn && hoverIdx === idx">
                <div class="image-text-editor-sidebar__replace-btn-abs">
                  <div
                    class="image-text-editor-sidebar__replace-btn"
                    @click.stop="editImg(idx)"
                  >
                    <Icon type="tihuan" class="w-[13px] h-[13px]" />
                    <span class="ml-[4px]">替换</span>
                  </div>
                </div>
              </template>
              <template v-if="idx === 0 && showCover && !showLoading">
                <div
                  class="image-text-editor-sidebar__cover"
                  :class="[`image-text-editor-sidebar__cover-${direction}`]"
                >
                  封面图
                </div>
              </template>
            </div>
          </div>
          <div
            v-if="showAddBtn"
            class="image-text-editor-sidebar__add"
            @click="addImg"
          >
            <fa-icon
              type="fa-add"
              class="image-text-editor-sidebar__add-icon"
            />
            <span class="image-text-editor-sidebar__add-text">添加次图</span>
          </div>
        </div>
      </div>
      <!-- 滚动按钮-下一页 -->
      <div
        v-if="showScrollButtons"
        class="image-text-editor-sidebar__scroll-btn-right"
        :class="{
          'image-text-editor-sidebar__scroll-btn--disabled': isRightEnd,
        }"
        @click="scrollRight"
      >
        <Icon type="jiantou_you" class="image-text-editor-sidebar__icon" />
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted, nextTick, watch } from 'vue';
import ScImg from '@/components/comm/ScImg.vue';

// 移除确认框显示状态
const removeConfirmIdxList = ref<boolean[]>([]);
const props = withDefaults(
  defineProps<{
    images: string[];
    direction: 'vertical' | 'horizontal';
    selectedIndex?: number;
    showAddBtn?: boolean;
    showCover?: boolean;
    showReplaceBtn?: boolean;
    enableImagePreview?: boolean;
    /** 是否显示加载状态 */
    showLoading?: boolean;
    /** 加载进度百分比 */
    loadingPercent?: number;
    /**是否需要滚动 */
    needScroll?: boolean;
    /**是否需要按钮切换滚动 */
    needScrollButtons?: boolean;
    /**图片是否有失效态（素材才会失效） */
    needInvalidFileStatus?: boolean;
    /**是否需要删除按钮 */
    needDelete?: boolean;
    /**是否启用hover效果（仅在horizontal方向生效） */
    enableHoverEffect?: boolean;
  }>(),
  {
    selectedIndex: 0,
    showAddBtn: false,
    showCover: false,
    showReplaceBtn: true,
    enableImagePreview: false,
    showLoading: false,
    loadingPercent: 0,
    needScroll: false,
    needScrollButtons: false,
    needInvalidFileStatus: false,
    needDelete: false,
    enableHoverEffect: false,
  },
);

const emit = defineEmits<{
  (e: 'select', index: number): void;
  (e: 'edit', index: number): void;
  (e: 'delete', index: number): void;
  (e: 'add'): void;
}>();

const selectedImgIdx = ref(props.selectedIndex);
const hoverIdx = ref(-1);

// 进度条配置常量
const PROGRESS_CONFIG = {
  type: 'circle',
  strokeLinecap: 'square',
  strokeColor: '#FFFFFF',
  trailColor: '#e9e9e9',
  width: 41,
  strokeWidth: 2,
} as const;

// 计算进度百分比
const progressPercent = computed(() => Math.floor(props.loadingPercent || 0));

// 监听selectedIndex prop的变化（仅在启用图片预览功能时）
watch(
  () => props.selectedIndex,
  newIndex => {
    if (props.enableImagePreview && newIndex !== undefined) {
      selectedImgIdx.value = newIndex;
    }
  },
  { immediate: true },
);

// 滚动相关变量(目前只针对图文编辑器-排版布局，如需复用可把相关变量改成prop自行拓展)
const scrollContent = ref<HTMLElement | null>(null);
const scrollPosition = ref(0);
const isLeftEnd = ref(true);
const isRightEnd = ref(false);
const ITEM_WIDTH = 88; // 每个图片的宽度
const ITEM_GAP = 16; // 图片间的间距
const MIN_IMAGES_TO_SCROLL = 6; // 大于等于6张图片时才显示滚动按钮
const IMAGES_PER_PAGE = 4; // 每页显示4张图片
const SCROLL_BTN_WIDTH = 28;
const SCROLL_AREA_WIDTH = 408; // 滚动区域的最大宽度（水平布局用）
const SCROLL_STEP_WIDTH = 423; // 滚动按钮切换一次的滚动距离

const SCROLL_AREA_HEIGHT = 850; // 滚动区域的最大高度（垂直布局用）

// 计算是否需要显示滚动按钮
const showScrollButtons = computed(() => {
  return (
    props.needScrollButtons &&
    (props.images?.length || 0) >= MIN_IMAGES_TO_SCROLL
  );
});

// 计算是否展示失效状态
const isInvalidFileStatus = computed(() => {
  return (resId: string) => {
    return props.needInvalidFileStatus && resId == '';
  };
});

// 计算当前页码
const currentPage = computed(() => {
  if (scrollPosition.value === 0) return 0;
  return 1; // 6图场景下只有两页
});

// 滚动容器样式
const scrollContainerStyle = computed(() => {
  if (props.needScroll) {
    return props.direction === 'horizontal'
      ? { width: `${SCROLL_AREA_WIDTH + SCROLL_BTN_WIDTH * 2}px` }
      : { maxHeight: `${SCROLL_AREA_HEIGHT}px`, overflowY: 'auto' as const };
  } else {
    return {};
  }
});

// 计算内容样式
const contentStyle = computed(() => {
  if (props.needScroll) {
    if (props.direction === 'horizontal') {
      const styles: Record<string, string | number> = {
        display: 'flex',
        gap: `${ITEM_GAP}px`,
      };

      styles.transform = `translateX(${scrollPosition.value}px)`;
      styles.transition = 'transform 0.3s ease';

      // 为6图场景预留足够空间，确保能分页显示
      styles.width = `${
        MIN_IMAGES_TO_SCROLL * ITEM_WIDTH +
        (MIN_IMAGES_TO_SCROLL - 1) * ITEM_GAP
      }px`;

      // 确保第二页的2张图片有足够间距，和第一页对齐
      if (
        currentPage.value === 1 &&
        props.images?.length === MIN_IMAGES_TO_SCROLL
      ) {
        // 在第二页时，给非图片内容预留足够空间
        // 第二页显示2张图片，但需预留4张图片的空间
        styles.paddingRight = `${
          (IMAGES_PER_PAGE - 2) * (ITEM_WIDTH + ITEM_GAP)
        }px`;
      }
      return styles;
    } else {
      return {};
    }
  } else {
    return 'flex-wrap: wrap;';
  }
});

// 向左滚动
const scrollLeft = () => {
  if (isLeftEnd.value) return;

  // 直接滚动到第一页
  scrollPosition.value = 0;
  updateScrollState();
};

// 向右滚动
const scrollRight = () => {
  if (isRightEnd.value || !scrollContent.value) return;

  // 直接滚动到下一页
  scrollPosition.value = -SCROLL_STEP_WIDTH;
  updateScrollState();
};

// 更新滚动状态
const updateScrollState = () => {
  // 当在第一页时，左滚动按钮禁用
  isLeftEnd.value = scrollPosition.value >= 0;

  if (!scrollContent.value) {
    isRightEnd.value = true;
    return;
  }

  // 6图场景下，当处于第二页时，右滚动按钮禁用
  isRightEnd.value = scrollPosition.value < 0;
};

// 监听图片数量变化，重新计算滚动状态
watch(
  () => props.images.length,
  () => {
    nextTick(() => {
      scrollPosition.value = 0;
      updateScrollState();
    });
  },
);

// 组件挂载后初始化滚动状态
onMounted(() => {
  nextTick(() => {
    updateScrollState();
  });
});

const selectImg = (idx: number) => {
  selectedImgIdx.value = idx;
  emit('select', idx);
};
const editImg = (idx: number) => {
  emit('edit', idx);
};
const deleteImg = (idx: number) => {
  removeConfirmIdxList.value[idx] = false;
  emit('delete', idx);
};
const addImg = () => {
  emit('add');
};
// 图片样式
const imgStyle = computed(() => {
  return props.direction === 'horizontal'
    ? 'width:88px;height:88px;'
    : 'width:104px;height:104px;';
});
</script>

<style lang="scss" scoped>
@import '@/style/mixins/scrollbar.scss';

.image-text-editor-sidebar {
  @apply flex items-center bg-white;
  &.image-text-editor-sidebar--vertical {
    @apply flex-col h-full border-r border-gray-200;
    .image-text-editor-sidebar__list {
      /* 纵向排版时添加上下边距，但移除gap以防止重叠 */
      @apply flex flex-col w-full items-center pt-16px pb-16px;
      /* 滚动条样式 */
      @include scrollbar-style;
    }

    /* 纵向排版中的项目样式 */
    .image-text-editor-sidebar__item {
      /* 使用margin代替gap，防止重叠 */
      @apply mb-16px;

      &:last-child {
        @apply mb-0;
      }

      &:hover {
        &::after {
          border: 1px solid #3261fd;
        }
      }

      &.image-text-editor-sidebar__item--active {
        &::after {
          border: 2px solid #3261fd;
        }
      }
    }
  }
  &.image-text-editor-sidebar--horizontal {
    @apply flex-row w-auto h-auto border-0;
    .image-text-editor-sidebar__list {
      @apply flex flex-row items-center relative;
      /* 左对齐 */
      @apply justify-start;

      .image-text-editor-sidebar__scroll-container {
        /* 布局相关 */
        @apply relative overflow-hidden;
      }

      .image-text-editor-sidebar__content {
        @apply flex flex-row gap-[16px];
      }

      .image-text-editor-sidebar__scroll-btn-left,
      .image-text-editor-sidebar__scroll-btn-right {
        /* 布局相关 */
        @apply flex items-center justify-center z-10 shrink-0;
        /* 尺寸相关 */
        @apply w-20px h-88px;
        /* 外观相关 */
        @apply bg-[#f5f5f5] text-[#999] cursor-pointer;
        /* 变换相关 */
        @apply transition-colors duration-200;

        &:hover {
          @apply text-[#666];
        }

        &.image-text-editor-sidebar__scroll-btn--disabled {
          @apply text-[#d9d9d9] cursor-not-allowed;
        }
      }

      .image-text-editor-sidebar__scroll-btn-left {
        @apply mr-[12px] rounded-[4px_0_0_4px];
      }

      .image-text-editor-sidebar__scroll-btn-right {
        @apply ml-[12px] rounded-[0_4px_4px_0];
      }

      .image-text-editor-sidebar__icon {
        @apply text-current w-10px h-10px;
      }
    }
  }
  .image-text-editor-sidebar__item {
    box-sizing: border-box;
    @apply flex flex-col items-center shrink-0 overflow-hidden;
    @apply bg-[#f3f3f5] rounded-[8px] transition-all duration-200;
    position: relative;

    &::after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      border-radius: 8px;
    }

    &.image-text-editor-sidebar__item--active {
      &::after {
        border: 2px solid #3261fd;
      }
    }

    /* 水平方向的hover效果 */
    &.image-text-editor-sidebar__item--hover-enabled {
      &:hover:not(.image-text-editor-sidebar__item--active) {
        &::after {
          border: 1px solid #3261fd;
          cursor: pointer;
        }
      }
    }
  }
  .image-text-editor-sidebar__img-wrap {
    @apply relative flex items-center justify-center bg-gray-100;
    @apply overflow-hidden;
    /* 确保图片容器尺寸固定，内容居中 */
  }
  /** 失效状态蒙层 start */
  .custom-upload__invalid-mask {
    /* 布局相关 */
    @apply absolute top-0 left-0 flex flex-col justify-center items-center;
    /* 尺寸相关 */
    @apply w-full h-full;
    /* 外观相关 */
    @apply bg-white border border-solid border-[#fa3534] rounded-[8px];
    /* 文字相关 */
    @apply text-white;

    &:hover .custom-upload__replace-button {
      /* 布局相关 */
      @apply flex;
    }
  }

  .custom-upload__invalid-icon {
    /* 文字相关 */
    @apply text-[#ffd2d2];
  }

  .custom-upload__invalid-text {
    /* 尺寸相关 */
    @apply mt-[6px];
    /* 文字相关 */
    @apply font-normal text-[12px] text-center text-[#fa3534];
  }

  // 更换按钮
  .custom-upload__replace-button {
    /* 布局相关 */
    @apply absolute bottom-4px left-4px right-4px items-center justify-center cursor-pointer hidden;
    /* 尺寸相关 */
    @apply p-[4px_8px] h-[24px];
    /* 外观相关 */
    @apply bg-[rgba(0,0,0,0.75)] rounded-[2px];
  }

  .custom-upload__replace-button span {
    /* 尺寸相关 */
    @apply ml-1;
    /* 文字相关 */
    @apply text-[12px] leading-[22px] text-left text-white;
  }
  /** 失效状态蒙层 end */
  .image-text-editor-sidebar__img {
    @apply object-contain block m-auto;
  }
  .image-text-editor-sidebar__mask {
    @apply absolute inset-0 flex flex-col items-center justify-center gap-[8px] z-10 cursor-pointer;
    // @apply absolute inset-0 bg-black bg-opacity-40 flex flex-col items-center justify-center gap-[8px] z-10 cursor-pointer;
  }
  .image-text-editor-sidebar__replace-btn-abs {
    @apply absolute left-0 bottom-0 w-full flex justify-center z-20 cursor-pointer;
    pointer-events: none;
    .image-text-editor-sidebar__replace-btn {
      @apply pointer-events-auto w-[100%] h-24px bg-[#000] op-75 text-12px text-[#fff] border-rd-0 flex items-center justify-center;
      &:hover {
        @apply op-100;
      }
    }
  }
  // 封面图
  .image-text-editor-sidebar__cover {
    /* 布局相关 */
    @apply absolute;
    /* 尺寸相关 */
    @apply w-48px h-20px lh-20px;
    /* 外观相关 */
    @apply rounded-[10px] bg-[#000] op-75;
    /* 文字相关 */
    @apply text-white text-[12px] text-center leading-[20px];
  }
  .image-text-editor-sidebar__cover-horizontal {
    /* 位置相关 */
    @apply right-[6px] bottom-[4px];
  }
  .image-text-editor-sidebar__cover-vertical {
    /* 位置相关 */
    @apply right-[6px] bottom-[6px];
  }
  .image-text-editor-sidebar__delete-btn {
    /* 布局相关 */
    @apply absolute flex justify-center items-center z-[10];
    /* 位置相关 */
    @apply right-5px top-5px;
    /* 尺寸相关 */
    @apply w-20px h-20px;
    /* 外观相关 */
    @apply bg-[#000] cursor-pointer op-75 rounded-[50%];
    /* 变换相关 */
    @apply transition-opacity duration-200 ease-in-out;
    &:hover {
      @apply op-90;
    }
  }
  .image-text-editor-sidebar__add {
    @apply flex flex-col items-center justify-center w-[104px] h-[104px] border-1 border-dashed border-gray-300 rounded-[8px] cursor-pointer;
    @apply text-gray-400 text-14px mt-16px mb-0;
    &:hover {
      @apply border-[#3261FD];
      .image-text-editor-sidebar__add-icon {
        @apply text-[#3261fd];
      }
      .image-text-editor-sidebar__add-text {
        @apply text-[#3261fd];
      }
    }

    .image-text-editor-sidebar__add-icon {
      @apply text-16px;
    }
  }

  // 进度条遮罩层样式
  .image-text-editor-sidebar__progress-overlay {
    /* 布局相关 */
    @apply absolute top-0 left-0 flex items-center justify-center;
    /* 尺寸相关 */
    @apply w-full h-full;
    /* 外观相关 */
    background-image: url('@/assets/EditProject/loading-new.svg');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    @apply rounded-[8px] overflow-hidden;
  }

  .image-text-editor-sidebar__progress-circle {
    /* 布局相关 */
    @apply relative flex items-center justify-center;

    :deep(.fa-progress) {
      .fa-progress-circle-trail {
        @apply stroke-white stroke-[2px] opacity-[0.2] !important;
      }

      .fa-progress-circle-path {
        @apply stroke-white stroke-[6px] !important;
      }

      .fa-progress-text {
        @apply text-white text-[9px];
      }
    }
  }
}
</style>
