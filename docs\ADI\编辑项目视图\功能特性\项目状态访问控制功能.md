# 项目状态访问控制功能说明

## 功能概述

项目状态访问控制功能用于控制用户在不同项目状态下对编辑页面第一步（上传原料页）的访问权限。该功能确保只有在合适的项目状态下，用户才能访问和编辑项目的原料内容。

## 核心设计原则

### 访问控制规则
- **允许访问**：草稿（0）、待保存（2）、已完成（3）状态
- **限制访问**：生成中（1）状态
- **开发者特权**：开发者账号（cookie包含`9875837`）跳过所有状态检查

### 统一判断标准
所有项目状态访问控制都使用 `shouldRestrictProjectAccess` 函数作为统一判断标准，确保系统各处逻辑的一致性。

## 核心函数

### 1. shouldRestrictProjectAccess
```typescript
/**
 * 判断项目状态能否访问上传原料页（生成中状态不能访问）
 * @param projectStatus 项目状态
 * @returns true=不能访问，false=可以访问
 */
export function shouldRestrictProjectAccess(
  projectStatus?: PROJECT_STATUS | number,
): boolean {
  return isProjectGenerating(projectStatus);
}
```

### 2. shouldRestrictProjectAccessByProjectId
```typescript
/**
 * 根据项目ID判断项目状态能否访问上传原料页
 * @param projectId 项目ID
 * @returns true=不能访问，false=可以访问
 */
export function shouldRestrictProjectAccessByProjectId(
  projectId: string,
): boolean {
  const projectStatus = getProjectStatusFromStore(projectId);
  return shouldRestrictProjectAccess(projectStatus);
}
```

## 应用场景

### 1. API层面的状态检查
**位置**：`src/api/EditProjectView/index.ts`

```typescript
// 检查项目状态能否访问上传原料页（生成中状态不能访问）
const shouldDenyAccess = shouldRestrictProjectAccess(projectData.status);

if (shouldDenyAccess) {
  // 返回错误，触发页面重定向到第二步
  return [new Error(errorMessage), null];
}
```

**功能**：在获取项目数据时进行状态检查，如果项目处于生成中状态，则拒绝访问并触发重定向。

### 2. 页面跳转控制
**位置**：`src/views/EditProjectView/composables/useInfiniteWorkList.ts`

```typescript
// 3. 检查项目状态能否访问上传原料页（生成中状态不能访问）
const shouldRestrictAccess = shouldRestrictProjectAccess(projectStatus);
const isAllowed = !shouldRestrictAccess;
```

**功能**：在删除最后一个作品后自动跳转到第一步时，检查项目状态是否允许跳转，避免死循环问题。

### 3. UI组件显示控制
**位置**：`src/views/EditProjectView/components/ProjectFooter/index.vue`

```typescript
const shouldHidePrevButton = computed(() => {
  return shouldRestrictProjectAccessByProjectId(props.projectId);
});
```

**功能**：控制"返回上一步"按钮的显示/隐藏，生成中状态时隐藏按钮。

## 开发者账号特殊处理

### 检测机制
通过检查 cookie 中是否包含 `9875837` 来识别开发者账号：

```typescript
const isDeveloperAccount = document.cookie.includes('9875837');
const shouldCheckProjectStatus = isEditMode.value && !isDeveloperAccount;
```

### 特权功能
- **跳过状态检查**：开发者账号在所有项目状态下都可以访问上传原料页
- **无重定向限制**：即使项目处于生成中状态，开发者也不会被重定向
- **调试便利**：方便开发和测试过程中的操作

## 错误处理机制

### 状态未知容错
```typescript
if (projectStatus === undefined || projectStatus === null) {
  console.log('项目状态未知，允许跳转', { 项目ID: projectId });
  return true; // 默认允许访问
}
```

### 异常情况处理
```typescript
try {
  // 状态检查逻辑
} catch (error) {
  console.error('项目状态检查失败，默认允许跳转:', error);
  return true; // 发生错误时默认允许访问
}
```

## 测试覆盖

### 正常场景测试
- ✅ 草稿状态（0）：允许访问上传原料页
- ✅ 待保存状态（2）：允许访问上传原料页  
- ✅ 已完成状态（3）：允许访问上传原料页
- ✅ 新建项目（无状态）：允许访问上传原料页

### 限制场景测试
- ❌ 生成中状态（1）：不允许访问，触发重定向到第二步

### 特殊场景测试
- ✅ 开发者账号：跳过所有状态检查，始终允许访问
- ✅ 状态获取失败：默认允许访问，避免阻塞用户操作
- ✅ 项目删除保护：通过错误码6100处理

## 相关文件

### 核心实现文件
- `src/views/EditProjectView/utils/projectStatus.ts` - 状态判断工具函数
- `src/api/EditProjectView/index.ts` - API层状态检查
- `src/views/EditProjectView/composables/useInfiniteWorkList.ts` - 页面跳转控制
- `src/views/EditProjectView/components/ProjectFooter/index.vue` - UI组件控制

### 测试文件
- `src/views/EditProjectView/composables/__tests__/useProjectData.projectStatus.test.ts`
- `src/views/EditProjectView/composables/__tests__/useInfiniteWorkList.projectStatus.test.ts`

### 配置文件
- `src/constants/project.ts` - 项目状态常量定义
- `src/store/modules/project.ts` - 项目状态Vuex模块

## 总结

项目状态访问控制功能通过统一的 `shouldRestrictProjectAccess` 判断标准，在API层、页面跳转层和UI组件层实现了一致的访问控制逻辑。该功能确保了：

1. **用户体验一致性**：生成中状态时统一限制访问上传原料页
2. **系统稳定性**：避免状态冲突导致的死循环问题
3. **开发便利性**：开发者账号可以跳过限制进行调试
4. **容错性**：异常情况下默认允许访问，避免阻塞用户操作

该功能是项目编辑流程中的重要安全机制，保证了用户在合适的时机进行相应的操作。
