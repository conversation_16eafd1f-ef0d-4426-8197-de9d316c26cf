<template>
  <ul class="nav-project">
    <!-- 步骤列表 -->
    <li
      v-for="(step, index) in steps"
      :key="index"
      class="nav-project__step"
      :class="{
        'nav-project__step--selected': index === currentStep,
        'nav-project__step--first': index === FIRST_STEP_INDEX,
        'nav-project__step--second': index === SECOND_STEP_INDEX,
        'nav-project__step--disabled': !allowStepChange,
      }"
      @click="stepChange(index)"
    >
      <span class="nav-project__step-index">{{ index + 1 }}</span>
      <span class="nav-project__step-text">{{ step }}</span>
    </li>
  </ul>
</template>

<script lang="ts">
import { defineComponent, computed } from 'vue';
import { message } from '@fk/faicomponent';
import store from '@/store';
import {
  FIRST_STEP_INDEX,
  SECOND_STEP_INDEX,
} from '@/views/EditProjectView/constants';
import {
  shouldRestrictProjectAccessByProjectId,
  shouldRestrictPreviewAccessByProjectId,
} from '@/views/EditProjectView/utils/projectStatus';
import { NAVIGATION_MESSAGES } from './config';

export default defineComponent({
  name: 'NavProject',
  props: {
    /**
     * 步骤列表
     */
    steps: {
      type: Array as () => string[],
      default: () => ['上传原料', '预览效果'],
    },
    /**
     * 当前激活的步骤索引
     */
    currentStep: {
      type: Number,
      default: 0,
    },
    /**
     * 是否允许切换步骤
     */
    allowStepChange: {
      type: Boolean,
      default: false,
    },
    /**
     * 项目ID，用于获取项目状态进行条件跳转控制
     */
    projectId: {
      type: [String, Number],
      default: null,
    },
  },
  setup(props, { emit }) {
    /**
     * 获取当前项目状态
     */
    const currentProjectStatus = computed(() => {
      if (!props.projectId) return undefined;
      return store.getters['project/getProjectStatus'](String(props.projectId));
    });

    /**
     * 检查是否可以跳转到指定步骤
     * @param targetStep - 目标步骤索引
     * @returns 是否可以跳转
     */
    const canNavigateToStep = (targetStep: number): boolean => {
      // 检查步骤索引是否有效
      if (targetStep < 0 || targetStep >= props.steps.length) {
        return false;
      }

      // 如果没有项目ID，允许跳转（新建项目等场景）
      if (!props.projectId) {
        return true;
      }

      // 步骤 0: 上传原料 - 使用现有的权限控制函数
      if (targetStep === FIRST_STEP_INDEX) {
        // 复用 shouldRestrictProjectAccessByProjectId 的逻辑
        // 该函数返回 true 表示不能访问，false 表示可以访问
        return !shouldRestrictProjectAccessByProjectId(String(props.projectId));
      }

      // 步骤 1: 预览效果 - 使用现有的权限控制函数
      if (targetStep === SECOND_STEP_INDEX) {
        // 复用 shouldRestrictPreviewAccessByProjectId 的逻辑
        // 该函数返回 true 表示不能访问，false 表示可以访问
        return !shouldRestrictPreviewAccessByProjectId(String(props.projectId));
      }

      return true;
    };

    /**
     * 获取跳转限制的提示信息
     * @param targetStep - 目标步骤索引
     * @returns 提示信息
     */
    const getNavigationRestrictMessage = (targetStep: number): string => {
      // 如果没有项目ID，无法获取限制信息
      if (!props.projectId) {
        return '';
      }

      // 步骤 0: 上传原料 - 检查是否被限制访问
      if (targetStep === FIRST_STEP_INDEX) {
        const isRestricted = shouldRestrictProjectAccessByProjectId(
          String(props.projectId),
        );
        return isRestricted
          ? NAVIGATION_MESSAGES.GENERATING_CANNOT_ACCESS_UPLOAD
          : '';
      }

      // 步骤 1: 预览效果 - 检查是否被限制访问
      if (targetStep === SECOND_STEP_INDEX) {
        const isRestricted = shouldRestrictPreviewAccessByProjectId(
          String(props.projectId),
        );
        return isRestricted
          ? NAVIGATION_MESSAGES.DRAFT_CANNOT_ACCESS_PREVIEW
          : '';
      }

      return '';
    };

    /**
     * 步骤切换处理函数
     * @param step - 目标步骤索引
     */
    const stepChange = (step: number) => {
      // 如果不允许切换步骤，直接返回
      if (!props.allowStepChange) {
        return;
      }

      // 如果是当前步骤，直接返回
      if (step === props.currentStep) {
        return;
      }

      // 检查是否可以跳转到目标步骤
      if (!canNavigateToStep(step)) {
        const restrictMessage = getNavigationRestrictMessage(step);
        if (restrictMessage) {
          message.warning(restrictMessage);
        }
        return;
      }

      // 执行跳转
      emit('stepChange', step);
    };

    return {
      stepChange,
      currentProjectStatus,
      canNavigateToStep,
      FIRST_STEP_INDEX,
      SECOND_STEP_INDEX,
    };
  },
});
</script>

<style scoped>
/* 导航项目主容器 */
.nav-project {
  /* 布局相关 */
  @apply flex items-center;
}

/* 步骤项 */
.nav-project__step {
  /* 布局相关 */
  @apply flex items-center justify-center;
  /* 尺寸相关 */
  @apply w-176px h-40px;
  /* 交互相关 */
  @apply cursor-pointer select-none;
  /* 文字相关 */
  @apply font-400;
  /* 背景相关 */
  @apply bg-center bg-no-repeat;
  background-size: 100% 100%;
  /* 过渡动画 */
  @apply transition-all duration-300 ease-in-out;
}

/* 禁用状态 */
.nav-project__step--disabled {
  /* 交互相关 */
  @apply cursor-default;
}

/* 步骤序号 */
.nav-project__step-index {
  /* 间距相关 */
  @apply mr-6px;
  /* 文字相关 */
  @apply text-20px font-bold text-[#999];
  /* 过渡动画 */
  @apply transition-colors duration-300 ease-in-out;
}

/* 步骤文本 */
.nav-project__step-text {
  /* 文字相关 */
  @apply text-14px text-[#999];
  /* 过渡动画 */
  @apply transition-colors duration-300 ease-in-out;
}

/* 步骤1 - 默认状态 */
.nav-project__step--first {
  /* 背景相关 */
  background-image: url('@/assets/EditProject/step-1.svg');
}

/* 步骤1 - 选中状态 */
.nav-project__step--first.nav-project__step--selected {
  /* 背景相关 */
  background-image: url('@/assets/EditProject/step-1-select.svg');
}

/* 步骤2 - 默认状态 */
.nav-project__step--second {
  /* 背景相关 */
  background-image: url('@/assets/EditProject/step-2.svg');
}

/* 步骤2 - 选中状态 */
.nav-project__step--second.nav-project__step--selected {
  /* 背景相关 */
  background-image: url('@/assets/EditProject/step-2-select.svg');
}

/* 选中状态样式 */
.nav-project__step--selected {
  /* 文字相关 */
  @apply font-700;
  /* 过渡动画 */
  @apply transition-all duration-300 ease-in-out;
}

.nav-project__step--selected .nav-project__step-index {
  /* 文字相关 */
  @apply text-[#324CFD];
}

.nav-project__step--selected .nav-project__step-text {
  /* 文字相关 */
  @apply text-[#111];
}

/* 步骤切换动画 */
.nav-project__step {
  position: relative;
  overflow: hidden;
}

.nav-project__step::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.1);
  opacity: 0;
  @apply transition-opacity duration-300 ease-in-out;
}

.nav-project__step:hover::after {
  opacity: 1;
}

/* 禁用状态下不显示 hover 效果 */
.nav-project__step--disabled:hover::after {
  opacity: 0;
}
</style>
