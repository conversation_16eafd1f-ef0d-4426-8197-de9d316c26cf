import { BaseWorkItem, ResourceBasicInfo } from '@/types/Work';

/**
 * 作品列表API响应数据
 */
export interface WorkViewItem extends BaseWorkItem {
  /** 模板ID */
  templateId: number;
  /** 是否生成中 */
  isGenerating?: boolean;
  /** 是否重新生成完成 */
  isReCompleted?: boolean;
  /** 是否有失败的作品 */
  hadFailWork?: boolean;
  data: {
    /** 失败原因 */
    errMsg?: string;
    /** 关联作品ID */
    relWorkId?: number;
  };
}

/**
 * 作品详情API响应数据
 */
export interface WorkDetailApiResponse extends BaseWorkItem {
  /** 作品数据详情 */
  data?: {
    /** 关联作品ID */
    relWorkId?: number;
    /** 错误信息（生成失败时） */
    errMsg?: string;

    /** 图文列表 */
    graphic?: ResourceBasicInfo[];

    /** 视频相关资源 */
    /** 基础视频资源 */
    baseVideo?: VideoResource;
    /** 基础封面资源 */
    baseCover?: VideoCoverResource;
    /** 完整视频资源 */
    fullVideo?: VideoResource;
    /** 完整封面资源 */
    fullCover?: VideoCoverResource;
  };
  /** 关联作品数据详情 */
  relData?: {
    /** 关联作品ID */
    relWorkId?: number;
    /** 基础视频资源 */
    baseVideo?: VideoResource;
    /** 基础封面资源 */
    baseCover?: VideoCoverResource;
    /** 完整视频资源 */
    fullVideo?: VideoResource;
    /** 完整封面资源 */
    fullCover?: VideoCoverResource;
  };
}

/**
 * 视频资源
 */
export interface VideoResource {
  /** 资源ID */
  id: string;
  /** 资源类型 */
  type: number;
  /** 资源时长 */
  duration: number;
}

/**
 * 视频封面资源
 */
export interface VideoCoverResource {
  /** 资源ID */
  id: string;
  /** 资源类型 */
  type: number;
}
