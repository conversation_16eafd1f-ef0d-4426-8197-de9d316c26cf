/**
 * @description 充值后自动更新积分的 composable
 */

import { ref, onMounted, onUnmounted } from 'vue';

/**
 * 充值配置选项
 */
export interface UsePointsRechargeOptions {
  /** 积分更新函数 */
  updatePoints: () => void;
  /** 充值页面URL，默认为 '/recharge' */
  rechargeUrl?: string;
}

/**
 * 充值后自动更新积分的 composable 返回值
 */
export interface UsePointsRechargeReturn {
  /** 处理充值跳转 */
  handleRecharge: () => void;
}

/**
 * 充值后自动更新积分的 composable
 */
export function usePointsRecharge(
  options: UsePointsRechargeOptions,
): UsePointsRechargeReturn {
  const { updatePoints, rechargeUrl = '/' } = options;

  // 是否正在等待从充值页面返回的标记
  const isReturningFromRecharge = ref(false);

  /**
   * 处理页面可见性变化
   * 当用户从充值页面返回时自动更新积分
   */
  const handleVisibilityChange = (): void => {
    if (!document.hidden && isReturningFromRecharge.value) {
      isReturningFromRecharge.value = false;
      updatePoints();
    }
  };

  /**
   * 处理充值跳转
   */
  const handleRecharge = (): void => {
    isReturningFromRecharge.value = true;
    window.open(rechargeUrl, '_blank');
  };

  // 页面可见性监听
  onMounted(() => {
    document.addEventListener('visibilitychange', handleVisibilityChange);
  });

  onUnmounted(() => {
    document.removeEventListener('visibilitychange', handleVisibilityChange);
  });

  return {
    handleRecharge,
  };
}
