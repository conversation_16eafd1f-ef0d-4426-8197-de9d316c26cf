<template>
  <div class="loading-wrapper" :class="{ defaultBg: !imgSrc }">
    <scImg v-if="!!imgSrc" :src="imgSrc" class="preview-pic" />
    <!-- 创建居中容器，使用 flexbox 自动处理间距 -->
    <div class="loading-content">
      <fa-progress
        strokeLinecap="square"
        strokeColor="#FFFFFF"
        :percent="percent"
        :width="width"
        type="circle"
        class="progress-circle"
      />
      <div class="loading-tips">
        <div v-for="(text, index) in displayText" :key="index">{{ text }}</div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { defineProps, computed, type PropType } from 'vue';

/**
 * LoadingPreview 组件 Props
 * @property imgSrc 封面图片地址
 * @property percent 进度百分比
 * @property loadingText 加载提示文案（支持字符串或字符串数组）
 * @property width 进度环宽度
 */
const props = defineProps({
  /** 封面图片地址 */
  imgSrc: {
    type: String,
    default: '',
  },
  /** 进度百分比 */
  percent: {
    type: Number,
    default: 0,
  },
  /** 加载提示文案（支持字符串或字符串数组） */
  loadingText: {
    type: [String, Array] as PropType<string | string[]>,
    default: null,
  },
  /** 进度环宽度 */
  width: {
    type: [String, Number],
    default: undefined,
  },
});

// 计算显示的文案
const displayText = computed<string[]>(() => {
  if (props.loadingText) {
    if (Array.isArray(props.loadingText)) {
      return props.loadingText;
    }
    return [props.loadingText];
  }
  // 默认文案
  return ['视频重新生成中', '请稍后预览'];
});
</script>

<style lang="scss" scoped>
.loading-wrapper {
  // 内层图片应用模糊滤镜，但其模糊扩散会被外层容器限制，外层容器preview-wrapper设置overflow: hidden来裁剪超出边界的模糊效果
  // 外层容器保持圆角设置rounded-[12px]，确保视觉效果一致
  /* 定位相关 */
  @apply position-relative;
  /* 尺寸相关 */
  @apply max-w-[270px] w-[270px] h-[480px];
  /* 外观相关 */
  @apply rounded-[12px] overflow-hidden;

  &.defaultBg {
    background-image: url('@/assets/EditProject/loading-new.svg');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
  }

  .preview-pic {
    /* 尺寸相关 */
    @apply size-full;
    /* 外观相关 */
    @apply object-cover;
    filter: blur(40px);
    // 应用transform: scale(1.2)轻微放大图片，防止边缘因模糊后出现白边
    transform: scale(1.2);
  }
}

.loading-content {
  /* 定位相关 - 绝对居中 */
  @apply absolute top-[50%] left-[50%] translate-x-[-50%] translate-y-[-50%];
  /* 布局相关 - flexbox 纵向布局 */
  @apply flex flex-col items-center;
  /* 尺寸相关 - 16px 间距 */
  @apply gap-[16px];
}

.progress-circle {
  :deep(.fa-progress-circle-trail) {
    @apply stroke-white stroke-[2px] opacity-[0.2] !important;
  }

  :deep(.fa-progress-circle-path) {
    @apply stroke-white stroke-[6px] !important;
  }

  :deep(.fa-progress-text) {
    /* 文字相关 */
    @apply text-white text-[17px];
  }
}

.loading-tips {
  /* 文字相关 */
  @apply text-white text-[14px] text-center lh-[19px];
}
</style>
