import { ref, Ref } from 'vue';
import { getProgressFromEvent } from '../utils';

interface UseProgressControlOptions {
  duration: Ref<number>;
  onSeek: (time: number) => void;
  onSeeking: (percent: number, time: number) => void;
}

/**
 * 进度条控制 hook
 */
export function useProgressControl(options: UseProgressControlOptions) {
  const { duration, onSeek, onSeeking } = options;

  const isDragging = ref(false);
  const recentlyDragged = ref(false);
  const dragResetTimer = ref<number | null>(null);
  const localPlayedPercent = ref(0);
  const localCurrentTime = ref(0);
  const lastSeekPosition = ref(0);

  /**
   * 重置拖动标记的定时器
   */
  const resetDragStateTimer = () => {
    if (dragResetTimer.value) {
      window.clearTimeout(dragResetTimer.value);
    }

    // 设置拖动标记
    recentlyDragged.value = true;

    // 3秒后重置，或者当视频实际更新到接近拖动位置时重置
    dragResetTimer.value = window.setTimeout(() => {
      recentlyDragged.value = false;
      dragResetTimer.value = null;
    }, 3000);
  };

  /**
   * 根据事件更新进度
   * @param event 鼠标事件
   * @param element 目标元素
   */
  const updateProgressFromEvent = (event: MouseEvent, element: HTMLElement) => {
    const position = getProgressFromEvent(event, element);

    // 更新本地进度值
    localPlayedPercent.value = position * 100;
    localCurrentTime.value = position * duration.value;
    lastSeekPosition.value = position * 100;

    // 通知进度更新
    onSeeking(localPlayedPercent.value, localCurrentTime.value);

    return position;
  };

  /**
   * 鼠标移动事件处理
   */
  const onMouseMove = (evt: MouseEvent, element: HTMLElement) => {
    if (isDragging.value) {
      updateProgressFromEvent(evt, element);
      evt.preventDefault(); // 防止选中文本
    }
  };

  /**
   * 鼠标抬起事件处理
   */
  const onMouseUp = (evt: MouseEvent, element: HTMLElement) => {
    if (isDragging.value) {
      const position = updateProgressFromEvent(evt, element);

      // 通知父组件跳转到新位置
      onSeek(position * duration.value);

      // 设置拖动标记，确保进度条保持在用户拖动的位置
      resetDragStateTimer();

      isDragging.value = false;
      document.body.style.userSelect = ''; // 恢复文本选择
    }

    removeProgressDragListeners();
  };

  /**
   * 添加进度拖动监听器
   */
  const addProgressDragListeners = (element: HTMLElement) => {
    const moveHandler = (e: MouseEvent) => onMouseMove(e, element);
    const upHandler = (e: MouseEvent) => onMouseUp(e, element);

    document.addEventListener('mousemove', moveHandler, { passive: false });
    document.addEventListener('mouseup', upHandler);

    return () => {
      document.removeEventListener('mousemove', moveHandler);
      document.removeEventListener('mouseup', upHandler);
    };
  };

  /**
   * 移除进度拖动监听器
   */
  const removeProgressDragListeners = () => {
    if (dragResetTimer.value) {
      window.clearTimeout(dragResetTimer.value);
      dragResetTimer.value = null;
    }
  };

  /**
   * 进度条拖动事件处理
   */
  const onProgressHandlerMouseDown = (
    event: MouseEvent,
    element: HTMLElement,
  ) => {
    isDragging.value = true;
    document.body.style.userSelect = 'none'; // 防止拖动时选中文本

    updateProgressFromEvent(event, element);
    addProgressDragListeners(element);
  };

  /**
   * 进度条点击事件处理
   */
  const onProgressClick = (event: MouseEvent, element: HTMLElement) => {
    const position = updateProgressFromEvent(event, element);

    // 通知父组件跳转到新位置
    onSeek(position * duration.value);

    // 设置拖动标记，确保进度条保持在用户点击的位置
    resetDragStateTimer();
  };

  return {
    isDragging,
    recentlyDragged,
    localPlayedPercent,
    localCurrentTime,
    lastSeekPosition,
    onProgressHandlerMouseDown,
    onProgressClick,
    removeProgressDragListeners,
  };
}
