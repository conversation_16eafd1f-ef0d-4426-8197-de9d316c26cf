import { VueConstructor } from 'vue';
import clickOutside from './clickOutside';

// 所有指令的集合
const directives = {
  clickOutside,
};

// 安装所有指令的函数
const install = (Vue: VueConstructor) => {
  Object.keys(directives).forEach(key => {
    Vue.directive(key, directives[key as keyof typeof directives]);
  });
};

// 声明全局 window 类型
declare global {
  interface Window {
    Vue?: VueConstructor;
  }
}

// 如果在浏览器环境且 Vue 存在，自动安装
if (typeof window !== 'undefined' && window.Vue) {
  install(window.Vue);
}

export default {
  install,
};

// 单独导出各个指令，方便按需引入
export { clickOutside };
