/**
 * @fileoverview 智能轮询事件管理器
 * @description 负责事件的注册、分发和管理
 */

import { logger } from '@/utils/logger';
import { WorkEvent, EventHandler } from '../types';

/**
 * 事件管理器类
 * 负责事件的注册、分发和管理
 */
export class WorkEventManager {
  private handlers: Map<string, EventHandler[]> = new Map();

  /**
   * 注册事件处理器
   * @param eventType 事件类型
   * @param handler 事件处理器
   */
  on<T extends WorkEvent>(
    eventType: T['type'],
    handler: EventHandler<T>,
  ): void {
    if (!this.handlers.has(eventType)) {
      this.handlers.set(eventType, []);
    }
    this.handlers.get(eventType)!.push(handler as EventHandler);
  }

  /**
   * 移除事件处理器
   * @param eventType 事件类型
   * @param handler 要移除的处理器（可选，不传则移除所有）
   */
  off<T extends WorkEvent>(
    eventType: T['type'],
    handler?: EventHandler<T>,
  ): void {
    const eventHandlers = this.handlers.get(eventType);
    if (!eventHandlers) return;

    if (handler) {
      const index = eventHandlers.indexOf(handler as EventHandler);
      if (index > -1) {
        eventHandlers.splice(index, 1);
      }
    } else {
      this.handlers.delete(eventType);
    }
  }

  /**
   * 触发事件
   * @param event 事件对象
   */
  async emit<T extends WorkEvent>(event: T): Promise<void> {
    const eventHandlers = this.handlers.get(event.type);
    if (!eventHandlers || eventHandlers.length === 0) {
      return;
    }

    // 记录事件触发日志
    this.logEventTrigger(event, eventHandlers.length);

    // 并行执行所有处理器
    const promises = eventHandlers.map(handler => 
      this.executeHandler(handler, event)
    );

    await Promise.allSettled(promises);
  }

  /**
   * 清除所有事件处理器
   */
  clear(): void {
    this.handlers.clear();
  }

  /**
   * 获取已注册的事件类型列表
   */
  getRegisteredEvents(): string[] {
    return Array.from(this.handlers.keys());
  }

  /**
   * 记录事件触发日志
   * @private
   */
  private logEventTrigger(event: WorkEvent, handlerCount: number): void {
    logger.debug(`📡 事件触发: ${event.type}`, {
      作品ID: event.workId,
      事件类型: event.type,
      触发上下文: event.context,
      处理器数量: handlerCount,
      时间戳: new Date(event.timestamp).toLocaleTimeString(),
    });
  }

  /**
   * 执行事件处理器
   * @private
   */
  private async executeHandler(handler: EventHandler, event: WorkEvent): Promise<void> {
    try {
      await handler(event);
    } catch (error) {
      logger.debug(`❌ 事件处理器执行失败: ${event.type}`, {
        作品ID: event.workId,
        错误信息: error instanceof Error ? error.message : '未知错误',
        事件类型: event.type,
      });
    }
  }
}
