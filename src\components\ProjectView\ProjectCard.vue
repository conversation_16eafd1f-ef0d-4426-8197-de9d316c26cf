<template>
  <fa-tooltip
    :title="failWorkTooltip"
    :overlayStyle="{ maxWidth: '338px' }"
    :getPopupContainer="getPopupContainer"
  >
    <div class="project-card">
      <fa-card
        :body-style="{ padding: '0px' }"
        hoverable
        @mouseover="isShowFooterBtn = true"
        @mouseout="isShowFooterBtn = false"
      >
        <div class="cover-wrapper">
          <img
            v-if="coverInfo"
            class="cover-image"
            :src="coverInfo.src"
            :alt="coverInfo.alt"
          />
          <ScImg
            v-else
            class="cover-image"
            fit="cover"
            :src="coverImg.resId"
            :belong="coverImg.belong"
            :maxWidth="250"
          />
          <div class="status-label" :class="getStatusClass(project.status)">
            {{ getStatusText(project.status) }}
          </div>
          <div class="project-id">
            <span>项目ID：</span>
            <span>{{ project.id }}</span>
          </div>
        </div>
        <div class="info-wrapper">
          <div class="h-[21px] flex items-center gap-[4px]">
            <div class="type-tag" :class="getProjectTagClass(project.type)">
              {{ getProjectType(project.type) }}
            </div>
            <div class="title-text">
              {{ project.name }}
            </div>
            <EditNamePopconfirm
              v-show="isShowEditBtn"
              inputLabel="项目名称"
              :itemId="project.id"
              :initialValue="project.name"
              :visible.sync="isShowEditNamePopconfirm"
              @confirm="handleUpdateProjectName"
            />
          </div>
          <div class="meta-wrapper">
            <template v-for="(item, index) in metaList">
              <div :key="index" class="meta-item">
                <span class="mr-[4px]">{{ item.label }}：</span>
                <span>{{ item.value }}</span>
                <img
                  v-if="item.type === 'progress' && hadFailWork"
                  src="@/assets/EditProject/xinxishizhi.svg"
                  alt="信息提示"
                  class="w-[16px] h-[16px] ml-[8px]"
                />
              </div>
            </template>
          </div>
          <div v-show="isShowEditBtn" class="actions-wrapper">
            <div class="flex gap-[12px]">
              <fa-button
                size="small"
                class="action-button"
                @click.stop="handleEdit"
                >进入项目</fa-button
              >
              <fa-button
                size="small"
                class="action-button"
                @click.stop="handleShowDetail"
                >项目详情</fa-button
              >
            </div>
            <fa-dropdown
              @visibleChange="visibleChange"
              placement="bottomCenter"
            >
              <div class="more-btn">
                <Icon type="gengduo" class="w-[20px] h-[20px]" />
              </div>
              <fa-menu slot="overlay" class="moreOptBox">
                <fa-menu-item
                  v-for="item in moreButtonList"
                  :key="item.name"
                  @click="item.click()"
                >
                  {{ item.name }}
                </fa-menu-item>
              </fa-menu>
            </fa-dropdown>
          </div>
        </div>
      </fa-card>
    </div>
  </fa-tooltip>
</template>

<script setup lang="ts">
import { defineProps, defineEmits, ref, computed, type PropType } from 'vue';
import { day } from '@/utils/dayjs';
import {
  PROJECT_TYPE,
  PROJECT_TYPE_NAME,
  PROJECT_STATUS,
  PROJECT_STATUS_NAME,
  PROJECT_STATUS_CLASS_NAME,
} from '@/constants/project';
import { ProjectData } from '@/types/Project';
import ScImg from '@/components/comm/ScImg.vue';
import EditNamePopconfirm from '@/components/ProjectView/EditNamePopconfirm.vue';
import emptyCover from '@/assets/ProjectView/empty.webp';
import failCover from '@/assets/ProjectView/fail.webp';
import { getProjectStatusInfo } from './utils';

/**
 * ProjectCard 组件 Props
 * @property project 项目信息对象
 */
const props = defineProps({
  /** 项目信息对象 */
  project: {
    type: Object as PropType<ProjectData>,
    required: true,
  },
});

const emit = defineEmits<{
  (e: 'detail', value: ProjectData): void;
  (e: 'copy', value: ProjectData): void;
  (e: 'edit', value: ProjectData): void;
  (e: 'update', value: { id: number; name: string }): void;
  (e: 'delete', value: ProjectData): void;
}>();

// States
const isShowEditNamePopconfirm = ref(false);
const isShowFooterBtn = ref(false);
const isShowDropdown = ref(false);

// Computed properties
const coverImg = computed(() => {
  const { coverImg, templateCoverImg } = props.project;
  if (coverImg && Object.keys(coverImg).length > 0) {
    return {
      ...coverImg,
      resType: coverImg.resType as number,
      belong: 'user' as const,
    };
  } else {
    return {
      ...templateCoverImg,
      resType: templateCoverImg?.resType as number,
      belong: 'oss' as const,
    };
  }
});

const coverInfo = computed(() => {
  const { totalNum, sucNum, failNum, hadFailWork } = props.project;
  if (totalNum === 0 || sucNum === 0) {
    return {
      src: emptyCover,
      alt: '无作品生成',
    };
  }
  if (hadFailWork && failNum === totalNum) {
    return {
      src: failCover,
      alt: '全部作品生成失败',
    };
  }
  return null;
});

const metaList = computed(() => {
  const { status, sucNum, failNum, totalNum, saveNum } = props.project;
  const info = getProjectStatusInfo({
    status,
    sucNum,
    failNum,
    totalNum,
    saveNum,
  });
  return [
    // 作品生成进度
    {
      type: 'progress',
      label: info.label,
      value: info.value,
    },
    // 最近编辑时间
    {
      type: 'time',
      label: '编辑于',
      value: day(props.project.updateTime).format('YYYY/MM/DD HH:mm:ss'),
    },
  ];
});

const hadFailWork = computed(() => {
  return props.project.hadFailWork;
});

const failWorkTooltip = computed(() => {
  if (hadFailWork.value) {
    return PROJECT_STATUS.DRAFT == props.project.status
      ? '作品生成失败，点数已返还，详情请进入项目查看。'
      : '存在部分作品生成失败，点数已返还。';
  }
  return '';
});

const isShowEditBtn = computed(() => {
  return (
    isShowFooterBtn.value ||
    isShowDropdown.value ||
    isShowEditNamePopconfirm.value
  );
});

const moreButtonList = computed(() => {
  return [
    {
      name: '复制项目',
      click: () => {
        handleCopy();
        isShowDropdown.value = false;
      },
    },
    {
      name: '删除项目',
      click: () => {
        handleDelete();
        isShowDropdown.value = false;
      },
    },
  ];
});

// Methods
const handleShowDetail = () => {
  emit('detail', props.project);
};

const handleCopy = () => {
  emit('copy', props.project);
};

const handleEdit = () => {
  emit('edit', props.project);
};

const handleDelete = () => {
  emit('delete', props.project);
};

const handleUpdateProjectName = (params: { id: number; name: string }) => {
  emit('update', params);
};

// 获取项目类型文本
const getProjectType = (type: PROJECT_TYPE) => {
  return PROJECT_TYPE_NAME[type];
};

// 获取项目类型样式类
const getProjectTagClass = (type: PROJECT_TYPE) => {
  return type === PROJECT_TYPE.VIDEO ? 'tag-video' : 'tag-image';
};

// 获取状态文本
const getStatusText = (status: PROJECT_STATUS) => {
  return PROJECT_STATUS_NAME[status];
};

// 获取状态样式类
const getStatusClass = (status: PROJECT_STATUS) => {
  return PROJECT_STATUS_CLASS_NAME[status];
};

const visibleChange = (visible: boolean) => (isShowDropdown.value = visible);

function getPopupContainer(triggerNode: HTMLElement) {
  // 返回最近的滚动容器, 弹窗跟随页面滚动
  return triggerNode;
}
</script>

<style lang="scss" scoped>
.project-card {
  transition: box-shadow 0.3s cubic-bezier(0.4, 0, 0.2, 1),
    transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  :deep(.fa-card-hoverable) {
    cursor: auto;
    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 2px 16px #0000001f;
    }
  }
  .cover-wrapper {
    @apply relative h-[220px];
    .cover-image {
      @apply rounded-t-[12px] w-full h-full object-cover;
    }
    .status-label {
      @apply absolute top-[11px] right-[-6px] h-[26px] leading-[26px] px-[8px] rounded-[13px_4px_4px_13px] text-white;
      &.status-draft {
        @apply bg-[#bfbfbf];
      }

      &.status-processing {
        @apply bg-[#ffad5c];
      }

      &.status-completed {
        @apply bg-[#75c15b];
      }
    }
    .project-id {
      @apply absolute top-[185px] left-[16px] h-[24px] px-[8px] rounded-[12px] bg-black/60 text-white flex items-center;
    }
  }

  .info-wrapper {
    @apply relative h-[92px] px-[16px] pt-[12px];
    .type-tag {
      @apply min-w-[36px] h-[20px] leading-[20px] rounded-[10px] text-[12px] text-center;
      &.tag-video {
        @apply bg-[#e2edff] text-[#307cff];
      }
      &.tag-image {
        @apply bg-[#c0f6f4] text-[#00a6b9];
      }
    }
    .title-text {
      @apply font-bold text-[16px] text-left overflow-hidden whitespace-nowrap text-ellipsis text-title;
    }
    .meta-wrapper {
      @apply text-[12px] mt-[8px] text-subText;
      .meta-item {
        @apply flex items-center mb-[4px] text-[12px] text-assist last:mb-0;
      }
    }
    .actions-wrapper {
      @apply absolute top-[40px] w-[calc(100%-32px)] h-[42px] flex items-center justify-between bg-white;
      .action-button {
        @apply box-border px-[11px];
      }

      .more-btn {
        @apply size-[40px] cursor-pointer flex items-center justify-center hover:(bg-[#f5f5f5] rounded-[8px]);
      }
    }
  }
}

:deep(.fa-dropdown-menu-item-active) {
  @apply bg-disabledBackground;
}
</style>
