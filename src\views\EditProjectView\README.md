# EditProjectView 功能文档

## 功能概述

EditProjectView 是一个项目编辑视图模块，支持创建和编辑两种类型的项目：视频项目和图文项目。该模块采用分步骤流程，将项目创建/编辑过程分为基础信息填写和作品生成预览两个主要步骤。

### 核心功能

- 支持视频和图文两种项目类型
- 两步式项目创建流程
- 表单验证和数据提交
- 文件资源上传和管理
- AI 辅助内容生成
- 作品预览和管理
- 生成结果保存至作品库

## 文件结构

```
src/views/EditProjectView/
├── components/               # 视图级组件
│   ├── BaseStepLayout/       # 基础步骤布局组件
│   ├── GenerateButton/       # 生成按钮组件
│   ├── NavProject/           # 项目导航组件
│   ├── ProjectFooter/        # 项目页脚组件
│   ├── ProjectHeader/        # 项目头部组件
│   └── UserPointDisplay/     # 用户积分显示组件
├── composables/              # 组合式API
│   ├── useFirstStep.ts       # 第一步逻辑
│   ├── useProjectData.ts     # 项目数据管理
│   └── useSecondStep.ts      # 第二步逻辑
├── constants/                # 常量定义
│   └── index.ts              # 项目常量
├── pages/                    # 页面组件
│   ├── FirstStep/            # 第一步页面(基础信息)
│   ├── SecondStepImage/      # 第二步图文编辑页面
│   └── SecondStepVideo/      # 第二步视频编辑页面
├── types/                    # 类型定义
│   ├── index.ts              # 主要类型定义
│   └── router.ts             # 路由相关类型
├── utils/                    # 工具函数
│   └── index.ts              # 通用工具函数
├── index.vue                 # 主视图组件
└── README.md                 # 模块说明文档
```

## 模块划分

### 1. 主视图组件 (index.vue)

核心组件，负责整体布局和业务流程的协调，包含：

- 步骤管理和导航
- 组件间通信
- 用户操作响应
- 全局状态维护

### 2. 组合式 API (Composables)

遵循关注点分离原则，将业务逻辑解耦为独立的功能模块：

#### useProjectData.ts

- 项目数据获取和管理
- 步骤切换逻辑
- 路由参数处理
- 组件状态同步

```typescript
const {
  currentStep, // 当前步骤
  currentComponentType, // 当前组件类型
  loading, // 加载状态
  formData, // 表单数据
  currentComponent, // 当前组件
  stepChange, // 步骤切换
  fetchFormData, // 获取表单数据
  goToNextStep, // 前往下一步
  goToPreviousStep, // 前往上一步
} = useProjectData(
  initialStep,
  initialType,
  updatePointsCallback,
  router,
  route,
);
```

#### useFirstStep.ts

- 基础信息表单处理
- 表单验证和错误处理
- 生成预览逻辑
- 项目保存逻辑

```typescript
const {
  saveLoading, // 保存加载状态
  generateLoading, // 生成加载状态
  generateButtonConfig, // 生成按钮配置
  handleGenerateButtonClick, // 处理生成按钮点击
  saveProject, // 保存项目
} = useFirstStep();
```

#### useSecondStep.ts

- 作品选择管理
- 作品保存逻辑
- 作品列表刷新

```typescript
const {
  selectedWorkIds, // 选中的作品ID
  saveLoading, // 保存加载状态
  handleSelectedWorksChange, // 处理选中作品变更
  handleSaveToWorks, // 处理保存至作品
} = useSecondStep();
```

### 3. 页面组件 (Pages)

#### FirstStep

- 基础信息表单
- 资源上传表单
- AI 辅助内容建议
- 表单验证

#### SecondStepVideo / SecondStepImage

- 作品列表展示
- 作品预览
- 背景音乐设置
- 配音设置
- 花字贴图设置
- 作品选择和保存

### 4. API 模块

#### src/api/EditProjectView/index.ts

- 基础项目数据获取和保存
- 作品列表获取
- 作品详情获取
- 作品保存和删除
- 资源信息获取

```typescript
// 主要API函数
getEditProjectViewData; // 获取项目数据
saveProjectData; // 保存项目数据
generatePreview; // 生成预览
getWorkListWithTransform; // 获取作品列表
getWorkInfoWithTransform; // 获取作品详情
saveWorks; // 保存作品到作品库
getResourceInfo; // 获取资源信息
```

#### src/api/EditProjectView/openAiTip.ts

- AI 推荐内容获取

```typescript
getOpenAiTip; // 获取AI推荐内容
```

### 5. 类型系统 (types/index.ts)

完整的类型定义系统，支持类型安全开发：

- 项目和作品类型定义
- 表单项类型定义
- API 参数和响应类型
- 组件通信和状态类型
- 组合式 API 返回值类型

## 核心业务流程

### 项目创建/编辑流程

1. **第一步：基础信息填写**

   - 输入基础信息（行业、名称等）
   - 上传资源文件（图片、视频等）
   - 可使用 AI 辅助生成内容建议
   - 验证表单并生成预览

2. **第二步：作品预览和管理**
   - 查看生成的作品
   - 视频项目：设置背景音乐、配音、花字特效
   - 图文项目：设置图片布局和文字排版
   - 选择并保存作品至作品库

### 数据流向

```
用户输入 → 表单验证 → API请求 → 服务器处理 → 生成预览 → 预览展示 → 作品保存
```

## 技术实现特点

1. **组合式 API 设计**

   - 利用 Vue 3 组合式 API 特性
   - 业务逻辑模块化和复用
   - 关注点分离

2. **类型安全**

   - 全面的 TypeScript 类型定义
   - 枚举类型代替魔法数字
   - 专用接口类型代替通用 Record

3. **状态管理**

   - 组件内部状态与全局状态结合
   - 响应式数据流
   - 清晰的状态同步机制

4. **UI 组件设计**
   - 模块化 UI 组件
   - BEM 命名规范
   - UnoCSS 实现样式

## 项目常量定义

```typescript
// 步骤索引
FIRST_STEP_INDEX = 0; // 第一步索引
SECOND_STEP_INDEX = 1; // 第二步索引

// 项目类型
PROJECT_TYPE_VIDEO = 0; // 视频项目类型
PROJECT_TYPE_IMAGE = 1; // 图文项目类型

// 项目状态
PROJECT_STATUS_NEW = 'new'; // 新建项目状态
PROJECT_STATUS_EDIT = 'edit'; // 编辑项目状态
```

## 优化建议

1. 统一命名规范，进一步规范化类型定义
