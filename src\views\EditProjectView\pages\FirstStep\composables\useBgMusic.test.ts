import { describe, it, expect, vi, beforeEach } from 'vitest';
import { useBgMusic } from './useBgMusic';
import type { Music } from '@/types';
import type { EditProjectViewData } from '@/views/EditProjectView/types/index';
import { PROJECT_TYPE_VIDEO } from '@/views/EditProjectView/constants';

// Mock API
vi.mock('@/api/VideoEditor/music', () => ({
  getMusicInfo: vi.fn(),
}));

describe('useBgMusic', () => {
  let mockMusic: Music;
  let mockInitialData: EditProjectViewData;

  beforeEach(() => {
    mockMusic = {
      resId: 'test-music-id',
      name: '测试音乐',
      duration: '3:30',
      cover: {
        resId: 'test-cover-id',
        resType: 1, // 修改为 number 类型
      },
      link: 'http://test-music-url.com',
    };

    mockInitialData = {
      templateId: 1,
      type: PROJECT_TYPE_VIDEO,
      inputForm: [],
      resForm: [],
      bgMusic: {
        open: true,
        useAiRecommend: false,
        resIds: [],
      },
    };
  });

  describe('handleChangeMusicInfo', () => {
    it('当用户选择音乐且未开启智能推荐时，应保存音乐信息', async () => {
      const { bgMusicSettings, bgMusicInfo, handleChangeMusicInfo } =
        useBgMusic(mockInitialData);

      await handleChangeMusicInfo([mockMusic], false);

      expect(bgMusicSettings.value.useAiRecommend).toBe(false);
      expect(bgMusicSettings.value.open).toBe(true); // 保持初始状态
      expect(bgMusicInfo.value).toEqual([mockMusic]);
      expect(bgMusicSettings.value.resIds).toEqual(['test-music-id']);
      expect(bgMusicSettings.value.name).toBe('测试音乐');
    });

    it('当用户开启智能推荐时，应清空具体音乐选择但保持背景音乐开启', async () => {
      const { bgMusicSettings, bgMusicInfo, handleChangeMusicInfo } =
        useBgMusic(mockInitialData);

      await handleChangeMusicInfo([], true);

      expect(bgMusicSettings.value.useAiRecommend).toBe(true);
      expect(bgMusicSettings.value.open).toBe(true); // 保持初始状态
      expect(bgMusicInfo.value).toEqual([]);
      expect(bgMusicSettings.value.resIds).toEqual([]);
    });

    it('当用户未开启智能推荐且没有选择音乐时，在多选模式下应关闭背景音乐', async () => {
      const { bgMusicSettings, bgMusicInfo, handleChangeMusicInfo } =
        useBgMusic(mockInitialData);

      await handleChangeMusicInfo([], false, 10); // 多选模式

      expect(bgMusicSettings.value.useAiRecommend).toBe(false);
      expect(bgMusicSettings.value.open).toBe(false);
      expect(bgMusicInfo.value).toEqual([]);
      expect(bgMusicSettings.value.resIds).toEqual([]);
    });

    it('当用户未开启智能推荐且没有选择音乐时，在单选模式下应保持背景音乐开启', async () => {
      const { bgMusicSettings, bgMusicInfo, handleChangeMusicInfo } =
        useBgMusic(mockInitialData);

      await handleChangeMusicInfo([], false, 1); // 单选模式

      expect(bgMusicSettings.value.useAiRecommend).toBe(false);
      expect(bgMusicSettings.value.open).toBe(true); // 保持开启
      expect(bgMusicInfo.value).toEqual([]);
      expect(bgMusicSettings.value.resIds).toEqual([]);
    });

    it('当数据变更时，应触发回调函数', async () => {
      const mockCallback = vi.fn();
      const { handleChangeMusicInfo, setOnDataChange } =
        useBgMusic(mockInitialData);

      setOnDataChange(mockCallback);
      await handleChangeMusicInfo([mockMusic], false);

      expect(mockCallback).toHaveBeenCalledWith(true);
    });

    it('在多选模式下取消时，如果没有选中音乐且未开启智能推荐，应关闭背景音乐但保持原有配置', async () => {
      // 创建有初始音乐配置的数据
      const initialDataWithMusic = {
        ...mockInitialData,
        bgMusic: {
          open: true,
          useAiRecommend: false,
          resIds: ['existing-music-1', 'existing-music-2'],
          name: '已有音乐',
        },
      };

      const { bgMusicSettings, handleChangeMusicInfo } =
        useBgMusic(initialDataWithMusic);

      // 模拟取消操作：传递空的音乐列表，未开启智能推荐，多选模式
      await handleChangeMusicInfo([], false, 10);

      expect(bgMusicSettings.value.useAiRecommend).toBe(false);
      expect(bgMusicSettings.value.open).toBe(false); // 应该关闭背景音乐
      // 重要：原有的音乐配置应该保持不变
      expect(bgMusicSettings.value.resIds).toEqual([
        'existing-music-1',
        'existing-music-2',
      ]);
      expect(bgMusicSettings.value.name).toBe('已有音乐');
    });

    it('在多选模式下取消时，如果开启了智能推荐，应保持背景音乐开启', async () => {
      const { bgMusicSettings, handleChangeMusicInfo } =
        useBgMusic(mockInitialData);

      // 模拟取消操作：传递空的音乐列表，开启智能推荐，多选模式
      await handleChangeMusicInfo([], true, 10);

      expect(bgMusicSettings.value.useAiRecommend).toBe(true);
      expect(bgMusicSettings.value.open).toBe(true); // 应该保持开启
      expect(bgMusicSettings.value.resIds).toEqual([]);
    });

    it('在单选模式下取消时，应清空音乐选择', async () => {
      // 创建有初始音乐配置的数据
      const initialDataWithMusic = {
        ...mockInitialData,
        bgMusic: {
          open: true,
          useAiRecommend: false,
          resIds: ['existing-music-1'],
          name: '已有音乐',
        },
      };

      const { bgMusicSettings, bgMusicInfo, handleChangeMusicInfo } =
        useBgMusic(initialDataWithMusic);

      // 模拟单选模式下的取消操作：传递空的音乐列表，未开启智能推荐，单选模式
      await handleChangeMusicInfo([], false, 1);

      expect(bgMusicSettings.value.useAiRecommend).toBe(false);
      expect(bgMusicSettings.value.open).toBe(true); // 单选模式下保持开启
      // 单选模式下应该清空音乐选择
      expect(bgMusicSettings.value.resIds).toEqual([]);
      expect(bgMusicInfo.value).toEqual([]);
    });

    it('在多选模式下取消时，应恢复到初始状态而不是保存用户在弹窗中的选择', async () => {
      // 创建初始没有音乐的数据
      const initialDataNoMusic = {
        ...mockInitialData,
        bgMusic: {
          open: true,
          useAiRecommend: false,
          resIds: [], // 初始没有音乐
        },
      };

      const { bgMusicSettings, bgMusicInfo, handleChangeMusicInfo } =
        useBgMusic(initialDataNoMusic);

      // 模拟用户在弹窗中选择了音乐但点击取消：传递空的音乐列表（表示恢复初始状态），未开启智能推荐，多选模式
      await handleChangeMusicInfo([], false, 10);

      expect(bgMusicSettings.value.useAiRecommend).toBe(false);
      expect(bgMusicSettings.value.open).toBe(false); // 应该关闭背景音乐
      // 应该恢复到初始状态（没有音乐）
      expect(bgMusicSettings.value.resIds).toEqual([]);
      expect(bgMusicInfo.value).toEqual([]);
    });
  });

  describe('handleBgMusicSwitchChange', () => {
    it('当开启背景音乐时，应显示音乐选择器', () => {
      const { handleBgMusicSwitchChange, isShowBgMusicSelector } =
        useBgMusic(mockInitialData);

      handleBgMusicSwitchChange(true);

      expect(isShowBgMusicSelector.value).toBe(true);
    });

    it('当切换背景音乐开关时，应触发数据变更回调', () => {
      const mockCallback = vi.fn();
      const { handleBgMusicSwitchChange, setOnDataChange } =
        useBgMusic(mockInitialData);

      setOnDataChange(mockCallback);
      handleBgMusicSwitchChange(true);

      expect(mockCallback).toHaveBeenCalledWith(true);
    });
  });

  describe('初始化状态', () => {
    it('应正确初始化背景音乐设置', () => {
      const { bgMusicSettings } = useBgMusic(mockInitialData);

      expect(bgMusicSettings.value.open).toBe(true);
      expect(bgMusicSettings.value.useAiRecommend).toBe(false);
      expect(bgMusicSettings.value.resIds).toEqual([]);
    });

    it('当没有初始数据时，应使用默认设置', () => {
      const { bgMusicSettings } = useBgMusic(null);

      expect(bgMusicSettings.value.open).toBe(false);
      expect(bgMusicSettings.value.useAiRecommend).toBe(false);
      expect(bgMusicSettings.value.resIds).toEqual([]);
    });
  });
});
