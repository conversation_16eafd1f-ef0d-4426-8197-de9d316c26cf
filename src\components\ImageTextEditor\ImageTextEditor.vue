<template>
  <div class="absolute z-1000">
    <el-drawer
      :visible.sync="visible"
      :with-header="false"
      :size="1286"
      @closed="handleClosed"
      :modal-append-to-body="false"
    >
      <div class="h-full flex flex-col">
        <div
          class="h-[65px] flex-shrink-0 b-divider border flex items-center pl-[30px]"
        >
          <Icon
            type="guanbi-tancong"
            class="w-[12px] h-[12px] text-disabledText"
            cursor-pointer
            hover:text-title
            @click="visible = false"
          />
          <span class="ml-[16px] font-bold text-black">编辑图文</span>
        </div>
        <div class="flex flex-1 min-h-0 overflow-auto">
          <!-- 左侧图片栏 -->
          <div class="w-[136px]">
            <ImageSidebar
              :images="slideBarImages"
              :selectedIndex="selectedSettingIdx"
              :enableImagePreview="true"
              direction="vertical"
              :showAddBtn="true"
              :showCover="true"
              :needScroll="true"
              :needDelete="true"
              :showReplaceBtn="false"
              @select="selectedSettingIdx = $event"
              @delete="handlerDelete"
              @add="handlerAddImg"
            />
          </div>
          <div class="w-[458px]">
            <!-- 中间预览窗 -->
            <ImagePreviewWindow />
          </div>
          <!-- 右边编辑窗 -->
          <div class="w-[692px]">
            <fa-tabs
              mode="horizontal"
              :defaultActiveKey="props.defaultTab || 'imageText'"
              class="flex-1 image-text-editor__tabs"
            >
              <fa-tab-pane tab="图文效果" key="imageText">
                <ImageEditor ref="imageEditorRef" />
              </fa-tab-pane>
              <fa-tab-pane tab="文本内容" key="text">
                <TextEditor ref="textEditorRef" />
              </fa-tab-pane>
            </fa-tabs>
          </div>
        </div>
        <div
          class="b-divider b-t-1 flex justify-center items-center h-[64px] flex-shrink-0"
        >
          <fa-button
            class="btn-special mr-[16px]"
            :class="{
              'btn-disabled': shouldDisableSaveButton,
            }"
            @click="handleSave"
            >保存并生成</fa-button
          >
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted, onUnmounted, watch } from 'vue';
import {
  workInfo,
  selectedSettingIndex,
  closeStatus,
  selectedIndexToOriginWMap,
  selectedIndexToNewOriginWMap,
} from './hook/useWorkInfo';
import ImageEditor from './components/ImageEditor.vue';
import TextEditor from './components/TextEditor.vue';
import ImageSidebar from './components/ImageSidebar.vue';
import ImagePreviewWindow from './components/ImagePreviewWindow.vue';
import { saveWork } from '@/api/VideoEditor/save';
import { message } from '@fk/faicomponent';
import { IMAGE_TEXT_EDITOR_CLOSE_STATUS } from './constants'; // 路径按实际调整
import { showMaterialBasicUpload } from '@/components/MaterialBasicUpload';
import type { MaterialUploadFile } from '@/components/MaterialBasicUpload/types';
import { getMaterialFullUrl } from '@/components/MaterialBasicUpload/utils/index.ts';
import { FILE_TYPES } from '@/constants/fileType';
import { eventBus, EVENT_NAMES } from '@/utils/eventBus';
import { STYLE_TYPE } from '@/types';

const shouldDisableSaveButton = ref(false);
const visible = ref(false);
// 保存中状态
const isSaving = ref(false);
const props = defineProps<{
  /** 默认tab */
  defaultTab?: 'imageText' | 'text';
}>();

onMounted(() => {
  visible.value = true;
  eventBus.on(EVENT_NAMES.DISABLED_SAVE_BUTTON, handleDisableSaveButton);
});

// 组件卸载时清理
onUnmounted(() => {
  eventBus.off(EVENT_NAMES.DISABLED_SAVE_BUTTON, handleDisableSaveButton);
});

// 侧边栏图片列表
const slideBarImages = computed<string[]>(() => {
  return workInfo.value?.data?.graphic?.map(item => String(item.resId)) || [];
});

const selectedSettingIdx = computed({
  get: () => selectedSettingIndex.value,
  set: val => {
    eventBus.emit(EVENT_NAMES.RESET_ACTIVE_IMAGE_INDEX);
    selectedSettingIndex.value = val;
  },
});

const hasEmptyResId = computed(() => {
  if (!workInfo.value) {
    return false;
  }
  let rt = false;
  workInfo.value.setting.graphicList.forEach(item => {
    item.puzzleStyle.forEach(pStyle => {
      if (pStyle.resId === '') {
        rt = true;
      }
    });
    item.style.forEach(style => {
      if (style.type == STYLE_TYPE.PIC && style.resId === '') {
        rt = true;
      }
    });
  });
  return rt;
});

const emit = defineEmits(['close']);
const handleClosed = () => {
  emit('close');
};
const handleSave = async () => {
  if (isSaving.value) return;
  isSaving.value = true;
  if (!workInfo.value) {
    isSaving.value = false;
    return message.error('作品信息为空');
  }
  if (shouldDisableSaveButton.value) {
    isSaving.value = false;
    return;
  }

  // 增加逻辑：根据图片尺寸变化调整样式坐标和尺寸
  if (workInfo.value?.setting?.graphicList) {
    // 遍历selectedIndexToOriginWMap
    selectedIndexToOriginWMap.forEach(
      (originWidth: number, selectedIndex: number) => {
        const graphicItem = workInfo.value!.setting.graphicList[selectedIndex];
        if (graphicItem && graphicItem.style) {
          const newOriginWidth =
            selectedIndexToNewOriginWMap.get(selectedIndex);
          if (newOriginWidth && originWidth > 0) {
            const scaleRatio = newOriginWidth / originWidth;

            // 遍历style里的每一项
            graphicItem.style.forEach(styleItem => {
              if (styleItem.type === STYLE_TYPE.FONT) {
                // 如果是文字类型，对x、y、fontSize都乘以scaleRatio
                styleItem.x = Math.round(styleItem.x * scaleRatio);
                styleItem.y = Math.round(styleItem.y * scaleRatio);
                styleItem.fontSize = Math.round(
                  styleItem.fontSize * scaleRatio,
                );
              } else if (styleItem.type === STYLE_TYPE.PIC) {
                // 如果是图片类型，对x、y、width、height都乘以scaleRatio
                styleItem.x = Math.round(styleItem.x * scaleRatio);
                styleItem.y = Math.round(styleItem.y * scaleRatio);
                styleItem.width = Math.round(styleItem.width * scaleRatio);
                styleItem.height = Math.round(styleItem.height * scaleRatio);
              }
            });
          }
        }
      },
    );
    selectedIndexToOriginWMap.clear();
    selectedIndexToNewOriginWMap.clear();
  }

  // 处理workInfo
  const [err] = await saveWork(workInfo.value, true);
  if (err) {
    isSaving.value = false;
    visible.value = false;
    message.error(err.message || '保存失败');
    return;
  }

  message.success('保存成功');
  closeStatus.value = IMAGE_TEXT_EDITOR_CLOSE_STATUS.GENERATED;
  isSaving.value = false;
  visible.value = false;
};

const handleDisableSaveButton = (val: unknown) => {
  shouldDisableSaveButton.value = !!val;
};

const handlerDelete = (index: number) => {
  if (selectedSettingIdx.value == index) {
    selectedSettingIdx.value = 0;
  }
  workInfo.value?.data?.graphic?.splice(index, 1);
  workInfo.value?.setting.graphicList.splice(index, 1);
};
const handlerAddImg = () => {
  if ((workInfo.value?.setting.graphicList?.length ?? 0) >= 18) {
    return message.error('封面图+次图最多支持18张图片');
  }
  showMaterialBasicUpload({
    title: '添加次图',
    maxChosenFileCount: 1,
    isVideo: false,
    onConfirm: (files: MaterialUploadFile[]) => {
      if (files.length === 0) {
        return;
      }
      const file = files[0];
      const img = new Image();
      img.src = getMaterialFullUrl(file.data.resId, FILE_TYPES.WEBP, 'user');
      img.onload = () => {
        // 确保图片加载完成后再添加
        workInfo.value?.data?.graphic?.push({
          resId: file.data.resId,
          type: file.data.type,
        });
        if (workInfo.value) {
          workInfo.value.setting.curMaxId++;
          workInfo.value?.setting.graphicList.push({
            id: workInfo.value?.setting.curMaxId,
            puzzleType: 1,
            ratio: 1,
            puzzleStyle: [
              {
                resId: file.data.resId,
                scale: 1,
                type: file.data.type,
                x: 0,
                y: 0,
              },
            ],
            style: [],
          });
        }
      };
    },
  });
};

// 监听 hasEmptyResId 的变化，自动调用 handleDisableSaveButton
watch(
  hasEmptyResId,
  (newValue: boolean) => {
    handleDisableSaveButton(newValue);
  },
  { immediate: true },
);
</script>

<style lang="scss" scoped>
::v-deep {
  .image-text-editor__tabs {
    @apply h-full;
    > .fa-tabs-top-content {
      height: calc(100% - 57px);

      > .fa-tabs-tabpane {
        // @apply h-793px overflow-x-hidden overflow-y-scroll;
        @apply h-full min-h-0 overflow-x-hidden overflow-y-auto;
        &::-webkit-scrollbar {
          @apply w-[0];
        }
      }
    }

    > .fa-tabs-bar {
      @apply mb-0;

      .fa-tabs-nav {
        @apply ml-[20px];

        .fa-tabs-tab {
          @apply pt-[20px] pb-[17px] px-[4px] mr-[36px];
        }

        .fa-tabs-tab-active {
          @apply font-bold;
        }
      }
    }
  }

  .btn-special {
    @apply w-[160px] h-[40px] text-white;
  }

  .btn-disabled {
    /* 外观相关 - 置灰样式 */
    background: linear-gradient(92.19deg, #105fff 0%, #7923f9 100%);
    opacity: 0.4;

    /* 交互相关 */
    @apply cursor-not-allowed;

    &:hover,
    &:focus,
    &:active {
      /* 文字相关 */
      @apply text-white;
    }
  }
  .el-drawer__wrapper {
    overflow-x: auto;
    .el-drawer__container {
      min-width: 1286px;
    }
  }
}
</style>
