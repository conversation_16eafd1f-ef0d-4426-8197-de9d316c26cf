import { VideoWorkItem } from '@/types';
import { calculateRemainingHours } from '@/constants/workExpire';
import {
  getWorkStatusInfo,
  isAnyCompletedWork,
  isAnyGeneratingWork,
} from '@/constants/workStatus';

/**
 * 检查视频是否可保存
 * @param video 视频项
 * @returns 是否可保存
 */
export function isVideoSavable(video: VideoWorkItem): boolean {
  // 只有完成状态的视频才能保存
  const statusInfo = getWorkStatusInfo(video);
  return isAnyCompletedWork(statusInfo);
}

/**
 * 检查视频是否可删除
 * @param video 视频项
 * @returns 是否可删除
 */
export function isVideoDeletable(video: VideoWorkItem): boolean {
  const statusInfo = getWorkStatusInfo(video);
  return !isAnyGeneratingWork(statusInfo);
}

/**
 * 获取视频剩余有效时间（小时）- 基于创建时间+固定天数
 * @param video 视频项
 * @returns 剩余有效时间（小时）
 */
export function getVideoRemainingHours(video: VideoWorkItem): number {
  if (!video.createTime) return -1;
  return calculateRemainingHours(video.createTime);
}

/**
 * 格式化视频时长
 * @param seconds 时长（秒）
 * @returns 格式化后的时长字符串 (MM:SS)
 */
export function formatDuration(seconds?: number): string {
  if (!seconds) return '00:00';

  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = Math.floor(seconds % 60);

  return `${minutes.toString().padStart(2, '0')}:${remainingSeconds
    .toString()
    .padStart(2, '0')}`;
}
