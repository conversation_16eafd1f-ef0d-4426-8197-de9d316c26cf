/**
 * @fileoverview VideoPlayer 内存监控工具
 * @description 用于监控和调试 VideoPlayer 组件的内存使用情况
 */

import { logger } from '@/utils/logger';

interface MemoryInfo {
  usedJSHeapSize: number;
  totalJSHeapSize: number;
  jsHeapSizeLimit: number;
}

// 扩展 Performance 接口以支持 memory 属性（Chrome 特有）
interface PerformanceWithMemory extends Performance {
  memory?: MemoryInfo;
}

interface VideoPlayerMemoryStats {
  timestamp: number;
  workId?: number;
  memoryInfo: MemoryInfo | null;
  playerInstanceCount: number;
  eventListenerCount: number;
}

/**
 * VideoPlayer 内存监控器
 */
export class VideoPlayerMemoryMonitor {
  private static instance: VideoPlayerMemoryMonitor;
  private stats: VideoPlayerMemoryStats[] = [];
  private playerInstances = new Set<string>();
  private eventListeners = new Map<string, number>();
  private memoryCheckInterval: ReturnType<typeof setInterval> | null = null;
  private isMonitoringStarted = false;

  private constructor() {}

  static getInstance(): VideoPlayerMemoryMonitor {
    if (!VideoPlayerMemoryMonitor.instance) {
      VideoPlayerMemoryMonitor.instance = new VideoPlayerMemoryMonitor();
    }
    return VideoPlayerMemoryMonitor.instance;
  }

  /**
   * 启动内存监控（懒加载模式）
   */
  private startMonitoring(): void {
    if (this.isMonitoringStarted || !import.meta.env.DEV) {
      return;
    }

    this.isMonitoringStarted = true;

    // 每60秒检查一次内存泄漏（降低检查频率，减少性能影响）
    this.memoryCheckInterval = setInterval(() => {
      this.checkMemoryLeaks();
    }, 60000);

    logger.memory('VideoPlayer: 内存监控定时器已启动（60秒间隔）');
  }

  /**
   * 停止内存监控
   */
  private stopMonitoring(): void {
    if (this.memoryCheckInterval) {
      clearInterval(this.memoryCheckInterval);
      this.memoryCheckInterval = null;
      this.isMonitoringStarted = false;
      logger.memory('VideoPlayer: 内存监控定时器已停止');
    }
  }

  /**
   * 记录播放器实例创建
   */
  recordPlayerCreated(workId: number): void {
    // 首次使用时启动监控
    this.startMonitoring();

    const instanceId = `player_${workId}_${Date.now()}`;
    this.playerInstances.add(instanceId);

    logger.memory(`播放器实例创建 (作品ID: ${workId})`, {
      当前实例数: this.playerInstances.size,
      实例ID: instanceId,
    });

    this.captureMemorySnapshot(workId);
  }

  /**
   * 记录播放器实例销毁
   */
  recordPlayerDestroyed(workId: number): void {
    // 移除相关实例（可能有多个）
    const instancesToRemove = Array.from(this.playerInstances).filter(id =>
      id.startsWith(`player_${workId}_`),
    );

    instancesToRemove.forEach(id => this.playerInstances.delete(id));

    logger.memory(`播放器实例销毁 , 作品ID: ${workId}`, {
      当前实例数: this.playerInstances.size,
      销毁实例数: instancesToRemove.length,
    });

    this.captureMemorySnapshot(workId);
  }

  /**
   * 记录事件监听器添加
   */
  recordEventListenerAdded(eventType: string): void {
    const current = this.eventListeners.get(eventType) || 0;
    this.eventListeners.set(eventType, current + 1);
  }

  /**
   * 记录事件监听器移除
   */
  recordEventListenerRemoved(eventType: string): void {
    const current = this.eventListeners.get(eventType) || 0;
    if (current > 0) {
      this.eventListeners.set(eventType, current - 1);
    }
  }

  /**
   * 捕获内存快照
   */
  private captureMemorySnapshot(workId?: number): void {
    const memoryInfo = this.getMemoryInfo();
    const totalEventListeners = Array.from(this.eventListeners.values()).reduce(
      (sum, count) => sum + count,
      0,
    );

    const snapshot: VideoPlayerMemoryStats = {
      timestamp: Date.now(),
      workId,
      memoryInfo,
      playerInstanceCount: this.playerInstances.size,
      eventListenerCount: totalEventListeners,
    };

    this.stats.push(snapshot);

    // 只保留最近50个快照
    if (this.stats.length > 50) {
      this.stats = this.stats.slice(-50);
    }
  }

  /**
   * 获取内存信息
   */
  private getMemoryInfo(): MemoryInfo | null {
    if ('memory' in performance) {
      const memory = (performance as PerformanceWithMemory).memory;
      if (memory) {
        return {
          usedJSHeapSize: memory.usedJSHeapSize,
          totalJSHeapSize: memory.totalJSHeapSize,
          jsHeapSizeLimit: memory.jsHeapSizeLimit,
        };
      }
    }
    return null;
  }

  /**
   * 检查内存泄漏
   */
  checkMemoryLeaks(): void {
    const currentStats = this.stats[this.stats.length - 1];
    if (!currentStats) return;

    const warnings: string[] = [];

    // 检查播放器实例数量（提高阈值，避免快速切换时的误报）
    if (currentStats.playerInstanceCount > 5) {
      warnings.push(`播放器实例数量过多: ${currentStats.playerInstanceCount}`);
    }

    // 检查事件监听器数量
    if (currentStats.eventListenerCount > 100) {
      warnings.push(`事件监听器数量过多: ${currentStats.eventListenerCount}`);
    }

    // 检查内存增长趋势（改进算法，考虑垃圾回收延迟）
    if (this.stats.length >= 15) {
      const recentStats = this.stats.slice(-15);
      const memoryGrowth = this.calculateMemoryGrowth(recentStats);
      const sustainedGrowth = this.calculateSustainedMemoryGrowth(recentStats);

      // 提高内存增长阈值到30MB，并检查持续增长
      if (memoryGrowth > 30 * 1024 * 1024 && sustainedGrowth) {
        warnings.push(
          `内存持续增长: ${(memoryGrowth / 1024 / 1024).toFixed(2)}MB`,
        );
      }
    }

    // 只有在有严重问题时才报警
    if (warnings.length > 0) {
      logger.memoryWarn('VideoPlayer 内存泄漏警告', warnings);
      this.printDetailedStats();
    }
  }

  /**
   * 计算内存增长
   */
  private calculateMemoryGrowth(stats: VideoPlayerMemoryStats[]): number {
    if (stats.length < 2) return 0;

    const first = stats[0].memoryInfo;
    const last = stats[stats.length - 1].memoryInfo;

    if (!first || !last) return 0;

    return last.usedJSHeapSize - first.usedJSHeapSize;
  }

  /**
   * 计算持续内存增长（检查是否真的是持续增长而不是临时波动）
   */
  private calculateSustainedMemoryGrowth(
    stats: VideoPlayerMemoryStats[],
  ): boolean {
    if (stats.length < 5) return false;

    // 检查最近5个数据点是否呈现持续增长趋势
    let increasingCount = 0;
    for (let i = 1; i < stats.length; i++) {
      const prev = stats[i - 1].memoryInfo;
      const curr = stats[i].memoryInfo;

      if (prev && curr && curr.usedJSHeapSize > prev.usedJSHeapSize) {
        increasingCount++;
      }
    }

    // 如果超过70%的数据点都在增长，认为是持续增长
    return increasingCount / (stats.length - 1) > 0.7;
  }

  /**
   * 打印详细统计信息
   */
  printDetailedStats(): void {
    const currentStats = this.stats[this.stats.length - 1];
    if (!currentStats) return;

    logger.memoryGroup('VideoPlayer 内存统计详情');

    const statsData: Record<string, unknown> = {
      播放器实例数: currentStats.playerInstanceCount,
      事件监听器数: currentStats.eventListenerCount,
    };

    if (currentStats.memoryInfo) {
      const { usedJSHeapSize, totalJSHeapSize } = currentStats.memoryInfo;
      statsData.已用内存 = `${(usedJSHeapSize / 1024 / 1024).toFixed(2)}MB`;
      statsData.总内存 = `${(totalJSHeapSize / 1024 / 1024).toFixed(2)}MB`;
    }

    statsData.事件监听器详情 = Object.fromEntries(this.eventListeners);

    logger.table(statsData, '内存统计数据');
    logger.memoryGroupEnd();
  }

  /**
   * 重置监控数据
   */
  reset(): void {
    this.stats = [];
    this.playerInstances.clear();
    this.eventListeners.clear();
    logger.memory('内存监控数据已重置');
  }

  /**
   * 完全停止监控（包括定时器）
   */
  stopMemoryMonitoring(): void {
    this.stopMonitoring();
    this.reset();
  }

  /**
   * 强制垃圾回收（仅在开发环境下可用）
   */
  forceGarbageCollection(): void {
    if (import.meta.env.DEV && 'gc' in window) {
      try {
        (window as Window & { gc?: () => void }).gc?.();
        logger.memory('VideoPlayer: 强制垃圾回收已执行');
      } catch (error) {
        logger.memoryError('VideoPlayer: 强制垃圾回收失败', error);
      }
    }
  }
}

// 导出单例实例
export const memoryMonitor = VideoPlayerMemoryMonitor.getInstance();

// 提供清理定时器的方法（用于测试或特殊情况）
export const stopMemoryMonitoring = () => {
  memoryMonitor.stopMemoryMonitoring();
};
