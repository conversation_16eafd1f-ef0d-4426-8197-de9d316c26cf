import store from '@/store';
import { FILE_TYPES, FILE_EXTENSIONS } from '@/constants/fileType';
import { MIMETYPE_ALIAS, VIDEO_PREVIEW_KEY_LIST } from '@/constants/material';
import { ExtraData, FileData } from '@/types/Material';
import { message as FaMessage } from '@fk/faicomponent';
import { INFO_KEYS } from '@/constants/material';
import { MaterialManageInfo } from '@/components/MaterialManager/types/index.ts';

/**
 * 获取文件后缀
 * @param {string} fileUrl 文件url
 * @returns {string} 文件后缀
 */
export function getFileSuffix(fileUrl: string) {
  return fileUrl.split('.').pop()?.toLowerCase();
}

/**
 * 转为mime类型
 * @param {Array} types 类型
 * @returns {Array} 结果
 */
export function transMimeTypes(types: string[]) {
  const rTypes = [];
  for (const i in types) {
    const alia = types[i];
    rTypes.push(MIMETYPE_ALIAS[alia]);
  }
  return rTypes;
}

/** 中台缩略图可选大小 */
const IMG_SIZE_OPTIONS = [
  60, 100, 160, 200, 300, 400, 450, 500, 600, 700, 800, 900, 1000, 1500, 2000,
];

/** 高清倍率 */
const HD_SCALE = 1.5;

/**
 * 获取素材的完整url
 * @param {string} resId 素材id
 * @param {string} fileType 素材类型
 * @param {string} belong 素材归属，默认是用户所属的素材
 * @param {number} width 图片最大宽度，仅控制加载图片的分辨率，不控制图片的显示宽度，为空时返回原图
 * @returns  {string} 素材的完整url
 */
export function getMaterialFullUrl(
  resId: string,
  fileType: FILE_TYPES,
  belong: 'user' | 'system' | 'oss' = 'user',
  width?: number,
) {
  let domain = '';
  switch (belong) {
    case 'user':
      domain = store.state.system.scUsrResRoot;
      break;
    case 'system':
      domain = store.state.system.scPortalResRoot;
      break;
    case 'oss':
      domain = store.state.system.scUsrResOssRoot;
      break;
    default:
      domain = store.state.system.scUsrResRoot;
  }
  let optionWidth: undefined | number = undefined;
  if (width) {
    optionWidth = IMG_SIZE_OPTIONS.find(size => size >= width * HD_SCALE);
  }
  return `${domain}/${fileType}/${resId}${
    optionWidth ? `!${optionWidth}x${optionWidth}` : ''
  }.${FILE_EXTENSIONS[fileType as keyof typeof FILE_EXTENSIONS]}`;
}

/**
 * 检查文件是否为可预览视频类型
 * @param data 文件数据
 * @returns 是否为视频
 */
export const checkVideoType = ({
  fileType,
  url,
}: {
  fileType: FILE_TYPES;
  url?: string;
}): boolean => {
  return (
    VIDEO_PREVIEW_KEY_LIST.includes(fileType) && url !== '' // TODO
  );
};

/**
 * 是否为可预览视频封面
 * @param data 文件数据
 * @returns 是否为视频封面
 */
export const checkVideoCover = ({
  fileType,
  url,
  extra,
}: {
  fileType: FILE_TYPES;
  url?: string;
  extra?: ExtraData;
}): boolean => {
  return checkVideoType({ fileType, url }) && !!extra?.cover;
};

/**
 * 限制输入最大长度，超出自动截断
 * @param value 当前输入内容
 * @param maxLength 最大长度
 * @returns 是否超出最大长度
 */
export function limitInput(value: string, maxLength: number): boolean {
  if (value.length > maxLength) {
    FaMessage.error(`名称不能超过${maxLength}个字`);
    return true;
  }
  return false;
}

/**
 * 文件视图类型
 * list: 列表视图
 * table: 表格视图
 */
export type FileViewTypeValues = 'list' | 'table';

/**
 * 检查文件是否正在上传
 * @param data 文件数据
 * @returns 是否正在上传
 */
export const checkFileUploading = (data: FileData | MaterialManageInfo) => {
  const percent = data[INFO_KEYS.FILE_PERCENT];
  return percent != undefined && percent !== 100;
};
