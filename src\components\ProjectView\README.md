# ProjectView 我的项目页面

项目视图（ProjectView）模块，负责项目的卡片展示、详情弹窗、名称编辑、搜索、空状态等核心业务流程。

## 目录结构

```
ProjectView/
├── ProjectCard.vue           # 项目卡片主组件
├── ProjectDetailModal.vue    # 项目详情弹窗
├── EditNamePopconfirm.vue    # 项目名称编辑弹窗
├── SearchInput.vue           # 项目搜索输入框
├── RefreshBtn.vue            # 刷新按钮
├── ProjectEmptyState.vue     # 空状态展示
├── CreateCard.vue            # 新建项目卡片
├── utils.ts                  # 工具函数
└── README.md                 # 目录说明文档
```

## 核心功能

- 项目卡片展示与操作（预览、编辑、删除、复制等）
- 项目详情弹窗（支持多标签页、详情信息、操作按钮）
- 项目名称编辑（支持弹窗确认、校验、回车保存）
- 项目搜索与筛选（支持模糊搜索、回车触发）
- 空状态与新建项目卡片展示
- 刷新按钮与状态管理

## 技术实现要点

- 采用 Vue3 组合式 API，状态与逻辑分离
- 组件高度解耦，便于复用与扩展
- 统一弹窗与输入交互体验
- 支持类型安全与详细注释
- 工具函数集中管理（utils.ts）

## 用法示例

```vue
<template>
  <ProjectCard :project="project" @edit="handleEdit" />
</template>

<script setup lang="ts">
import ProjectCard from '@/components/ProjectView/ProjectCard.vue';
const project = {
  /* ... */
};
function handleEdit(updated) {
  // 处理编辑回调
}
</script>
```

## 组件说明

| 组件名                 | 说明             |
| ---------------------- | ---------------- |
| ProjectCard.vue        | 项目卡片主视图   |
| ProjectDetailModal.vue | 项目详情弹窗     |
| EditNamePopconfirm.vue | 项目名称编辑弹窗 |
| SearchInput.vue        | 项目搜索输入框   |
| RefreshBtn.vue         | 刷新按钮         |
| ProjectEmptyState.vue  | 空状态展示       |
| CreateCard.vue         | 新建项目卡片     |
| utils.ts               | 项目相关工具函数 |

## 扩展与维护

- 支持自定义项目卡片样式与操作扩展
- 支持多种业务弹窗复用
- 便于接入新业务（如批量操作、标签管理等）
- 详细注释与类型定义，便于团队协作

## 更新日志

- 2025-07-01 v1.0.0 初始版本
- 2025-08-01 v1.1.0 修复已知问题

---

如需扩展或维护，请参考各组件/逻辑文件内注释与类型定义。
