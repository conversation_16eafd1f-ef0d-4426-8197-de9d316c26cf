/**
 * @fileoverview 项目编辑视图中与媒体资源相关的类型定义。
 * @description 此文件定义了用于处理脚本、背景音乐、配音、花字、以及通用资源信息和编辑器实例的接口和类型。
 * <AUTHOR>
 * @since 1.0.0
 */

import { ScriptSegment } from '@/types';

// ============== 脚本相关类型 ==============

/**
 * @description 示例脚本数据结构，用于存储图文或视频项目的脚本信息。
 * @interface ScriptData
 */
export interface ScriptData {
  /**
   * 示例脚本标题，主要用于图文类型的项目。
   * @type {string}
   * @memberof ScriptData
   * @optional
   */
  title?: string;
  /**
   * 示例脚本的主要内容文本。
   * @type {string}
   * @memberof ScriptData
   */
  content: string;
  /**
   * 视频脚本的片段数组，每个片段遵循 ScriptSegment 接口定义。
   * @type {ScriptSegment[]}
   * @memberof ScriptData
   * @optional
   */
  segments?: ScriptSegment[];
}

// ============== 音频视频设置 ==============

/**
 * @description 背景音乐设置选项。
 * @interface BgMusicSetting
 */
export interface BgMusicSetting {
  /**
   * 是否开启背景音乐。
   * @type {boolean}
   * @memberof BgMusicSetting
   */
  open: boolean;
  /**
   * 是否使用AI智能推荐背景音乐。
   * @type {boolean}
   * @memberof BgMusicSetting
   * @optional
   */
  useAiRecommend?: boolean;
  /**
   * 选定的背景音乐资源ID列表。
   * @type {string[]}
   * @memberof BgMusicSetting
   * @optional
   */
  resIds?: string[];
  /**
   * 背景音乐的名称。
   * @type {string}
   * @memberof BgMusicSetting
   * @optional
   */
  name?: string;
  /**
   * 背景音乐的音量大小，取值范围 0-100。
   * @type {number}
   * @memberof BgMusicSetting
   * @optional
   */
  vol?: number;
}

/**
 * @description 配音设置选项。
 * @interface VoiceSetting
 */
export interface VoiceSetting {
  /**
   * 是否使用AI智能推荐配音。
   * @type {boolean}
   * @memberof VoiceSetting
   * @optional
   */
  useAiRecommend?: boolean;
  /**
   * 选定的配音资源ID。
   * @type {string}
   * @memberof VoiceSetting
   * @optional
   */
  voiceId?: string;
  /**
   * 配音的名称。
   * @type {string}
   * @memberof VoiceSetting
   * @optional
   */
  voiceName?: string;
  /**
   * 配音的语速，常用范围 0.5 (慢) - 2.0 (快)。
   * @type {number}
   * @memberof VoiceSetting
   * @optional
   */
  speed?: number;
  /**
   * 额外类型。
   * @type {string}
   * @memberof VoiceSetting
   * @optional
   */
  extraType?: string;
}

/**
 * @description 花字（屏幕文字叠加）的样式设置。
 * @interface FlowerTextStyle
 */
export interface FlowerTextStyle {
  /**
   * 字号大小。
   * @type {number}
   * @memberof FlowerTextStyle
   * @optional
   */
  fontSize?: number;
  /**
   * 文字颜色，例如 '#FFFFFF'。
   * @type {string}
   * @memberof FlowerTextStyle
   * @optional
   */
  color?: string;
  /**
   * 文字背景颜色，例如 '#000000'。
   * @type {string}
   * @memberof FlowerTextStyle
   * @optional
   */
  backgroundColor?: string;
  /**
   * 其他自定义样式属性，允许动态扩展。
   * @type {string | number | undefined}
   * @memberof FlowerTextStyle
   */
  [key: string]: string | number | undefined;
}

/**
 * @description 单个花字对象的定义。
 * @interface FlowerText
 */
export interface FlowerText {
  /**
   * 花字的文本内容。
   * @type {string}
   * @memberof FlowerText
   */
  text: string;
  /**
   * 花字在视频中显示的起始时间点（单位：秒）。
   * @type {number}
   * @memberof FlowerText
   */
  time: number;
  /**
   * 花字的样式设置对象。
   * @type {FlowerTextStyle}
   * @memberof FlowerText
   * @optional
   */
  style?: FlowerTextStyle;
}

// ============== 资源信息类型 ==============

/**
 * @description 通用媒体资源类型枚举定义。
 * - `image`: 图片资源。
 * - `video`: 视频资源。
 * - `audio`: 音频资源。
 */
export type ResourceType = 'image' | 'video' | 'audio';

/**
 * @description 通用资源信息结构，用于描述从素材库等获取的媒体文件详情。
 * @interface ResourceInfo
 */
export interface ResourceInfo {
  /**
   * 资源的唯一标识符。
   * @type {string}
   * @memberof ResourceInfo
   */
  id: string;
  /**
   * 资源的名称。
   * @type {string}
   * @memberof ResourceInfo
   */
  name: string;
  /**
   * 资源的类型，如图库、视频库或音频库中的资源。
   * @type {ResourceType}
   * @memberof ResourceInfo
   */
  type: ResourceType;
  /**
   * 资源的访问URL。
   * @type {string}
   * @memberof ResourceInfo
   */
  url: string;
  /**
   * 资源的创建时间戳或日期字符串。
   * @type {string}
   * @memberof ResourceInfo
   */
  createTime: string;
  /**
   * 图片的宽度（像素），仅图片类型有效。
   * @type {number}
   * @memberof ResourceInfo
   * @optional
   */
  width?: number;
  /**
   * 图片的高度（像素），仅图片类型有效。
   * @type {number}
   * @memberof ResourceInfo
   * @optional
   */
  height?: number;
  /**
   * 音频或视频的时长（单位：秒），仅音频/视频类型有效。
   * @type {number}
   * @memberof ResourceInfo
   * @optional
   */
  duration?: number;
  /**
   * 音频或视频的封面图URL，仅音频/视频类型有效。
   * @type {string}
   * @memberof ResourceInfo
   * @optional
   */
  coverImg?: string;
  /**
   * 文件大小（单位：字节）。
   * @type {number}
   * @memberof ResourceInfo
   * @optional
   */
  size?: number;
  /**
   * 文件的格式，例如 'mp4', 'jpg'。
   * @type {string}
   * @memberof ResourceInfo
   * @optional
   */
  format?: string;
  /**
   * 视频的分辨率描述，例如 '1920x1080'。
   * @type {string}
   * @memberof ResourceInfo
   * @optional
   */
  resolution?: string;
}

// ============== 编辑器结构接口 ==============

/**
 * @description 富文本编辑器或特定内容编辑器的标准实例接口。
 * @interface EditorInstance
 */
export interface EditorInstance {
  /**
   * 获取编辑器当前的内容。
   * @returns {string} 编辑器内容字符串，可能为HTML、Markdown或其他格式。
   * @memberof EditorInstance
   */
  getContent: () => string;
  /**
   * 设置编辑器的内容。
   * @param {string} content - 要设置到编辑器的内容字符串。
   * @returns {void}
   * @memberof EditorInstance
   */
  setContent: (content: string) => void;
  /**
   * 清空编辑器的所有内容。
   * @returns {void}
   * @memberof EditorInstance
   */
  clearContent: () => void;
  /**
   * 销毁编辑器实例，释放资源。
   * @returns {void}
   * @memberof EditorInstance
   */
  destroy: () => void;
}
