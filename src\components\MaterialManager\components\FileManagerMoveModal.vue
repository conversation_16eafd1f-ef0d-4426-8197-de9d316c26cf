<template>
  <FaFileManagerMoveModal
    v-model="visible"
    :tree-data="dirTreeData"
    :expanded-keys.sync="expandedKeys"
    :selected-keys.sync="selectedKeys"
    wrap-class-name="file-manager-demo--move-modal"
    :disabled-ok="isMoveDisabled"
    @ok="handleOk"
    @cancel="handleCancel"
  >
    <div slot="header" class="file-manager-demo--move-modal-header flex">
      <fa-button class="flex items-center" @click="handleClick">
        <FaDesignerIcon type="folder_add" />
        新增文件夹
      </fa-button>
    </div>

    <template #icon>
      <img
        class="file-manager-demo--sidebar-tree-icon"
        src="@/assets/Material/icon_folder.svg"
      />
    </template>

    <template #default="{ info }">
      <div class="file-manager-demo--move-modal-input">
        <FaFileManagerInput
          :disabled="checkFileUploading(info)"
          :ref="`input${info[INFO_KEYS.ID]}`"
          :value="info[INFO_KEYS.NAME]"
          :maxlength="isFolder(info) ? folderNameMaxLen : inputNameMaxLen"
          @blur="inputBlur(info, $event.value)"
        />
      </div>
    </template>

    <!-- <template #icon>这里有插槽</template> -->
  </FaFileManagerMoveModal>
</template>

<script>
import {
  FileManager as FaFileManager,
  DesignerIcon as FaDesignerIcon,
} from '@fk/fa-component-cus';
import { Button as FaButton } from '@fk/faicomponent';
import store from '@/store';
import {
  INFO_KEYS,
  INPUT_NAME_MAX_LENGTH,
  FOLDER_NAME_MAX_LENGTH,
} from '@/constants/material';
import {
  folderAdd,
  changeFmName,
} from '@/components/MaterialManager/utils/index.ts';
import { checkFileUploading } from '@/components/MaterialBasicUpload/utils/index.ts';
import { isFolder } from '@/utils/resource';

export default {
  name: 'FileManagerMoveModal',
  components: {
    FaButton,
    FaDesignerIcon,
    FaFileManagerInput: FaFileManager.FileManagerInput,
    FaFileManagerMoveModal: FaFileManager.FileManagerMoveModal,
  },

  data() {
    return {
      INFO_KEYS,
      selectedKeys: [],
      expandedKeys: [],
      inputNameMaxLen: INPUT_NAME_MAX_LENGTH,
      folderNameMaxLen: FOLDER_NAME_MAX_LENGTH,
    };
  },

  computed: {
    visible: {
      get() {
        return store.state.meta.showMoveModal;
      },
      set(newVal) {
        store.commit('setMoveModal', newVal);
      },
    },

    dirTreeData() {
      return store.getters.dirTreeData;
    },

    folderAddInfo() {
      return store.state.meta.folderAddInfo;
    },

    id() {
      return store.state.meta.id;
    },
    currDirPath() {
      return store.getters.currDirPath;
    },
    selectedDirKeys() {
      return store.getters.selectedDirKeys;
    },

    isMoveDisabled() {
      return this.targetDirId === this.id;
    },
    targetDirId() {
      return this.selectedKeys[0];
    },
  },

  watch: {
    id() {
      this.selectedKeys = [this.id];
    },
    currDirPath: {
      handler(newVal = []) {
        this.expandedKeys = [
          ...new Set([
            ...this.expandedKeys,
            ...newVal.map(item => item[INFO_KEYS.ID]),
          ]),
        ];
      },
      immediate: true,
    },
    folderAddInfo(info) {
      this.$nextTick(() => {
        const { id, form } = info;
        if (form !== 'moveModal') return;
        const [selectId] = this.selectedKeys;
        !this.expandedKeys.includes(selectId) &&
          this.expandedKeys.push(selectId);
        setTimeout(() => {
          this.$refs[`input${id}`] && this.$refs[`input${id}`].focus();
        }, 100);
      });
    },
  },

  created() {
    this.expandedKeys = this.dirTreeData.map(item => item[INFO_KEYS.ID]);
    this.selectedKeys = [this.id];
  },

  methods: {
    checkFileUploading,
    isFolder,
    async handleOk() {
      if (this.isMoveDisabled) return;
      await store.dispatch('moveIn', this.targetDirId);
    },
    handleCancel() {
      store.commit('setMoveModal', false);
    },

    handleClick() {
      folderAdd(this.targetDirId, 'moveModal');
    },

    async inputBlur(info, newName) {
      await changeFmName(info, newName);
    },
  },
};
</script>

<style lang="scss" scoped>
.file-manager-demo--move-modal {
  &-header {
    padding: 16px 0;

    .fa-file-manager-tool-bar-btn {
      line-height: 18px;
    }
  }

  &-input {
    display: inline-block;
    height: 32px;
    margin-left: -5px;

    ::v-deep {
      .fa-file-manager-input {
        display: flex;

        > div,
        > input {
          height: calc(100% - 4px);
        }

        > div {
          padding: 2px 4px;
          line-height: 22px;
        }
      }
    }
  }
}
</style>
<style lang="scss">
.file-manager-demo--move-modal {
  .fa-tree-node-content-wrapper:hover {
    background-color: #fafafa !important;
  }

  .fa-tree-node-content-wrapper.fa-tree-node-selected {
    background-color: #f0f7ff !important;
  }
}
</style>
