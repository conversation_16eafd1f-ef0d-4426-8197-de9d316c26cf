import { POST, GET } from '@/api/request';

/**
 * 设置登录账号的版本
 * @param version 版本
 * @returns
 */
export const setVersion = (version: number) => {
  return POST('/api/adm/setVer', {
    version,
  });
};

/**
 * 通过aid设置版本
 * @param aid aid
 * @param version 版本
 * @returns
 */
export const setVersionByAid = (aid: number, version: number) => {
  return POST('/api/adm//setVerByAid', {
    aid,
    version,
  });
};

/**
 * 克隆项目
 * @param aid aid
 * @param id 项目id
 * @returns
 */
export const cloneProject = (aid: number, id: number) => {
  return GET('/api/adm/copyProjectInfo', {
    aid,
    id,
  });
};
