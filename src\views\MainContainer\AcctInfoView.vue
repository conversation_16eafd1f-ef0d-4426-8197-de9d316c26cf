<template>
  <div class="acct-info">
    <!-- 点数明细面包屑 -->
    <fa-breadcrumb class="mb-[20px]" v-if="currentMenu === 'PointDetail'">
      <fa-breadcrumb-item class="cursor-pointer"
        ><a @click="handleBack">企业信息</a></fa-breadcrumb-item
      >
      <fa-breadcrumb-item>创作点数明细</fa-breadcrumb-item>
    </fa-breadcrumb>
    <BaseScrollContainer>
      <div class="acct-info__content" :class="heightFullClass">
        <!-- 企业信息/成员权限 -->
        <div v-if="currentMenu !== 'PointDetail'">
          <fa-menu
            class="fa-menu__spec-horizontal-round fa-menu__spec-horizontal"
            :selectedKeys="[currentMenu]"
            mode="horizontal"
          >
            <fa-menu-item
              v-for="item in menuList"
              :key="item.componentName"
              @click="handleSelect(item.componentName)"
            >
              {{ item.name }}
            </fa-menu-item>
          </fa-menu>
          <div class="p-[0_24px_20px]">
            <component
              :is="componentMap[currentMenu]"
              @showDetail="handleShowDetail"
            />
          </div>
        </div>
        <!-- 点数明细 -->
        <div class="p-[0_24px_20px]" v-else>
          <PointDetail />
        </div>
      </div>
    </BaseScrollContainer>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed } from 'vue';
// components
import BaseScrollContainer from '@/components/comm/BaseScrollContainer.vue';
import AcctInfoPage from '@/components/AcctInfo/AcctInfoPage.vue';
import MemberPermission from '@/components/AcctInfo/MemberPermission.vue';
import PointDetail from '@/components/AcctInfo/PointDetail.vue';
const componentMap = {
  AcctInfoPage,
  MemberPermission,
  PointDetail,
};

const props = defineProps<{
  tab?: 'AcctInfoPage' | 'MemberPermission' | 'PointDetail' | ''; // 用于路由传参，默认显示企业信息
}>();

const menuList = [
  {
    name: '企业信息',
    componentName: 'AcctInfoPage',
  },
  {
    name: '成员权限',
    componentName: 'MemberPermission',
  },
];
const currentMenu = ref<keyof typeof componentMap>(props.tab || 'AcctInfoPage');

const heightFullClass = computed(() => {
  console.log(currentMenu.value, 'heightFullClass');
  return currentMenu.value === 'PointDetail'
    ? 'min-h-[calc(100vh-151px)]'
    : 'min-h-[calc(100vh-110px)]';
});

const handleSelect = (key: string) => {
  currentMenu.value = key as keyof typeof componentMap;
};

/** 显示点数明细 */
const handleShowDetail = () => {
  currentMenu.value = 'PointDetail';
};

const handleBack = () => {
  currentMenu.value = 'AcctInfoPage';
};
</script>
<style lang="scss" scoped>
.acct-info {
  @apply size-full p-[20px] bg-background h-full;
  &__content {
    @apply bg-white rounded-[16px] min-w-[1068px];
  }
}

::v-deep(.fa-menu-item-selected) {
  font-weight: bold;
}
</style>
