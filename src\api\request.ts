import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import qs from 'qs';
import Cookies from 'js-cookie';
import { message } from '@fk/faicomponent';
import { RT_CODE } from '@/constants/api';
import router from '@/router';

// 常量定义
const isDev = import.meta.env.MODE === 'development';
const REQUEST_TIMEOUT = 60 * 1000; // 60秒超时
const TOKEN_COOKIE_KEY = '_TOKEN';
const DEFAULT_ERROR_MESSAGE = '网络请求失败，请稍后重试';
const PROTOCOL_ERROR_MESSAGE = '服务器响应格式错误';
const UNKNOWN_BUSINESS_ERROR_MESSAGE = '未知业务错误';
const PROJECT_DELETED_ERROR_CODE = 6100; // 项目删除错误码
const NO_PERMISSION_ERROR_CODE = -20; // 无权限错误码

// API响应接口定义
export interface ApiResponse<T = unknown> {
  totalSize?: number;
  data: T;
  msg: string;
  rt: number;
  success: boolean;
  total: number;
}

/**
 * @interface StandardizedError
 * @description 标准化错误对象接口
 * @template DataType - 业务数据类型，默认为 unknown
 */
export interface StandardizedError<DataType = unknown> {
  /** 统一的错误消息 */
  message: string;
  /** 错误类型：'business' 表示业务逻辑错误，'network' 表示网络或请求层错误 */
  type: 'business' | 'network';
  /** 错误码 (可选)。对于业务错误，通常是 ApiResponse.rt；对于网络错误，可能是 HTTP 状态码。 */
  code?: number;
  /** 原始错误对象 (可选)，用于需要访问特定错误类型属性的场景 */
  original?: unknown;
  /**
   * 响应数据 (可选).
   * 对于 'business' 类型的错误, 此字段包含来自 ApiResponse.data 的内容 (类型为 DataType).
   * 对于 'network' 类型的错误, 此字段通常为 undefined.
   */
  data?: DataType;
  /** 业务错误特有：业务返回码 (来自 ApiResponse.rt) */
  rt?: number;
  /** 业务错误特有：操作是否成功 (对于错误，始终为 false) */
  success?: false;
  /** 业务错误特有：总数 (来自 ApiResponse.total) */
  total?: number;
}

/**
 * @description 请求结果类型。
 * 成功时：[null, ApiResponse<T>]
 * 失败时：[StandardizedError<T>, null]
 */
export type RequestResult<T> =
  | [StandardizedError<unknown>, null]
  | [null, ApiResponse<T>];

// Content-Type 枚举
export enum ContentType {
  FORM_URLENCODED = 'application/x-www-form-urlencoded;charset=UTF-8',
  JSON = 'application/json',
  MULTIPART = 'multipart/form-data',
}

// 请求配置接口扩展
export interface ExtendedAxiosRequestConfig
  extends Omit<AxiosRequestConfig, 'headers'> {
  contentType?: ContentType;
  headers?: Record<string, string>;
}

// 错误消息映射表
const ERROR_MESSAGE_MAP: Record<string, string> = {
  'Network Error': '网络连接失败，请检查网络设置',
  'timeout': '请求超时，请稍后重试',
  'Request timeout': '请求超时，请稍后重试',
  'Request failed with status code 404': '请求的资源不存在',
  'Request failed with status code 500': '服务器内部错误，请稍后重试',
  'Request failed with status code 502': '网关错误，请稍后重试',
  'Request failed with status code 503': '服务暂时不可用，请稍后重试',
  'Request failed with status code 504': '网关超时，请稍后重试',
  'ECONNREFUSED': '连接被拒绝，请检查网络连接',
  'ENOTFOUND': '无法找到服务器，请检查网络连接',
  'ECONNRESET': '连接被重置，请重试',
  'ETIMEDOUT': '连接超时，请检查网络连接',
};

// 预编译关键词数组，提高查找性能
const ERROR_KEYWORDS = Object.keys(ERROR_MESSAGE_MAP);

/**
 * @description : 错误消息中文化映射
 * @param        {string} message - 原始错误消息
 * @return       {string} 中文化后的错误消息
 */
function translateErrorMessage(message: string): string {
  // 检查是否有完全匹配的错误消息
  if (ERROR_MESSAGE_MAP[message]) {
    return ERROR_MESSAGE_MAP[message];
  }

  // 检查是否包含特定关键词（优化后的查找）
  const matchedKeyword = ERROR_KEYWORDS.find(keyword =>
    message.includes(keyword),
  );
  if (matchedKeyword) {
    return ERROR_MESSAGE_MAP[matchedKeyword];
  }

  // 如果没有匹配的映射，返回通用的中文错误消息
  return message || DEFAULT_ERROR_MESSAGE;
}

/**
 * @description : 数据转换工具
 * @param        {unknown} data - 请求数据
 * @param        {ContentType} contentType - 内容类型
 * @return       {unknown} 转换后的数据
 */
function transformRequestData(
  data: unknown,
  contentType?: ContentType,
): unknown {
  if (!data) return data;

  switch (contentType) {
    case ContentType.FORM_URLENCODED:
      return qs.stringify(data);
    case ContentType.MULTIPART: {
      if (data instanceof FormData) {
        return data;
      }
      // 将普通对象转换为FormData
      const formData = new FormData();
      if (data && typeof data === 'object') {
        Object.entries(data as Record<string, unknown>).forEach(
          ([key, value]) => {
            if (value !== null && value !== undefined) {
              formData.append(key, value as string | Blob);
            }
          },
        );
      }
      return formData;
    }
    case ContentType.JSON:
    default:
      return data;
  }
}

// 创建axios实例并配置
const axiosInstance: AxiosInstance = axios.create({
  timeout: REQUEST_TIMEOUT,
  headers: {
    'Content-Type': ContentType.FORM_URLENCODED, // 默认使用 x-www-form-urlencoded
  },
});

/**
 * @description : 统一请求方法
 * @param        {String} method - 请求方法
 * @param        {String} url - 请求URL
 * @param        {any} data - 请求数据
 * @param        {ExtendedAxiosRequestConfig} config - 请求配置
 * @return       {Promise<RequestResult<T>>}
 */
async function REQUEST<T = unknown>(
  method: 'get' | 'post' | 'put' | 'delete',
  url: string,
  data?: unknown,
  config: ExtendedAxiosRequestConfig = {},
): Promise<RequestResult<T>> {
  try {
    const requestConfig: ExtendedAxiosRequestConfig = { ...config };
    const contentType = config.contentType || ContentType.FORM_URLENCODED;

    // 设置 Content-Type
    if (contentType !== ContentType.MULTIPART) {
      requestConfig.headers = {
        ...requestConfig.headers,
        'Content-Type': contentType,
      };
    }

    // 处理请求数据
    if (method === 'get' || method === 'delete') {
      requestConfig.params = data;
    } else {
      data = transformRequestData(data, contentType);
    }

    const response: AxiosResponse<ApiResponse<T>> = await axiosInstance[method](
      url,
      method === 'get' || method === 'delete' ? requestConfig : data,
      method === 'get' || method === 'delete' ? undefined : requestConfig,
    );

    const apiResponse: ApiResponse<T> = response.data;

    // 健壮性检查：如果响应数据不是预期的 ApiResponse 结构
    if (!apiResponse || typeof apiResponse.success !== 'boolean') {
      const protocolError: StandardizedError<T> = {
        message: PROTOCOL_ERROR_MESSAGE,
        type: 'network',
        original: apiResponse, // 保留原始响应体
      };
      return [protocolError, null];
    }

    if (apiResponse.success === false) {
      // 统一处理项目删除错误
      if (apiResponse.rt === PROJECT_DELETED_ERROR_CODE) {
        const errorMessage = apiResponse.msg || '项目已被删除';
        message.error(errorMessage);
        setTimeout(() => {
          router.push('/project');
        }, 1000);
      }

      if (apiResponse.rt === NO_PERMISSION_ERROR_CODE) {
        // 无权限
        router.push('/no-permission');
      }

      const businessError: StandardizedError<T> = {
        message: apiResponse.msg || UNKNOWN_BUSINESS_ERROR_MESSAGE,
        type: 'business',
        code: apiResponse.rt,
        rt: apiResponse.rt,
        success: false, // 明确为 false
        total: apiResponse.total,
        data: apiResponse.data,
        original: apiResponse,
      };
      return [businessError, null];
    }

    return [null, apiResponse]; // 成功时直接返回 ApiResponse
  } catch (error) {
    const errInstance =
      error instanceof Error
        ? error
        : new Error(String(error) || DEFAULT_ERROR_MESSAGE);

    let httpStatusCode: number | undefined;
    let errorResponseData: unknown;

    if (axios.isAxiosError(error) && error.response) {
      httpStatusCode = error.response.status;
      errorResponseData = error.response.data;
    }

    const networkError: StandardizedError<unknown> = {
      // 网络错误时，data 类型为 unknown
      message: translateErrorMessage(errInstance.message),
      type: 'network',
      code: httpStatusCode,
      original: error,
      data: errorResponseData,
    };
    return [networkError as StandardizedError<T>, null];
  }
}

// 通用请求数据类型
type RequestData = Record<string, unknown> | FormData | unknown;

/**
 * @description : GET请求
 * @param        {string} url - 请求URL
 * @param        {Record<string, unknown>} params - 请求参数
 * @param        {ExtendedAxiosRequestConfig} config - 请求配置
 * @return       {Promise<RequestResult<T>>}
 */
export function GET<T = unknown>(
  url: string,
  params?: Record<string, unknown>,
  config?: ExtendedAxiosRequestConfig,
): Promise<RequestResult<T>> {
  return REQUEST<T>('get', url, params, config);
}

/**
 * @description : POST请求
 * @param        {string} url - 请求URL
 * @param        {RequestData} data - 请求数据
 * @param        {ExtendedAxiosRequestConfig} config - 请求配置
 * @return       {Promise<RequestResult<T>>}
 */
export function POST<T = unknown>(
  url: string,
  data?: RequestData,
  config?: ExtendedAxiosRequestConfig,
): Promise<RequestResult<T>> {
  return REQUEST<T>('post', url, data, config);
}

/**
 * @description : POST请求(application/x-www-form-urlencoded)
 * @param        {string} url - 请求URL
 * @param        {RequestData} data - 请求数据
 * @param        {ExtendedAxiosRequestConfig} config - 请求配置
 * @return       {Promise<RequestResult<T>>}
 */
export function POST_FORM<T = unknown>(
  url: string,
  data?: RequestData,
  config?: ExtendedAxiosRequestConfig,
): Promise<RequestResult<T>> {
  return REQUEST<T>('post', url, data, {
    ...config,
    contentType: ContentType.FORM_URLENCODED,
  });
}

/**
 * @description : POST请求(application/json)
 * @param        {string} url - 请求URL
 * @param        {RequestData} data - 请求数据
 * @param        {ExtendedAxiosRequestConfig} config - 请求配置
 * @return       {Promise<RequestResult<T>>}
 */
export function POST_JSON<T = unknown>(
  url: string,
  data?: RequestData,
  config?: ExtendedAxiosRequestConfig,
): Promise<RequestResult<T>> {
  return REQUEST<T>('post', url, data, {
    ...config,
    contentType: ContentType.JSON,
  });
}

/**
 * @description : POST请求(multipart/form-data)
 * @param        {string} url - 请求URL
 * @param        {RequestData} data - 请求数据
 * @param        {ExtendedAxiosRequestConfig} config - 请求配置
 * @return       {Promise<RequestResult<T>>}
 */
export function POST_MULTIPART<T = unknown>(
  url: string,
  data?: RequestData,
  config?: ExtendedAxiosRequestConfig,
): Promise<RequestResult<T>> {
  return REQUEST<T>('post', url, data, {
    ...config,
    contentType: ContentType.MULTIPART,
  });
}

/**
 * @description : PUT请求
 * @param        {string} url - 请求URL
 * @param        {RequestData} data - 请求数据
 * @param        {ExtendedAxiosRequestConfig} config - 请求配置
 * @return       {Promise<RequestResult<T>>}
 */
export function PUT<T = unknown>(
  url: string,
  data?: RequestData,
  config?: ExtendedAxiosRequestConfig,
): Promise<RequestResult<T>> {
  return REQUEST<T>('put', url, data, config);
}

/**
 * @description : DELETE请求
 * @param        {string} url - 请求URL
 * @param        {Record<string, unknown>} params - 请求参数
 * @param        {ExtendedAxiosRequestConfig} config - 请求配置
 * @return       {Promise<RequestResult<T>>}
 */
export function DELETE<T = unknown>(
  url: string,
  params?: Record<string, unknown>,
  config?: ExtendedAxiosRequestConfig,
): Promise<RequestResult<T>> {
  return REQUEST<T>('delete', url, params, config);
}

// 请求拦截器
axiosInstance.interceptors.request.use(
  config => {
    // 为所有请求都加上 _TOKEN 参数到 url 上，值从 cookie 获取
    const token = Cookies.get(TOKEN_COOKIE_KEY);
    if (token) {
      config.params = config.params || {};
      config.params[TOKEN_COOKIE_KEY] = token;
    }
    return config;
  },
  error => Promise.reject(error),
);

// 响应拦截器
axiosInstance.interceptors.response.use(
  response => {
    if (response.data.rt === RT_CODE.AUTH_ERROR) {
      Cookies.remove(TOKEN_COOKIE_KEY);
      if (isDev) {
        router.replace('/dev-login');
      } else {
        Cookies.set('callBackUrl', window.location.href, {
          expires: 1,
          domain: response.data.data.homeDomain,
          path: '/',
        });
        let jumpUrl = response.data.data.jumpUrl;
        if (!jumpUrl.startsWith('//') || !jumpUrl.startsWith('http')) {
          jumpUrl = '//' + jumpUrl;
        }
        if (jumpUrl) {
          window.location.href = jumpUrl;
        }
      }
    }
    return response;
  },
  error => Promise.reject(error),
);

// 导出axios实例，便于自定义使用
export default axiosInstance;
