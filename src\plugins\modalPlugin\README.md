# modalPlugin 插件说明文档

## 简介

modalPlugin 是基于 Vue 2.7 的全局弹窗管理插件，支持在任意组件中通过代码方式动态创建、显示和关闭弹窗组件。适用于需要全局控制弹窗、参数传递、自动销毁的场景。

## 基本用法

1. 打开弹窗

```typescript
import VideoViewer from '@/components/comm/VideoViewer.vue';

this.$modal.open('VideoViewer', {
  component: VideoViewer,
  props: {
    visible: true,
    autoPlay: true,
    videoSrc: 'http://example.com/video.mp4',
  },
  onClose: () => {
    console.log('VideoViewer 已关闭');
  },
});
```

2. 关闭弹窗

```typescript
this.$modal.close('VideoViewer');
```

3. 关闭所有弹窗

```typescript
this.$modal.closeAll();
```

## 组件参数

## API

key：弹窗唯一标识（字符串），同一时刻同 key 只会有一个弹窗。 component：弹窗组件（需为 Vue 2 选项式 API 组件）。 props：传递给弹窗组件的参数对象。 onClose：弹窗关闭时的回调函数。

## 弹窗组件要求

1. 必须通过 $emit('close') 事件通知插件关闭弹窗
2. 通过 $emit('confirm', data) 传递参数，插件自动将参数传递给 open 时的 onConfirm 回调。
3. 必须声明所有 props，确保参数类型正确。
4. 推荐使用选项式 API（即 export default { ... }）

## 注意事项

1. 支持多种弹窗类型（如图片预览、视频播放、表单等），只需传递不同的组件和参数。
2. 可扩展为弹窗队列，支持多个弹窗同时展示（需修改插件实现）。
