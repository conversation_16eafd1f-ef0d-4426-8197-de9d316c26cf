import { FILE_TYPES } from '@/constants/fileType';

/**
 * 文件字段（固定配置项，用于文件上传、文件管理等功能）
 * 定义枚举类型替代纯字符串常量
 */
export enum INFO_KEYS {
  /** 唯一标识 */
  ID = 'id',
  /** 素材所属的文件夹id */
  FOLDER_ID = 'folderId',
  /** 名字 */
  NAME = 'name',
  /** 父级文件夹标识 */
  PARENT_ID = 'parentId',
  /** 资源id（同时用于判断是否文件夹，有值即为资源） */
  RES_ID = 'resId',
  /** 孩子 */
  CHILDREN = 'children',
  /** 选择 */
  SELECT = 'select',
  /** 时间 */
  CREATE_TIME = 'createTime',
  /** 文件大小 */
  FILE_SIZE = 'fileSize',
  /** 文件类型 */
  FILE_TYPE = 'fileType',
  /** 类型 */
  TYPE = 'type',
  /** 视频额外字段 */
  EXTRA = 'extra',
  /** 文件上传进度 */
  FILE_PERCENT = 'percent',
}

/**
 * 排序方式 升序、降序
 */
export enum SORT_MODE_KEY {
  /** 升序 */
  ASCE = 'asce',
  /** 降序 */
  DESC = 'desc',
}

/** 文件类型 */
export enum MIMETYPE_KEY {
  ZIP = 'zip',
  RAR = 'rar',
  JPG = 'jpg',
  SVG = 'svg',
  JPEG = 'jpeg',
  PNG = 'png',
  GIF = 'gif',
  BMP = 'bmp',
  DOC = 'doc',
  XLS = 'xls',
  DOCX = 'docx',
  XLSX = 'xlsx',
  PPT = 'ppt',
  PPTX = 'pptx',
  MP3 = 'mp3',
  M4A = 'm4a',
  MP4 = 'mp4',
  MOV = 'mov',
  PDF = 'pdf',
  EXE = 'exe',
  HTML = 'html',
  ICO = 'ico',
  TXT = 'txt',
  FLV = 'flv',
  SWF = 'swf',
  PSD = 'psd',
  AI = 'ai',
  EPUB = 'epub',
}

/**
 * 可预览图片类型
 * @type {Array}
 */
export const IMG_PREVIEW_KEY_LIST = [
  FILE_TYPES.JPEG,
  FILE_TYPES.PNG,
  FILE_TYPES.GIF,
  FILE_TYPES.BMP,
];

/**
 * 可预览视频类型（同时支持自定义封面）
 * @type {Array}
 */
export const VIDEO_PREVIEW_KEY_LIST = [FILE_TYPES.MP4, FILE_TYPES.MOV];

/**
 * 支持上传的图片类型
 * @type {Array}
 */
export const IMG_UPLOAD_LIST = [
  MIMETYPE_KEY.JPG,
  MIMETYPE_KEY.JPEG,
  MIMETYPE_KEY.PNG,
];

/**
 * 支持上传的视频类型
 * @type {Array}
 */
export const VIDEO_UPLOAD_LIST = [MIMETYPE_KEY.MP4, MIMETYPE_KEY.MOV];

/**
 * 文件类型别名
 * 用于兼容部分文件类型
 * @type {Object}
 */
export const MIMETYPE_ALIAS: { [key: string]: string } = {
  [MIMETYPE_KEY.ZIP]: 'application/x-zip-compressed',
  [MIMETYPE_KEY.JPG]: 'image/jpg',
  [MIMETYPE_KEY.SVG]: 'image/svg+xml',
  [MIMETYPE_KEY.JPEG]: 'image/jpeg',
  [MIMETYPE_KEY.PNG]: 'image/png',
  [MIMETYPE_KEY.GIF]: 'image/gif',
  [MIMETYPE_KEY.BMP]: 'image/bmp',
  [MIMETYPE_KEY.DOC]: 'application/msword',
  [MIMETYPE_KEY.XLS]: 'application/vnd.ms-excel',
  [MIMETYPE_KEY.DOCX]:
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
  [MIMETYPE_KEY.XLSX]:
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  [MIMETYPE_KEY.PPT]: 'application/vnd.ms-powerpoint',
  [MIMETYPE_KEY.PPTX]:
    'application/vnd.openxmlformats-officedocument.presentationml.presentation',
  [MIMETYPE_KEY.MP3]: 'audio/mpeg',
  [MIMETYPE_KEY.M4A]: 'audio/MP4A-LATM',
  [MIMETYPE_KEY.MP4]: 'video/mp4',
  [MIMETYPE_KEY.PDF]: 'application/pdf',
  [MIMETYPE_KEY.EPUB]: 'application/epub+zip',
  [MIMETYPE_KEY.EXE]: 'application/x-msdownload',
  [MIMETYPE_KEY.HTML]: 'text/html',
  [MIMETYPE_KEY.ICO]: 'image/x-icon',
  [MIMETYPE_KEY.TXT]: 'text/plain',
  [MIMETYPE_KEY.FLV]: 'flv',
  [MIMETYPE_KEY.SWF]: 'application/x-shockwave-flash',
  [MIMETYPE_KEY.PSD]: 'psd',
  [MIMETYPE_KEY.AI]: 'ai',
  [MIMETYPE_KEY.RAR]: '.rar',
  [MIMETYPE_KEY.MOV]: 'video/quicktime',
};

/** 文件最大名称长度 */
export const INPUT_NAME_MAX_LENGTH = 100;
/** 文件夹名称最大长度 */
export const FOLDER_NAME_MAX_LENGTH = 20;

/** 文件视图类型 */
export const File_View_Type_KEY_LIST = {
  LIST: 'list',
  TABLE: 'table',
};

/** 根文件夹id */
export const ROOT_FOLDER_ID = 0;
