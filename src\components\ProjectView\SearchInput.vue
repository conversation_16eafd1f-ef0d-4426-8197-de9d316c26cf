<template>
  <fa-input
    v-model.trim="searchText"
    size="large"
    class="search-input"
    :placeholder="placeholder"
    @keyup.enter="handleSearch"
  >
    <template #suffix>
      <fa-icon
        v-show="isShowClearInput"
        type="fa-delete"
        theme="filled"
        :style="{ fontSize: '18px' }"
        class="mr-[4px] cursor-pointer text-disabledText"
        @click="handleClickClearInput"
      />
      <div @click="handleSearch" class="search-btn">
        <Icon
          type="xiaosuo"
          class="size-[20px] text-[#bfbfbf] hover:text-assist"
        />
      </div>
    </template>
  </fa-input>
</template>

<script setup lang="ts">
import { defineProps, defineEmits, ref, computed } from 'vue';

defineProps({
  placeholder: {
    type: String,
    default: '',
  },
});

/**
 * SearchInput 组件 Emits
 * @event search 搜索事件，参数为输入内容
 */
const emit = defineEmits<{
  (e: 'search', value: string): void;
}>();

const searchText = ref('');

/** 是否显示清除输入框的图标 */
const isShowClearInput = computed(() => searchText.value.length > 0);

/**
 * 触发搜索事件
 */
const handleSearch = () => {
  emit('search', searchText.value); // 发送文本值
};

/**
 * 清空输入框并触发搜索
 */
function handleClickClearInput() {
  searchText.value = '';
  handleSearch();
}
</script>

<style lang="scss" scoped>
.search-input {
  :deep(.fa-input) {
    @apply shadow-none rounded-[8px] text-[14px] pr-[54px];
    &:not(:last-child) {
      @apply pr-[54px];
    }
  }
  .search-btn {
    @apply cursor-pointer;
    &:hover {
    }
  }
}
</style>
