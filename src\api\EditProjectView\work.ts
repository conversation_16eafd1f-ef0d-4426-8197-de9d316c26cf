/**
 * @fileoverview 作品相关API
 * @description 提供作品列表和详情的接口
 */

import { GET, POST_JSON } from '@/api/request';
import type { ApiResponse } from '@/api/request';
import type {
  // 请求参数类型
  GetWorkListParams,
  GetWorkListByIdParams,
  GetWorkInfoParams,
  SaveWorksParams,
  UpdateWorkFlagParams,
  WorkListResponse,
  ApiWorkListItem,
  ApiWorkInfo,
  SaveWorksResponse,
} from './types';
import { VIEW_MODE } from '@/views/EditProjectView/constants';
import {
  transformWorkInfoFromApiToProject,
  transformWorkListItemFromApiToProject,
} from '@/api/EditProjectView/utils/inputDataTransform';
import type { ExtendedAxiosRequestConfig } from '@/api/request';
import store from '@/store';

/**
 * @description 获取作品列表（分页）
 * @param params 请求参数
 * @returns Promise<[Error | null, { data: ApiWorkListItem[]; total: number } | null]>
 */
export const getWorkList = async (params: GetWorkListParams) => {
  const requestData: Record<string, unknown> = {
    projectId: params.projectId,
    limit: params.limit,
    pageNow: params.pageNow,
    viewMode: params.viewMode ?? VIEW_MODE.PRE_VIEW,
  };

  // 只有传入了 sortKey 才添加到请求中
  if (params.sortKey) {
    requestData.sortKey = params.sortKey;
  }

  // 只有传入了 desc 才添加到请求中
  if (params.desc !== undefined) {
    requestData.desc = params.desc;
  }

  // 如果提供了 idList 参数，则添加到请求中（用于轮询特定作品）
  if (params.idList && params.idList.length > 0) {
    requestData.idList = params.idList;
  }

  return POST_JSON<WorkListResponse['data']>('/api/work/getList', requestData);
};

/**
 * @description 轮询专用接口 - 根据作品ID列表获取作品数据
 * @param params 请求参数
 * @returns Promise<[Error | null, { data: ApiWorkListItem[]; total: number } | null]>
 */
export const getWorkListById = async (params: GetWorkListByIdParams) => {
  return POST_JSON<WorkListResponse['data']>('/api/work/getListById', params);
};

/**
 * @description 通用的作品列表数据转换函数
 * @param res API响应数据
 * @param projectId 项目ID，用于更新Vuex状态
 * @param updateStore 是否更新Vuex状态，默认为true
 * @returns 转换后的数据结构
 */
const transformWorkListResponse = (
  res: ApiResponse<WorkListResponse['data']> | null,
  projectId: number,
  updateStore: boolean = true,
) => {
  if (!res?.data) {
    return null;
  }

  // 适配新的API响应结构：从 res.data.workList 获取作品列表数据
  const workListData = res.data.workList;
  const items = Array.isArray(workListData) ? workListData : [];
  const transformedWorkList = items.map((apiWorkItem: ApiWorkListItem) => {
    return transformWorkListItemFromApiToProject(apiWorkItem);
  });

  // 更新Vuex状态（仅在需要时）
  if (updateStore) {
    const projectStatusMap: Record<string, number> = {};
    const projectStatus = res.data.projectStatus;
    if (projectStatus !== undefined) {
      projectStatusMap[String(projectId)] = projectStatus;
      store.dispatch('project/setProjectStatusBatch', projectStatusMap);
    }
  }

  // 返回统一的数据结构，保持原有的 res 结构
  return {
    ...res,
    workList: transformedWorkList,
    totalSize: res.data.totalSize || 0,
    projectStatus: res.data.projectStatus || 0,
    sucNum: res.data.sucNum || 0,
  };
};

/**
 * @description 轮询专用接口 - 根据作品ID列表获取作品数据并转换为前端数据结构
 * @param params 请求参数
 * @returns Promise<[Error | null, { workList: WorkItem[]; totalSize: number; total: number; projectStatus: number; sucNum: number } | null]>
 */
export const getWorkListByIdWithTransform = async (
  params: GetWorkListByIdParams,
) => {
  // 调用轮询专用API获取数据
  const [err, res] = await getWorkListById(params);

  if (err) {
    return [err, null] as const;
  }

  // 使用通用转换函数，轮询时不更新Vuex状态以避免不必要的状态更新
  const result = transformWorkListResponse(res, params.projectId, false);

  return [null, result] as const;
};

/**
 * @description 获取作品列表并转换为前端数据结构
 * @param params 请求参数
 * @returns Promise<[Error | null, { workList: WorkItem[]; totalSize: number; total: number; projectStatus: number; sucNum: number } | null]>
 */
export const getWorkListWithTransform = async (params: GetWorkListParams) => {
  // 调用原始API获取数据
  const [err, res] = await getWorkList(params);

  if (err) {
    return [err, null] as const;
  }

  // 使用通用转换函数，更新Vuex状态
  const result = transformWorkListResponse(res, params.projectId, true);

  return [null, result] as const;
};

/**
 * @description 获取作品详情
 * @param params 请求参数
 * @returns Promise<[Error | null, ApiWorkInfo | null]>
 */
export const getWorkInfo = async (
  params: GetWorkInfoParams,
  config?: ExtendedAxiosRequestConfig,
) => {
  return GET<ApiWorkInfo>(
    '/api/work/getInfo',
    {
      id: params.id,
      viewMode: params.viewMode ?? VIEW_MODE.PRE_VIEW,
    },
    config,
  );
};

/**
 * @description 获取作品详情并转换为前端数据结构
 * @param params 请求参数
 * @returns Promise<[Error | null, { data: WorkItem } | null]>
 */
export const getWorkInfoWithTransform = async (
  params: GetWorkInfoParams,
  config?: ExtendedAxiosRequestConfig,
) => {
  // 调用原始API获取数据
  const [err, res] = await getWorkInfo(params, config);

  if (err) {
    return [err, null] as const;
  }

  // 转换API返回的详情数据为前端数据结构
  const transformedWorkInfo = transformWorkInfoFromApiToProject(res.data);

  return [null, { ...res, data: transformedWorkInfo }] as const;
};

/**
 * @description 保存作品到作品库
 * @param params 请求参数
 * @param params.projectId 项目ID（必填）
 * @param params.workIds 作品ID列表
 * @param params.isAll 是否全选（用于分页场景下的全选操作）
 * @returns Promise<[Error | null, SaveWorksResponse | null]>
 */
export const saveWorks = async (params: SaveWorksParams) => {
  // 检查必填参数
  if (!params.projectId) {
    return [new Error('项目ID是必填参数'), null] as const;
  }

  const workIds = Array.isArray(params.workIds) ? params.workIds : [];
  const idListString = workIds.join(',');

  return GET<SaveWorksResponse>('/api/work/batchSave', {
    projectId: params.projectId,
    idList: idListString,
    isAll: params.isAll || false,
  });
};

/**
 * @description 更新作品标识
 * @summary 用于标识作品已被用户查看，通常在用户点击新生成的作品后调用
 *
 * @param params 请求参数
 * @param params.id 作品ID（必填）- 需要更新标识的作品唯一标识
 */
export const updateWorkFlag = async (params: UpdateWorkFlagParams) => {
  return GET('/api/work/updateFlag', {
    id: params.id,
  });
};
