<template>
  <div class="relative">
    <ScVideo
      v-bind="$attrs"
      v-on="$listeners"
      ref="videoRef"
      class="size-full"
      :src="src"
      :type="type"
      :belong="belong"
      :loop="loop"
      @canplay="onCanPlay"
    />
    <!-- 只有传入coverId且videoLoading为true时显示封面图 -->
    <div
      v-if="coverId && videoLoading"
      class="absolute top-0 left-0 overflow-hidden z-10 m-auto w-full h-full"
    >
      <ScImg
        :src="coverId"
        :belong="belong"
        :max-width="324"
        class="w-full h-full"
      />
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, defineProps, withDefaults, defineExpose } from 'vue';
import ScVideo from '@/components/comm/ScVideo.vue';
import ScImg from '@/components/comm/ScImg.vue';

/**
 * 组件props定义
 */
const props = withDefaults(
  defineProps<{
    /** 视频地址或资源id */
    src?: string;
    /** 视频资源类型，后端接口会和资源id一起返回，没有就找后端要 */
    type?: number;
    /** 资源归属，一般不用传值，默认是用户资源。*/
    belong?: 'user' | 'system' | 'oss';
    /** 是否循环播放 */
    loop?: boolean;
    /** 封面图url或资源id */
    coverId?: string;
  }>(),
  {
    loop: true,
  },
);

/**
 * 只有传入coverId时，才启用videoLoading逻辑
 */
const videoLoading = ref(!!props.coverId);

const videoRef = ref<HTMLElement | null>(null);

/**
 * 获取video元素
 */
function getVideoEl() {
  if (
    videoRef.value &&
    (videoRef.value as unknown as { $el?: HTMLVideoElement }).$el instanceof
      HTMLVideoElement
  ) {
    return (videoRef.value as unknown as { $el: HTMLVideoElement }).$el;
  }
}

/**
 * 播放视频
 */
function startVideo() {
  const el = getVideoEl();
  el?.play?.();
}

/**
 * 暂停视频
 */
function stopVideo() {
  const el = getVideoEl();
  el?.pause?.();
}

/**
 * 视频canplay时回调，隐藏封面图
 */
function onCanPlay() {
  videoLoading.value = false;
}

defineExpose({
  startVideo,
  stopVideo,
});
</script>
