# 在 Vue 中使用图片资源的说明

## 1. 在 `<img>` 标签中使用图片

在模板中直接通过 `src` 属性引用图片资源。  
建议使用 `@` 别名来正确解析路径。

示例：

```vue
<template>
  <div>
    <!-- 使用 @ 别名引用图片 -->
    <img src="@/assets/images/example.png" alt="示例图片" />
  </div>
</template>
```

## 2. 在 CSS 中使用图片

在 CSS 中通过 `background-image` 或其他属性引用图片资源。  
同样需要使用 `@` 来正确解析路径。

示例：

```vue
<template>
  <div class="image-container">CSS 中使用图片示例</div>
</template>

<style>
.image-container {
  /* 使用背景图片 */
  background-image: url(@/assets/images/example.png);
  width: 200px;
  height: 200px;
  background-size: cover;
  background-position: center;
}
</style>
```
