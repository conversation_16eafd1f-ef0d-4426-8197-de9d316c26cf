# 配音弹窗取消操作状态恢复修复

## 问题描述

在上传原料页面的更换配音弹窗中，当用户已设置【智能推荐】，打开弹窗后关闭智能推荐，然后不点确定直接关闭弹窗（点击关闭、取消、遮罩），会导致状态变成【未选择】。

**预期结果**：点击关闭、取消、遮罩都不保存修改，保留原先的【智能推荐】状态。

## 问题原因

`DubbingSelector` 组件使用了 `.sync` 修饰符来双向绑定 `useAiRecommendValue`，当用户在弹窗内修改智能推荐开关时，会立即同步到父组件。在 `handleCancel` 方法中，组件只是简单地关闭弹窗，没有恢复到打开弹窗时的初始状态。

## 解决方案

参考 `BgMusicSelector` 组件的实现，在 `DubbingSelector` 组件中添加初始状态保存和恢复逻辑：

### 1. 添加初始状态保存

```typescript
// 保存弹窗打开时的初始状态
const initialState = ref<{
  selectedDubbing: Dubbing | null;
  useAiRecommend: boolean;
}>({
  selectedDubbing: null,
  useAiRecommend: false,
});
```

### 2. 在弹窗打开时保存初始状态

```typescript
watch(
  () => props.value,
  async newVal => {
    visible.value = newVal;
    
    // 当弹窗打开时，保存初始状态
    if (newVal) {
      await initSelectedDubbing();
      initialState.value = {
        selectedDubbing: selectedDubbing.value,
        useAiRecommend: useAiRecommend.value,
      };
    }
  },
);
```

### 3. 在取消时恢复初始状态

```typescript
/** 取消 */
const handleCancel = () => {
  handlePlayDubbing();
  
  // 恢复到弹窗打开时的初始状态
  selectedDubbing.value = initialState.value.selectedDubbing;
  emit('update:useAiRecommendValue', initialState.value.useAiRecommend);
  
  emit('input', false);
};
```

## 修改的文件

- `src/components/VideoEditor/MusicEditor/DubbingSelector.vue`

## 测试场景

1. **智能推荐状态取消测试**：
   - 设置配音为【智能推荐】
   - 打开配音弹窗
   - 关闭智能推荐开关
   - 点击取消/关闭/遮罩
   - 验证状态恢复为【智能推荐】

2. **具体配音状态取消测试**：
   - 设置配音为具体的配音选项
   - 打开配音弹窗
   - 开启智能推荐开关
   - 点击取消/关闭/遮罩
   - 验证状态恢复为原来的具体配音

3. **确认操作测试**：
   - 修改配音设置
   - 点击确认
   - 验证修改被正确保存

## 影响范围

此修复只影响 `DubbingSelector` 组件的取消操作行为，不会影响确认操作和其他功能。所有使用该组件的页面都会受益于这个修复。
