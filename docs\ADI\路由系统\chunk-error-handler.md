# Vue Router Chunk 错误处理方案

## 问题背景

在生产环境中，当开发团队部署新版本时，旧版本的静态资源文件（JS chunks）会被删除。如果用户正在浏览网站并尝试导航到懒加载路由，浏览器会尝试加载已被删除的旧 chunk 文件，导致页面白屏和路由无法正常加载。

## 解决方案

我们实现了一个基于 Vue Router `onError`钩子的自动错误处理机制，当检测到 chunk 加载失败时，自动刷新页面以获取最新版本的资源。

## 核心特性

- ✅ 自动检测 chunk 加载失败错误
- ✅ 防止无限刷新循环
- ✅ 支持自定义错误检测逻辑
- ✅ 可选的用户通知功能
- ✅ 灵活的配置选项

## 使用方法

### 基础用法

```typescript
import { createRouterErrorHandler } from '@/utils/chunkErrorHandler';

// 在路由配置中添加错误处理
router.onError(createRouterErrorHandler());
```

### 高级配置

```typescript
router.onError(
  createRouterErrorHandler({
    reloadInterval: 10000, // 10秒内不重复刷新
    showUserNotification: true, // 显示用户通知
    beforeReload: () => {
      // 刷新前的自定义逻辑
      console.log('即将刷新页面...');
    },
    customErrorDetector: error => {
      // 自定义错误检测逻辑
      return error.message.includes('custom-chunk-error');
    },
  }),
);
```

## 配置选项

| 选项                   | 类型     | 默认值 | 说明                     |
| ---------------------- | -------- | ------ | ------------------------ |
| `reloadInterval`       | number   | 10000  | 重新加载间隔时间（毫秒） |
| `showUserNotification` | boolean  | false  | 是否显示用户提示         |
| `customErrorDetector`  | function | -      | 自定义错误检测函数       |
| `beforeReload`         | function | -      | 重新加载前的回调函数     |

## 错误检测模式

系统会自动检测以下类型的错误：

- `loading chunk \d* failed`
- `failed to fetch dynamically imported module`
- `loading css chunk \d* failed`
- `chunk load failed`
- `network error`

## 防护机制

### 无限刷新保护

- 使用 `sessionStorage` 记录上次刷新时间
- 在指定时间间隔内不会重复刷新
- 默认间隔为 10 秒，可自定义配置

### 用户体验优化

- 可选的用户通知功能
- 支持延迟刷新，给用户通知时间显示
- 优雅的错误处理，不会突然中断用户操作

## 生产环境建议

```typescript
// 生产环境推荐配置
router.onError(
  createRouterErrorHandler({
    reloadInterval: 15000, // 15秒内不重复刷新
    showUserNotification: true, // 启用用户通知
    beforeReload: () => {
      // 可以在这里发送错误统计
      console.log('Chunk loading failed, reloading page...');
    },
  }),
);
```

## 注意事项

1. **避免过度刷新**：合理设置 `reloadInterval` 避免频繁刷新
2. **用户体验**：在生产环境建议启用 `showUserNotification`
3. **错误监控**：可以在 `beforeReload` 回调中添加错误统计逻辑
4. **测试验证**：部署前在测试环境验证错误处理逻辑

## 兼容性

- ✅ Vue 2.7+
- ✅ Vue Router 3.6+
- ✅ 支持所有现代浏览器
- ✅ 兼容 Vite 和 Webpack 构建工具

## 相关文件

- `src/utils/chunkErrorHandler.ts` - 核心工具函数
- `src/router/index.ts` - 路由配置中的使用示例
