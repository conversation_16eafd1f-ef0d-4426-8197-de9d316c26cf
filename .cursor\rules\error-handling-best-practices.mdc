---
description: Error handling and boundary case management guidelines
globs: src/**/*
alwaysApply: false
---

rules:

- error_handling:

  - Implement unified error handling mechanism
  - Capture errors for all asynchronous operations
  - Distinguish between system errors and business errors
  - Provide user-friendly error messages

- boundary_cases:
  - Validate all external inputs and implement data type checking
  - Handle all possible state transitions and ensure data consistency
  - Implement resource limitation mechanisms and handle resource exhaustion scenarios
  - Implement deep data validation and protect critical business processes
  - Implement health check mechanisms and maintain operation audit logs
