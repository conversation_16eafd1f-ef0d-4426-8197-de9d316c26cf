/**
 * @fileoverview 上下文创建工具函数
 * @description 提供轮询上下文信息创建功能
 */

import { PROJECT_TYPE_IMAGE } from '@/views/EditProjectView/constants';
import { PollingContext } from '../types';

/**
 * 获取项目类型的中文描述
 * @param projectType 项目类型
 * @returns 项目类型描述
 */
export const getProjectTypeDescription = (projectType: number): string => {
  return projectType === PROJECT_TYPE_IMAGE ? '图文' : '视频';
};

/**
 * 创建轮询上下文信息
 * @param triggerType 触发类型
 * @param projectId 项目ID
 * @param projectType 项目类型
 * @param pollingWorksCount 轮询作品数量
 * @param isPageVisible 页面可见状态
 * @returns 上下文信息对象
 */
export const createPollingContext = (
  triggerType: string,
  projectId: number,
  projectType: number,
  pollingWorksCount: number,
  isPageVisible: boolean,
): PollingContext => ({
  触发类型: triggerType,
  项目ID: projectId,
  项目类型: getProjectTypeDescription(projectType),
  轮询作品数量: pollingWorksCount,
  页面可见状态: isPageVisible,
  时间戳: new Date().toLocaleTimeString(),
});
