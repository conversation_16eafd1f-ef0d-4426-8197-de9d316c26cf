import Vue from 'vue';
import VideoViewer from './VideoViewer.vue';

/**
 * 打开视频预览弹窗组件
 * @param props 配置参数
 * @param props.videoSrc 视频地址
 * @returns void
 */
export const showVideoViewer = (props: { videoSrc: string }) => {
  Vue.prototype.$modal.open('VideoViewer', {
    component: VideoViewer,
    props: {
      visible: true,
      ...props,
    },
    onClose: () => {
      console.log('VideoViewer已关闭');
    },
  });
};

/**
 * 关闭视频预览弹窗组件
 */
export const closeVideoViewer = () => {
  Vue.prototype.$modal.close('VideoViewer');
};
