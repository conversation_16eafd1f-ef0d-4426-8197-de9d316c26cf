---
description: Best practices and guidelines for Vue.js 2.7.14
globs:
alwaysApply: true
---
- vue_styling:
  - [REQUIRED] 所有Vue组件样式必须严格遵循unocss_styling规则
  - 禁止在Vue模板中直接使用大量UnoCSS工具类，必须使用@apply组织到BEM类名中
  - Follow Vue.js style guide for component naming and structure
  - Maintain consistent props and events naming conventions
  - Use scoped styles in Vue components to prevent CSS conflicts
  - Use BEM and UnoCSS for component styling:
    - [CRITICAL]: 必须按照下方unocss_styling规则实现样式
    - [CRITICAL] 使用 SCSS 的嵌套语法的前提下，显式写出完整的类名，方便后续代码定位与维护
    - [IMPORTANT] 显式写出完整的类名
    - 每个BEM类名必须使用@apply组织UnoCSS工具类
  - Implement responsive design patterns consistently
  - Always clean up event listeners in hook:beforeDestroy when registering event listeners to prevent memory leaks

- vue_component_development:
  - Use Vue Single File Components (.vue) for all new component development
  - Implement proper props validation and default values
  - Use Vuex for complex state management when necessary
  - Use PascalCase (UpperCamelCase) for component names
  - Follow event naming conventions: componentName:eventName
  - Implement proper component registration strategy
  - When using mixins, variable names should be suffixed with Mixin to improve maintainability
  - Use Vuex as the primary state management solution for application-wide state
  - Use UnoCSS with BEM methodology as the primary styling solution:
    - [CRITICAL]: 必须按照下方unocss_styling规则实现样式
    - [CRITICAL] 使用 SCSS 的嵌套语法的前提下，显式写出完整的类名，方便后续代码定位与维护
    - [IMPORTANT] 显式写出完整的类名
    - 每个BEM类名必须使用@apply组织UnoCSS工具类

- unocss_styling:
  - [CRITICAL] 所有Vue组件的样式必须遵循此规则，无例外
  - 示例格式（必须遵循）:
    ```vue
    <style scoped>
    .component-block {
      /* 布局相关 */
      @apply flex flex-col items-center justify-between;
      /* 尺寸相关 */
      @apply w-full h-60px p-15px;
      /* 外观相关 */
      @apply bg-white border border-gray-200 rounded-4px;
      /* 文字相关 */
      @apply text-16px font-medium text-gray-800;
    }

    .component-block__element {
      @apply flex items-center p-10px;
    }

    .component-block__element--modifier {
      @apply bg-blue-100 text-blue-800;
    }
    </style>
    ```
  - Vue模板中的类名引用:
    ```vue
    <template>
      <!-- 正确: 使用BEM类名 -->
      <div class="component-block">
        <div class="component-block__element"></div>
        <div class="component-block__element--modifier"></div>
      </div>

      <!-- 错误: 直接使用大量UnoCSS工具类 -->
      <div class="flex flex-col items-center w-full h-60px p-15px bg-white border rounded-4px">
        <!-- 避免这种写法 -->
      </div>
    </template>
    ```
  - Group UnoCSS classes by functionality categories:
    - Layout: flex, grid, positioning related classes
    - Sizing: width, height, padding, margin related classes
    - Appearance: background, border, shadow related classes
    - Typography: text, font related classes
  - [IMPORTANT] 避免使用rem单位，使用px或viewport单位代替
  - Keep related properties grouped together for easier maintenance
    - [IMPORTANT] Write out full class names explicitly
  - Use multi-line format for better readability:
    - One category per line with proper indentation
    - Use consistent indentation for grouped classes
  - Maintain logical order in class declarations
  - Add comments for complex style combinations
  - Use UnoCSS with BEM methodology as the primary styling solution:
    - Follow BEM (Block Element Modifier) naming conventions for class names
    - Use @apply directive to compose utility classes into BEM classes
    - Example structure:
      .block {
        @apply bg-white p-4;
      }
    - [CRITICAL] 一定要为每个@apply声明添加注释说明其功能分类
    - [CRITICAL] SCSS BEM 嵌套时必须显式写出完整类名:
      - **核心要求**：当在 SCSS 中使用嵌套语法定义 BEM 的元素 (Element) 或修饰符 (Modifier) 时，**必须** 在父选择器内部明确、完整地写出其全类名。
      - **禁止行为**：严禁使用 `&__element` 或 `&--modifier` 这种简写形式来定义 BEM 子元素的类名。
      - **目的**：此规则旨在提高代码库中类名的可搜索性、可读性和整体可维护性。
      - **详细示例**：
        ```scss
        // SCSS BEM 嵌套规范

        // 正确的写法：在父级选择器内部，显式写出完整的 BEM 类名
        // ====================================================
        .component-block {
          /* 块的基础样式 */
          @apply p-4;

          // 元素 (Element) - 必须完整写出
          .component-block__element {
            /* 元素的样式 */
            @apply m-2;
          }

          // 修饰符 (Modifier) - 必须完整写出
          .component-block--modifier {
            /* 修饰符的样式 */
            @apply bg-blue-100;
          }

          // 伪类、属性选择器等可以继续使用 &
          &:hover {
            @apply shadow-lg;
          }
          &[disabled] {
            @apply opacity-50;
          }
        }

        // 错误的写法：在父级选择器内部，使用 & 简写 BEM 元素或修饰符
        // ======================================================
        .component-block {
          /* 块的基础样式 */
          @apply p-4;

          // 元素 (Element) - 禁止此种简写
          &__element { // 避免：不符合规范，影响类名搜索和可读性
            @apply m-2;
          }

          // 修饰符 (Modifier) - 禁止此种简写
          &--modifier { // 避免：不符合规范，影响类名搜索和可读性
            @apply bg-blue-100;
          }
        }

        // 关于在同一HTML元素上应用基础类和修饰类 (如 <div class="block block--modifier">):
        // 在 SCSS 中，针对这种情况，以下写法是符合"显式写出完整类名"原则的：
        .component-block {
          // ... 块的其他样式 ...

          // 当修饰符是应用在 .component-block 自身上时
          &.component-block--modifier { // 注意是 '&.' 加上完整的修饰类名
            @apply border-red-500;     // 这种方式会生成 .component-block.component-block--modifier
                                        // 它清楚地表明了完整的修饰类名，是允许的。
          }
        }
        // 本规则主要强调的是避免在源码中出现不完整的 &__element 和 &--modifier 形式。
        ```
    - [IMPORTANT] 避免使用rem单位，使用px或viewport单位代替
    - Avoid direct utility classes in templates when possible
    - Group related styles using BEM structure
    - Document BEM naming patterns and @apply usage in components
    - Only use utility classes directly for one-off styling needs
    - Maintain consistent BEM naming across the codebase

- api_request_styling:
  - [CRITICAL] 所有API请求必须遵循以下规范，无例外
  - 基本规则:
    - 使用 await-to 范式处理异步请求
    - 错误优先处理原则
    - 禁止在业务代码中使用 try-catch 包装请求
    - 统一使用 @/api/request 中提供的请求方法

  - 请求方法使用规范:
    ```typescript
    // 导入方法
    import { GET, POST, POST_FORM, POST_JSON, POST_MULTIPART } from '@/api/request';

    // API 定义示例 (@/api/[module]/index.ts)
    interface UserData {
      name: string;
      role: string;
    }

    export const getUserInfo = () => {
      return GET<UserData>('/user/info');
    };

    // 业务代码使用示例 (@/views/[module]/index.vue)
    async function initData() {
      const [err, res] = await getUserInfo();

      if (err) {
        // 错误优先处理
        console.error('获取用户信息失败:', err.message);
        return;
      }

      // 处理业务逻辑
      const { data } = res;
      console.log(data?.name);
    }
    ```

  - 不同内容类型的请求规范:
    - 表单数据 (application/x-www-form-urlencoded):
      ```typescript
      function loginUser(username: string, password: string) {
        return POST_FORM('/api/login', { username, password });
      }
      ```

    - JSON数据 (application/json):
      ```typescript
      function createArticle(article: ArticleData) {
        return POST_JSON('/api/articles', article);
      }
      ```

    - 文件上传 (multipart/form-data):
      ```typescript
      function uploadAvatar(userId: string, file: File) {
        const formData = new FormData();
        formData.append('userId', userId);
        formData.append('avatar', file);
        return POST_MULTIPART('/api/upload/avatar', formData);
      }
      ```

  - [CRITICAL] 错误处理规范:
    - 禁止使用传统的 try-catch 方式
    - 必须使用 [err, res] 元组格式处理结果
    - 必须优先处理错误情况
    - 错误处理必须明确（不允许静默失败）

  - 类型定义规范:
    - 所有请求方法必须定义返回类型
    - 所有请求参数必须定义接口类型
    - 禁止使用 any 类型
    - 响应数据必须符合后端接口文档定义

  - 目录组织规范:
    - API 定义文件必须放在 @/api/[module] 目录下
    - 每个模块的接口必须单独成文件

  - 代码风格规范:
    - 函数名必须语义化，表明其用途
    - 必须添加适当的中文注释说明接口用途
    - 必须注明请求方法、URL 和参数说明
    - 必须标注返回值类型和结构
