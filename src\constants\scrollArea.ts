/**
 * FaScrollArea 组件的公用样式配置
 *
 * 使用说明：
 * 1. 基础用法 - 直接使用预定义的样式常量：
 *    ```typescript
 *    import {
 *      DEFAULT_SCROLL_BAR_STYLE,
 *      DEFAULT_SCROLL_THUMB_STYLE,
 *      WORK_LIST_SCROLL_THUMB_STYLE,
 *      SCRIPT_MODAL_SCROLL_THUMB_STYLE
 *    } from '@/constants/scrollArea';
 *
 *    // 在组件中使用
 *    const barStyle = DEFAULT_SCROLL_BAR_STYLE;
 *    const thumbStyle = DEFAULT_SCROLL_THUMB_STYLE;
 *    // 或特定场景的样式
 *    const workListThumbStyle = WORK_LIST_SCROLL_THUMB_STYLE;
 *    const modalThumbStyle = SCRIPT_MODAL_SCROLL_THUMB_STYLE;
 *    ```
 *
 * 2. 便捷用法 - 使用参数化工具函数（推荐）：
 *    ```typescript
 *    import { getDefaultScrollAreaStyles } from '@/constants/scrollArea';
 *
 *    // 通用场景
 *    const { barStyle, thumbStyle } = getDefaultScrollAreaStyles();
 *
 *    // 作品列表场景 - 自定义滑块位置
 *    const workListStyles = getDefaultScrollAreaStyles({
 *      thumbStyle: { right: '-11px' }
 *    });
 *
 *    // 示例脚本弹窗场景 - 自定义滑块位置和颜色
 *    const modalStyles = getDefaultScrollAreaStyles({
 *      thumbStyle: { right: '0px', backgroundColor: '#bbb' }
 *    });
 *    ```
 *
 * 3. 在Vue模板中使用：
 *    ```vue
 *    <template>
 *      <FaScrollArea
 *        :thumb-style="thumbStyle"
 *        :bar-style="barStyle"
 *      >
 *        <!-- 内容 -->
 *      </FaScrollArea>
 *    </template>
 *    ```
 *
 * 4. 自定义样式（基于默认样式扩展）：
 *    ```typescript
 *    import { getDefaultScrollAreaStyles } from '@/constants/scrollArea';
 *
 *    // 方式一：使用参数化函数（推荐）
 *    const { barStyle, thumbStyle } = getDefaultScrollAreaStyles({
 *      thumbStyle: {
 *        backgroundColor: '#007bff', // 自定义颜色
 *        width: '8px',               // 自定义宽度
 *      }
 *    });
 *
 *    // 方式二：直接使用常量扩展
 *    import { DEFAULT_SCROLL_THUMB_STYLE } from '@/constants/scrollArea';
 *    const customThumbStyle = {
 *      ...DEFAULT_SCROLL_THUMB_STYLE,
 *      backgroundColor: '#007bff',
 *      width: '8px',
 *    };
 *    ```
 */

/**
 * 滚动条轨道样式配置
 * 将滚动条轨道设置为透明且不可见
 */
export const DEFAULT_SCROLL_BAR_STYLE = {
  right: '0',
  borderRadius: '0px',
  backgroundColor: 'transparent',
  width: '0px',
  opacity: 0,
} as const;

export const DEFAULT_SCROLL_THUMB_STYLE = {
  borderRadius: '3px',
  backgroundColor: '#d9d9d9',
  width: '6px',
  opacity: 0.75,
} as const;

/**
 * 作品列表滚动条滑块样式配置
 * 设置滑块的位置、大小、颜色和透明度
 */
export const WORK_LIST_SCROLL_THUMB_STYLE = {
  ...DEFAULT_SCROLL_THUMB_STYLE,
  right: '-11px',
} as const;

/**
 * 示例脚本弹窗滚动条滑块样式配置
 * 设置滑块的位置、大小、颜色和透明度
 */
export const SCRIPT_MODAL_SCROLL_THUMB_STYLE = {
  ...DEFAULT_SCROLL_THUMB_STYLE,
  right: '0px',
  backgroundColor: '#bbb',
} as const;

/**
 * 滚动区域样式配置类型定义
 */
export type ScrollAreaStyleType = { [key: string]: string | number };

/**
 * 滚动区域样式配置选项接口
 */
export interface ScrollAreaStyleOptions {
  /** 自定义滚动条轨道样式，会与默认样式合并 */
  barStyle?: Partial<ScrollAreaStyleType>;
  /** 自定义滚动条滑块样式，会与默认样式合并 */
  thumbStyle?: Partial<ScrollAreaStyleType>;
}

/**
 * 获取滚动区域样式配置
 * 支持自定义样式覆盖，保持向后兼容性
 *
 * @param options 可选的样式覆盖配置
 * @param options.barStyle 自定义滚动条轨道样式，会与默认样式合并
 * @param options.thumbStyle 自定义滚动条滑块样式，会与默认样式合并
 * @returns 包含barStyle和thumbStyle的配置对象
 *
 * @example
 * // 基础用法（向后兼容）
 * const styles = getDefaultScrollAreaStyles();
 *
 * @example
 * // 自定义滑块位置
 * const workListStyles = getDefaultScrollAreaStyles({
 *   thumbStyle: { right: '-11px' }
 * });
 *
 * @example
 * // 自定义滑块颜色和位置
 * const modalStyles = getDefaultScrollAreaStyles({
 *   thumbStyle: { right: '0px', backgroundColor: '#bbb' }
 * });
 */
export function getDefaultScrollAreaStyles(options?: ScrollAreaStyleOptions) {
  const { barStyle: customBarStyle, thumbStyle: customThumbStyle } =
    options || {};

  return {
    barStyle: {
      ...DEFAULT_SCROLL_BAR_STYLE,
      ...customBarStyle,
    },
    thumbStyle: {
      ...DEFAULT_SCROLL_THUMB_STYLE,
      ...customThumbStyle,
    },
  };
}
