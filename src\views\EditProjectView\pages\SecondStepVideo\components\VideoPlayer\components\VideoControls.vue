<template>
  <div class="video-controls">
    <div class="video-controls__container">
      <!-- 播放按钮 -->
      <button
        class="video-controls__play-btn"
        :class="{ disabled: isDisabled }"
        @click="$emit('toggle-play')"
        :disabled="isDisabled"
      >
        <Icon v-if="!isPlaying" type="bofang" class="w-[24px] h-[28px]" />
        <Icon v-else type="zanting" class="w-[24px] h-[28px]" />
      </button>

      <!-- 进度条区域 -->
      <div
        class="video-controls__progress-wrapper"
        ref="progressRef"
        @mousedown="handleProgressMouseDown"
        :class="{ disabled: isDisabled }"
      >
        <div class="video-controls__progress">
          <!-- 进度条背景 -->
          <div
            class="video-controls__progress-bar"
            :style="{ width: `${progress}%` }"
          />
          <!-- 加载动画条 - 哔哩哔哩风格效果 -->
          <div
            v-if="isLoading"
            class="video-controls__progress-loading video-controls__progress-bilibili"
          ></div>
          <!-- 拖拽时的悬浮提示 -->
          <div
            v-if="isDragging"
            class="video-controls__progress-hover"
            :style="{ left: `${hoverPosition}%` }"
          >
            {{ formatTime(hoverTime) }}
          </div>
        </div>
      </div>

      <!-- 时间显示 -->
      <div class="video-controls__time" :class="{ disabled: isDisabled }">
        {{ formatTime(currentTime) }}
        <fa-divider type="vertical" class="text-[#E8E8E8]" />
        {{ formatTime(duration) }}
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, computed, ref, onUnmounted } from 'vue';

export default defineComponent({
  name: 'VideoControls',
  props: {
    /** 当前播放时间（秒） */
    currentTime: {
      type: Number,
      required: true,
    },
    /** 视频总时长（秒） */
    duration: {
      type: Number,
      required: true,
    },
    /** 是否正在播放 */
    isPlaying: {
      type: Boolean,
      required: true,
    },
    /** 是否禁用 */
    isDisabled: {
      type: Boolean,
      default: false,
    },
    /** 是否正在加载 */
    isLoading: {
      type: Boolean,
      default: false,
    },
  },
  emits: ['toggle-play', 'seek', 'seeking', 'drag-start', 'drag-end'],
  setup(props, { emit }) {
    const progressRef = ref<HTMLElement | null>(null);
    const isDragging = ref(false);
    const hoverPosition = ref(0);
    const hoverTime = ref(0);

    // 拖动时的本地进度状态
    const localProgress = ref(0);

    // 存储事件监听器的引用，用于清理
    let mouseMoveHandler: ((event: MouseEvent) => void) | null = null;
    let mouseUpHandler: ((event: MouseEvent) => void) | null = null;

    /** 计算播放进度百分比 */
    const progress = computed(() => {
      if (props.duration === 0) return 0;

      // 拖动期间使用本地进度，避免被timeupdate事件干扰
      if (isDragging.value) {
        return localProgress.value;
      }

      // 非拖动期间使用播放器的实际进度
      return (props.currentTime / props.duration) * 100;
    });

    /** 计算鼠标位置对应的时间和百分比 */
    const calculatePositionFromEvent = (event: MouseEvent) => {
      if (!progressRef.value || props.isDisabled)
        return { time: 0, percentage: 0 };

      const rect = progressRef.value.getBoundingClientRect();
      const position = event.clientX - rect.left;
      const percentage = Math.max(0, Math.min(1, position / rect.width));
      const time = percentage * props.duration;

      return { time, percentage };
    };

    // 节流控制
    let lastUpdateTime = 0;
    const THROTTLE_DELAY = 16; // 约60fps

    /** 更新拖拽时的悬浮显示 */
    const updateHoverDisplay = (percentage: number, time: number) => {
      hoverPosition.value = percentage * 100;
      hoverTime.value = time;

      // 更新本地进度状态
      localProgress.value = percentage * 100;

      emit('seeking', time);
    };

    /** 处理鼠标移动事件（全局监听） */
    const handleMouseMove = (event: MouseEvent) => {
      if (!isDragging.value || props.isDisabled) return;

      // 节流处理，避免过于频繁的更新
      const now = Date.now();
      if (now - lastUpdateTime < THROTTLE_DELAY) {
        return;
      }
      lastUpdateTime = now;

      const { time, percentage } = calculatePositionFromEvent(event);
      updateHoverDisplay(percentage, time);

      // 防止拖拽时选中文本
      event.preventDefault();
    };

    /** 处理鼠标抬起事件（全局监听） */
    const handleMouseUp = (event: MouseEvent) => {
      if (!isDragging.value || props.isDisabled) return;

      const { time, percentage } = calculatePositionFromEvent(event);

      // 更新最终的本地进度状态
      localProgress.value = percentage * 100;

      // 发送最终的seek事件
      emit('seek', time);

      // 结束拖拽状态
      isDragging.value = false;

      // 通知父组件拖动结束
      emit('drag-end');

      // 恢复文本选择
      document.body.style.userSelect = '';

      // 移除全局事件监听器
      removeGlobalListeners();

      // 重置节流计时器
      lastUpdateTime = 0;
    };

    /** 添加全局事件监听器 */
    const addGlobalListeners = () => {
      mouseMoveHandler = handleMouseMove;
      mouseUpHandler = handleMouseUp;

      document.addEventListener('mousemove', mouseMoveHandler, {
        passive: false,
      });
      document.addEventListener('mouseup', mouseUpHandler);
    };

    /** 移除全局事件监听器 */
    const removeGlobalListeners = () => {
      if (mouseMoveHandler) {
        document.removeEventListener('mousemove', mouseMoveHandler);
        mouseMoveHandler = null;
      }
      if (mouseUpHandler) {
        document.removeEventListener('mouseup', mouseUpHandler);
        mouseUpHandler = null;
      }
    };

    /** 处理进度条鼠标按下事件 */
    const handleProgressMouseDown = (event: MouseEvent) => {
      if (props.isDisabled) return;

      const { time, percentage } = calculatePositionFromEvent(event);

      // 开始拖拽状态
      isDragging.value = true;

      // 通知父组件拖动开始
      emit('drag-start');

      // 初始化本地进度状态
      localProgress.value = percentage * 100;

      // 防止拖拽时选中文本
      document.body.style.userSelect = 'none';

      // 更新悬浮显示
      updateHoverDisplay(percentage, time);

      // 添加全局事件监听器
      addGlobalListeners();

      // 重置节流计时器
      lastUpdateTime = 0;

      // 防止事件冒泡
      event.preventDefault();
    };

    /** 格式化时间为 mm:ss 格式 */
    const formatTime = (seconds: number) => {
      const minutes = Math.floor(seconds / 60);
      const remainingSeconds = Math.floor(seconds % 60);
      return `${minutes.toString().padStart(2, '0')}:${remainingSeconds
        .toString()
        .padStart(2, '0')}`;
    };

    // 组件卸载时清理事件监听器
    onUnmounted(() => {
      removeGlobalListeners();
      // 恢复文本选择
      document.body.style.userSelect = '';
      // 重置拖动状态
      isDragging.value = false;
      lastUpdateTime = 0;
    });

    return {
      progressRef,
      progress,
      formatTime,
      isDragging,
      hoverPosition,
      hoverTime,
      handleProgressMouseDown,
    };
  },
});
</script>

<style lang="scss" scoped>
.video-controls {
  /* 布局相关 */
  @apply zi-video-controls;
  /* 尺寸相关 */
  @apply px-0 pt-24px;
  /* 外观相关 */
  @apply bg-transparent;
}

.video-controls__container {
  /* 布局相关 */
  @apply flex items-center;
  /* 尺寸相关 */
  @apply gap-16px;
}

.video-controls__play-btn {
  /* 布局相关 */
  @apply flex-shrink-0;
  /* 外观相关 */
  @apply bg-transparent border-none text-black cursor-pointer transition-opacity disabled:opacity-50 disabled:cursor-not-allowed;

  &.disabled {
    /* 外观相关 */
    @apply text-[#bfbfbf];
  }
}

.video-controls__play-btn:not(:disabled) {
  /* 外观相关 */
  @apply hover:opacity-80;
}

.video-controls__progress-wrapper {
  /* 布局相关 */
  @apply flex-1;
}

.video-controls__progress-wrapper.disabled {
  /* 外观相关 */
  @apply cursor-not-allowed;
}

.video-controls__progress {
  /* 布局相关 */
  @apply relative;
  /* 尺寸相关 */
  @apply h-8px;
  /* 外观相关 */
  @apply cursor-pointer border-rd-4px overflow-hidden;
  background-color: #e8e8e8;
}

.disabled .video-controls__progress {
  /* 外观相关 */
  @apply cursor-not-allowed opacity-50;
}

.video-controls__progress-bar {
  /* 尺寸相关 */
  @apply h-full;
  /* 外观相关 */
  @apply border-rd-4px bg-gradient-linear bg-gradient-[92.19deg,#105fff_0%,#7923f9_100%] transition="width duration-0.1s linear";
}

.video-controls__progress-loading {
  /* 布局相关 */
  @apply absolute top-0 left-0;
  /* 尺寸相关 */
  @apply h-full w-full;
  /* 外观相关 */
  @apply border-rd-4px;
}

.video-controls__progress-bilibili {
  /* 进度条渐变色光波背景 - 从左到右 */
  background: linear-gradient(
    90deg,
    transparent 0%,
    rgba(16, 95, 255, 0.6) 20%,
    rgba(121, 35, 249, 0.8) 30%,
    rgba(16, 95, 255, 0.6) 40%,
    transparent 50%,
    transparent 100%
  );
  background-size: 400% 100%;
  background-position: -50% 0%;
  /* 动画相关 */
  animation: progress-bilibili-wave 2.5s linear infinite;
}

.video-controls__progress-hover {
  /* 布局相关 */
  @apply absolute bottom-full transform -translate-x-1/2 text-center;
  /* 尺寸相关 */
  @apply mb-2 px-2 py-1 min-w-40px;
  /* 外观相关 */
  @apply bg-black/80 rounded;
  /* 文字相关 */
  @apply text-white text-xs;
  display: none;
}

.video-controls__time {
  /* 布局相关 */
  @apply flex-shrink-0 text-right;
  /* 文字相关 */
  @apply font-400 text-14px text-[#999];
}

.video-controls__time.disabled {
  /* 外观相关 */
  @apply text-[#999];
}

/* 进度条光波扫描动画关键帧 - 明确从左到右 */
@keyframes progress-bilibili-wave {
  0% {
    background-position: -50% 0%;
  }
  100% {
    background-position: 150% 0%;
  }
}
</style>
