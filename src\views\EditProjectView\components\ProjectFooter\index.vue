<template>
  <footer class="project-footer">
    <!-- 返回上一步按钮 -->
    <fa-button
      v-if="currentStep === SECOND_STEP_INDEX && !shouldHidePrevButton"
      class="project-footer__prev-button"
      :disabled="shouldHidePrevButton"
      @click="onPrevStep"
    >
      <span>返回上一步</span>
    </fa-button>

    <!-- 第一步显示生成按钮和点数显示 -->
    <template v-if="currentStep === FIRST_STEP_INDEX">
      <!-- GenerateButton 包装容器，确保按钮居中 -->
      <div class="project-footer__generate-wrapper">
        <!-- GenerateButton 相对定位容器，作为UserPointDisplay的定位参考 -->
        <div class="project-footer__generate-container">
          <GenerateButton
            :num.sync="internalNum"
            :integral="integral"
            :show-integral="showIntegral"
            :loading="loading"
            :min-count="minCount"
            :max-count="maxCount"
            @click="onGenerateClick"
          />
          <UserPointDisplay
            class="project-footer__point-display"
            :point="userPoint"
          />
        </div>
      </div>
    </template>

    <!-- 第二步显示保存至我的作品按钮 -->
    <fa-button
      v-if="currentStep === SECOND_STEP_INDEX"
      class="project-footer__save-button btn-special"
      :class="{
        'btn-special-disabled': shouldDisableSaveButton,
      }"
      type="default"
      :disabled="shouldDisableSaveButton"
      @click="onSaveToWorks"
    >
      保存至我的作品
    </fa-button>
  </footer>
</template>

<script lang="ts">
import { defineComponent, computed } from 'vue';
import GenerateButton from '@/views/EditProjectView/components/GenerateButton/index.vue';
import UserPointDisplay from '@/views/EditProjectView/components/UserPointDisplay/index.vue';
import {
  FIRST_STEP_INDEX,
  SECOND_STEP_INDEX,
} from '@/views/EditProjectView/constants/index';
import {
  shouldDisableEditByProjectId,
  shouldRestrictProjectAccessByProjectId,
} from '@/views/EditProjectView/utils/projectStatus';
import {
  getWorkStatusInfo,
  isGeneratingWork,
  isRegeneratingWork,
  isAnyFailedWork,
} from '@/constants/workStatus';

/**
 * 作品状态检查所需的最小接口
 */
interface WorkStatusCheckItem {
  id: number;
  status: number;
  editAgain?: boolean;
  saveTime?: string;
}

/**
 * @description 项目视图底部组件
 */
export default defineComponent({
  name: 'ProjectFooter',
  components: { GenerateButton, UserPointDisplay },
  props: {
    /**
     * 生成数量 (支持 .sync)
     */
    num: {
      type: Number,
      required: true,
    },
    /**
     * 单次生成消耗积分
     */
    integral: {
      type: Number,
      required: true,
    },
    /**
     * 是否显示积分
     */
    showIntegral: {
      type: Boolean,
      default: true,
    },
    /**
     * 生成按钮加载状态
     */
    loading: {
      type: Boolean,
      default: false,
    },
    /**
     * 用户积分
     */
    userPoint: {
      type: [Number, String],
      required: true,
    },
    /**
     * 当前步骤索引
     */
    currentStep: {
      type: Number,
      required: true,
    },
    /**
     * 项目ID
     */
    projectId: {
      type: Number,
      default: 0,
    },
    /**
     * 最小生成数量
     */
    minCount: {
      type: Number,
      default: 1,
    },
    /**
     * 最大生成数量
     */
    maxCount: {
      type: Number,
      default: 10,
    },
    /**
     * 选中的作品ID列表
     */
    selectedWorkIds: {
      type: Array,
      default: () => [],
    },
    /**
     * 是否全选
     */
    isAllSelected: {
      type: Boolean,
      default: false,
    },
    /**
     * 作品列表
     */
    workList: {
      type: Array as () => WorkStatusCheckItem[],
      default: () => [],
    },
  },
  setup(props, { emit }) {
    /**
     * 内部生成数量
     */
    const internalNum = computed({
      get: () => props.num,
      set: (value: number) => {
        emit('update:num', value);
      },
    });

    /**
     * 是否应该禁用返回上一步按钮
     */
    const shouldDisablePrevButton = computed(() => {
      if (!props.projectId) {
        return false;
      }
      return shouldDisableEditByProjectId(String(props.projectId));
    });

    /**
     * 是否应该隐藏返回上一步按钮
     */
    const shouldHidePrevButton = computed(() => {
      if (!props.projectId) {
        return false;
      }
      return shouldRestrictProjectAccessByProjectId(String(props.projectId));
    });

    /**
     * 检查选中作品是否包含不可保存的状态
     * 包括：生成中、重新生成中、生成失败、重新生成失败、已保存
     */
    const shouldDisableSaveButton = computed(() => {
      // 如果没有选中任何作品且不是全选状态，则禁用按钮
      if (!props.selectedWorkIds?.length && !props.isAllSelected) {
        return true;
      }

      /**
       * 检查单个作品是否不可保存
       * @param work 作品项
       * @returns 是否不可保存
       */
      const isWorkUnsavable = (work: WorkStatusCheckItem): boolean => {
        const statusInfo = getWorkStatusInfo(work);
        const isSaved = Boolean(work.saveTime);

        return (
          isGeneratingWork(statusInfo) ||
          isRegeneratingWork(statusInfo) ||
          isAnyFailedWork(statusInfo) ||
          isSaved
        );
      };

      // 如果是全选状态，检查所有作品中是否有不可保存的状态
      if (props.isAllSelected) {
        return props.workList.some(isWorkUnsavable);
      }

      // 非全选状态，检查选中的作品中是否有不可保存的状态
      const selectedWorks = props.workList.filter((work: WorkStatusCheckItem) =>
        props.selectedWorkIds.includes(work.id),
      );

      return selectedWorks.some(isWorkUnsavable);
    });

    /**
     * 处理生成按钮点击事件
     */
    const onGenerateClick = () => {
      // 触发生成流程，但需要先进行表单校验
      emit('generate-click');
    };

    /**
     * 处理返回上一步按钮点击事件
     */
    const onPrevStep = () => {
      emit('step-change', FIRST_STEP_INDEX); // 返回第一步（索引为0）
    };

    /**
     * 处理保存至我的作品按钮点击事件
     */
    const onSaveToWorks = () => {
      emit('save-to-works');
    };

    return {
      FIRST_STEP_INDEX,
      SECOND_STEP_INDEX,
      internalNum,
      shouldDisablePrevButton,
      shouldHidePrevButton,
      shouldDisableSaveButton,
      onGenerateClick,
      onPrevStep,
      onSaveToWorks,
    };
  },
});
</script>

<style lang="scss" scoped>
.project-footer {
  /* 布局相关 */
  @apply flex items-center justify-center;

  /* 尺寸相关 */
  @apply h-64px mt-16px py-12px;

  /* 外观相关 */
  @apply bg-white;
}

.project-footer__prev-button {
  /* 布局相关 */
  @apply flex items-center justify-center;

  /* 尺寸相关 */
  @apply min-w-160px mr-16px;
}

.project-footer__generate-wrapper {
  /* 布局相关 - 确保GenerateButton在页面中完全居中 */
  @apply flex items-center justify-center;

  /* 尺寸相关 - 占据容器的完整宽度以实现居中 */
  @apply w-full;
}

.project-footer__generate-container {
  /* 布局相关 - 相对定位，作为UserPointDisplay的定位参考 */
  @apply relative flex items-center;
}

.project-footer__point-display {
  /* 布局相关 - 绝对定位，相对于GenerateButton容器定位 */
  @apply absolute flex items-center;

  /* 位置相关 - 垂直居中 */
  @apply left-full top-1/2 transform -translate-y-1/2;

  /* 尺寸相关 - 占据容器的完整宽度以实现居中 */
  @apply w-full w-max ml-24px;
}

.project-footer__save-button {
  /* 布局相关 */
  @apply flex items-center justify-center;

  /* 尺寸相关 */
  @apply min-w-240px;

  /* 文字相关 */
  @apply font-bold text-16px text-white text-center;
}
</style>
