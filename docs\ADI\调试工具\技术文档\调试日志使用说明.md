# 高级调试日志系统说明

## 🎨 日志系统特色

为了更好地调试和监控作品操作，我们实现了一套完整的高级日志系统，支持：

- 🎯 **分组日志**：使用 `console.groupCollapsed` 组织相关日志
- 📊 **表格输出**：使用 `console.table` 展示结构化数据
- 🎛️ **日志开关**：支持运行时动态控制日志级别和类别
- 🎨 **彩色输出**：不同类型的操作使用不同颜色和图标
- ⚡ **性能监控**：详细的耗时统计和性能指标

### 📋 日志图标和颜色含义

| 图标 | 颜色             | 含义     | 使用场景         |
| ---- | ---------------- | -------- | ---------------- |
| 🚀   | 蓝色 (#2196F3)   | 操作开始 | 方法开始执行     |
| ⚠️   | 橙色 (#FF9800)   | 警告跳过 | 参数验证失败     |
| 📋   | 紫色 (#9C27B0)   | 完整刷新 | 执行完整刷新操作 |
| 🗑️   | 深橙色 (#FF5722) | 清除缓存 | 清除详情缓存     |
| 📡   | 蓝灰色 (#607D8B) | 数据请求 | 获取更新数据     |
| 🔄   | 靛蓝色 (#3F51B5) | 数据合并 | 合并更新数据     |
| 🎯   | 粉色 (#E91E63)   | 详情刷新 | 刷新当前详情     |
| ✅   | 绿色 (#4CAF50)   | 操作成功 | 操作完成         |
| ❌   | 红色 (#F44336)   | 操作失败 | 错误或异常       |

## 🎛️ 日志控制台

### 快速开始

在浏览器控制台中输入以下命令来控制日志输出：

```javascript
// 查看帮助信息
loggerControl.help();

// 启用所有日志（开发调试时使用）
loggerControl.enableAll();

// 只查看作品操作日志（推荐日常使用）
loggerControl.onlyWorkOperations();

// 关闭所有日志（生产环境）
loggerControl.disableAll();
```

### 可用命令

| 命令 | 功能 | 使用场景 |
| --- | --- | --- |
| `loggerControl.enableAll()` | 启用所有日志 | 深度调试时查看所有细节 |
| `loggerControl.disableAll()` | 禁用所有日志 | 生产环境或不需要日志时 |
| `loggerControl.onlyWorkOperations()` | 只显示作品操作日志 | 日常开发，关注业务逻辑 |
| `loggerControl.onlyApiRequests()` | 只显示 API 请求日志 | 调试网络请求问题 |
| `loggerControl.onlyPerformance()` | 只显示性能监控 | 性能优化时使用 |
| `loggerControl.reset()` | 重置为默认配置 | 恢复初始设置 |
| `loggerControl.showConfig()` | 显示当前配置 | 查看当前日志设置 |

### 🔍 日志输出示例

> **💡 核心操作维度说明**：
>
> 日志现在只输出核心的业务操作维度，去除了冗余信息：
>
> - **列表数据更新方式**：局部更新、完整刷新、移除指定作品
> - **详情缓存清除范围**：不清除缓存、清除指定作品缓存、清空所有缓存
> - **是否修改选中作品**：是否会改变当前选中的作品
> - **是否重新获取详情数据**：是否会重新获取作品详情
>
> 这四个维度涵盖了所有作品操作的核心影响，便于快速理解操作效果。

#### 1. 分组日志 - 局部更新操作

```console
🚀 保存作品 开始
├─ 📊 核心操作维度
│  ┌─────────────────┬──────────────────┐
│  │ 列表数据更新方式  │ 局部更新          │
│  │ 详情缓存清除范围  │ 不清除缓存        │
│  │ 是否修改选中作品  │ 否               │
│  │ 是否重新获取详情数据│ 否             │
│  │ 作品数量        │ 1                │
│  └─────────────────┴──────────────────┘
└─ ✅ 保存作品 完成
   ┌─────────────────┬──────────────────┐
   │ 列表数据更新方式  │ 局部更新          │
   │ 详情缓存清除范围  │ 不清除缓存        │
   │ 是否修改选中作品  │ 否               │
   │ 是否重新获取详情数据│ 否             │
   │ 更新作品数量      │ 1                │
   │ 总耗时          │ 267.89ms         │
   └─────────────────┴──────────────────┘
```

#### 2. 分组日志 - 批量保存操作

```console
🚀 批量保存作品 开始
├─ 📊 核心操作维度
│  ┌─────────────────┬──────────────────┐
│  │ 列表数据更新方式  │ 完整刷新          │
│  │ 详情缓存清除范围  │ 清空所有缓存      │
│  │ 是否修改选中作品  │ 是（重置为第一个） │
│  │ 是否重新获取详情数据│ 否             │
│  │ 作品数量        │ 0                │
│  └─────────────────┴──────────────────┘
└─ ✅ 批量保存作品 完成
   ┌─────────────────┬──────────────────┐
   │ 列表数据更新方式  │ 完整刷新          │
   │ 详情缓存清除范围  │ 已清空所有缓存    │
   │ 是否修改选中作品  │ 是（重置为第一个） │
   │ 是否重新获取详情数据│ 否             │
   │ 更新后作品总数    │ 12               │
   │ 总耗时          │ 1234.56ms        │
   └─────────────────┴──────────────────┘
```

#### 3. 分组日志 - 删除作品操作

```console
🗑️ 删除作品 开始
├─ 📊 核心操作维度
│  ┌─────────────────┬──────────────────────┐
│  │ 列表数据更新方式  │ 移除指定作品          │
│  │ 详情缓存清除范围  │ 清除指定作品缓存      │
│  │ 是否修改选中作品  │ 是（智能选择下一个）   │
│  │ 是否重新获取详情数据│ 否                 │
│  │ 删除作品ID      │ 789                  │
│  └─────────────────┴──────────────────────┘
└─ ✅ 删除作品 完成
   ┌─────────────────┬──────────────────────┐
   │ 列表数据更新方式  │ 移除指定作品          │
   │ 详情缓存清除范围  │ 已清除指定作品缓存    │
   │ 是否修改选中作品  │ 是（智能选择下一个）   │
   │ 是否重新获取详情数据│ 否                 │
   │ 删除作品ID      │ 789                  │
   │ 剩余作品数      │ 14                   │
   │ 总耗时          │ 189.45ms             │
   └─────────────────┴──────────────────────┘
```

### 🛠️ 调试技巧

#### 1. 在浏览器控制台中过滤日志

```javascript
// 只显示作品操作相关日志
console.log = (function (originalLog) {
  return function (...args) {
    if (args[0] && args[0].includes('作品')) {
      originalLog.apply(console, args);
    }
  };
})(console.log);
```

#### 2. 监控特定操作

```javascript
// 监控批量保存操作
console.log = (function (originalLog) {
  return function (...args) {
    if (args[0] && args[0].includes('批量保存')) {
      originalLog.apply(console, args);
      // 可以在这里添加额外的调试逻辑
    }
  };
})(console.log);
```

### 📊 性能监控

日志中包含了详细的性能信息：

- **耗时统计**：每个操作的执行时间
- **数据量统计**：处理的作品数量
- **状态变化**：作品状态的变化情况

### 🔧 自定义日志级别

可以通过环境变量控制日志输出：

```javascript
// 在开发环境中启用详细日志
if (process.env.NODE_ENV === 'development') {
  // 显示所有日志
} else {
  // 只显示错误日志
}
```

## 🎯 实用调试场景

### 常见调试场景

#### 1. 调试作品保存问题

```javascript
// 只关注保存相关的日志
loggerControl.onlyWorkOperations();

// 然后执行保存操作，观察日志输出
// 重点关注：API耗时、缓存操作、状态变化
```

#### 2. 调试性能问题

```javascript
// 启用性能监控
loggerControl.onlyPerformance();

// 执行操作后查看耗时统计
// 关注：总耗时 > 1000ms 的操作需要优化
```

#### 3. 调试网络请求问题

```javascript
// 只显示API请求日志
loggerControl.onlyApiRequests();

// 观察请求参数和响应时间
// 检查是否有请求失败或超时
```

#### 4. 深度调试复杂问题

```javascript
// 启用所有日志
loggerControl.enableAll();

// 执行问题操作，获取完整的执行流程
// 分析每个步骤的执行情况
```

### 📊 日志分析要点

#### 🔍 关注指标

- **API 耗时**：单个请求 > 500ms 需要关注
- **总耗时**：完整操作 > 1000ms 需要优化
- **缓存命中**：检查缓存是否正确清除和更新
- **状态变化**：确认作品状态变化符合预期
- **列表状态**：对比操作前后的列表总数、页码、选中状态变化
- **智能选择**：删除操作后是否正确自动选择下一个作品

#### ⚠️ 常见问题

- **请求失败**：检查网络连接和 API 状态
- **缓存问题**：确认缓存清除时机是否正确
- **状态不一致**：检查前后端数据同步
- **性能瓶颈**：分析耗时分布，定位慢操作

### 📝 最佳实践

1. **开发阶段**：使用 `loggerControl.onlyWorkOperations()` 关注业务逻辑
2. **调试阶段**：使用 `loggerControl.enableAll()` 获取完整信息
3. **性能优化**：使用 `loggerControl.onlyPerformance()` 监控性能指标
4. **生产环境**：使用 `loggerControl.disableAll()` 关闭所有日志
5. **问题排查**：根据具体问题选择对应的日志类别
