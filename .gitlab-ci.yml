stages:
  - test_prepare
  - test_install
  - test_build
  - test_deploy
  - pre_prepare
  - pre_install
  - pre_build
  - pre_deploy

variables:
  NODE_ENV: production
  GIT_STRATEGY: fetch
  GIT_DEPTH: 1
  PROJECT_ID: "${CI_PROJECT_ID}"
  GITLAB_API_URL: "http://gitlab.faidev.cc"

  # 需要配置 GITLAB_API_TOKEN 用于自动取消旧Pipeline

  # Test 环境配置
  TEST_SSH_HOST: "ssh -p 50805 <EMAIL>"
  TEST_PROJECT_DIR: "~/git/web/scportal-web"
  TEST_DEP_SSH_HOST: "ssh -p 50805 <EMAIL>"
  TEST_DEP_PROJECT_DIR: "~/git/web/scportal-web"

  # Pre 环境配置
  PRE_SSH_HOST: "ssh -p 50805 <EMAIL>"
  PRE_PROJECT_DIR: "~/git/web/scportal-web"

# 通用函数定义
.common_functions: &common_functions |
  # Pipeline 管理函数
  cancel_old_pipelines() {
    echo "检查并取消同分支的旧Pipeline..."

    if [ -n "$GITLAB_API_TOKEN" ]; then
      # 查询running和pending状态的Pipeline
      local response=$(curl -s --header "PRIVATE-TOKEN: $GITLAB_API_TOKEN" \
        "$GITLAB_API_URL/api/v4/projects/$PROJECT_ID/pipelines?ref=$CI_COMMIT_REF_NAME&per_page=10" || echo "[]")

      # 提取Pipeline ID和状态
      local all_pipelines=$(echo "$response" | grep -o '{"id":[0-9]*[^}]*}' || true)
      local pipelines_to_cancel=""

      while IFS= read -r pipeline_entry; do
        if [ -n "$pipeline_entry" ]; then
          local pipeline_id=$(echo "$pipeline_entry" | grep -o '"id":[0-9]*' | grep -o '[0-9]*')
          local status=$(echo "$pipeline_entry" | grep -o '"status":"[^"]*' | cut -d'"' -f4)

          if [ "$status" = "running" ] || [ "$status" = "pending" ]; then
            pipelines_to_cancel="$pipelines_to_cancel $pipeline_id"
          fi
        fi
      done <<< "$all_pipelines"

      # 取消旧Pipeline
      for pipeline_id in $pipelines_to_cancel; do
        if [ -n "$pipeline_id" ] && [ "$pipeline_id" != "$CI_PIPELINE_ID" ]; then
          echo "取消旧Pipeline: $pipeline_id"
          curl -s --request POST --header "PRIVATE-TOKEN: $GITLAB_API_TOKEN" \
            "$GITLAB_API_URL/api/v4/projects/$PROJECT_ID/pipelines/$pipeline_id/cancel" > /dev/null || true
        fi
      done
    else
      echo "警告: 未配置 GITLAB_API_TOKEN，无法自动取消旧Pipeline"
    fi
  }

  # 通用进程清理函数
  cleanup_processes() {
    echo "清理构建进程..."
    $SSH_HOST "pkill -f 'pnpm run build' 2>/dev/null || true" || true
    $SSH_HOST "sleep 2" || true
    $SSH_HOST "pkill -9 -f 'pnpm.*build' 2>/dev/null || true" || true
    $SSH_HOST "pkill -9 -f 'vue-tsc' 2>/dev/null || true" || true
    $SSH_HOST "pkill -9 -f 'vite build' 2>/dev/null || true" || true
    $SSH_HOST "pkill -9 -f 'esbuild' 2>/dev/null || true" || true
    echo "进程清理完成"
  }

# 脚本模板
.test_template: &test_template
  script:
    - *common_functions
    - |
      $TEST_SSH_HOST << EOF
      cd $TEST_PROJECT_DIR
      nvm use 20
      $COMMAND
      EOF

.test_prepare_template: &test_prepare_template
  script:
    - *common_functions
    - cancel_old_pipelines
    - |
      export SSH_HOST="$TEST_SSH_HOST"
      cleanup_processes
      $TEST_SSH_HOST << EOF
      cd $TEST_PROJECT_DIR
      nvm use 20
      $COMMAND
      EOF

.test_dep_template: &test_dep_template
  script:
    - *common_functions
    - |
      $TEST_DEP_SSH_HOST << EOF
      cd $TEST_DEP_PROJECT_DIR
      nvm use 20
      $COMMAND
      EOF

.test_dep_prepare_template: &test_dep_prepare_template
  script:
    - *common_functions
    - cancel_old_pipelines
    - |
      export SSH_HOST="$TEST_DEP_SSH_HOST"
      cleanup_processes
      $TEST_DEP_SSH_HOST << EOF
      cd $TEST_DEP_PROJECT_DIR
      nvm use 20
      $COMMAND
      EOF

.pre_template: &pre_template
  script:
    - *common_functions
    - |
      $PRE_SSH_HOST << EOF
      cd $PRE_PROJECT_DIR
      nvm use 20
      $COMMAND
      EOF

.pre_prepare_template: &pre_prepare_template
  script:
    - *common_functions
    - cancel_old_pipelines
    - |
      export SSH_HOST="$PRE_SSH_HOST"
      cleanup_processes
      $PRE_SSH_HOST << EOF
      cd $PRE_PROJECT_DIR
      nvm use 20
      $COMMAND
      EOF

# Test环境部署任务
test_prepare:
  <<: *test_prepare_template
  stage: test_prepare
  only:
    refs:
      - test
  variables:
    COMMAND: "git checkout -- . && git pull"

test_install:
  <<: *test_template
  stage: test_install
  only:
    refs:
      - test
  variables:
    COMMAND: "pnpm install --frozen-lockfile"

test_build:
  <<: *test_template
  stage: test_build
  only:
    refs:
      - test
  variables:
    COMMAND: "pnpm run build"

test_deploy:
  <<: *test_template
  stage: test_deploy
  only:
    refs:
      - test
  variables:
    COMMAND: "pnpm run after:build"
  environment:
    name: test
    url: http://i.sc.fff.com/

# Test依赖环境部署任务
test_dep_prepare:
  <<: *test_dep_prepare_template
  stage: test_prepare
  only:
    refs:
      - test
  variables:
    COMMAND: "git checkout -- . && git pull"

test_dep_install:
  <<: *test_dep_template
  stage: test_install
  only:
    refs:
      - test
  variables:
    COMMAND: "pnpm install --frozen-lockfile"

test_dep_build:
  <<: *test_dep_template
  stage: test_build
  only:
    refs:
      - test
  variables:
    COMMAND: "pnpm run build"

test_dep_deploy:
  <<: *test_dep_template
  stage: test_deploy
  only:
    refs:
      - test
  variables:
    COMMAND: "pnpm run after:build"
  environment:
    name: test-dep
    url: http://i.sc.fkw.com.faidev.cc/

# Pre环境部署任务
pre_prepare:
  <<: *pre_prepare_template
  stage: pre_prepare
  only:
    refs:
      - pre-production
  variables:
    COMMAND: "git checkout -- . && git pull"

pre_install:
  <<: *pre_template
  stage: pre_install
  only:
    refs:
      - pre-production
  variables:
    COMMAND: "pnpm install --frozen-lockfile"

pre_build:
  <<: *pre_template
  stage: pre_build
  only:
    refs:
      - pre-production
  variables:
    COMMAND: "pnpm run build"

pre_deploy:
  <<: *pre_template
  stage: pre_deploy
  only:
    refs:
      - pre-production
  variables:
    COMMAND: "pnpm run after:build"
  environment:
    name: pre-production
    url: http://i.sc.fkw.com.faidev.cc/
