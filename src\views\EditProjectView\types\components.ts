/**
 * @fileoverview 项目编辑视图中与Vue组件通信和状态管理相关的类型定义。
 * @description 此文件定义了组件标识、组件实例的预期结构（Shape）、组件引用以及组合式API（Composables）的返回值类型。
 * <AUTHOR>
 * @since 1.0.0
 */

import type { FieldType } from './base';
import type { BaseFormRef, BaseFormItem, ValidationResult } from './form';
import type { BgMusicSetting, VoiceSetting } from './media';

// ============== 组件相关类型 ==============

/**
 * @description 定义项目编辑视图中可能存在的步骤组件的类型标识。
 * @typedef {'FirstStep' | 'SecondStepVideo' | 'SecondStepImage'} ComponentType
 * - `FirstStep`: 第一步，素材上传与配置组件。
 * - `SecondStepVideo`: 第二步，视频预览与编辑组件。
 * - `SecondStepImage`: 第二步，图文预览与编辑组件。
 */
export type ComponentType = 'FirstStep' | 'SecondStepVideo' | 'SecondStepImage';

/**
 * @description 定义 `FirstStep` 组件实例预期暴露的属性和方法结构 (Shape)。
 * @interface FirstStepComponentShape
 * @property {$refs} $refs - 组件内部的模板引用集合。
 * @property {BaseFormItem[]} [formItems] - 基础表单项配置数组。
 * @property {BaseFormItem[]} [fileFormItems] - 文件表单项配置数组。
 * @property {(type: string | number) => FieldType} [mapComponentTypeToFieldType] - 将组件内部类型映射到通用字段类型的函数。
 * @property {boolean} [hasExampleScript] - 是否存在示例脚本。
 * @property {string} [exampleScriptContent] - 示例脚本内容。
 * @property {() => Promise<ValidationResult>} [validateAllForms] - 验证所有内部表单的方法。
 * @property {() => { basicFormData: Record<string, unknown>; filesFormData: Record<string, unknown>; bgMusic?: BgMusicSetting; voice?: VoiceSetting; }} [getAllFormData] - 获取所有表单数据，包括背景音乐和配音设置。
 */
export interface FirstStepComponentShape {
  /**
   * 组件内部模板引用的集合。
   * @type {{ dynamicForm?: BaseFormRef; dynamicFormFiles?: BaseFormRef; }}
   * @memberof FirstStepComponentShape
   */
  $refs: {
    /** 基础表单（如文本输入、选择等）的引用 */
    dynamicForm?: BaseFormRef;
    /** 文件上传表单的引用 */
    dynamicFormFiles?: BaseFormRef;
  };
  /**
   * 基础表单项的配置数组。
   * @type {BaseFormItem[]}
   * @memberof FirstStepComponentShape
   * @optional
   */
  formItems?: BaseFormItem[];
  /**
   * 文件表单项的配置数组。
   * @type {BaseFormItem[]}
   * @memberof FirstStepComponentShape
   * @optional
   */
  fileFormItems?: BaseFormItem[];
  /**
   * 将组件内部使用的特定类型标识映射到标准 {@link FieldType} 的函数。
   * @param {string | number} type - 组件内部类型标识。
   * @returns {FieldType} 对应的标准字段类型。
   * @memberof FirstStepComponentShape
   * @optional
   */
  mapComponentTypeToFieldType?: (type: string | number) => FieldType;
  /**
   * 指示当前是否存在示例脚本。
   * @type {boolean}
   * @memberof FirstStepComponentShape
   * @optional
   */
  hasExampleScript?: boolean;
  /**
   * 示例脚本的文本内容。
   * @type {string}
   * @memberof FirstStepComponentShape
   * @optional
   */
  exampleScriptContent?: string;
  /**
   * 异步验证组件内部所有表单的方法（生成模式：完整校验）。
   * @returns {Promise<ValidationResult>} 返回包含验证结果的Promise。
   * @memberof FirstStepComponentShape
   * @see {@link ValidationResult} - 定义于 `./form.ts`
   * @optional
   */
  validateAllForms?: () => Promise<ValidationResult>;
  /**
   * 异步验证组件内部所有表单的方法（保存模式：仅最大长度校验）。
   * @returns {Promise<ValidationResult>} 返回包含验证结果的Promise。
   * @memberof FirstStepComponentShape
   * @see {@link ValidationResult} - 定义于 `./form.ts`
   * @optional
   */
  validateAllFormsForSave?: () => Promise<ValidationResult>;
  /**
   * 获取组件内部所有表单数据，并聚合背景音乐和配音设置。
   * @returns {{ basicFormData: Record<string, unknown>; filesFormData: Record<string, unknown>; bgMusic?: BgMusicSetting; voice?: VoiceSetting; }}
   * @memberof FirstStepComponentShape
   * @optional
   */
  getAllFormData?: () => {
    /** 基础表单的键值对数据 */
    basicFormData: Record<string, unknown>;
    /** 文件表单的键值对数据 */
    filesFormData: Record<string, unknown>;
    /** 当前背景音乐设置 */
    bgMusic?: BgMusicSetting;
    /** 当前配音设置 */
    voice?: VoiceSetting;
  };
  /**
   * 是否有未保存的更改。
   * @type {{ value: boolean }}
   * @memberof FirstStepComponentShape
   * @optional
   */
  hasUnsavedChanges?: { value: boolean };
  /**
   * 重置未保存状态的方法。
   * @returns {void}
   * @memberof FirstStepComponentShape
   * @optional
   */
  resetUnsavedChanges?: () => void;
  /**
   * 手动设置未保存状态的方法。
   * @param {boolean} value - 是否有未保存的更改
   * @returns {void}
   * @memberof FirstStepComponentShape
   * @optional
   */
  setUnsavedChanges?: (value: boolean) => void;
  /**
   * 重新激活数据保护功能的方法（KeepAlive 场景）。
   * @returns {void}
   * @memberof FirstStepComponentShape
   * @optional
   */
  reactivateUnsavedChanges?: () => void;
  /**
   * 允许组件暴露其他自定义属性和方法。
   * @type {unknown}
   * @memberof FirstStepComponentShape
   */
  [key: string]: unknown;
}

/**
 * @description 通用组件引用接口，定义了组件可能暴露的基础方法。
 * @interface ComponentRef
 */
export interface ComponentRef {
  /**
   * 异步验证组件内部所有表单的方法。
   * @returns {Promise<ValidationResult>} 返回包含验证结果的Promise。
   * @memberof ComponentRef
   * @optional
   */
  validateAllForms?: () => Promise<ValidationResult>;
  /**
   * 作品列表数据（用于检查作品状态）。
   * @type {Array<{ id: number; status: number; [key: string]: unknown }>}
   * @memberof ComponentRef
   * @optional
   */
  workList?: Array<{ id: number; status: number; [key: string]: unknown }>;
  /**
   * 组件内部的模板引用集合。
   * @type {Record<string, unknown>}
   * @memberof ComponentRef
   * @optional
   */
  $refs?: Record<string, unknown>;
  /**
   * 是否有未保存的更改（数据保护功能）。
   * @type {{ value: boolean }}
   * @memberof ComponentRef
   * @optional
   */
  hasUnsavedChanges?: { value: boolean };
  /**
   * 重置未保存状态的方法（数据保护功能）。
   * @returns {void}
   * @memberof ComponentRef
   * @optional
   */
  resetUnsavedChanges?: () => void;
  /**
   * 手动设置未保存状态的方法（数据保护功能）。
   * @param {boolean} value - 是否有未保存的更改
   * @returns {void}
   * @memberof ComponentRef
   * @optional
   */
  setUnsavedChanges?: (value: boolean) => void;
  /**
   * 允许组件暴露其他自定义属性和方法。
   * @type {unknown}
   * @memberof ComponentRef
   */
  [key: string]: unknown;
}

/**
 * @description 组件引用集合的接口，通常用于管理多个动态组件的引用。
 * @interface ComponentRefs
 * @example
 * const refs: ComponentRefs = {
 *   firstStep: {} as ComponentRef, // FirstStepComponentShape 也是一种 ComponentRef
 *   secondStepVideo: {} as ComponentRef
 * };
 */
export interface ComponentRefs {
  /** 键为组件的唯一标识，值为组件的引用。 */
  [key: string]: ComponentRef;
}
