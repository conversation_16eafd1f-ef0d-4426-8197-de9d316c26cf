import { Font, TextSticker, STYLE_TYPE } from '@/types';
import { currentEditorType } from '@/constants/project';
import {
  selectedIndexToOriginWMap,
  currentSpace,
  selectedSettingIndex,
  globalPreviewImageBoxSize,
  globalTextStickerItemSize,
  globalImageStickerItemSize,
  currentSetting,
} from '@/components/ImageTextEditor/hook/useWorkInfo';

const defPreviewWidth = 324;
const videoTargetWidth = 720;
/** 描边系数，同样的描边数值，后端ffmpeg和前端svg渲染的结果不同，通过一个系数转换 */
const STROKE_FACTOR = 2;

/** 补全颜色前的井号 */
function withSharp(color?: string): string {
  if (!color) return '';
  return color.startsWith('#') ? color : `#${color}`;
}

/** 尺寸换算 */
/** 视频编辑器只需要传入size/px转换即可，图文编辑器需要根据原图宽度和渲染宽度进行计算 */
export const sizeToPx = (size: number): number => {
  if (currentEditorType === 'image') {
    // 图文编辑器尺寸转换
    // 原图尺寸
    const imageTargetWidth = currentSpace.value.originW;
    // 如果是高图（左右留白），使用传入的宽度作为预览宽度，如果是宽图or拼图（左右不留白），使用默认324作为预览宽度
    const imagePreviewWidth = currentSpace.value.renderW;
    const previewWidth = imagePreviewWidth
      ? imagePreviewWidth
      : defPreviewWidth;
    let sizeScale = 1;
    const originW = selectedIndexToOriginWMap.get(selectedSettingIndex.value);
    if (originW) {
      sizeScale = imageTargetWidth! / originW;
    }
    return Math.round((size / (imageTargetWidth! / previewWidth)) * sizeScale);
  } else {
    // 视频编辑器尺寸转换
    return Math.round(size / (videoTargetWidth / defPreviewWidth));
  }
};
export const pxToSize = (px: number): number => {
  if (currentEditorType === 'image') {
    // 图文编辑器尺寸转换
    // 原图尺寸
    const imageTargetWidth = currentSpace.value.originW;
    // 如果是高图（左右留白），使用传入的宽度作为预览宽度，如果是宽图or拼图（左右不留白），使用默认324作为预览宽度
    const imagePreviewWidth = currentSpace.value.renderW;
    const previewWidth = imagePreviewWidth
      ? imagePreviewWidth
      : defPreviewWidth;
    let sizeScale = 1;
    const originW = selectedIndexToOriginWMap.get(selectedSettingIndex.value);
    if (originW) {
      sizeScale = imageTargetWidth! / originW;
    }
    return Math.round((px / sizeScale) * (imageTargetWidth! / previewWidth));
  } else {
    // 视频编辑器尺寸转换
    return Math.round(px * (videoTargetWidth / defPreviewWidth));
  }
};

export const fixStickerPos = (
  x: number,
  y: number,
  type: STYLE_TYPE,
): {
  x: { isResetX: boolean; val: number };
  y: { isResetY: boolean; val: number };
} => {
  let resultX = 0;
  let resultY = 0;
  const boxSizeW = parseInt(globalPreviewImageBoxSize.value.width);
  const boxSizeH = parseInt(globalPreviewImageBoxSize.value.height);
  const globalSize =
    type == STYLE_TYPE.FONT
      ? globalTextStickerItemSize
      : globalImageStickerItemSize;
  const isResetX =
    sizeToPx(x) +
      (currentSetting.value?.space?.h ?? 0) +
      globalSize.value.width >
    boxSizeW;
  const isResetY =
    sizeToPx(y) +
      (currentSetting.value?.space?.v ?? 0) +
      globalSize.value.height >
    boxSizeH;
  if (isResetX) {
    resultX = pxToSize(
      boxSizeW - globalSize.value.width - (currentSetting.value?.space?.h ?? 0),
    );
  }
  if (isResetY) {
    resultY = pxToSize(
      boxSizeH -
        globalSize.value.height -
        (currentSetting.value?.space?.v ?? 0),
    );
  }
  return {
    x: { isResetX: isResetX, val: resultX },
    y: { isResetY: isResetY, val: resultY },
  };
};

/**
 * 根据 FontPreset 生成符合 CSS 规范的样式对象
 * @param preset FontPreset
 * @returns Record<string, string>
 */
export function getFontPresetStyle(
  preset: TextSticker,
): Record<string, string> {
  const style: Record<string, string> = {};

  // 文字填充色
  style.fill = withSharp(preset.color) || 'black';

  // 描边
  if (preset.strokeColor && preset.strokeWidth) {
    style.stroke = withSharp(preset.strokeColor);
    style['stroke-width'] = String(
      sizeToPx(preset.strokeWidth * (preset.fontSize || 1) * STROKE_FACTOR),
    );
    style['paint-order'] = 'stroke fill';
  } else {
    style.stroke = 'none';
  }

  // 阴影
  if (preset.shadowColor) {
    style['filterId'] = `text-shadow-${withSharp(preset.shadowColor).replace(
      '#',
      '',
    )}-${preset.shadowX || 0}-${preset.shadowY || 0}`;
    style['shadowColor'] = withSharp(preset.shadowColor);
    style['shadowX'] = String(
      sizeToPx(Number(preset.shadowX) * (preset.fontSize || 1) || 0),
    );
    style['shadowY'] = String(
      sizeToPx(Number(preset.shadowY) * (preset.fontSize || 1) || 0),
    );
    style['shadowBlur'] = String(0);
    if (preset.shadowStrokeColor) {
      style['shadowStrokeColor'] = withSharp(preset.shadowStrokeColor);
    }
    if (preset.shadowStrokeWidth) {
      style['shadowStrokeWidth'] = String(
        sizeToPx(
          Number(preset.shadowStrokeWidth) *
            (preset.fontSize || 1) *
            STROKE_FACTOR,
        ),
      );
    }
  }

  // 其他
  if (preset.boxColor) {
    style['background-color'] = withSharp(preset.boxColor);
  }
  return style;
}

/**
 * 精确测量文本宽度
 * @param text 要测量的文本
 * @param fontFamily 字体族
 * @param fontSize 字体大小
 * @returns 文本宽度（像素）
 */
export const measureTextWidth = async (
  text: string,
  fontFamily: string,
  fontSize: number,
): Promise<{
  width: number;
  height: number;
  ascent: number;
  descent: number;
}> => {
  // 创建临时canvas进行文本测量
  const canvas = document.createElement('canvas');
  const ctx = canvas.getContext('2d');
  if (!ctx) return { width: 0, height: 0, ascent: 0, descent: 0 };
  let extendWidth = 0; // 特殊字体扩展宽度，避免测量结果过小
  if (fontFamily.includes('Playball')) {
    extendWidth = fontSize / 4;
  }

  // 设置字体样式
  ctx.font = `${fontSize}px ${fontFamily}`;

  // 等待字体加载完成
  await document.fonts.ready;

  // 测量文本宽度
  const metrics = ctx.measureText(text);

  // 计算高度（ascent + descent）
  // actualBoundingBoxAscent/Descent 是实际像素高度，更精确
  const ascent = metrics.actualBoundingBoxAscent || 0;
  const descent = metrics.actualBoundingBoxDescent || 0;
  const height = ascent + descent;
  const width = metrics.width + extendWidth;

  return { width, height, ascent, descent };
};

/**
 * 如果贴图尺寸大于限制区域（162*150），计算等比例缩放的比例（图文编辑器用）
 * @returns 缩放比例，如果不需要缩放则返回1
 */
export const getCleanScale = (w: number, h: number): number => {
  const originalWidth = sizeToPx(w);
  const originalHeight = sizeToPx(h);

  // 最大允许尺寸
  const maxWidth = 162;
  const maxHeight = 150;

  // 检查是否需要缩放
  if (originalWidth <= maxWidth && originalHeight <= maxHeight) {
    return 1;
  }

  // 计算缩放比例，取宽高比例中的较小值以保持等比例
  const scaleX = maxWidth / originalWidth;
  const scaleY = maxHeight / originalHeight;
  return Math.min(scaleX, scaleY);
};

/** 将扁平化的字体列表转换为树形结构 */
export const formatToTree = (list: Font[]) => {
  const tree: Record<string, Font[]> = {};

  list.forEach(item => {
    if (item.styleCategory) {
      if (!tree[item.styleCategory]) {
        tree[item.styleCategory] = [];
      }
      tree[item.styleCategory].push(item);
    } else {
      if (!tree[item.fontFamily]) {
        tree[item.fontFamily] = [];
      }
      tree[item.fontFamily].push(item);
    }
  });

  return Object.entries(tree).map(([key, value]) => ({
    category: key,
    items: value,
  }));
};
