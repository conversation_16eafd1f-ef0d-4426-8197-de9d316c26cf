/**
 * 上传文件状态管理 Hook（单例）
 * 管理上传文件列表的状态，提供响应式的文件管理方法
 */

import { computed, Ref } from 'vue';
import type { FileData } from '@/types/Material';
import { TMP_ID_KEY, getFileUniqueId } from '../utils/uploadTempStorage';
import { shallowRef, triggerRef } from 'vue';
type FILE_LIST_TYPE = FileData[]; // 文件列表类型 素材弹窗、素材库通用

/**
 * 上传文件状态管理 Hook 的返回值类型
 */
export interface UseUploadingFileStateReturn {
  /** 上传文件列表映射，按文件夹ID分组 */
  uploadingFileListMap: Ref<Map<number, FILE_LIST_TYPE>>;
  /** 获取指定文件夹的上传文件列表 */
  getUploadingFileList: (folderId: number) => FILE_LIST_TYPE;
  /** 检查是否有文件正在上传 */
  hasUploadingFiles: Ref<boolean>;
  /** 获取所有正在上传的文件总数 */
  totalUploadingFiles: Ref<number>;
  /** 更新指定文件夹的上传文件列表 */
  updateUploadingFileList: (folderId: number, fileList: FILE_LIST_TYPE) => void;
  /** 添加文件到指定文件夹的上传列表 */
  addFileToUploadingList: (folderId: number, fileList: FILE_LIST_TYPE) => void;
  /** 从指定文件夹的上传列表中移除文件 */
  removeFileFromUploadingList: (folderId: number, file: File) => void;
  /** 更新指定文件夹中指定文件的上传进度 */
  updateFileUploadProgress: (
    folderId: number,
    file: File,
    percent: number,
  ) => void;
  /** 清理已完成的上传文件（进度为100%的文件） */
  cleanupCompletedUploads: (folderId: number) => void;
  /** 清除指定文件夹的上传文件列表 */
  clearUploadingFileList: (folderId: number) => void;
  /** 清除所有上传文件列表 */
  clearAllUploadingFileList: () => void;
}

// =========================
// 模块级单例状态与派生数据
// =========================
const uploadingFileListMap = shallowRef<Map<number, FILE_LIST_TYPE>>(new Map());

const hasUploadingFiles = computed(() => {
  return Array.from(uploadingFileListMap.value.values()).some(
    list => list.length > 0,
  );
});

const totalUploadingFiles = computed(() => {
  return Array.from(uploadingFileListMap.value.values()).reduce(
    (total, list) => total + list.length,
    0,
  );
});

// =========================
// 模块级函数（操作同一份 Map）
// =========================
const getUploadingFileList = (folderId: number): FILE_LIST_TYPE => {
  return uploadingFileListMap.value.get(folderId) || [];
};

const updateUploadingFileList = (
  folderId: number,
  fileList: FILE_LIST_TYPE,
): void => {
  uploadingFileListMap.value.set(folderId, fileList);
  triggerRef(uploadingFileListMap);
};

const addFileToUploadingList = (
  folderId: number,
  fileList: FILE_LIST_TYPE,
): void => {
  const currentList = uploadingFileListMap.value.get(folderId) || [];
  const newList = [...fileList, ...currentList];
  uploadingFileListMap.value.set(folderId, newList);
  triggerRef(uploadingFileListMap);
};

const removeFileFromUploadingList = (folderId: number, file: File): void => {
  const currentList = uploadingFileListMap.value.get(folderId) || [];
  const newList = currentList.filter(
    item => item[TMP_ID_KEY] !== getFileUniqueId(file),
  );
  uploadingFileListMap.value.set(folderId, newList);
  triggerRef(uploadingFileListMap);
};

const updateFileUploadProgress = (
  folderId: number,
  file: File,
  percent: number,
): void => {
  const currentList = uploadingFileListMap.value.get(folderId) || [];
  const newList = currentList.map(item =>
    item[TMP_ID_KEY] === getFileUniqueId(file) ? { ...item, percent } : item,
  );

  uploadingFileListMap.value.set(folderId, newList);
  triggerRef(uploadingFileListMap);
};

const cleanupCompletedUploads = (folderId: number): void => {
  const currentList = uploadingFileListMap.value.get(folderId) || [];
  const newList = currentList.filter(item => item.percent !== 100);
  uploadingFileListMap.value.set(folderId, newList);
  triggerRef(uploadingFileListMap);
};

const clearUploadingFileList = (folderId: number): void => {
  uploadingFileListMap.value.delete(folderId);
  triggerRef(uploadingFileListMap);
};

const clearAllUploadingFileList = (): void => {
  uploadingFileListMap.value.clear();
  triggerRef(uploadingFileListMap);
};

// =========================
// 导出单例访问器（返回同一份对象引用）
// =========================
export function useUploadingFileState(): UseUploadingFileStateReturn {
  return {
    uploadingFileListMap,
    getUploadingFileList,
    hasUploadingFiles,
    totalUploadingFiles,
    updateUploadingFileList,
    addFileToUploadingList,
    removeFileFromUploadingList,
    updateFileUploadProgress,
    cleanupCompletedUploads,
    clearUploadingFileList,
    clearAllUploadingFileList,
  };
}
