# useSkeleton 骨架屏使用文档

## 简介

`useSkeleton` 是一个用于全局或局部控制骨架屏（Skeleton）显示的自定义 Hook。它适用于页面或组件在数据加载期间展示骨架屏，提升用户体验。

## API 说明

| 方法/属性      | 类型                 | 说明                               |
| -------------- | -------------------- | ---------------------------------- |
| isShowSkeleton | ComputedRef<boolean> | 是否显示骨架屏（只读）             |
| addSkeletonTask        | () => void           | 增加一个"加载任务"，触发骨架屏显示 |
| finishSkeletonTask     | () => void           | 完成一个"加载任务"，可能关闭骨架屏 |

- **isShowSkeleton**  
  是否显示骨架屏。只要有未完成的任务（即调用了 `addSkeletonTask` 但未调用对应的 `finishSkeletonTask`），该值为 `true`，否则为 `false`。

- **addSkeletonTask**  
  增加一个任务。每调用一次，任务计数加一，骨架屏保持显示。

- **finishSkeletonTask**  
  完成一个任务。每调用一次，任务计数减一。当所有任务完成后，骨架屏自动隐藏。

## 基本用法

### 1. 引入 useSkeleton

```ts
import { useSkeleton } from '@/hook/useSkeleton';
```

### 2. 在组件中使用

```vue
<script lang="ts" setup>
import { onMounted } from 'vue';
import { useSkeleton } from '@/hook/useSkeleton';

const { isShowSkeleton, addSkeletonTask, finishSkeletonTask } = useSkeleton();

onMounted(async () => {
  addSkeletonTask(); // 开始加载，显示骨架屏
  // 模拟异步数据加载
  await fetchData();
  finishSkeletonTask(); // 加载完成，隐藏骨架屏
});
</script>
```

### 3. 在模板中绑定

```vue
<template>
  <div>
    <fa-skeleton v-if="isShowSkeleton" :loading="isShowSkeleton" />
    <div v-else >
      内容
    </div>
  </div>
</template>
```

## 注意事项

- 每次调用 `addSkeletonTask` 必须有对应的 `finishSkeletonTask`，否则骨架屏不会自动关闭。
- 适用于多异步任务并发场景，只有所有任务都完成后骨架屏才会隐藏。
- 推荐在页面或组件加载数据前后分别调用 `addSkeletonTask` 和 `finishSkeletonTask`。

## 典型场景

- 页面初始化数据加载
- 切换 Tab 时局部数据刷新
- 多接口并发请求时统一骨架屏控制

---

如需进一步补充示例或特殊场景说明，请告知！
