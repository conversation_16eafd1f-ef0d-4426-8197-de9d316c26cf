/**
 * 骨架屏管理器
 * 统一管理所有骨架屏的显示和隐藏逻辑
 */

import type VueRouter from 'vue-router';

// 骨架屏状态常量
const SKELETON_STATES = {
  HOME: '', // 首页骨架屏（默认状态）
  COMMON: 'common', // 通用骨架屏（其他页面使用）
  HIDE: 'hide', // 隐藏所有骨架屏
} as const;

type SkeletonState = (typeof SKELETON_STATES)[keyof typeof SKELETON_STATES];

// 需要通用骨架屏的路由
const COMMON_SKELETON_ROUTES = new Set([
  '/meta',
  '/acct-info',
  '/project',
  '/work',
]);

class SkeletonManager {
  /**
   * 初始化骨架屏管理器
   * @param router 路由实例
   */
  init(router: VueRouter) {
    this.setupRouterHooks(router);
  }

  /**
   * 设置路由钩子
   */
  private setupRouterHooks(router: VueRouter) {
    router.afterEach(() => {
      this.setSkeleton(SKELETON_STATES.HIDE);
    });
  }

  /**
   * 根据路由显示对应的骨架屏
   * @param route 当前路由路径（包含查询参数）
   */
  showSkeletonByRoute(route: string) {
    const { path } = this.parseRoute(route);

    if (path === '/') {
      this.setSkeleton(SKELETON_STATES.HOME);
    } else if (COMMON_SKELETON_ROUTES.has(path)) {
      // 其他需要骨架屏的页面显示通用骨架屏
      this.setSkeleton(SKELETON_STATES.COMMON);
    } else {
      this.setSkeleton(SKELETON_STATES.HIDE);
    }
  }

  /**
   * 手动隐藏骨架屏
   */
  hideSkeleton() {
    this.setSkeleton(SKELETON_STATES.HIDE);
  }

  /**
   * 设置骨架屏状态
   */
  private setSkeleton(state: SkeletonState) {
    const { documentElement } = document;

    if (state === SKELETON_STATES.HOME) {
      documentElement.removeAttribute('data-skeleton');
    } else {
      documentElement.setAttribute('data-skeleton', state);
    }
  }

  /**
   * 解析路由，提取路径
   * @param route 完整的路由字符串（包含查询参数）
   * @returns 包含路径的对象
   */
  private parseRoute(route: string): { path: string } {
    if (!route) return { path: '/' };

    // 处理不同格式的路由：/#/path 或 #/path
    let fullPath = route;
    if (fullPath.startsWith('/#/')) {
      fullPath = fullPath.substring(2); // 移除 /#
    } else if (fullPath.startsWith('#/')) {
      fullPath = fullPath.substring(1); // 移除 #
    }

    // 如果是根路径
    if (!fullPath || fullPath === '/') return { path: '/' };

    // 分离路径和查询参数
    const queryIndex = fullPath.indexOf('?');
    const path =
      queryIndex !== -1 ? fullPath.substring(0, queryIndex) : fullPath;

    return { path };
  }
}

// 创建单例实例
export const skeletonManager = new SkeletonManager();

// 兼容原有的函数导出
export const hideFirstSkeleton = () => {
  skeletonManager.hideSkeleton();
};
