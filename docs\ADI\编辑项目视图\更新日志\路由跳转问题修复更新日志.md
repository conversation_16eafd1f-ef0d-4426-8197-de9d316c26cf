# 路由跳转问题修复说明

## 问题描述

在 `useInfiniteWorkList.ts` 中，当作品列表为空时会自动跳转到第一步页面。虽然控制台显示了"作品列表为空，已自动跳转到第一步页面"的消息，路由也更新了，但是页面没有跳转，需要手动刷新才能看到跳转效果。

## 问题原因

1. **Vue Router 3 的路由更新机制**: 在 Vue Router 3.6.5 中，当只更新 `query` 参数时（`router.replace({ query: newQuery })`），不会触发页面重新渲染，需要手动刷新。

2. **缺少 path 参数**: 经过测试发现：

   - `router.replace({ query: newQuery })` - ❌ 不会更新页面，需要手动刷新
   - `router.replace({ path: currentRoute.path, query: newQuery })` - ✅ 会更新页面，不需要手动刷新

3. **路由跳转的完整性**: Vue Router 需要明确的 `path` 参数才能正确触发组件的重新渲染和生命周期。

## 修复方案

### 1. 确保路由跳转包含 path 参数

**关键发现**: 在 Vue Router 3 中，必须同时指定 `path` 和 `query` 参数才能触发页面更新。

正确的路由跳转方式：

```typescript
router.replace({
  path: currentRoute.path, // 必须包含 path 参数
  query: newQuery,
});
```

错误的路由跳转方式（不会触发页面更新）：

```typescript
router.replace({
  query: newQuery, // 仅更新 query 不会触发页面重新渲染
});
```

### 2. 使用 nextTick 确保执行时机

使用双重 `nextTick` 确保路由跳转在正确的时机执行：

```typescript
nextTick(() => {
  // 执行路由跳转 - 必须包含 path 参数
  router.replace({
    path: currentRoute.path,
    query: newQuery,
  });

  // 强制触发路由更新，确保页面重新渲染
  nextTick(() => {
    console.log('作品列表为空，已自动跳转到第一步页面', {
      当前路由查询参数: router.currentRoute.query,
      新查询参数: newQuery,
    });
  });
});
```

### 3. 增强日志输出

添加更详细的日志输出，便于调试和确认跳转状态：

```typescript
console.log('准备跳转到第一步页面', {
  当前步骤: currentStep,
  目标步骤: FIRST_STEP_INDEX,
  当前路径: currentRoute.path,
  新查询参数: newQuery,
});
```

## 修复后的完整代码

```typescript
const handleEmptyWorkListRedirect = (): void => {
  try {
    // 获取当前路由信息
    const currentRoute = router.currentRoute;

    // 检查当前是否在预览模式（第二步）
    const currentStep = Number(currentRoute.query?.step) || 0;
    if (currentStep === FIRST_STEP_INDEX) {
      // 如果已经在第一步，无需跳转
      console.log('当前已在第一步，无需跳转');
      return;
    }

    // 构建跳转到第一步的路由参数
    const newQuery = {
      ...currentRoute.query,
      step: String(FIRST_STEP_INDEX),
    };

    console.log('准备跳转到第一步页面', {
      当前步骤: currentStep,
      目标步骤: FIRST_STEP_INDEX,
      当前路径: currentRoute.path,
      新查询参数: newQuery,
    });

    // 使用 nextTick 确保在下一个 DOM 更新周期执行路由跳转
    nextTick(() => {
      // 执行路由跳转 - 使用与 useProjectData 相同的方式
      router.replace({
        query: newQuery,
      });

      // 强制触发路由更新，确保页面重新渲染
      nextTick(() => {
        console.log('作品列表为空，已自动跳转到第一步页面', {
          当前路由查询参数: router.currentRoute.query,
          新查询参数: newQuery,
        });
      });
    });
  } catch (error) {
    console.error('跳转到第一步页面失败:', error);
  }
};
```

## 测试验证

修复后，当删除最后一个作品时：

1. 控制台会显示详细的跳转日志
2. 路由查询参数会正确更新
3. 页面会自动跳转到第一步，无需手动刷新

## 相关文件

- `src/views/EditProjectView/composables/useInfiniteWorkList.ts` - 主要修复文件
- `src/views/EditProjectView/composables/useProjectData.ts` - 参考的路由跳转实现
- `src/router/index.ts` - Vue Router 配置

## 注意事项

1. 此修复适用于 Vue 2 + Vue Router 3 的项目
2. 如果项目升级到 Vue 3，可能需要调整路由跳转的实现方式
3. 建议在其他需要程序化路由跳转的地方也使用相同的模式
