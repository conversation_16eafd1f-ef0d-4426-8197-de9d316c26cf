<template>
  <div
    class="media-file-item"
    :class="{
      'media-file-item--selected': isSelected,
    }"
    @click="handleClick"
  >
    <div class="media-file-item__content">
      <!-- 缩略图区域 -->
      <div class="media-file-item__thumbnail">
        <!-- 新生成标签 -->
        <div v-if="isRegenerated" class="media-file-item__regenerated-tag">
          新生成
        </div>
        <ScImg
          v-if="shouldShowCoverImage"
          :src="workItem.coverImg"
          :alt="workListBarType === WORK_TYPE.VIDEO ? '视频封面' : '图片封面'"
          :fit="workListBarType === WORK_TYPE.VIDEO ? 'contain' : 'contain'"
          class="w-full h-full rounded-[8px]"
          :maxWidth="120"
        />
        <div
          v-else
          class="w-full h-full flex items-center justify-center bg-gray-100"
        >
          <!-- 失败状态显示失败图片 -->
          <img
            v-if="shouldShowFailedImage"
            src="@/assets/EditProject/workFailed.webp"
            alt="生成失败"
            class="w-full h-full object-cover rounded-[8px]"
          />
          <!-- 其他状态显示无缩略图文字 -->
          <span v-else-if="shouldShowNoThumbnailText" class="text-gray-400"
            >无缩略图</span
          >
        </div>

        <!-- 作品ID提示小块 -->
        <fa-tooltip v-if="shouldShowWorkIdWithTooltip" color="#000">
          <template slot="title">
            <span class="media-file-item__work-id-tooltip">
              作品ID: {{ workItem.id }}
            </span>
          </template>
          <div class="media-file-item__work-id">作品ID: {{ workItem.id }}</div>
        </fa-tooltip>
        <div v-else-if="shouldShowWorkIdOnly" class="media-file-item__work-id">
          作品ID: {{ workItem.id }}
        </div>

        <!-- 进度条遮罩层（当状态为生成中或重新生成中时显示） -->
        <div
          v-if="showProgressOverlay"
          class="media-file-item__progress-overlay"
        >
          <div class="media-file-item__progress-circle">
            <fa-progress
              type="circle"
              strokeLinecap="square"
              strokeColor="#FFFFFF"
              trailColor="#e9e9e9"
              :width="51"
              :strokeWidth="4"
              :percent="Math.floor(workItem.progress || 0)"
            />
          </div>
        </div>
      </div>

      <!-- 文件信息区域 -->
      <div class="media-file-item__info">
        <!-- 生成中状态 -->
        <template v-if="showGeneratingStatus">
          <div class="media-file-item__generating-container">
            <div class="media-file-item__generating">正在努力生成中...</div>
            <fa-tooltip color="#000">
              <template slot="title">
                <span class="text-[#fff] text-[14px] break-all"
                  >作品ID: {{ workItem.id }}</span
                >
              </template>
              <Icon
                type="xinxishizhi"
                class="w-[20px] h-[20px] ml-[8px] text-[#999] cursor-pointer hover:text-[#666] transition-colors"
              />
            </fa-tooltip>
          </div>
        </template>
        <!-- 非生成中状态 -->
        <template v-else>
          <div>
            <div
              class="media-file-item__title"
              :class="{ 'media-file-item__title--failed': isFailed }"
            >
              <!-- 普通生成失败显示作品ID和失败文案 -->
              <template v-if="isNormalFailed">
                作品ID：{{ workItem.id }}<br />生成失败{{
                  shouldShowPointsRefund ? '，点数已自动返还' : ''
                }}
              </template>
              <!-- 其他状态显示作品名称 -->
              <template v-else>
                {{ workItem.name }}
              </template>
            </div>
            <div class="media-file-item__time">
              <!-- 显示时长或图片数量信息 -->
              <template v-if="workListBarType === WORK_TYPE.VIDEO">
                {{ formatDuration(workItem.duration) }}
              </template>
              <template v-else>
                {{ `共${imageCount}张` }}
              </template>
            </div>
          </div>

          <div class="media-file-item__actions">
            <div class="media-file-item__status">
              <span class="media-file-item__status-tag" :class="statusClass">
                {{ statusText }}
              </span>
              <!-- 失败状态的tooltip -->
              <fa-tooltip v-if="isFailed" color="#000">
                <template slot="title">
                  <span class="text-[#fff] text-[14px] break-all">
                    失败原因：{{ workItem.errMsg || '生成失败' }}
                  </span>
                </template>
                <img
                  src="@/assets/EditProject/xinxishizhi.svg"
                  alt="信息提示"
                  class="w-[16px] h-[16px] ml-[8px] cursor-pointer"
                />
              </fa-tooltip>
            </div>
            <div
              class="media-file-item__more"
              :class="{ 'media-file-item__more--menu-visible': showMenu }"
              v-if="shouldShowMoreActions"
            >
              <fa-dropdown
                :trigger="['click']"
                placement="topCenter"
                :getPopupContainer="getPopupContainer"
                @visibleChange="handleDropdownVisibleChange"
              >
                <div
                  class="media-file-item__more-icon"
                  :class="showMenu ? 'media-file-item__more-icon--hover' : ''"
                  key="save"
                  v-show="!showRemoveConfirm"
                  @click.stop="showMenu = true"
                >
                  <Icon type="gengduo" class="w-[20px] h-[20px]" />
                </div>
                <fa-menu slot="overlay">
                  <!-- 失败状态不显示保存按钮 -->
                  <fa-menu-item
                    v-if="!isFailed"
                    key="save"
                    @click="handleSave"
                    :loading="isSaving"
                    ><p class="p-[4px]">保存</p></fa-menu-item
                  >
                  <fa-menu-item key="remove" @click="handleRemoveClick"
                    ><p class="p-[4px]">移除</p></fa-menu-item
                  >
                </fa-menu>
              </fa-dropdown>
              <fa-popconfirm
                v-model:visible="showRemoveConfirm"
                arrowPointAtCenter
                placement="top"
                @confirm="handleRemove"
                @cancel="showRemoveConfirm = false"
                :disabled="isRemoving"
              >
                <template #title>
                  <div class="media-file-item__confirm-title">
                    移除后，该作品在项目中永久移除，无法找回。确定移除吗？
                  </div>
                </template>

                <template #footer>
                  <div class="text-center">
                    <fa-button
                      type="danger"
                      size="small"
                      @click="handleRemove"
                      class="min-w-[60px]"
                      :loading="isRemoving"
                      >确定</fa-button
                    >
                    <fa-button
                      type="normal"
                      size="small"
                      class="min-w-[60px] !ml-[8px]"
                      @click="showRemoveConfirm = false"
                      >取消</fa-button
                    >
                  </div>
                </template>

                <div
                  class="media-file-item__more-icon"
                  :class="'media-file-item__more-icon--hover'"
                  key="remove"
                  v-show="showRemoveConfirm"
                  @click.stop
                >
                  <Icon type="gengduo" class="w-[20px] h-[20px]" />
                </div>
              </fa-popconfirm>
            </div>
          </div>
        </template>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, computed, PropType, ref } from 'vue';
import { WorkType, WorkTypeValue } from '@/components/WorkListBar/types';
import { WorkItem } from '@/types';
import {
  WORK_STATUS,
  getWorkStatusInfo,
  isRegeneratingWork,
  isRecompletedWork,
  isAnyFailedWork,
  isGeneratingWork,
  isNormalCompletedWork,
  getWorkStatusDisplayName,
  isAnyGeneratingWork,
  isRetryFailedWork,
  isAnyCompletedWork,
  isNormalFailedWork,
} from '@/constants/workStatus';
import {
  WORK_EXPIRE_DAYS,
  EXPIRE_WARNING_HOURS,
  calculateExpireTime,
  calculateRemainingDays,
} from '@/constants/workExpire';
import { saveWorks, updateWorkFlag } from '@/api/EditProjectView/work';
import { message } from '@fk/faicomponent';
import { eventBus, EVENT_NAMES } from '@/utils/eventBus';
import { useWorkRestore } from '@/views/EditProjectView/composables/useWorkRestore';
import { showSaveSuccessMessage } from '@/utils/messageHelpers';

import {
  PROJECT_TYPE_IMAGE,
  PROJECT_TYPE_VIDEO,
} from '@/views/EditProjectView/constants';

export default defineComponent({
  name: 'WorkListBarItem',
  props: {
    workItem: {
      type: Object as PropType<WorkItem>,
      required: true,
    },
    isSelected: {
      type: Boolean,
      default: false,
    },
    workListBarType: {
      type: Number as PropType<WorkTypeValue>,
      default: WorkType.VIDEO,
      validator: (value: WorkTypeValue) =>
        [WorkType.VIDEO, WorkType.IMAGE].includes(value),
    },
  },
  emits: ['select'],
  setup(props, { emit }) {
    // 导出 WorkType 以在模板中使用
    const WORK_TYPE = WorkType;

    // 菜单显示状态
    const showMenu = ref(false);
    // 移除确认框显示状态
    const showRemoveConfirm = ref(false);
    // 保存中状态
    const isSaving = ref(false);
    // 移除中状态
    const isRemoving = ref(false);

    // 初始化恢复旧作品功能
    const { restoreWork } = useWorkRestore({
      showSuccessMessage: true, // 显示成功消息
      showErrorMessage: true,
    });

    // 使用 eventBus 替代 inject 方式

    // 计算是否已保存（通过saveTime字段判断）
    const isSaved = computed(() => Boolean(props.workItem.saveTime));

    // 获取统一状态信息
    const statusInfo = computed(() => getWorkStatusInfo(props.workItem));

    // 计算是否正在重新生成
    const isRegenerating = computed(() => isRegeneratingWork(statusInfo.value));

    // 计算是否为重新生成完成状态（视频）或新生成状态（图文）
    const isRegenerated = computed(() => {
      // 防御性编程：确保workItem存在
      if (!props.workItem) {
        return false;
      }

      // 视频作品：使用重新生成完成状态判断
      if (props.workItem.type === WorkType.VIDEO) {
        return isRecompletedWork(statusInfo.value);
      }

      // 图文作品：当作品状态为已生成且editAgainGraphic为true时显示新生成标签
      if (props.workItem.type === WorkType.IMAGE) {
        const isInFlaggedSet = !!flaggedWorkIds.value[props.workItem.id];
        const isCompleted = isAnyCompletedWork(statusInfo.value);
        const hasEditFlag = props.workItem.editAgainGraphic === true;

        // 如果该作品已被本地标识为已查看，则不显示新生成标签
        if (isInFlaggedSet) {
          return false;
        }

        return isCompleted && hasEditFlag;
      }

      return false;
    });

    // 计算是否为生成失败状态
    const isFailed = computed(() => isAnyFailedWork(statusInfo.value));

    // 计算是否为普通生成失败状态（用于文件信息区域显示判断）
    const isNormalFailed = computed(() => isNormalFailedWork(statusInfo.value));

    // 计算图片数量（仅用于图文作品）
    const imageCount = computed(() => {
      if (
        props.workItem.type === PROJECT_TYPE_IMAGE &&
        props.workItem.data &&
        'graphic' in props.workItem.data
      ) {
        return (props.workItem.data.graphic || []).length;
      }
      return 0;
    });

    // 计算是否即将过期（状态为已完成，基于创建时间+固定天数，剩余时间少于24小时）
    const isExpiringSoon = computed(() => {
      if (
        !isNormalCompletedWork(statusInfo.value) ||
        !props.workItem.createTime
      )
        return false;

      const now = new Date();
      const expireTime = calculateExpireTime(props.workItem.createTime);
      const diffHours =
        (expireTime.getTime() - now.getTime()) / (1000 * 60 * 60);

      return diffHours <= EXPIRE_WARNING_HOURS && diffHours > 0;
    });

    // 计算是否几天后过期（状态为已完成，基于创建时间+固定天数，剩余时间在24小时到固定天数之间）
    const isExpiring = computed(() => {
      if (
        !isNormalCompletedWork(statusInfo.value) ||
        !props.workItem.createTime
      )
        return false;

      const now = new Date();
      const expireTime = calculateExpireTime(props.workItem.createTime);
      const diffHours =
        (expireTime.getTime() - now.getTime()) / (1000 * 60 * 60);

      return (
        diffHours > EXPIRE_WARNING_HOURS && diffHours <= 24 * WORK_EXPIRE_DAYS
      );
    });

    // 计算是否已超过有效期但还未被系统移除（显示即将失效）
    const isOverdueButNotRemoved = computed(() => {
      if (
        !isNormalCompletedWork(statusInfo.value) ||
        !props.workItem.createTime
      )
        return false;

      const now = new Date();
      const expireTime = calculateExpireTime(props.workItem.createTime);
      const diffHours =
        (expireTime.getTime() - now.getTime()) / (1000 * 60 * 60);

      // 如果已经超过有效期（diffHours <= 0），但作品还存在，说明还未被系统移除
      return diffHours <= 0;
    });

    // 计算状态类名
    const statusClass = computed(() => {
      const status = statusInfo.value;

      // 失败状态
      if (isAnyFailedWork(status)) return 'media-file-item__status-tag--failed';

      // 生成中状态
      if (isGeneratingWork(status))
        return 'media-file-item__status-tag--generating';

      // 重新生成中状态
      if (isRegeneratingWork(status))
        return 'media-file-item__status-tag--regenerating';

      // 重新生成完成状态
      if (isRecompletedWork(status))
        return 'media-file-item__status-tag--recompleted';

      // 已保存状态优先级最高
      if (isSaved.value) return 'media-file-item__status-tag--saved';

      // 普通完成状态的过期判断
      if (isNormalCompletedWork(status)) {
        if (isExpiringSoon.value)
          return 'media-file-item__status-tag--expiring-soon';
        if (isExpiring.value) return 'media-file-item__status-tag--expiring';
        if (isOverdueButNotRemoved.value)
          return 'media-file-item__status-tag--expiring-soon';
      }

      return '';
    });

    // 计算状态文本
    const statusText = computed(() => {
      const status = statusInfo.value;

      // 使用统一状态处理函数获取基础状态名称
      const baseStatusName = getWorkStatusDisplayName(status);

      // 重新生成相关状态优先显示 baseStatusName
      if (
        isRegeneratingWork(status) ||
        isRetryFailedWork(status) ||
        isRecompletedWork(status)
      ) {
        return baseStatusName;
      }

      // 失败状态优先级高于已保存状态
      if (isAnyFailedWork(status)) {
        return baseStatusName;
      }

      // 已保存状态优先级最高（但低于重新生成相关状态和失败状态）
      if (isSaved.value) return '已保存';

      // 普通完成状态的过期判断
      if (isNormalCompletedWork(status)) {
        if (isExpiringSoon.value) return '即将失效';
        if (isExpiring.value) return getExpiringDays() + '天后失效';
        if (isOverdueButNotRemoved.value) return '即将失效';
        return '';
      }

      // 返回基础状态名称
      return baseStatusName;
    });

    // 计算是否应该显示点数返还文案
    const shouldShowPointsRefund = computed(() => {
      // 视频和图文作品都根据需要扣点情况显示点数返还文案
      return props.workItem.pinchPoint === true;
    });

    // 格式化时间
    const formatDuration = (seconds: number = 0) => {
      const minutes = Math.floor(seconds / 60);
      const remainingSeconds = Math.floor(seconds % 60);
      return `${minutes.toString().padStart(2, '0')}:${remainingSeconds
        .toString()
        .padStart(2, '0')}`;
    };

    // 本地状态：记录已标识的作品ID
    const flaggedWorkIds = ref<Record<number, boolean>>({});

    // 点击媒体文件项
    const handleClick = async () => {
      // 如果是图文作品且显示新生成标签，调用updateFlag接口
      if (
        props.workItem.type === WorkType.IMAGE &&
        isRegenerated.value &&
        props.workItem.editAgainGraphic === true
      ) {
        const [err] = await updateWorkFlag({ id: props.workItem.id });

        if (err) {
          console.error('更新作品标识失败:', err.message);
          // 不阻止选择操作，继续执行
        } else {
          console.log('作品标识更新成功:', props.workItem.id);
          // 记录该作品已被标识，用于本地状态管理
          // 创建新对象确保响应式更新
          flaggedWorkIds.value = {
            ...flaggedWorkIds.value,
            [props.workItem.id]: true,
          };
          console.log(
            '本地状态已更新，flaggedWorkIds:',
            Object.keys(flaggedWorkIds.value),
          );
        }
      }

      emit('select', props.workItem.id);
    };

    // 保存媒体文件
    const handleSave = async () => {
      if (isSaving.value) return;

      // 只有视频作品的重新生成完成状态才显示新旧选择弹窗
      // 图文作品不需要新旧选择弹窗，即使有editAgainGraphic标识
      if (
        props.workItem.type === WorkType.VIDEO &&
        isRecompletedWork(statusInfo.value)
      ) {
        showMenu.value = false;
        eventBus.emit(EVENT_NAMES.SHOW_NEW_OLD_CHOICE_MODAL, props.workItem);
        return;
      }

      isSaving.value = true;

      if (!props.workItem.projectId) {
        message.error('项目ID为空，无法保存');
        isSaving.value = false;
        showMenu.value = false;
        return;
      }

      const [err] = await saveWorks({
        workIds: [props.workItem.id],
        projectId: props.workItem.projectId,
      });

      if (err) {
        isSaving.value = false;
        showMenu.value = false;
        message.error(err.message);
        return;
      }

      // 显示保存成功提示，包含跳转到"我的作品"的链接
      showSaveSuccessMessage();

      // 通过 eventBus 发送保存请求
      eventBus.emit(EVENT_NAMES.WORK_SAVE_REQUEST, {
        workIds: [props.workItem.id],
        isBatchSave: false,
      });

      isSaving.value = false;
      showMenu.value = false;
    };

    // 计算剩余天数（基于创建时间+固定天数）
    const getExpiringDays = () => {
      if (!props.workItem.createTime) return 0;
      return calculateRemainingDays(props.workItem.createTime);
    };

    // 移除媒体文件
    const handleRemove = async () => {
      if (isRemoving.value) return;

      isRemoving.value = true;

      // 特殊条件判断：视频作品且重新生成失败状态
      const statusInfo = getWorkStatusInfo(props.workItem);
      const isVideoWork = props.workListBarType === PROJECT_TYPE_VIDEO;
      const isRetryFailed = isRetryFailedWork(statusInfo);

      if (isVideoWork && isRetryFailed) {
        // 满足特殊条件：使用 useWorkRestore 执行恢复旧作品逻辑
        if (!props.workItem.relWorkId) {
          console.warn('无法获取有效的关联作品ID，无法恢复旧作品', {
            workItem: props.workItem,
          });
          isRemoving.value = false;
          showRemoveConfirm.value = false;
          showMenu.value = false;
          return;
        }

        const [err] = await restoreWork({
          relWorkId: props.workItem.relWorkId,
        });

        if (err) {
          console.error('恢复旧作品失败:', err.message);
        } else {
          // 恢复成功后刷新作品列表
          eventBus.emit(EVENT_NAMES.REFRESH_WORK_LIST);
        }

        isRemoving.value = false;
        showRemoveConfirm.value = false;
        showMenu.value = false;
      } else {
        // 普通情况：执行标准删除逻辑
        eventBus.emit(EVENT_NAMES.WORK_REMOVE_REQUEST, {
          workId: props.workItem.id,
        });

        // 监听删除结果
        const handleRemoveSuccess = () => {
          isRemoving.value = false;
          showRemoveConfirm.value = false;
          showMenu.value = false;

          // 判断作品状态，只有生成成功和重新生成成功的作品删除了，才需要更新totalItems
          // 因为totalItems是已生成的作品数量，生成失败、生成中的不算
          const statusInfo = getWorkStatusInfo(props.workItem);
          const shouldUpdateTotalItems = isAnyCompletedWork(statusInfo);

          if (shouldUpdateTotalItems) {
            // 临时更新 WorkListBar 的 totalItems 显示
            // 通过 eventBus 发送临时更新事件，确保 UI 立即响应
            eventBus.emit(EVENT_NAMES.TEMP_UPDATE_TOTAL_ITEMS, {
              operation: 'decrease',
              amount: 1,
            });
          }

          eventBus.off(EVENT_NAMES.WORK_REMOVE_SUCCESS, handleRemoveSuccess);
          eventBus.off(EVENT_NAMES.WORK_REMOVE_ERROR, handleRemoveError);
        };

        const handleRemoveError = () => {
          isRemoving.value = false;
          showRemoveConfirm.value = false;
          eventBus.off(EVENT_NAMES.WORK_REMOVE_SUCCESS, handleRemoveSuccess);
          eventBus.off(EVENT_NAMES.WORK_REMOVE_ERROR, handleRemoveError);
        };

        eventBus.once(EVENT_NAMES.WORK_REMOVE_SUCCESS, handleRemoveSuccess);
        eventBus.once(EVENT_NAMES.WORK_REMOVE_ERROR, handleRemoveError);
      }
    };

    // 计算是否显示进度条
    const showProgressOverlay = computed(() => {
      const statusInfo = getWorkStatusInfo(props.workItem);
      return isAnyGeneratingWork(statusInfo);
    });

    // 计算是否显示生成中状态
    const showGeneratingStatus = computed(() => {
      const statusInfo = getWorkStatusInfo(props.workItem);
      return isAnyGeneratingWork(statusInfo);
    });

    // 计算是否显示更多操作按钮
    const shouldShowMoreActions = computed(() => {
      // 生成中和重新生成中的作品不显示操作按钮
      if (showGeneratingStatus.value) return false;

      // 未保存的作品显示操作按钮
      if (!isSaved.value) return true;

      // 已保存但失败状态的作品也显示操作按钮（用于移除）
      if (isFailed.value) return true;

      // 其他情况不显示操作按钮
      return false;
    });

    // 计算是否显示带tooltip的作品ID
    const shouldShowWorkIdWithTooltip = computed(() => {
      return (
        !showGeneratingStatus.value && String(props.workItem.id).length > 5
      );
    });

    // 计算是否只显示作品ID（不带tooltip）
    const shouldShowWorkIdOnly = computed(() => {
      return (
        !showGeneratingStatus.value && String(props.workItem.id).length <= 5
      );
    });

    // 计算是否显示封面图片
    const shouldShowCoverImage = computed(() => {
      return (
        props.workItem.coverImg &&
        !isFailed.value &&
        !showGeneratingStatus.value
      );
    });

    // 计算是否显示失败状态图片
    const shouldShowFailedImage = computed(() => {
      return isFailed.value && !showProgressOverlay.value;
    });

    // 计算是否显示无缩略图文字
    const shouldShowNoThumbnailText = computed(() => {
      return !showProgressOverlay.value;
    });

    // 处理下拉菜单可见性变化
    const handleDropdownVisibleChange = (visible: boolean) => {
      // 如果正在显示确认框，则不关闭下拉菜单
      if (!visible && showRemoveConfirm.value) {
        return;
      }
      showMenu.value = visible;
    };

    // 处理移除点击
    const handleRemoveClick = () => {
      showMenu.value = false; // 关闭下拉菜单
      setTimeout(() => {
        showRemoveConfirm.value = true; // 显示确认框
      }, 100);
    };

    // 获取弹出框容器
    const getPopupContainer = (triggerNode: HTMLElement) => {
      return triggerNode;
    };

    return {
      WORK_TYPE,
      WORK_STATUS,
      PROJECT_TYPE_IMAGE,
      showMenu,
      showRemoveConfirm,
      isSaving,
      isRemoving,
      statusClass,
      statusText,
      isExpiring,
      isExpiringSoon,
      isOverdueButNotRemoved,
      isSaved,
      isRegenerating,
      isRegenerated,
      isFailed,
      isNormalFailed,
      shouldShowPointsRefund,
      imageCount,
      formatDuration,
      handleClick,
      handleSave,
      getExpiringDays,
      handleRemove,
      showProgressOverlay,
      showGeneratingStatus,
      shouldShowMoreActions,
      shouldShowWorkIdWithTooltip,
      shouldShowWorkIdOnly,
      shouldShowCoverImage,
      shouldShowFailedImage,
      shouldShowNoThumbnailText,
      handleDropdownVisibleChange,
      handleRemoveClick,
      getPopupContainer,
    };
  },
});
</script>

<style lang="scss" scoped>
/* 主容器样式
-------------------------------------------------- */
.media-file-item {
  /* 外观相关 */
  @apply bg-white rounded-8px border-1px border-solid border-[white];
  /* 尺寸相关 */
  @apply p-16px mb-0;
  /* 交互相关 */
  @apply cursor-pointer transition-all duration-300;
  /* 布局相关 */
  @apply relative;

  &:hover {
    /* 外观相关 */
    @apply bg-[#f3f3f5] cursor-pointer border-[#f3f3f5];

    /* hover时显示更多操作按钮 */
    .media-file-item__more {
      @apply opacity-100 visible;
    }
  }

  &.media-file-item--selected {
    /* 外观相关 */
    @apply border-[#3261fd];
    &:hover {
      /* 外观相关 */
      @apply bg-[white] cursor-default border-[#3261fd];

      /* 选中状态hover时也显示更多操作按钮 */
      .media-file-item__more {
        @apply opacity-100 visible;
      }
    }
  }
}

/* 内容布局
-------------------------------------------------- */
.media-file-item__content {
  /* 布局相关 */
  @apply flex gap-16px;
}

/* 缩略图区域
-------------------------------------------------- */
.media-file-item__thumbnail {
  /* 尺寸相关 */
  @apply w-120px h-120px;
  /* 外观相关 */
  @apply rounded-8px bg-[#fff] border-1px border-solid border-[#d9d9d9];
  /* 布局相关 */
  @apply relative flex-shrink-0;
}

/* 信息区域
-------------------------------------------------- */
.media-file-item__info {
  /* 布局相关 */
  @apply flex flex-1 flex-col justify-between;
  /* 尺寸相关 */
  @apply min-w-0;
}

/* 标题样式
-------------------------------------------------- */
.media-file-item__title {
  /* 文字相关 */
  @apply text-15px font-700 text-[#111] line-clamp-3 lh-20px break-all;
}

.media-file-item__title--failed {
  /* 文字相关 */
  @apply font-400 text-15px text-[#111];
}

/* 时间显示
-------------------------------------------------- */
.media-file-item__time {
  /* 文字相关 */
  @apply font-400 text-14px text-left text-[#bfbfbf] lh-19px truncate;
  /* 尺寸相关 */
  @apply mt-7px;
}

/* 操作区域
-------------------------------------------------- */
.media-file-item__actions {
  /* 布局相关 */
  @apply flex items-end justify-between h-34px;
}

/* 状态显示
-------------------------------------------------- */
.media-file-item__status {
  /* 布局相关 */
  @apply flex items-center;
}

/* 状态标签
-------------------------------------------------- */
.media-file-item__status-tag {
  /* 布局相关 */
  @apply inline-block;
  /* 尺寸相关 */
  @apply py-4px px-8px;
  /* 外观相关 */
  @apply rounded-4px;
  /* 文字相关 */
  @apply text-13px leading-[14px] whitespace-nowrap;
  /* 防止压缩相关 */
  @apply flex-shrink-0 min-w-fit;

  &.media-file-item__status-tag--saved {
    /* 外观相关 */
    @apply bg-[#ecf8e7] border border-[#52C41A];
    /* 文字相关 */
    @apply text-[#52C41A];
  }

  &.media-file-item__status-tag--generating {
    /* 外观相关 */
    @apply bg-[#f5f5f5] border border-[#BFBFBF];
    /* 文字相关 */
    @apply text-[#BFBFBF];
  }

  &.media-file-item__status-tag--regenerating {
    /* 外观相关 */
    @apply bg-[#f5f5f5] border border-[#BFBFBF];
    /* 文字相关 */
    @apply text-[#BFBFBF];
  }

  &.media-file-item__status-tag--recompleted {
    /* 外观相关 */
    @apply bg-[#e6f7ff] border border-[#1890ff];
    /* 文字相关 */
    @apply text-[#1890ff];
  }

  &.media-file-item__status-tag--expiring {
    /* 外观相关 */
    @apply bg-[#f5f5f5] border border-[#BFBFBF];
    /* 文字相关 */
    @apply text-[#999999];
  }

  &.media-file-item__status-tag--failed {
    /* 外观相关 */
    @apply bg-[#F3F3F5] border border-[#BFBFBF];
    /* 文字相关 */
    @apply text-[#999999];
  }

  &.media-file-item__status-tag--expiring-soon {
    /* 外观相关 */
    @apply bg-red-50 border border-red-300;
    /* 文字相关 */
    @apply text-red-500;
  }
}

/* 更多操作按钮
-------------------------------------------------- */
.media-file-item__more {
  /* 布局相关 */
  @apply relative;
  /* 显示控制 - 默认隐藏 */
  @apply opacity-0 invisible;
  /* 过渡效果 */
  @apply transition-all duration-200 ease-in-out;

  /* 当菜单显示时保持可见 */
  &.media-file-item__more--menu-visible {
    @apply opacity-100 visible;
  }
}

.media-file-item__more-icon {
  /* 交互相关 */
  @apply cursor-pointer;
  /* 尺寸相关 */
  @apply w-40px h-40px;
  /* 布局相关 */
  @apply flex items-center justify-center;
  /* 外观相关 */
  @apply rounded-6px color-#999;

  &.media-file-item__more-icon--hover {
    /* 外观相关 */
    @apply bg-[#E8E8E8];
  }

  &:hover {
    /* 外观相关 */
    @apply bg-[#E8E8E8];
  }
}

/* 生成中状态显示
-------------------------------------------------- */
.media-file-item__generating-container {
  /* 布局相关 */
  @apply flex items-center;
  /* 尺寸相关 */
  @apply h-full;
}

.media-file-item__generating {
  /* 文字相关 */
  @apply font-400 text-14px text-center text-[#999];
}

/* 进度条遮罩层
-------------------------------------------------- */
.media-file-item__progress-overlay {
  /* 布局相关 */
  @apply absolute top-0 left-50% -translate-x-1/2 flex items-center justify-center zi-media-file-item__progress-overlay;
  /* 尺寸相关 */
  @apply w-[56.6666%] h-full;
  /* 外观相关 */
  background-image: url('@/assets/EditProject/loading-new.svg');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
}

.media-file-item__progress-circle {
  /* 尺寸相关 */
  @apply w-51px h-51px;
  /* 布局相关 */
  @apply relative flex items-center justify-center;

  :deep(.fa-progress) {
    .fa-progress-circle-trail {
      @apply stroke-white stroke-[2px] opacity-[0.2] !important;
    }

    .fa-progress-circle-path {
      @apply stroke-white stroke-[6px] !important;
    }

    .fa-progress-text {
      @apply text-white text-[12px];
    }
  }
}

.media-file-item__progress-indicator {
  /* 动画相关 */
  @apply transition-[stroke-dashoffset] duration-600 ease;
}

.media-file-item__progress-text {
  /* 布局相关 */
  @apply absolute top-1/2 left-1/2;
  /* 文字相关 */
  @apply text-16px text-white font-medium;
  /* Transform */
  @apply transform -translate-x-1/2 -translate-y-1/2 rotate-90;
}

/* 动画效果
-------------------------------------------------- */
@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* 确认弹窗标题
-------------------------------------------------- */
.media-file-item__confirm-title {
  /* 尺寸相关 */
  @apply max-w-258px;
  /* 文字相关 */
  @apply text-14px text-[#000000d9] lh-19px;
}

/* 新生成标签
-------------------------------------------------- */
.media-file-item__regenerated-tag {
  /* 布局相关 */
  @apply absolute top-[-5px] right-[-5px] zi-media-file-item__regenerated-tag;
  /* 尺寸相关 */
  @apply py-0px px-7px;
  /* 文字相关 */
  @apply text-12px leading-[23px];
  font-weight: 400;
  font-size: 12px;
  color: #fff;
  border-radius: 11px;
  background: linear-gradient(92.19deg, #105fff 0%, #7923f9 100%);
}

/* 作品ID提示小块
-------------------------------------------------- */
.media-file-item__work-id {
  /* 布局相关 */
  @apply absolute bottom-4px left-1/2 transform -translate-x-1/2 zi-media-file-item__work-id;
  /* 尺寸相关 */
  @apply max-w-[100px] py-[3px] px-[8px];
  /* 外观相关 */
  @apply bg-[rgba(0,0,0,0.6)] rounded-[10.5px];
  /* 文字相关 */
  @apply font-400 text-12px text-center text-white lh-16px;
  @apply whitespace-nowrap overflow-hidden text-ellipsis;
  /* 阴影效果 */
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.6);
}

.media-file-item__work-id-tooltip {
  /* 文字相关 */
  @apply text-white text-14px break-all;
}
</style>
