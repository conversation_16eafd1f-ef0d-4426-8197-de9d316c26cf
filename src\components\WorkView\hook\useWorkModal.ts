import { ref, watch, onBeforeUnmount, Ref, computed } from 'vue';
import { getWorkDetailInfo } from '@/api/WorkView/index';
import { PROJECT_TYPE } from '@/constants/project';
import { message } from '@fk/faicomponent';
import { getWorkStatusInfo, isAnyGeneratingWork } from '@/constants/workStatus';
import {
  WorkDetailApiResponse,
  VideoResource,
  VideoCoverResource,
} from '@/components/WorkView/types';

export function useWorkModal(
  props: { visible: boolean; workId?: number },
  emit: (event: string, ...args: unknown[]) => void,
) {
  const isShow = ref(false);
  const workData = ref<WorkDetailApiResponse | null>(null);

  // 视频组件引用（使用unknown类型，避免类型递归）
  const mainVideoRef = ref<unknown>(null);
  const newVideoRef = ref<unknown>(null);
  const oldVideoRef = ref<unknown>(null);

  // 是否视频作品
  const isVideo = computed(() => workData.value?.type === PROJECT_TYPE.VIDEO);

  /** 是否生成中 */
  const isGenerating = computed(() => {
    const statusInfo = getWorkStatusInfo(workData.value || { status: 0 });
    return isAnyGeneratingWork(statusInfo);
  });

  // 当前视频作品的资源信息
  const videoData = computed<{
    video?: VideoResource;
    videoCover?: VideoCoverResource;
  }>(() => {
    if (!workData.value) return {};
    const currentWorkData = workData.value.data;
    return {
      video: currentWorkData?.fullVideo || currentWorkData?.baseVideo,
      videoCover: currentWorkData?.fullCover || currentWorkData?.baseCover, // 当前视频作品的封面图信息
    };
  });

  // 关联的旧视频作品的资源信息
  const videoRelData = computed<{
    video?: VideoResource;
    videoCover?: VideoCoverResource;
  }>(() => {
    if (!workData.value) return {};
    const relWorkData = workData.value.relData;
    return {
      video: relWorkData?.fullVideo || relWorkData?.baseVideo,
      videoCover: relWorkData?.fullCover || relWorkData?.baseCover, // 旧视频作品的封面图信息
    };
  });

  // 获取详情
  const getWorkDetail = async () => {
    if (props.workId == undefined) return;
    const [err, res] = await getWorkDetailInfo({
      id: props.workId,
      viewMode: 2,
    });
    if (err) {
      message.error(err.message || '获取数据失败');
    }
    workData.value = res?.data || null;
  };

  // 关闭弹窗
  const handleClose = () => {
    stopAllVideos();
    emit('close');
  };

  // 停止所有视频播放
  const stopAllVideos = () => {
    if (!isVideo.value) return;
    if (mainVideoRef.value) stopVideo(mainVideoRef);
    if (newVideoRef.value) stopVideo(newVideoRef);
    if (oldVideoRef.value) stopVideo(oldVideoRef);
  };
  // 停止某个视频的播放
  const stopVideo = (videoRef: Ref<unknown>) => {
    // 兼容VideoPreview组件暴露的stopVideo方法
    (videoRef.value as { stopVideo?: () => void })?.stopVideo?.();
  };

  // 开始某个视频的播放
  const startVideo = (videoRef: Ref<unknown>) => {
    // 兼容VideoPreview组件暴露的startVideo方法
    (videoRef.value as { startVideo?: () => void })?.startVideo?.();
  };

  // 监听props.visible
  watch(
    () => props.visible,
    (val: boolean) => {
      if (val) {
        workData.value = null;
        (async () => {
          await getWorkDetail();
          isShow.value = true;
        })();
      } else {
        isShow.value = false;
      }
    },
    {
      immediate: true,
    },
  );

  // 组件卸载前清理
  onBeforeUnmount(() => {
    stopAllVideos();
  });

  return {
    isShow,
    workData,
    getWorkDetail,
    mainVideoRef,
    newVideoRef,
    oldVideoRef,
    stopAllVideos,
    stopVideo,
    startVideo,
    handleClose,
    isVideo,
    videoData,
    videoRelData,
    isGenerating,
  };
}
