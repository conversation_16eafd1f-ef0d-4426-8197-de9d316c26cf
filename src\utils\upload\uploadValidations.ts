import { message as FaMessage } from '@fk/faicomponent';
import { getFileSuffix } from '@/components/MaterialBasicUpload/utils/index.ts';
import { VIDEO_UPLOAD_LIST, MIMETYPE_KEY } from '@/constants/material';
import {
  VERSION_UPLOAD_VIDEO_LIMIT_MAP,
  VERSION_UPLOAD_IMAGE_LIMIT_MAP,
  VERSION_CAPACITY_LIMIT_TIP,
  VERSION,
} from '@/constants/version';
import { showVersionMsg } from '@/utils/version';

// === 本模块内部常量 ===
const BYTES_TO_MB = 1024 * 1024;
const DEFAULT_MAX_IMAGE = 7680; // 默认最大图片尺寸（7680px）

/**
 * 读取图片尺寸
 */
function getImageDimensions(
  file: File,
): Promise<{ width: number; height: number }> {
  return new Promise((resolve, reject) => {
    const img = new Image();

    img.onload = () => {
      const dimensions = { width: img.width, height: img.height };
      URL.revokeObjectURL(img.src);
      resolve(dimensions);
    };

    img.onerror = () => {
      URL.revokeObjectURL(img.src);
      reject(new Error('无法读取图片尺寸'));
    };

    img.src = URL.createObjectURL(file);
  });
}

/**
 * 验证文件类型
 */
export function validateFileType(file: File, acceptList: string[]): boolean {
  const ext = getFileSuffix(file.name);
  if (!acceptList.includes(ext || '')) {
    FaMessage.error(`文件 ${file.name} 类型不符合要求。`);
    return false;
  }
  return true;
}

/**
 * 验证文件大小（按版本与类型）
 */
export function validateFileSize(file: File, version: VERSION): boolean {
  const fileSizeMB = file.size / BYTES_TO_MB;
  console.log(`文件 ${file.name} 大小：${fileSizeMB}MB`);

  const ext = getFileSuffix(file.name) || '';
  let versionUploadLimit: { limit: number; msg: string };

  if (VIDEO_UPLOAD_LIST.includes(ext as MIMETYPE_KEY)) {
    // 视频文件：先做统一的 200MB 上限校验（>= 200MB 统一提示）
    const maxVideoBytes =
      VERSION_UPLOAD_VIDEO_LIMIT_MAP[VERSION.PRO].limit * BYTES_TO_MB;
    if (file.size >= maxVideoBytes) {
      // 使用专业版的提示文案并以专业版身份展示，以避免出现“去升级”链接
      showVersionMsg(
        VERSION_UPLOAD_VIDEO_LIMIT_MAP[VERSION.PRO].msg,
        VERSION.PRO,
      );
      return false;
    }

    // 再按版本的各自上限（免费 10MB，基础 100MB）进行校验
    versionUploadLimit = VERSION_UPLOAD_VIDEO_LIMIT_MAP[version];
  } else {
    // 图片文件：先做统一的 30MB 上限校验（>= 30MB 统一提示）
    const maxImageBytes =
      VERSION_UPLOAD_IMAGE_LIMIT_MAP[VERSION.PRO].limit * BYTES_TO_MB;
    if (file.size >= maxImageBytes) {
      // 使用专业版的提示文案并以专业版身份展示，以避免出现“去升级”链接
      showVersionMsg(
        VERSION_UPLOAD_IMAGE_LIMIT_MAP[VERSION.PRO].msg,
        VERSION.PRO,
      );
      return false;
    }

    // 再按版本的各自上限（免费 10MB，其他版本 30MB）进行校验
    versionUploadLimit = VERSION_UPLOAD_IMAGE_LIMIT_MAP[version];
  }

  if (file.size > versionUploadLimit.limit * BYTES_TO_MB) {
    showVersionMsg(versionUploadLimit.msg, version);
    return false;
  }
  return true;
}

/**
 * 验证图片分辨率
 */
export async function validateImageResolution(file: File): Promise<boolean> {
  if (!file.type.startsWith('image/')) {
    return true;
  }

  try {
    const { width, height } = await getImageDimensions(file);
    if (width > DEFAULT_MAX_IMAGE || height > DEFAULT_MAX_IMAGE) {
      FaMessage.error(
        `图片 ${file.name} 分辨率过大，不能超过${DEFAULT_MAX_IMAGE}*${DEFAULT_MAX_IMAGE}`,
      );
      return false;
    }
    return true;
  } catch (error) {
    console.error('获取图片分辨率失败:', error);
    FaMessage.error(`图片 ${file.name} 分辨率验证失败，请检查文件是否损坏`);
    return false;
  }
}

/**
 * 验证容量限制
 */
export function validateCapacity(
  totalSize: number,
  capacityTotalSize: number,
  version: VERSION,
): boolean {
  if (totalSize > capacityTotalSize) {
    showVersionMsg(VERSION_CAPACITY_LIMIT_TIP[version], version);
    return false;
  }
  return true;
}
