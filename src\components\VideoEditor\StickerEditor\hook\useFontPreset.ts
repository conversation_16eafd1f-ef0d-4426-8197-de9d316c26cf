import { getFontPreset } from '@/api/VideoEditor/sticker';
import { FontPreset } from '@/types';
import { ref } from 'vue';

const fontPresetList = ref<FontPreset[]>([]);
const fontPresetMap = ref<Map<number, FontPreset>>(new Map());
let loaded = false;

export const useFontPreset = () => {
  if (!loaded) {
    loaded = true;
    getFontPreset().then(([err, res]) => {
      if (err) {
        console.error('get font preset error', err);
        loaded = false;
        return;
      }
      fontPresetList.value = res.data;
      fontPresetList.value.forEach(item => {
        fontPresetMap.value.set(item.id, item);
      });
    });
  }
  return {
    fontPresetList,
    fontPresetMap,
  };
};
