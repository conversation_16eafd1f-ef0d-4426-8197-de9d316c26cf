<template>
  <div class="material-container-example">
    <h2 class="example-title">素材容器组件示例</h2>

    <div class="example-section">
      <h3>文本类型</h3>
      <MaterialContainer
        type="text"
        content="如果你是烧鸟爱好者，或者只是想找个地方解决深夜食堂的煎熬，那这家隐藏在海珠区的小店一定不能错过！"
        @action="handleAction('text')"
      />
    </div>

    <div class="example-section">
      <h3>音乐类型</h3>
      <MaterialContainer
        type="music"
        title="Piano Music ver."
        duration="00:50"
        @action="handleAction('music')"
      />
    </div>

    <div class="example-section">
      <h3>配音类型</h3>
      <MaterialContainer
        type="voiceover"
        title="影视解说小帅"
        voiceType="男声"
        :avatar="defaultAvatar"
        @action="handleAction('voiceover')"
      />
    </div>

    <div class="example-section">
      <h3>卡片类型</h3>
      <MaterialContainer
        type="card"
        content="你有多久没有挑战自己了？学习，不只是运动，更是一种超越自己的方式！"
      />
    </div>

    <div class="example-section">
      <h3>自定义操作按钮文本</h3>
      <MaterialContainer
        type="text"
        content="同城必打卡🔥海珠宝藏烧鸟店——野狗烧鸟"
        actionText="修改内容"
        @action="handleAction('custom')"
      />
    </div>

    <div class="example-section">
      <h3>不显示操作按钮</h3>
      <MaterialContainer
        type="text"
        content="📍 地址：广州海珠区龙凤街道"
        :showActions="false"
      />
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent } from 'vue';
import MaterialContainer from './MaterialContainer.vue';

export default defineComponent({
  name: 'MaterialContainerExample',
  components: {
    MaterialContainer,
  },
  data() {
    return {
      defaultAvatar: 'https://placeholder.com/40',
    };
  },
  methods: {
    handleAction(type: string) {
      this.$message.success(`点击了${type}类型的操作按钮`);
    },
  },
});
</script>

<style lang="less" scoped>
.material-container-example {
  max-width: 600px;
  margin: 0 auto;
  padding: 20px;

  .example-title {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 20px;
    color: #333;
  }

  .example-section {
    margin-bottom: 24px;

    h3 {
      font-size: 14px;
      margin-bottom: 12px;
      color: #666;
    }
  }
}
</style>
