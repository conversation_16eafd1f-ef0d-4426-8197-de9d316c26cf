import Vue, { VueConstructor, ComponentOptions } from 'vue';

interface ModalOptions {
  component: VueConstructor;
  props?: Record<string, unknown>;
  onConfirm?: (payload?: unknown) => void;
  onClose?: () => void;
}

class ModalPlugin {
  private containers: Map<string, HTMLElement> = new Map();
  private modals: Map<string, Vue> = new Map();

  /**
   * 打开弹窗
   * @param key 弹窗名称，用于唯一标识弹窗
   * @param options  弹窗props
   */
  open(key: string, options: ModalOptions) {
    // 关闭已存在的弹窗
    this.close(key);

    // 创建容器
    const container = document.createElement('div');
    document.body.appendChild(container);
    this.containers.set(key, container);

    // 创建弹窗实例
    const ModalComponent = Vue.extend(
      options.component as ComponentOptions<Vue>,
    );

    const modalInstance = new ModalComponent({
      propsData: options.props,
      // parent: this, // 可选：设置父实例
    });

    modalInstance.$on('confirm', (payload: unknown) => {
      options.onConfirm?.(payload);
    });

    // 监听关闭事件
    modalInstance.$on('close', () => {
      this.close(key);
      options.onClose?.();
    });

    // 挂载弹窗
    modalInstance.$mount(container);
    this.modals.set(key, modalInstance);
  }

  // 关闭弹窗
  close(key: string) {
    const modalInstance = this.modals.get(key);
    const container = this.containers.get(key);

    if (modalInstance && container) {
      modalInstance.$destroy();
      container.remove();
      this.modals.delete(key);
      this.containers.delete(key);
    }
  }

  // 关闭所有弹窗
  closeAll() {
    this.containers.forEach((_, key) => this.close(key));
  }
}

export default {
  install(Vue: VueConstructor) {
    const modalPlugin = new ModalPlugin();
    Vue.prototype.$modal = modalPlugin;
  },
};
