# 作品恢复后选中状态保持方案 - 优化版

## 问题描述

当作品重新生成失败后，用户点击"恢复旧作品"时，系统会调用 `refreshWorkList()` 回到第一页并刷新列表。由于后端按特定业务逻辑排序返回数据，刷新后选中的第一个作品可能不是用户操作的旧作品，导致用户体验割裂。

**具体场景：**
- 用户操作作品 10 进行恢复
- 系统刷新列表后自动选中第一个作品（可能是作品 1）
- 用户感知上的操作对象发生了变化

## 优化方案：基于 onSuccess 回调的智能选中恢复

### 核心思路

利用现有的 `updateWorkItems` 方法的 `onSuccess` 回调机制，在完整刷新列表后执行智能选中恢复逻辑。这样既保持了现有架构的一致性，又实现了功能解耦。

### 架构优势

1. **保持现有架构一致性**：继续使用 `updateWorkItems` 进行列表刷新
2. **功能解耦**：选中恢复逻辑独立于刷新逻辑
3. **扩展性好**：可以轻松扩展到其他需要智能选中的场景
4. **向后兼容**：不影响现有的刷新调用

### 实现方案

#### 1. 新增智能选中恢复方法

```typescript
/**
 * 智能选中作品恢复方法
 * @param targetWorkId 目标作品ID
 * @param context 操作上下文，用于日志记录
 * @returns Promise<boolean> 是否成功选中目标作品
 */
const smartSelectWorkAfterRefresh = async (
  targetWorkId: number,
  context: string = '智能选中恢复'
): Promise<boolean> => {
  const startTime = performance.now();
  
  logger.operationStart(context, {
    目标作品ID: targetWorkId,
    当前列表长度: cachedItems.value.length,
    操作策略: '优先精确匹配，回退到默认选中',
  });

  try {
    // 检查列表是否为空
    if (cachedItems.value.length === 0) {
      logger.warn(`${context} 失败`, { 原因: '作品列表为空' });
      return false;
    }

    // 策略1：精确匹配目标作品ID
    const targetWork = cachedItems.value.find(work => work.id === targetWorkId);
    if (targetWork) {
      selectedWorkIds.value = [targetWorkId];
      const [err] = await switchToWork(targetWorkId);
      
      if (!err) {
        logger.operationEnd(context, {
          选中策略: '精确匹配',
          选中作品ID: targetWorkId,
          选中作品名称: targetWork.name,
        }, performance.now() - startTime);
        return true;
      } else {
        logger.warn(`${context} 切换失败`, { 
          作品ID: targetWorkId, 
          错误: err.message 
        });
      }
    }

    // 策略2：回退到默认选中第一个作品
    const firstWork = cachedItems.value[0];
    selectedWorkIds.value = [firstWork.id];
    const [err] = await switchToWork(firstWork.id);
    
    if (!err) {
      logger.operationEnd(context, {
        选中策略: '回退默认',
        原目标作品ID: targetWorkId,
        实际选中作品ID: firstWork.id,
        实际选中作品名称: firstWork.name,
        回退原因: targetWork ? '切换失败' : '目标作品不存在',
      }, performance.now() - startTime);
      return false; // 返回false表示未能选中目标作品
    } else {
      logger.operationError(context, err, performance.now() - startTime);
      return false;
    }
  } catch (error) {
    const err = error instanceof Error ? error : new Error('未知错误');
    logger.operationError(context, err, performance.now() - startTime);
    return false;
  }
};
```

#### 2. 新增带回调的刷新方法

```typescript
/**
 * 刷新作品列表并执行智能选中恢复
 * @param targetWorkId 目标作品ID（可选）
 * @param context 操作上下文
 * @returns Promise<[Error | null, boolean | null]> 错误状态和是否成功选中目标作品
 */
const refreshWorkListWithSmartSelection = async (
  targetWorkId?: number,
  context: string = '刷新列表并智能选中'
): Promise<[Error | null, boolean | null]> => {
  
  // 使用现有的 updateWorkItems 方法进行完整刷新
  return new Promise((resolve) => {
    updateWorkItems([], {
      refreshFullList: true,
      allowEmptyIds: true,
      context,
      onSuccess: async () => {
        // 在刷新成功后执行智能选中恢复
        if (targetWorkId) {
          const success = await smartSelectWorkAfterRefresh(
            targetWorkId, 
            `${context}-智能选中恢复`
          );
          resolve([null, success]);
        } else {
          // 如果没有指定目标作品ID，使用默认选中逻辑
          resolve([null, null]);
        }
      },
      onError: (error) => {
        resolve([error, null]);
      }
    });
  });
};
```

#### 3. 更新接口定义

```typescript
export interface UseInfiniteWorkListReturn<T extends WorkItem> {
  // ... 现有接口
  
  /** 刷新作品列表并执行智能选中恢复 */
  refreshWorkListWithSmartSelection: (
    targetWorkId?: number,
    context?: string
  ) => Promise<[Error | null, boolean | null]>;
  
  /** 智能选中作品恢复方法 */
  smartSelectWorkAfterRefresh: (
    targetWorkId: number,
    context?: string
  ) => Promise<boolean>;
}
```

#### 4. 在恢复旧作品场景中的使用

```typescript
// 在恢复旧作品的处理函数中
const handleRestoreOldWork = async (oldWorkId: number) => {
  try {
    // 执行恢复旧作品的API调用
    const [restoreErr] = await restoreOldWorkAPI(oldWorkId);
    
    if (restoreErr) {
      message.error('恢复旧作品失败');
      return;
    }

    // 刷新列表并智能选中恢复的旧作品
    const [refreshErr, selectionSuccess] = await refreshWorkListWithSmartSelection(
      oldWorkId,
      '恢复旧作品后刷新'
    );

    if (refreshErr) {
      message.error('刷新作品列表失败');
      return;
    }

    if (selectionSuccess) {
      message.success('恢复旧作品成功');
    } else {
      message.success('恢复旧作品成功，但未能自动选中该作品');
    }
  } catch (error) {
    console.error('恢复旧作品失败:', error);
    message.error('恢复旧作品失败');
  }
};
```

### 方案优势

#### ✅ 架构层面
- **保持一致性**：继续使用 `updateWorkItems` 的完整刷新机制
- **功能解耦**：选中恢复逻辑独立，不影响刷新逻辑
- **扩展性强**：可以轻松扩展到其他场景
- **向后兼容**：不破坏现有调用方式

#### ✅ 用户体验
- **操作连贯**：恢复后自动选中用户操作的作品
- **智能回退**：目标作品不存在时自动选中第一个
- **状态反馈**：明确告知用户选中结果

#### ✅ 开发维护
- **代码清晰**：逻辑分离，职责明确
- **易于测试**：独立的方法便于单元测试
- **日志完善**：详细的操作日志便于调试
- **错误处理**：完善的错误处理和回退机制

### 实施步骤

1. **第一步**：在 `useInfiniteWorkList.ts` 中添加 `smartSelectWorkAfterRefresh` 方法
2. **第二步**：添加 `refreshWorkListWithSmartSelection` 方法
3. **第三步**：更新接口定义，导出新方法
4. **第四步**：在恢复旧作品的业务逻辑中使用新方法
5. **第五步**：编写单元测试验证功能
6. **第六步**：在实际场景中测试用户体验

### 测试用例

```typescript
describe('智能选中恢复功能', () => {
  test('目标作品存在时应该精确选中', async () => {
    // 模拟作品列表
    const mockWorks = [
      { id: 1, name: '作品1' },
      { id: 10, name: '作品10' },
      { id: 20, name: '作品20' }
    ];
    
    // 执行智能选中
    const success = await smartSelectWorkAfterRefresh(10, '测试精确选中');
    
    expect(success).toBe(true);
    expect(selectedWorkIds.value).toEqual([10]);
    expect(currentWorkId.value).toBe(10);
  });

  test('目标作品不存在时应该回退到第一个', async () => {
    const mockWorks = [
      { id: 1, name: '作品1' },
      { id: 20, name: '作品20' }
    ];
    
    const success = await smartSelectWorkAfterRefresh(10, '测试回退选中');
    
    expect(success).toBe(false);
    expect(selectedWorkIds.value).toEqual([1]);
    expect(currentWorkId.value).toBe(1);
  });
});
```

### 总结

这个优化方案通过利用现有的 `onSuccess` 回调机制，实现了功能解耦和架构一致性的完美平衡。既解决了用户体验问题，又保持了代码的清晰性和可维护性。
