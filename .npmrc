# 配置 npm 的默认镜像源为淘宝镜像源
registry=https://registry.npmmirror.com/

# 配置 @fk 私有源
@fk:registry=http://registry.npm.faidev.cc

# 配置了一些静态文件的下载路径，某些原生模块在编译时node-gyp可能会用它下载额外的头文件
disturl=https://npmmirror.com/dist

# sharp模块的二进制文件下载地址
sharp_dist_base_url=https://npmmirror.com/mirrors/sharp-libvips/

# node-sass模块的二进制文件下载地址
sass_binary_site=https://npmmirror.com/mirrors/node-sass/

# Electron模块的二进制文件下载地址
electron_mirror=https://npmmirror.com/mirrors/electron/

# chromedriver模块的二进制文件下载地址
chromedriver_cdnurl=https://npmmirror.com/mirrors/chromedriver/

# operadriver模块的二进制文件下载地址
operadriver_cdnurl=https://npmmirror.com/mirrors/operadriver/

# phantomjs模块的二进制文件下载地址
phantomjs_cdnurl=https://npmmirror.com/mirrors/phantomjs/

# selenium模块的二进制文件下载地址
selenium_cdnurl=https://npmmirror.com/mirrors/selenium/

# node-inspector模块的二进制文件下载地址
node_inspector_cdnurl=https://npmmirror.com/mirrors/node-inspector/

# puppeteer模块的下载地址
puppeteer_download_host=https://npmmirror.com/mirrors/
