# useSecondStep 组件引用调用优化文档

## 优化背景

在原有的 `useSecondStep.ts` 中，`handleSaveToWorks` 方法通过组件引用直接调用 `currentCompRef.handleSaveUpdate()` 方法，这种方式存在以下问题：

1. **高耦合度**：直接依赖组件实例，增加了组件间的耦合
2. **维护性差**：需要确保组件暴露特定方法，容易出现调用错误
3. **测试困难**：难以进行单元测试，需要模拟完整的组件实例
4. **类型安全性**：依赖运行时检查方法是否存在

## 优化方案

### 方案选择：EventBus 事件总线

采用 EventBus 事件总线机制替代直接的组件引用调用，具有以下优势：

- ✅ **低耦合度**：通过事件通信，组件间解耦
- ✅ **高可维护性**：事件驱动，易于理解和维护
- ✅ **易于测试**：可以独立测试事件发送和接收
- ✅ **类型安全**：通过 TypeScript 类型定义确保类型安全

## 具体修改

### 1. useSecondStep.ts 修改

#### 修改前

```typescript
const handleSaveToWorks = async (
  currentCompRef: ComponentRef,
  projectId?: number,
): Promise<void> => {
  // ... 业务逻辑 ...

  // 通过组件引用直接调用方法
  if (typeof currentCompRef.handleSaveUpdate === 'function') {
    await currentCompRef.handleSaveUpdate(
      isAllSelected.value ? [] : selectedWorkIds.value,
      isAllSelected.value,
    );
  }
};
```

#### 修改后

```typescript
const handleSaveToWorks = async (
  projectId?: number,
  workList?: Array<{ id: number; status: number; [key: string]: unknown }>,
): Promise<void> => {
  // ... 业务逻辑 ...

  // 使用 EventBus 通知第二步组件更新作品列表
  eventBus.emit(EVENT_NAMES.WORK_SAVE_REQUEST, {
    workIds: isAllSelected.value ? [] : selectedWorkIds.value,
    isBatchSave: isAllSelected.value,
  });
};
```

### 2. 类型定义修改

#### 修改前

```typescript
handleSaveToWorks: (currentCompRef: ComponentRef, projectId?: number) =>
  Promise<void>;
```

#### 修改后

```typescript
handleSaveToWorks: (
  projectId?: number,
  workList?: Array<{ id: number; status: number; [key: string]: unknown }>,
) => Promise<void>;
```

### 3. 主组件调用修改

#### 修改前

```typescript
const handleSaveToWorks = async (): Promise<void> => {
  const component = activeComponentRef.value;
  if (!component) return;

  await saveToWorks(component, currentProjectId.value);
};
```

#### 修改后

```typescript
const handleSaveToWorks = async (): Promise<void> => {
  const component = activeComponentRef.value;
  if (!component) return;

  // 获取当前组件的作品列表数据
  const workList = component.workList || [];
  await saveToWorks(currentProjectId.value, workList);
};
```

## 事件处理机制

### 事件发送

```typescript
// useSecondStep.ts 中发送事件
eventBus.emit(EVENT_NAMES.WORK_SAVE_REQUEST, {
  workIds: isAllSelected.value ? [] : selectedWorkIds.value,
  isBatchSave: isAllSelected.value,
});
```

### 事件接收

```typescript
// useInfiniteWorkList.ts 中已有的事件监听
onMounted(() => {
  eventBus.on(EVENT_NAMES.WORK_SAVE_REQUEST, handleWorkSaveRequest);
});

const handleWorkSaveRequest = async (data: WorkSaveRequestData) => {
  await handleSaveUpdate(data.workIds, data.isBatchSave);
};
```

## 优化效果

### 1. 降低耦合度

- 移除了对 `ComponentRef` 类型的依赖
- 组件间通过事件通信，不再直接依赖组件实例

### 2. 提高可维护性

- 事件驱动的架构更清晰
- 减少了组件间的直接依赖关系
- 符合用户偏好的简单可维护代码架构

### 3. 增强类型安全

- 通过 TypeScript 类型定义确保事件数据结构正确
- 编译时检查，减少运行时错误

### 4. 改善测试性

- 可以独立测试事件发送逻辑
- 可以模拟事件接收进行测试
- 不需要创建完整的组件实例

## 相关文件

- `src/views/EditProjectView/composables/useSecondStep.ts` - 主要修改文件
- `src/views/EditProjectView/types/components.ts` - 类型定义修改
- `src/views/EditProjectView/index.vue` - 调用方式修改
- `src/views/EditProjectView/composables/useInfiniteWorkList.ts` - 事件接收处理
- `src/utils/eventBus.ts` - 事件总线定义

## 注意事项

1. **事件名称一致性**：确保发送和接收使用相同的事件名称
2. **数据结构一致性**：确保事件数据符合 `WorkSaveRequestData` 类型定义
3. **错误处理**：事件接收方需要处理可能的错误情况
4. **内存泄漏**：确保在组件卸载时清理事件监听器

## 进一步清理：移除不必要的方法暴露

### 4. 组件方法暴露清理

在完成 EventBus 优化后，发现第二步组件不再需要暴露 `handleSaveUpdate` 方法：

#### 清理前

```typescript
// SecondStepVideo/index.vue 和 SecondStepImage/index.vue
return {
  // ... 其他属性
  handleSaveUpdate, // 不再需要暴露
  handleEditUpdate,
  handleGenerateUpdate,
  workList: displayedWorkList,
};
```

#### 清理后

```typescript
// SecondStepVideo/index.vue 和 SecondStepImage/index.vue
return {
  // ... 其他属性
  handleEditUpdate, // 保留：视频编辑器回调需要
  handleGenerateUpdate, // 保留：视频编辑器回调需要
  workList: displayedWorkList, // 保留：父组件需要访问作品列表
};
```

### 5. 类型定义清理

同时更新 `ComponentRef` 接口，移除不再需要的方法定义：

#### 清理前

```typescript
export interface ComponentRef {
  validateAllForms?: () => Promise<ValidationResult>;
  handleSaveUpdate?: (
    workIds: number[],
    isBatchSave?: boolean,
  ) => Promise<void>;
  workList?: Array<{ id: number; status: number; [key: string]: unknown }>;
  // ...
}
```

#### 清理后

```typescript
export interface ComponentRef {
  validateAllForms?: () => Promise<ValidationResult>;
  workList?: Array<{ id: number; status: number; [key: string]: unknown }>;
  // ...
}
```

### 清理效果

1. **减少暴露面**：组件只暴露真正需要的方法
2. **提高安全性**：避免外部意外调用不应该直接调用的方法
3. **简化接口**：类型定义更加简洁明确
4. **保持功能**：所有必要的功能都通过 EventBus 正常工作

## 总结

这次优化成功地将组件引用调用方式改为 EventBus 事件总线机制，并进一步清理了不必要的方法暴露，符合用户偏好的低耦合、高可维护性的代码架构。通过事件驱动的方式，提高了代码的可测试性和可维护性，同时保持了功能的完整性。
