/**
 * @fileoverview 智能轮询工具函数入口文件
 * @description 统一导出所有工具函数
 */

// 进度计算工具
export { calculateMaxProgress } from './progress';

// 间隔计算工具
export { calculatePollingInterval } from './interval';

// 上下文创建工具
export { createPollingContext, getProjectTypeDescription } from './context';

// 验证工具
export { validateOptions } from './validation';

// 事件处理器工具
export { registerEventHandlers } from './eventHandlers';

// 状态检查辅助函数
export {
  isCompletionStatusChange,
  isFailureStatusChange,
  isCriticalStatusChange,
  getStatusChangeType,
  isValidStatusChangeEvent,
} from './statusHelpers';
