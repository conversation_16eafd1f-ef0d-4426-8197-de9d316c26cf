/**
 * @description 页面导航相关常量
 */

/**
 * 页面来源类型枚举
 * 用于标识用户从哪个页面跳转到编辑项目页面
 */
export enum PAGE_SOURCE {
  /** 模板页面 */
  TEMPLATE = 'template',
  /** 项目列表页面 */
  PROJECT = 'project',
  /** 作品列表页面 */
  WORK = 'work',
}

/**
 * 页面来源对应的返回路径映射
 */
export const PAGE_SOURCE_RETURN_PATH: Record<PAGE_SOURCE, string> = {
  [PAGE_SOURCE.TEMPLATE]: '/',
  [PAGE_SOURCE.PROJECT]: '/project',
  [PAGE_SOURCE.WORK]: '/work',
};
