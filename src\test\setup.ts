/**
 * Vitest 测试环境设置文件
 * 配置全局测试环境和模拟
 */

import { vi } from 'vitest';
import { config } from '@vue/test-utils';
import Vue from 'vue';

// 检测是否为浏览器环境
const isBrowser =
  typeof window !== 'undefined' && typeof document !== 'undefined';

// 模拟 Element UI 组件 (仅在非浏览器环境)
if (!isBrowser) {
  vi.mock('element-ui', () => ({
    Message: {
      success: vi.fn(),
      error: vi.fn(),
      warning: vi.fn(),
      info: vi.fn(),
    },
    MessageBox: {
      confirm: vi.fn(),
      alert: vi.fn(),
      prompt: vi.fn(),
    },
    Loading: {
      service: vi.fn(() => ({
        close: vi.fn(),
      })),
    },
    Notification: {
      success: vi.fn(),
      error: vi.fn(),
      warning: vi.fn(),
      info: vi.fn(),
    },
  }));

  // 模拟 Vue Router
  vi.mock('vue-router', () => ({
    useRouter: () => ({
      push: vi.fn(),
      replace: vi.fn(),
      go: vi.fn(),
      back: vi.fn(),
      forward: vi.fn(),
    }),
    useRoute: () => ({
      params: {},
      query: {},
      path: '/',
      name: 'test',
    }),
  }));

  // 模拟 Vuex Store
  vi.mock('@/store', () => ({
    default: {
      state: {},
      getters: {},
      mutations: {},
      actions: {},
      dispatch: vi.fn(),
      commit: vi.fn(),
    },
  }));

  // 模拟 API 请求
  vi.mock('@/api/request', () => ({
    GET: vi.fn(),
    POST: vi.fn(),
    POST_FORM: vi.fn(),
    POST_JSON: vi.fn(),
    POST_MULTIPART: vi.fn(),
    PUT: vi.fn(),
    DELETE: vi.fn(),
  }));
}

// 模拟浏览器 API (仅在非浏览器环境)
if (!isBrowser && typeof window !== 'undefined') {
  Object.defineProperty(window, 'matchMedia', {
    writable: true,
    value: vi.fn().mockImplementation(query => ({
      matches: false,
      media: query,
      onchange: null,
      addListener: vi.fn(), // deprecated
      removeListener: vi.fn(), // deprecated
      addEventListener: vi.fn(),
      removeEventListener: vi.fn(),
      dispatchEvent: vi.fn(),
    })),
  });
}

// 模拟 localStorage (仅在非浏览器环境)
if (!isBrowser && typeof window !== 'undefined') {
  const localStorageMock = {
    getItem: vi.fn(),
    setItem: vi.fn(),
    removeItem: vi.fn(),
    clear: vi.fn(),
  };
  Object.defineProperty(window, 'localStorage', {
    value: localStorageMock,
  });

  // 模拟 sessionStorage
  const sessionStorageMock = {
    getItem: vi.fn(),
    setItem: vi.fn(),
    removeItem: vi.fn(),
    clear: vi.fn(),
  };
  Object.defineProperty(window, 'sessionStorage', {
    value: sessionStorageMock,
  });
}

// 模拟 ResizeObserver (仅在非浏览器环境)
if (!isBrowser) {
  (globalThis as any).ResizeObserver = vi.fn().mockImplementation(() => ({
    observe: vi.fn(),
    unobserve: vi.fn(),
    disconnect: vi.fn(),
  }));

  // 模拟 IntersectionObserver
  (globalThis as any).IntersectionObserver = vi.fn().mockImplementation(() => ({
    observe: vi.fn(),
    unobserve: vi.fn(),
    disconnect: vi.fn(),
  }));
}

// 配置 Vue Test Utils 全局选项
if (!isBrowser) {
  config.mocks = {
    $router: {
      push: vi.fn(),
      replace: vi.fn(),
      go: vi.fn(),
      back: vi.fn(),
      forward: vi.fn(),
    },
    $route: {
      params: {},
      query: {},
      path: '/',
      name: 'test',
    },
    $store: {
      state: {},
      getters: {},
      dispatch: vi.fn(),
      commit: vi.fn(),
    },
    $message: {
      success: vi.fn(),
      error: vi.fn(),
      warning: vi.fn(),
      info: vi.fn(),
    },
  };
} else {
  // 浏览器环境下的简化配置
  config.mocks = {
    $router: {
      push: () => {},
      replace: () => {},
      go: () => {},
      back: () => {},
      forward: () => {},
    },
    $route: {
      params: {},
      query: {},
      path: '/',
      name: 'test',
    },
    $store: {
      state: {},
      getters: {},
      dispatch: () => {},
      commit: () => {},
    },
    $message: {
      success: () => {},
      error: () => {},
      warning: () => {},
      info: () => {},
    },
  };
}

// 配置 Vue 全局设置
Vue.config.productionTip = false;

// 全局错误处理
if (typeof process !== 'undefined') {
  process.on('unhandledRejection', reason => {
    console.error('Unhandled Rejection:', reason);
  });
}

// 设置测试超时时间 (仅在非浏览器环境)
if (!isBrowser) {
  vi.setConfig({
    testTimeout: 10000,
  });
}
