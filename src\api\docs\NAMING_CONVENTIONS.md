# API 数据转换函数命名规范

## 概述

本文档定义了项目中 API 相关数据转换函数的命名规范，旨在提高代码的可读性和可维护性。良好的命名规范可以使开发者更容易理解数据流向和函数用途。

## 数据转换方向

在我们的项目中，数据转换主要有两个方向：

1. **输入转换 (Input Transform)**: 将 API 响应数据转换为前端内部使用的数据结构
2. **输出转换 (Output Transform)**: 将前端内部数据结构转换为 API 请求格式

## 命名规则

### 基本命名模式

```
transform{ModuleName}{Source}To{Target}
```

其中：

- `{ModuleName}`: API 模块名称或接口名称，用于区分不同模块的转换函数
- `{Source}`: 数据的来源/原始格式
- `{Target}`: 数据的目标格式

### 输入转换函数 (API → 前端)

```typescript
transformTemplateApiDataToProjectData;
transformUserApiResponseToViewModel;
transformProductServerDataToClientModel;
```

### 输出转换函数 (前端 → API)

```typescript
transformProjectDataToTemplateApiFormat;
transformViewModelToUserApiRequest;
transformClientModelToProductServerData;
```

### 辅助函数命名

对于特定字段或部分数据的转换函数：

```typescript
transform{ModuleName}{SpecificData}From{Source}To{Target}

// 示例
transformTemplateInputFormFromApiToProject
transformProjectResFormFromProjectToApi
```

## 实际示例

```typescript
// 输入转换 (API → 前端) - 模板数据
export function transformTemplateApiDataToProjectData(apiData) {
  // 将模板API数据转换为前端项目数据
}

// 输出转换 (前端 → API) - 项目更新
export function transformProjectDataToUpdateApiFormat(projectData) {
  // 将前端项目数据转换为更新API请求格式
}

// 特定字段转换 - 用户表单
export function transformUserInputFormFromApiToProject(apiInputForm) {
  // 将用户API输入表单数据转换为前端表单数据
}
```

## 文件组织

为保持代码结构清晰，我们采用以下文件组织方式：

```shell
src/api/ModuleName/
  ├── index.ts               # 主API导出文件
  ├── types/                 # 类型定义
  │   ├── index.ts           # 类型导出
  │   ├── request.ts         # 请求类型
  │   └── response.ts        # 响应类型
  └── utils/                 # 工具函数
      ├── inputDataTransform.ts  # API→前端 转换函数
      └── outputDataTransform.ts # 前端→API 转换函数
```

## 模块名称缩写建议

为了避免函数名过长，可以为常用模块定义统一的缩写：

| 完整模块名称   | 建议缩写 |
| -------------- | -------- |
| Template       | Tmpl     |
| Project        | Proj     |
| EditProject    | EditProj |
| UserProfile    | User     |
| Authentication | Auth     |
| Configuration  | Config   |

## 总结

遵循这些命名规范可以：

1. 清晰表达数据流向
2. 明确区分不同模块的转换函数
3. 提高代码可读性
4. 便于新成员理解代码
5. 保持项目风格一致性

请在开发过程中严格遵循这些规范，确保代码质量和可维护性。
