import { FontIdsMap } from '../types';

export const fontStylesConfig: FontIdsMap = {
  888: {
    fontStyle: '微软雅黑',
    fontSize: '14px',
    color: '#000000',
    border: '1px solid #000000',
    shadow: '0 0 10px 0 rgba(0, 0, 0, 0.5)',
  },
  889: {
    fontStyle: '宋体',
    fontSize: '16px',
    color: '#FF0000',
    border: '2px dashed #FF0000',
    shadow: '0 0 8px 0 rgba(255, 0, 0, 0.5)',
  },
  890: {
    fontStyle: '黑体',
    fontSize: '18px',
    color: '#0000FF',
    border: '2px dotted #0000FF',
    shadow: '0 0 12px 0 rgba(0, 0, 255, 0.5)',
  },
  891: {
    fontStyle: '楷体',
    fontSize: '20px',
    color: '#008000',
    border: '3px double #008000',
    shadow: '0 0 15px 0 rgba(0, 128, 0, 0.5)',
  },
  892: {
    fontStyle: 'Arial',
    fontSize: '16px',
    color: '#800080',
    border: '1px solid #800080',
    shadow: '0 0 10px 0 rgba(128, 0, 128, 0.5)',
  },
  893: {
    fontStyle: 'Times New Roman',
    fontSize: '18px',
    color: '#FFA500',
    border: '2px ridge #FFA500',
    shadow: '0 0 12px 0 rgba(255, 165, 0, 0.5)',
  },
  894: {
    fontStyle: 'Georgia',
    fontSize: '14px',
    color: '#A52A2A',
    border: '2px groove #A52A2A',
    shadow: '0 0 8px 0 rgba(165, 42, 42, 0.5)',
  },
  895: {
    fontStyle: 'Verdana',
    fontSize: '16px',
    color: '#4B0082',
    border: '1px outset #4B0082',
    shadow: '0 0 10px 0 rgba(75, 0, 130, 0.5)',
  },
  896: {
    fontStyle: 'Helvetica',
    fontSize: '15px',
    color: '#006400',
    border: '2px inset #006400',
    shadow: '0 0 12px 0 rgba(0, 100, 0, 0.5)',
  },
  897: {
    fontStyle: 'Tahoma',
    fontSize: '17px',
    color: '#8B4513',
    border: '1px solid #8B4513',
    shadow: '0 0 10px 0 rgba(139, 69, 19, 0.5)',
  },
  898: {
    fontStyle: 'Palatino',
    fontSize: '19px',
    color: '#483D8B',
    border: '2px dashed #483D8B',
    shadow: '0 0 14px 0 rgba(72, 61, 139, 0.5)',
  },
  899: {
    fontStyle: '宋体',
    fontSize: '18px',
    color: '#2F4F4F',
    border: '3px dotted #2F4F4F',
    shadow: '0 0 13px 0 rgba(47, 79, 79, 0.5)',
  },
  900: {
    fontStyle: 'Trebuchet MS',
    fontSize: '16px',
    color: '#8B008B',
    border: '2px double #8B008B',
    shadow: '0 0 11px 0 rgba(139, 0, 139, 0.5)',
  },
  901: {
    fontStyle: 'Garamond',
    fontSize: '17px',
    color: '#556B2F',
    border: '1px ridge #556B2F',
    shadow: '0 0 12px 0 rgba(85, 107, 47, 0.5)',
  },
  902: {
    fontStyle: '黑体',
    fontSize: '15px',
    color: '#8B0000',
    border: '2px groove #8B0000',
    shadow: '0 0 10px 0 rgba(139, 0, 0, 0.5)',
  },
};
