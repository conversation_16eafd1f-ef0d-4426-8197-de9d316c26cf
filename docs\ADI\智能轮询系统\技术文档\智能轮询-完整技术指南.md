# 智能轮询系统 - 完整技术指南

## 📋 系统概述

智能轮询系统是作品生成页面的核心功能，经过多次重构优化，目前采用模块化的事件驱动架构，提供了完整的状态监控和进度更新解决方案。

**当前版本**: v6.0.0 (模块化架构)  
**最后更新**: 2025-01-11  
**维护状态**: 架构稳定，功能完善

## 🏗️ 架构设计

### 模块化文件结构

```text
src/views/EditProjectView/composables/useWorkPolling/
├── index.ts                    # 主入口文件 (421行)
├── config/                     # 配置模块
│   ├── index.ts               # 配置入口
│   ├── polling.ts             # 轮询间隔配置
│   └── thresholds.ts          # 进度阈值配置
├── constants/                  # 常量模块
│   ├── index.ts               # 常量入口
│   ├── events.ts              # 事件类型常量
│   └── triggers.ts            # 触发类型常量
├── types/                      # 类型模块
│   ├── index.ts               # 类型入口
│   ├── events.ts              # 事件类型定义
│   ├── config.ts              # 配置类型定义
│   └── polling.ts             # 轮询类型定义
├── core/                       # 核心模块
│   ├── index.ts               # 核心入口
│   ├── EventManager.ts        # 事件管理器
│   ├── StateAnalyzer.ts       # 状态分析器
│   └── PollingController.ts   # 轮询控制器
└── utils/                      # 工具模块
    ├── index.ts               # 工具入口
    ├── context.ts             # 上下文创建
    ├── eventHandlers.ts       # 事件处理器注册
    ├── interval.ts            # 间隔计算
    ├── progress.ts            # 进度计算
    └── validation.ts          # 配置验证
```

### 核心设计原则

1. **事件驱动架构**: 基于事件系统处理状态变化
2. **模块化设计**: 职责分离，便于维护和扩展
3. **类型安全**: 完全使用 TypeScript，禁止 any 类型
4. **配置集中**: 所有配置项统一管理
5. **测试覆盖**: 完整的单元测试和集成测试

## 🔧 核心模块详解

### 1. EventManager (事件管理器)

负责事件的注册、触发和管理：

```typescript
// 事件类型定义
export const WORK_POLLING_EVENTS = {
  PROGRESS_CHANGED: 'progress:changed',
  WORK_STATUS_CHANGED: 'work:status:changed',
  WORK_COMPLETED: 'work:completed',
  POLLING_STARTED: 'polling:started',
  POLLING_STOPPED: 'polling:stopped',
} as const;
```

### 2. StateAnalyzer (状态分析器)

分析作品状态变化，检测需要轮询的作品：

- 识别生成中的作品
- 计算进度变化
- 检测状态转换

### 3. PollingController (轮询控制器)

控制轮询的启动、停止和频率调整：

- 智能频率调整
- 超时控制
- 页面可见性控制

## ⚙️ 配置系统

### 轮询间隔配置

```typescript
// config/polling.ts
export const POLLING_INTERVALS = {
  VIDEO: {
    LOW_PROGRESS: 5000, // < 20%
    MID_PROGRESS: 10000, // 20%-90%
    HIGH_PROGRESS: 3000, // ≥ 90%
  },
  IMAGE: {
    LOW_PROGRESS: 4000, // < 50%
    HIGH_PROGRESS: 2000, // ≥ 50%
  },
};
```

### 进度阈值配置

```typescript
// config/thresholds.ts
export const PROGRESS_THRESHOLDS = {
  VIDEO: {
    LOW: 20,
    HIGH: 90,
  },
  IMAGE: {
    MEDIUM: 50,
  },
};
```

## 🎯 事件系统使用

### 基本用法

```typescript
import { useWorkPolling } from '@/views/EditProjectView/composables/useWorkPolling';
import {
  isCompletionStatusChange,
  isFailureStatusChange,
} from '@/views/EditProjectView/composables/useWorkPolling/utils';

const { startPolling, stopPolling } = useWorkPolling({
  workList: cachedItems,
  projectId,
  projectType,
  eventHandlers: {
    onDataUpdated: async event => {
      console.log('数据更新:', event.updatedWorks);
    },
    onProgressChanged: async event => {
      console.log('进度变化:', event.currentProgress);
    },
    onStatusChanged: async event => {
      // 统一的状态变化处理
      if (isCompletionStatusChange(event)) {
        console.log('作品完成:', event.workId);
        // 处理完成逻辑
      }

      if (isFailureStatusChange(event)) {
        console.log('作品失败:', event.workId);
        // 处理失败逻辑
      }
    },
  },
});
```

### 可用事件类型

- `progress_changed`: 进度变化事件
- `status_changed`: 作品状态变化事件（统一处理完成、失败等所有状态变化）
- `data_updated`: 数据更新事件

### 新的事件处理架构

从 v7.0.0 开始，智能轮询系统采用了简化的事件处理架构：

#### 核心变化

1. **统一状态处理**：所有状态变化（完成、失败等）都通过 `onStatusChanged` 事件处理器处理
2. **辅助函数支持**：提供了状态检查辅助函数，简化状态判断逻辑
3. **更好的可维护性**：减少了事件类型数量，降低了维护复杂度

#### 状态检查辅助函数

```typescript
import {
  isCompletionStatusChange, // 检查是否为完成状态变化
  isFailureStatusChange, // 检查是否为失败状态变化
  isCriticalStatusChange, // 检查是否为重要状态变化
  getStatusChangeType, // 获取状态变化类型描述
  isValidStatusChangeEvent, // 验证事件有效性
} from '@/views/EditProjectView/composables/useWorkPolling/utils';
```

#### 最佳实践

1. **使用辅助函数**：优先使用提供的辅助函数进行状态检查
2. **添加日志记录**：在状态变化处理中添加适当的日志记录
3. **验证事件有效性**：在处理事件前验证事件的有效性
4. **集中处理逻辑**：将相关的状态处理逻辑集中在 `onStatusChanged` 中

## 🧪 测试环境

### 单元测试

智能轮询系统提供了完整的单元测试覆盖：

**测试文件位置**: `src/views/EditProjectView/composables/__tests__/useWorkPolling*.test.ts`

**测试覆盖范围**:

- 轮询逻辑测试
- 事件系统测试
- 状态分析测试
- 错误处理测试

**运行测试**:

```bash
# 运行智能轮询相关测试
npm test -- useWorkPolling

# 运行所有测试
npm test
```

## 🔍 问题排查

### 常见问题

1. **类型错误**: 检查是否使用了 any 类型，确认类型导入正确
2. **测试失败**: 运行完整测试，检查接口兼容性
3. **功能异常**: 检查事件处理器注册，确认配置导入

### 调试工具

- 浏览器控制台日志
- 单元测试: `npm test -- useWorkPolling`
- 类型检查: `npm run type-check`

## 📚 重要文件位置

### 核心实现

- 主入口: `src/views/EditProjectView/composables/useWorkPolling/index.ts`
- 调用方: `src/views/EditProjectView/composables/useInfiniteWorkList.ts`

### 测试文件

- 主测试: `src/views/EditProjectView/composables/__tests__/useWorkPolling.test.ts`
- 事件测试: `src/views/EditProjectView/composables/__tests__/useWorkPolling-events.test.ts`

### 文档目录

- 技术文档: `docs/ADI/智能轮询系统/技术文档/`
- 更新日志: `docs/ADI/智能轮询系统/更新日志/`

## 🚀 扩展指南

### 添加新功能

1. **新的工具函数** → 添加到 `utils/` 模块
2. **新的配置项** → 添加到 `config/` 模块
3. **新的事件类型** → 添加到 `constants/events.ts`
4. **新的类型定义** → 添加到 `types/` 模块

### 代码质量要求

- 禁止使用 `any` 类型
- 遵循模块化原则
- 保持测试覆盖
- 更新相关文档

## ⚠️ 重要提醒

**架构已经稳定**: 当前架构经过充分验证，不建议进行大的架构调整。

**扩展优先**: 新功能应该在现有模块基础上扩展，而不是重构整体架构。

**测试先行**: 任何修改都必须确保所有测试通过。

---

**维护团队**: 前端开发团队  
**文档版本**: v2.0  
**创建时间**: 2025-01-15
