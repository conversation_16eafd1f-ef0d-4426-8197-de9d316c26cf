/**
 * @fileoverview 分页逻辑的可复用 Composable
 * @description 提供分页相关的状态和方法
 */

import { ref, Ref } from 'vue';

export interface UsePaginationReturn {
  /** 当前页码 */
  currentPage: Ref<number>;
  /** 每页条数 */
  pageSize: Ref<number>;
  /** 总条数 */
  totalItems: Ref<number>;
  /** 设置页码 */
  setPage: (page: number) => void;
  /** 设置每页条数 */
  setPageSize: (size: number) => void;
  /** 设置总条数 */
  setTotalItems: (total: number) => void;
  /** 重置分页 */
  resetPagination: () => void;
  /** 获取分页参数 */
  getPaginationParams: () => { pageNow: number; limit: number };
}

/**
 * 分页逻辑的可复用 Composable
 * @param initialPageSize 初始每页条数
 * @returns 分页相关的状态和方法
 */
export function usePagination(initialPageSize = 10): UsePaginationReturn {
  // 分页相关状态
  const currentPage = ref<number>(1);
  const pageSize = ref<number>(initialPageSize);
  const totalItems = ref<number>(0);

  /**
   * 设置页码
   * @param page 页码
   */
  const setPage = (page: number): void => {
    currentPage.value = page;
  };

  /**
   * 设置每页条数
   * @param size 每页条数
   */
  const setPageSize = (size: number): void => {
    pageSize.value = size;
    // 切换每页条数时，重置为第一页
    currentPage.value = 1;
  };

  /**
   * 设置总条数
   * @param total 总条数
   */
  const setTotalItems = (total: number): void => {
    totalItems.value = total;
  };

  /**
   * 重置分页
   */
  const resetPagination = (): void => {
    currentPage.value = 1;
    totalItems.value = 0;
  };

  /**
   * 获取分页参数
   * @returns 分页参数对象
   */
  const getPaginationParams = () => {
    return {
      pageNow: currentPage.value,
      limit: pageSize.value,
    };
  };

  return {
    currentPage,
    pageSize,
    totalItems,
    setPage,
    setPageSize,
    setTotalItems,
    resetPagination,
    getPaginationParams,
  };
}
