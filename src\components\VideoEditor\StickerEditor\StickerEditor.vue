<template>
  <div class="flex h-full">
    <!-- 左侧预览窗 -->
    <div
      class="relative w-[498px] bg-background flex justify-center items-center overflow-hidden"
    >
      <div class="w-full h-full overflow-auto pt-[16px] pb-[64px] flex">
        <!-- 视频和花字贴图容器 -->
        <div class="relative w-[324px] h-[576px] overflow-hidden z-1 m-auto">
          <!-- 花字贴图容器 -->
          <div class="absolute w-full h-full top-0 left-0 zi-popover">
            <ImageStickerItem
              v-for="(item, index) in imageStickerList"
              :key="'imageSticker-' + index"
              :stickerInfo="item"
              :is-limit="true"
              :max-width="720"
            />
            <TextStickerItem
              v-for="(item, index) in textStickerList"
              :key="index"
              :textInfo="item"
              :is-limit="true"
            />
          </div>
          <!-- 视频控件 -->
          <video
            ref="videoPlayer"
            class="absolute w-full h-full top-0 left-0 video-js"
          ></video>
          <div
            class="absolute top-0 left-0 w-[324px] h-[576px] overflow-hidden z-10 m-auto"
            v-show="videoLoading"
          >
            <ScImg
              :src="workInfo?.data?.videoCover?.baseId"
              :max-width="324"
              class="w-full h-full"
            />
          </div>
        </div>
      </div>
      <!-- 控制栏 -->
      <div class="video-controls-wrap">
        <VideoControls
          :is-playing="isPlaying"
          :is-disabled="isControlDisabled"
          :is-loading="isControlsLoading"
          :buffered-percent="bufferedPercent"
          :played-percent="playedPercent"
          :current-time="currentTime"
          :duration="duration || workInfo?.duration || 0"
          @toggle-play="togglePlay"
          @seek="setPlayerTime"
          @seeking="handleSeeking"
        />
      </div>
      <TextStickerDebugPreview v-if="workInfo" :workInfo="workInfo" />
    </div>
    <!-- 右侧编辑窗 -->
    <div class="flex-1 py-[24px] px-[40px] overflow-y-auto">
      <!-- 花字 -->
      <div class="flex mb-[24px]">
        <div class="text-text w-[80px] pt-[8px]">花字</div>
        <div class="flex-1">
          <TextStickerSetting
            v-for="(item, index) in textStickerList"
            :key="'textStickerSetting-' + index"
            :textInfo="item"
            :class="{
              'mb-[16px]': index !== (textStickerList?.length || 0) - 1,
            }"
            @applyToAll="handleApplyToAll(item)"
            @deleteText="handleDeleteText(item)"
          />
          <fa-button
            type="dashed"
            class="w-full mt-[16px] first:mt-0"
            @click="handleAddText"
            :disabled="(textStickerList?.length || 0) >= 10"
          >
            <div class="flex items-center justify-center">
              <fa-icon type="fa-add" class="mr-[4px]" />
              <span>添加花字</span>
            </div>
          </fa-button>
        </div>
      </div>
      <!-- 贴图 -->
      <div class="flex">
        <div class="text-text w-[80px] pt-[8px]">贴图</div>
        <div class="flex flex-wrap gap-[16px]">
          <!-- 上传按钮 -->
          <div
            class="custom-upload__button"
            :class="{ 'custom-upload__button--disabled': false }"
            @click="handleAddImage"
            v-show="(imageStickerList?.length || 0) + uploadingImgNum < 3"
          >
            <fa-icon type="plus" class="custom-upload__button-icon" />
          </div>
          <!-- 图片 -->
          <ImageStickerSetting
            v-for="(sticker, index) in imageStickerList?.slice().reverse()"
            :key="'imageStickerSetting-' + index"
            :stickerInfo="sticker"
            @delete="handleDeleteSticker(sticker)"
            :max-width="720"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import Vue, { computed, nextTick, onMounted, Ref, ref } from 'vue';
import { workInfo } from '../hook/useWorkInfo';
import TextStickerSetting from './TextStickerSetting.vue';
import { ImageSticker, STYLE_TYPE, TextSticker } from '@/types';
import TextStickerItem from './TextStickerItem.vue';
import ImageStickerItem from './ImageStickerItem.vue';
import ImageStickerSetting from './ImageStickerSetting.vue';
import { showMaterialBasicUpload } from '@/components/MaterialBasicUpload';
import type { MaterialUploadFile } from '@/components/MaterialBasicUpload/types';
import { pxToSize } from './utils';
import { useFontInfo } from './hook/useFontInfo';
import { useFontPreset } from './hook/useFontPreset';
import { FILE_TYPES } from '@/constants/fileType';
import { getMaterialFullUrl } from '@/components/MaterialBasicUpload/utils/index';
import { usePlayer } from '@/views/EditProjectView/pages/SecondStepVideo/components/VideoPlayer/hooks/usePlayer';
import { usePlayerEvents } from '@/views/EditProjectView/pages/SecondStepVideo/components/VideoPlayer/hooks/usePlayerEvents';
import { VideoPlayerEventParams } from '@/views/EditProjectView/pages/SecondStepVideo/components/VideoPlayer/types';
import type Player from 'video.js/dist/types/player';
import VideoControls from '@/views/EditProjectView/pages/SecondStepVideo/components/VideoPlayer/components/VideoControls.vue';
import ScImg from '@/components/comm/ScImg.vue';
import TextStickerDebugPreview from '@/components/DevMode/TextStickerDebugPreview.vue';

/********************视频控件 start***********************/
const emit = defineEmits<{
  (e: 'video-player:action', params: VideoPlayerEventParams): void;
}>();
const videoPlayer = ref<HTMLVideoElement | null>(null);
// 封面图URL计算属性
const posterUrl = computed(() => {
  if (!workInfo.value?.data || !workInfo.value?.data?.videoCover?.baseId)
    return '';

  return getMaterialFullUrl(
    workInfo.value.data?.videoCover?.baseId || '',
    FILE_TYPES.WEBP || workInfo.value.coverImgType,
    'user',
    324,
  );
});

const videoUrl = computed(() => {
  if (!workInfo.value || !workInfo.value.data) return '';
  const url = getMaterialFullUrl(
    workInfo.value?.data?.video?.baseId || '',
    workInfo.value?.data?.video?.baseType || 0,
    'user',
  );
  return url;
});

// 播放器管理 hook
const {
  player,
  initializePlayer,
  isPlaying,
  currentTime,
  duration,
  bufferedPercent,
  playedPercent,
  isControlDisabled,
  isControlsLoading,
} = usePlayer({
  videoElement: videoPlayer,
  videoUrl: videoUrl,
  workItem: computed(() => workInfo.value || null),
  posterUrl: posterUrl,
  onInitialized: () => {
    // 设置事件监听
    setupPlayerEvents();
    player.value?.on('canplay', () => {
      // 资源加载完成，设置加载状态为 false
      nextTick(() => {
        videoLoading.value = false;
      });
    });
  },
});

// 资源加载中loading
const videoLoading = ref(true);

// 贴图上传中数量（上传完成前的数量）
const uploadingImgNum = ref<number>(0);

// 使用播放器事件 hook
const { setupPlayerEvents } = usePlayerEvents({
  emit: (actionType: VideoPlayerEventParams['actionType']) => {
    emit('video-player:action', { actionType });
  },
  player: player as unknown as Ref<Player | null>,
  isPlaying,
  currentTime,
  duration,
});
/**
 * 切换播放状态
 * @description 在播放和暂停状态之间切换
 */
const togglePlay = () => {
  if (!player.value) return;

  if (isPlaying.value) {
    player.value.pause();
  } else {
    player.value.play();
  }
};
/**
 * 设置播放时间
 * @description 跳转到指定时间点，确保时间值有效
 * @param time 目标时间（秒）
 */
const setPlayerTime = (time: number) => {
  if (!player.value) return;
  player.value.currentTime(time);
};
/**
 * 处理拖动进度条时的预览
 * @description 在拖动进度条时更新时间显示，但不实际改变播放位置
 * @param time 预览的时间点（秒）
 */
const handleSeeking = (time: number) => {
  if (!player.value) return;

  currentTime.value = time;
};
onMounted(() => {
  // 初始化播放器
  initializePlayer();
});
/********************视频控件 end***********************/

/********************花字 start***********************/
/** 花字列表 */
const textStickerList = computed(() => {
  return workInfo.value?.setting.style.filter(sticker => {
    return sticker.type === STYLE_TYPE.FONT;
  });
});
/** 字体列表 */
const { fontList } = useFontInfo();
/** 花字预设表 */
const { fontPresetMap } = useFontPreset();
/** 点击添加花字 */
const handleAddText = () => {
  if (workInfo.value?.setting.style) {
    /** 默认字体Name */
    const DEFAULT_FONT_NAME = '抖音美好体';
    const DEFAULT_FONT = fontList.value.find(
      font => font.name === DEFAULT_FONT_NAME,
    );
    /** 默认预设 */
    const defaultPresetId = 4;
    const defaultPreset = fontPresetMap.value.get(defaultPresetId);
    const newText: TextSticker = {
      type: STYLE_TYPE.FONT,
      text: `花字#${
        workInfo.value?.setting.style.filter(
          sticker => sticker.type === STYLE_TYPE.FONT,
        ).length + 1
      }`,
      x: 30,
      y: 0,
      color: '',
      fontSize: pxToSize(40),
      align: 'center',
      fileName: DEFAULT_FONT?.fileName || '',
      fontName: DEFAULT_FONT?.fontName || '',
      styleId: 1,
    };
    if (defaultPreset) {
      // 应用默认预设的属性到新文本，但保持原有的id
      Object.assign(newText, {
        ...defaultPreset,
        styleId: defaultPreset.id,
        type: newText.type,
        text: newText.text,
        x: newText.x,
        y: newText.y,
      });
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      delete (newText as any).id; // 删除id属性
    }
    workInfo.value.setting.style.push(newText);
  }
};
/** 删除花字 */
const handleDeleteText = (textInfo: TextSticker) => {
  const delIndex = workInfo.value?.setting.style.findIndex(
    item => item === textInfo,
  );
  if (typeof delIndex === 'number' && delIndex !== -1) {
    workInfo.value?.setting.style.splice(delIndex, 1);
  }
};
/** 样式应用到全部 */
const handleApplyToAll = (textInfo: TextSticker) => {
  workInfo.value?.setting.style.forEach(sticker => {
    if (sticker.type === STYLE_TYPE.FONT) {
      Vue.set(sticker, 'align', textInfo.align);
      Vue.set(sticker, 'fontName', textInfo.fontName);
      Vue.set(sticker, 'fileName', textInfo.fileName);
      Vue.set(sticker, 'fontSize', textInfo.fontSize);
      Vue.set(sticker, 'color', textInfo.color);
      Vue.set(sticker, 'styleId', textInfo.styleId);
      Vue.set(sticker, 'boxColor', textInfo.boxColor);
      Vue.set(sticker, 'strokeColor', textInfo.strokeColor);
      Vue.set(sticker, 'strokeWidth', textInfo.strokeWidth);
      Vue.set(sticker, 'shadowColor', textInfo.shadowColor);
      Vue.set(sticker, 'shadowX', textInfo.shadowX);
      Vue.set(sticker, 'shadowY', textInfo.shadowY);
      Vue.set(sticker, 'shadowStrokeColor', textInfo.shadowStrokeColor);
      Vue.set(sticker, 'shadowStrokeWidth', textInfo.shadowStrokeWidth);
    }
  });
};
/********************花字 end***********************/

/********************贴图 start***********************/
/** 贴图列表 */
const imageStickerList = computed(() => {
  return workInfo.value?.setting.style.filter(sticker => {
    return sticker.type === STYLE_TYPE.PIC;
  });
});
/** 点击添加贴图 */
const handleAddImage = () => {
  showMaterialBasicUpload({
    title: '添加贴图',
    maxChosenFileCount: 1,
    isVideo: false,
    onConfirm: (files: MaterialUploadFile[]) => {
      if (files.length === 0) {
        return;
      }
      const file = files[0];
      const img = new Image();
      img.src = getMaterialFullUrl(
        file.data.resId,
        FILE_TYPES.WEBP,
        'user',
        720,
      );
      img.onload = () => {
        // 确保图片加载完成后再添加，并找到最后一个贴图的索引，这个图后面
        if (workInfo.value) {
          const lastImageIndex = workInfo.value.setting.style
            .map(item => item.type)
            .lastIndexOf(STYLE_TYPE.PIC);
          let width = img.width;
          let height = img.height;
          const maxWidth = 324;
          const maxHeight = 300;
          if (width > maxWidth || height > maxHeight) {
            // 如果图片过大，缩放到最大限制以内
            const scale = Math.min(maxWidth / width, maxHeight / height);
            width = Math.floor(width * scale);
            height = Math.floor(height * scale);
          }
          const newImageSticker: ImageSticker = {
            type: STYLE_TYPE.PIC,
            resId: file.data.resId,
            x: 0,
            y: 0,
            width: width,
            height: height,
            resType: file.data.type,
          };
          workInfo.value.setting.style.splice(
            lastImageIndex + 1,
            0,
            newImageSticker,
          );
          uploadingImgNum.value--;
        }
      };
      uploadingImgNum.value++;
    },
  });
};
/** 删除贴图 */
const handleDeleteSticker = (sticker: ImageSticker) => {
  const index = workInfo.value?.setting.style.findIndex(
    item => item === sticker,
  );
  if (index !== undefined && index !== -1) {
    workInfo.value?.setting.style.splice(index, 1);
  }
};
/********************贴图 end***********************/
</script>

<style lang="scss" scoped>
.video-controls-wrap {
  @apply absolute bottom-[8px] left-[8px] bg-white w-[calc(100%-16px)] z-10;
  @apply h-40px flex items-center px-[16px];
  box-shadow: 0 0 6px #0000000a;
  border-radius: 8px;
  ::v-deep .video-controls {
    @apply pt-0 flex-1;
    .video-controls__play-btn {
      .icon {
        @apply h-20px w-20px;
      }
    }
    .video-controls__container {
      @apply gap-x-[12px];
    }
  }
}
// 上传按钮
.custom-upload__button {
  @apply flex flex-col justify-center items-center cursor-pointer;
  @apply w-[88px] h-[88px] rounded-[8px];
  @apply bg-white border b-solid border-[#d9d9d9] border-dashed;
  @apply transition-all duration-300 ease-in-out;

  &:hover:not(.custom-upload__button--disabled) {
    @apply border-primary;
  }
  &:hover .custom-upload__button-icon {
    @apply text-primary;
  }
}

.custom-upload__button--disabled {
  @apply cursor-not-allowed opacity-60 bg-[#f5f5f5];
}

.custom-upload__button-icon {
  @apply text-[#666] text-[20px];
}

.custom-upload__button-text {
  @apply mt-0 text-[#666] font-normal text-[14px];
}
</style>
