/**
 * @fileoverview 项目数据管理 Vuex 模块
 * @description 管理项目层面的各种数据，包括项目状态等
 *
 * @example
 * // 设置项目状态
 * store.dispatch('project/setProjectStatus', {
 *   projectId: 'project123',
 *   status: PROJECT_STATUS.GENERATING
 * });
 *
 * // 批量设置项目状态
 * store.dispatch('project/setProjectStatusBatch', {
 *   'project123': PROJECT_STATUS.GENERATING,
 *   'project456': PROJECT_STATUS.COMPLETED
 * });
 *
 * // 获取项目状态
 * const status = store.getters['project/getProjectStatus']('project123');
 *
 * // 后续可以扩展更多项目数据，例如：
 * // store.dispatch('project/setProjectInfo', { projectId, info });
 * // store.dispatch('project/setProjectConfig', { projectId, config });
 */

import { Module } from 'vuex';
import { RootState } from '@/store';
import { PROJECT_STATUS } from '@/constants/project';

export interface ProjectState {
  /** 项目ID与项目状态的映射 */
  statusMap: Record<string, PROJECT_STATUS>;

  // 后续可以添加更多项目层面的数据，例如：
  // /** 项目ID与项目基本信息的映射 */
  // infoMap: Record<string, {
  //   name: string;
  //   description?: string;
  //   createTime: string;
  //   updateTime: string;
  // }>;

  // /** 项目ID与项目配置的映射 */
  // configMap: Record<string, {
  //   theme?: string;
  //   template?: string;
  //   settings?: Record<string, any>;
  // }>;

  // /** 项目ID与项目统计数据的映射 */
  // statsMap: Record<string, {
  //   workCount: number;
  //   totalSize: number;
  //   lastAccessTime: string;
  // }>;
}

export const state: ProjectState = {
  statusMap: {},
};

export const mutations = {
  /**
   * 设置项目状态
   * @param state 状态
   * @param payload 项目ID和状态
   */
  SET_PROJECT_STATUS(
    state: ProjectState,
    payload: { projectId: string; status: PROJECT_STATUS },
  ) {
    state.statusMap = {
      ...state.statusMap,
      [payload.projectId]: payload.status,
    };
  },

  /**
   * 批量设置项目状态
   * @param state 状态
   * @param payload 项目状态映射
   */
  SET_PROJECT_STATUS_BATCH(
    state: ProjectState,
    payload: Record<string, PROJECT_STATUS>,
  ) {
    state.statusMap = {
      ...state.statusMap,
      ...payload,
    };
  },

  /**
   * 清除项目状态
   * @param state 状态
   * @param projectId 项目ID
   */
  CLEAR_PROJECT_STATUS(state: ProjectState, projectId: string) {
    const newMap = { ...state.statusMap };
    delete newMap[projectId];
    state.statusMap = newMap;
  },

  /**
   * 清除所有项目状态
   * @param state 状态
   */
  CLEAR_ALL_PROJECT_STATUS(state: ProjectState) {
    state.statusMap = {};
  },
};

export const actions = {
  /**
   * 设置项目状态
   * @param context Vuex 上下文
   * @param payload 项目ID和状态
   */
  setProjectStatus(
    { commit }: { commit: (type: string, payload?: unknown) => void },
    payload: { projectId: string; status: PROJECT_STATUS },
  ) {
    commit('SET_PROJECT_STATUS', payload);
  },

  /**
   * 批量设置项目状态
   * @param context Vuex 上下文
   * @param payload 项目状态映射
   */
  setProjectStatusBatch(
    { commit }: { commit: (type: string, payload?: unknown) => void },
    payload: Record<string, PROJECT_STATUS>,
  ) {
    commit('SET_PROJECT_STATUS_BATCH', payload);
  },

  /**
   * 清除项目状态
   * @param context Vuex 上下文
   * @param projectId 项目ID
   */
  clearProjectStatus(
    { commit }: { commit: (type: string, payload?: unknown) => void },
    projectId: string,
  ) {
    commit('CLEAR_PROJECT_STATUS', projectId);
  },

  /**
   * 清除所有项目状态
   * @param context Vuex 上下文
   */
  clearAllProjectStatus({
    commit,
  }: {
    commit: (type: string, payload?: unknown) => void;
  }) {
    commit('CLEAR_ALL_PROJECT_STATUS');
  },
};

export const getters = {
  /**
   * 获取项目状态
   * @param state 状态
   * @returns 获取项目状态的函数
   */
  getProjectStatus:
    (state: ProjectState) =>
    (projectId: string): PROJECT_STATUS | undefined => {
      return state.statusMap[projectId];
    },

  /**
   * 获取所有项目状态映射
   * @param state 状态
   * @returns 项目状态映射
   */
  getAllProjectStatus: (state: ProjectState) => {
    return state.statusMap;
  },
};

const projectModule: Module<ProjectState, RootState> = {
  namespaced: true,
  state,
  mutations,
  actions,
  getters,
};

export default projectModule;
