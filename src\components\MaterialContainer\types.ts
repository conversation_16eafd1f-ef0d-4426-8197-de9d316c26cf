/**
 * 素材容器组件支持的素材类型
 */
export type MaterialType = 'text' | 'music' | 'voiceover' | 'card';

/**
 * 素材容器组件属性接口
 */
export interface MaterialContainerProps {
  /** 素材类型 */
  type: MaterialType;
  /** 标题 (用于音乐和配音类型) */
  title?: string;
  /** 内容 (用于文本和卡片类型) */
  content?: string;
  /** 时长 (用于音乐类型) */
  duration?: string;
  /** 头像 (用于配音类型) */
  avatar?: string;
  /** 配音类型 (用于配音类型) */
  voiceType?: string;
  /** 是否显示操作按钮 */
  showActions?: boolean;
  /** 操作按钮文本 */
  actionText?: string;
}
