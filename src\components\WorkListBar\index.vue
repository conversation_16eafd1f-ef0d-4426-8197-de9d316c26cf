<template>
  <div class="media-file-list">
    <!-- 顶部统计栏 -->
    <div v-if="!loading" class="media-file-list__header">
      <div class="media-file-list__stats">
        <div class="media-file-list__title">
          已生成{{ totalItems }}个{{ typeText }}
        </div>
      </div>

      <div
        class="media-file-list__select-all"
        v-if="false && workList.length > 0"
      >
        <fa-checkbox
          :checked="isAllSelected"
          @change="toggleSelectAll"
          class="select-none"
        >
          全选
        </fa-checkbox>
      </div>
    </div>

    <!-- 保存期限提示 -->
    <fa-alert
      v-show="!loading && workList.length > 0"
      :message="`生成${typeText}临时保存${WORK_EXPIRE_DAYS}天，到期后将自动清除，建议尽快保存至「作品库」`"
      type="warning"
      showIcon
      class="mx-[32px] mb-[16px] rounded-[8px]"
    />

    <!-- 加载中状态 -->
    <div v-if="loading" class="media-file-list__loading">
      <!-- 顶部统计栏骨架屏 -->
      <div class="media-file-list__loading-header">
        <fa-skeleton
          :loading="loading"
          :title="false"
          active
          :paragraph="{
            rows: 1,
            width: ['40%'],
          }"
          class="w-full px-[32px]"
        />
      </div>

      <!-- 保存期限提示骨架屏 -->
      <div class="media-file-list__loading-alert">
        <fa-skeleton
          :loading="loading"
          :title="false"
          active
          :paragraph="{
            rows: 1,
            width: ['100%'],
          }"
          class="w-full px-[32px]"
        />
      </div>

      <!-- 列表内容骨架屏 -->
      <fa-skeleton
        :loading="loading"
        :title="false"
        active
        :paragraph="{
          rows: 3,
          width: ['100%', '100%', '100%'],
        }"
        class="w-full px-[32px]"
      />
    </div>

    <!-- 媒体文件列表内容 -->
    <div class="media-file-list__content" v-else-if="workList.length > 0">
      <FaScrollArea
        ref="scrollAreaRef"
        class="h-full"
        :thumb-style="thumbStyle"
        :bar-style="barStyle"
        @scroll-end="handleScrollEnd"
      >
        <div class="media-file-list__items mb-[24px]">
          <WorkListBarItem
            v-for="workItem in workList"
            :key="`work-item-${workItem.id}`"
            :workItem="workItem"
            :isSelected="isItemSelected(workItem)"
            :workListBarType="workListBarType"
            @select="toggleSelect"
          />
        </div>

        <!-- 加载更多提示 -->
        <div v-if="isLoadingMore" class="media-file-list__loading-more">
          <fa-spin size="small" />
          <span class="loading-text">加载更多...</span>
        </div>

        <!-- 加载错误提示 -->
        <div v-if="hasLoadingError" class="media-file-list__error">
          <fa-icon name="exclamation-circle" class="error-icon" />
          <span class="error-message">{{ loadingErrorMessage }}</span>
          <fa-button
            v-if="canRetryLoading"
            type="default"
            size="small"
            @click="handleRetry"
            class="retry-button"
          >
            重试
          </fa-button>
        </div>

        <!-- 全部加载完成提示 -->
        <div
          v-if="hasLoadedAll && workList.length > 6 && !hasLoadingError"
          class="media-file-list__no-more"
        >
          已加载全部作品
        </div>
      </FaScrollArea>
    </div>

    <!-- 空状态 -->
    <EmptyState v-else :workListBarType="workListBarType" />
  </div>
</template>

<script lang="ts">
import {
  defineComponent,
  ref,
  computed,
  PropType,
  ComputedRef,
  Ref,
  watch,
  onMounted,
  onUnmounted,
} from 'vue';
import WorkListBarItem from '@/components/WorkListBar/WorkListBarItem.vue';
import EmptyState from '@/components/WorkListBar/EmptyState.vue';
import { WorkType, WorkTypeValue } from '@/components/WorkListBar/types';
import { WorkItem } from '@/types';
import {
  WORK_STATUS,
  getWorkStatusInfo,
  isNormalCompletedWork,
} from '@/constants/workStatus';
import { WORK_EXPIRE_DAYS } from '@/constants/workExpire';
import {
  getDefaultScrollAreaStyles,
  type ScrollAreaStyleType,
} from '@/constants/scrollArea';
import {
  eventBus,
  EVENT_NAMES,
  type TempUpdateTotalItemsData,
} from '@/utils/eventBus';

interface ComponentReturn {
  workList: ComputedRef<WorkItem[]>;
  isAllSelected: Ref<boolean>;
  typeText: ComputedRef<string>;
  hasSelectableFiles: ComputedRef<boolean>;
  toggleSelectAll: () => void;
  toggleSelect: (id: number) => void;
  handleRefresh: () => void;
  // 分页相关
  currentPage: Ref<number>;
  pageSize: Ref<number>;
  totalItems: Ref<number>;
  isLoadingMore: ComputedRef<boolean>;
  hasLoadedAll: ComputedRef<boolean>;
  handleScrollEnd: () => void;
  // 错误处理相关
  hasLoadingError: ComputedRef<boolean>;
  loadingErrorMessage: ComputedRef<string>;
  canRetryLoading: ComputedRef<boolean>;
  handleRetry: () => void;
  // 滚动区域引用
  scrollAreaRef: Ref<HTMLElement | null>;
  barStyle: ScrollAreaStyleType;
  thumbStyle: ScrollAreaStyleType;
  // 添加常量
  WORK_STATUS: typeof WORK_STATUS;
  WORK_EXPIRE_DAYS: typeof WORK_EXPIRE_DAYS;
  // 添加作品选中判断方法
  isItemSelected: (workItem: WorkItem) => boolean;
}

export default defineComponent({
  name: 'MediaFileList',
  components: {
    WorkListBarItem,
    EmptyState,
  },
  props: {
    // 工作项列表数据（从父组件传入）
    workList: {
      type: Array as PropType<WorkItem[]>,
      default: () => [],
    },
    // 加载中状态
    loading: {
      type: Boolean,
      default: false,
    },
    // 媒体类型：1为视频，2为图片
    workListBarType: {
      type: Number as PropType<WorkTypeValue>,
      default: WorkType.VIDEO,
      validator: (value: WorkTypeValue) =>
        [WorkType.VIDEO, WorkType.IMAGE].includes(value),
    },
    // 选中的作品ID列表（从父组件传入）
    selectedWorkIds: {
      type: Array as PropType<number[]>,
      default: () => [],
    },
    // 分页相关
    // 当前页码
    currentPage: {
      type: Number,
      default: 1,
    },
    // 每页条数
    pageSize: {
      type: Number,
      default: 10,
    },
    // 总条数
    totalItems: {
      type: Number,
      default: 0,
    },
    // 是否显示分页
    showPagination: {
      type: Boolean,
      default: true,
    },
    // 是否正在加载更多
    isLoadingMore: {
      type: Boolean,
      default: false,
    },
    // 是否已加载全部
    hasLoadedAll: {
      type: Boolean,
      default: false,
    },
    // 是否有加载错误
    hasLoadingError: {
      type: Boolean,
      default: false,
    },
    // 加载错误信息
    loadingError: {
      type: Object as PropType<{ message: string; type: string } | null>,
      default: null,
    },
    // 是否可以重试
    canRetryLoading: {
      type: Boolean,
      default: false,
    },
  },
  emits: [
    'select-change',
    'scroll-end',
    'all-select-change',
    'refresh',
    'retry',
  ],

  setup(props, { emit }): ComponentReturn {
    // 滚动区域引用
    const scrollAreaRef = ref<HTMLElement | null>(null);

    // 使用公用的滚动区域样式配置
    const { barStyle, thumbStyle } = getDefaultScrollAreaStyles({
      thumbStyle: { right: '-11px' },
    });

    // 工作项列表数据
    const workList = computed(() => props.workList || []);

    // 全选状态（独立管理，不影响具体选中的作品ID）
    const isAllSelected = ref<boolean>(false);

    // 媒体类型文本
    const typeText = computed(() =>
      props.workListBarType === WorkType.VIDEO ? '视频' : '图文',
    );

    // 判断作品是否为可选状态(已完成且未保存)
    const isSelectableStatus = (workItem: WorkItem): boolean => {
      // 使用统一状态处理函数判断是否为普通完成状态
      const statusInfo = getWorkStatusInfo(workItem);
      const isCompleted = isNormalCompletedWork(statusInfo);
      // 必须未保存（saveTime字段不存在）
      const isNotSaved = !workItem.saveTime;

      return isCompleted && isNotSaved;
    };

    // 计算是否有可选择的文件
    const hasSelectableFiles = computed(() => {
      return workList.value.some(v => isSelectableStatus(v));
    });

    // 切换全选/取消全选
    const toggleSelectAll = () => {
      isAllSelected.value = !isAllSelected.value;

      // 只通知父组件全选状态变化，不触发 select-change
      // 全选是独立的批量操作状态，不应该影响当前选中的具体作品
      emit('all-select-change', isAllSelected.value);
    };

    // 判断作品是否被选中
    const isItemSelected = (workItem: WorkItem): boolean => {
      // 全选状态下：所有可选择的作品都显示为选中
      if (isAllSelected.value && isSelectableStatus(workItem)) {
        return true;
      }
      // 非全选状态下：根据props中的selectedWorkIds判断
      return props.selectedWorkIds.includes(workItem.id);
    };

    // 切换单个工作项选中状态
    const toggleSelect = (id: number) => {
      // 单选时重置全选状态
      if (isAllSelected.value) {
        isAllSelected.value = false;
        emit('all-select-change', false);
      }

      // 通知父组件选中的作品ID变化
      emit('select-change', [id]);
    };

    // 处理刷新事件
    const handleRefresh = () => {
      emit('refresh');
    };

    // 分页相关
    const currentPage = ref(props.currentPage);
    const pageSize = ref(props.pageSize);
    const totalItems = ref(props.totalItems);

    // 从props获取加载状态
    const isLoadingMore = computed(() => props.isLoadingMore);
    const hasLoadedAll = computed(() => props.hasLoadedAll);
    const hasLoadingError = computed(() => props.hasLoadingError);
    const canRetryLoading = computed(() => props.canRetryLoading);

    // 错误信息
    const loadingErrorMessage = computed(() => {
      return props.loadingError?.message || '加载失败，请重试';
    });

    // 处理滚动到底部事件
    const handleScrollEnd = () => {
      // 如果正在加载、已加载全部或有错误，则不处理
      if (isLoadingMore.value || hasLoadedAll.value || hasLoadingError.value) {
        return;
      }

      // 通知父组件处理滚动到底部事件
      emit('scroll-end');
    };

    // 处理重试事件
    const handleRetry = () => {
      emit('retry');
    };

    // 监听父组件传入的选中状态变化，重置全选状态
    watch(
      () => props.selectedWorkIds,
      (newSelectedIds, oldSelectedIds) => {
        // 避免不必要的更新
        if (JSON.stringify(newSelectedIds) === JSON.stringify(oldSelectedIds)) {
          return;
        }

        // 当父组件的选中状态发生变化时，重置全选状态
        // 这确保了单选操作不会与全选状态冲突
        if (isAllSelected.value) {
          isAllSelected.value = false;
        }
      },
      { immediate: false, deep: true },
    );

    // 监听props中分页参数变化
    watch(
      () => props.currentPage,
      val => {
        currentPage.value = val;
      },
    );

    watch(
      () => props.pageSize,
      val => {
        pageSize.value = val;
      },
    );

    watch(
      () => props.totalItems,
      val => {
        totalItems.value = val;
      },
    );

    // 监听临时更新 totalItems 事件
    const handleTempUpdateTotalItems = (...args: unknown[]) => {
      const data = args[0] as TempUpdateTotalItemsData;

      if (!data || typeof data !== 'object') return;

      if (data.operation === 'decrease') {
        totalItems.value = Math.max(0, totalItems.value - data.amount);
      } else if (data.operation === 'increase') {
        totalItems.value = totalItems.value + data.amount;
      }
    };

    // 注册事件监听
    onMounted(() => {
      eventBus.on(
        EVENT_NAMES.TEMP_UPDATE_TOTAL_ITEMS,
        handleTempUpdateTotalItems,
      );
    });

    // 清理事件监听
    onUnmounted(() => {
      eventBus.off(
        EVENT_NAMES.TEMP_UPDATE_TOTAL_ITEMS,
        handleTempUpdateTotalItems,
      );
    });

    return {
      workList,
      isAllSelected,
      typeText,
      hasSelectableFiles,
      toggleSelectAll,
      toggleSelect,
      handleRefresh,
      // 分页相关
      currentPage,
      pageSize,
      totalItems,
      isLoadingMore,
      hasLoadedAll,
      handleScrollEnd,
      // 错误处理相关
      hasLoadingError,
      loadingErrorMessage,
      canRetryLoading,
      handleRetry,
      // 滚动区域引用
      scrollAreaRef,
      barStyle,
      thumbStyle,
      // 添加常量
      WORK_STATUS,
      WORK_EXPIRE_DAYS,
      // 添加作品选中判断方法
      isItemSelected,
    };
  },
});
</script>

<style lang="scss" scoped>
@import '@/style/mixins/scrollbar.scss';

/* 媒体文件列表组件
------------------------------------------ */
.media-file-list {
  /* 布局相关 */
  @apply flex flex-col pt-[24px];
  /* 尺寸相关 */
  @apply h-full;
  /* 外观相关 */
  @apply bg-[#fff] border-rd-16px shadow="[0_0_5px_#00000008]";

  /* 头部区块 */
  .media-file-list__header {
    /* 布局相关 */
    @apply flex justify-between items-center;
    /* 尺寸相关 */
    @apply pb-16px px-32px pt-0;
  }

  .media-file-list__stats {
    /* 布局相关 */
    @apply flex items-baseline;
  }

  .media-file-list__title {
    /* 文字相关 */
    @apply text-16px font-500 text-[#111];
  }

  .media-file-list__select-all {
    /* 布局相关 */
    @apply flex items-baseline;
    :deep(.fa-checkbox-wrapper) {
      /* 文字相关 */
      @apply text-14px font-400 text-left text-[#333];
    }
    :deep(.fa-checkbox + span) {
      @apply pr-0;
    }
  }

  /* 加载状态区块 */
  .media-file-list__loading {
    /* 布局相关 */
    @apply flex flex-col;
    /* 尺寸相关 */
    @apply flex-1 pb-[24px];

    /* 顶部统计栏骨架屏 */
    .media-file-list__loading-header {
      /* 尺寸相关 */
      @apply h-40px w-full mb-16px;

      :deep(.fa-skeleton) {
        /* 布局相关 */
        @apply flex flex-col;
        /* 尺寸相关 */
        @apply h-full w-full;

        .fa-skeleton-paragraph > li {
          /* 尺寸相关 */
          @apply h-40px mb-0;
        }
      }
    }

    /* 保存期限提示骨架屏 */
    .media-file-list__loading-alert {
      /* 尺寸相关 */
      @apply h-60px w-full mb-16px;

      :deep(.fa-skeleton) {
        /* 布局相关 */
        @apply flex flex-col;
        /* 尺寸相关 */
        @apply h-full w-full;

        .fa-skeleton-paragraph > li {
          /* 尺寸相关 */
          @apply h-60px mb-0;
        }
      }
    }

    /* 列表内容骨架屏 */
    :deep(.fa-skeleton) {
      /* 布局相关 */
      @apply flex flex-col;
      /* 尺寸相关 */
      @apply h-full w-full;

      .fa-skeleton-paragraph > li {
        /* 尺寸相关 */
        @apply h-134px mb-16px;
      }
    }
  }

  /* 内容区块 */
  .media-file-list__content {
    /* 布局相关 */
    @apply flex-1;
    /* 尺寸相关 */
    @apply px-16px;
  }

  .media-file-list__items {
    /* 布局相关 */
    @apply flex flex-col;
  }

  /* 加载更多提示 */
  .media-file-list__loading-more {
    /* 布局相关 */
    @apply flex items-center justify-center;
    /* 尺寸相关 */
    @apply py-16px;
    /* 文字相关 */
    @apply text-14px text-[#999];

    .loading-text {
      @apply ml-8px;
    }
  }

  /* 加载错误提示 */
  .media-file-list__error {
    /* 布局相关 */
    @apply flex items-center justify-center;
    /* 尺寸相关 */
    @apply py-16px;

    .error-icon {
      /* 尺寸相关 */
      @apply w-16px h-16px mr-8px;
      /* 外观相关 */
      @apply text-gray-500;
    }

    .error-message {
      /* 文字相关 */
      @apply text-14px text-gray-600;
      /* 尺寸相关 */
      @apply mr-12px;
    }

    .retry-button {
      /* 尺寸相关 */
      @apply px-16px py-6px;
      /* 文字相关 */
      @apply text-12px;
    }
  }

  /* 全部加载完成提示 */
  .media-file-list__no-more {
    /* 布局相关 */
    @apply flex justify-center items-center;
    /* 尺寸相关 */
    @apply mt-[-18px] pb-24px;
    /* 文字相关 */
    @apply text-14px text-[#bfbfbf];
  }
}
</style>
