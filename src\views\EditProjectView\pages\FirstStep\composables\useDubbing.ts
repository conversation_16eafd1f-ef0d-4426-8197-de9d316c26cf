import { ref } from 'vue';
import type { VoiceSetting } from '@/views/EditProjectView/types/index';
import type { Dubbing } from '@/types';
import { getDubbingInfo } from '@/api/VideoEditor/music';
import type { EditProjectViewData } from '@/views/EditProjectView/types/index';

/**
 * @description 配音管理的composable
 * @param {EditProjectViewData | null} initialData - 初始配音数据
 */
export function useDubbing(initialData: EditProjectViewData | null = null) {
  // 配音设置
  const voiceSettings = ref<VoiceSetting>({
    useAiRecommend: true,
    voiceId: '',
  });

  // 配音信息
  const voiceInfo = ref<Dubbing | null>(null);

  // 是否显示配音选择器
  const isShowDubbingSelector = ref(false);

  // 显示配音选择器
  const showDubbingSelector = () => {
    isShowDubbingSelector.value = true;
  };

  // 处理配音变更
  const handleChangeDubbingInfo = (
    dubbing: Dubbing | null,
    useAiRecommend: boolean,
  ) => {
    voiceSettings.value.useAiRecommend = useAiRecommend;

    if (dubbing && !useAiRecommend) {
      voiceInfo.value = dubbing;
      voiceSettings.value.voiceId = dubbing.voiceId;
      voiceSettings.value.voiceName = dubbing.name;
      voiceSettings.value.extraType = dubbing.extraType;
    } else {
      // 如果使用智能推荐或没有选择配音，则清空
      voiceInfo.value = null;
      voiceSettings.value.voiceId = '';
      voiceSettings.value.voiceName = '';
      voiceSettings.value.extraType = undefined;
    }
  };

  // 初始化配音信息
  const initVoiceInfo = async () => {
    if (initialData?.voice) {
      const voice = initialData.voice;
      voiceSettings.value = {
        useAiRecommend: voice.useAiRecommend || false,
        voiceId: voice.voiceId,
        extraType: voice.extraType,
      };

      // 获取配音详情
      if (voice.voiceId && !voice.useAiRecommend) {
        const [err, res] = await getDubbingInfo(voice.voiceId);
        if (!err && res?.data) {
          voiceInfo.value = res.data;
          voiceSettings.value.voiceName = res.data.name;
          // 如果API返回的数据中有extraType，则使用API的数据
          if (res.data.extraType !== undefined) {
            voiceSettings.value.extraType = res.data.extraType;
          }
        }
      }
    }
  };

  return {
    voiceSettings,
    voiceInfo,
    isShowDubbingSelector,
    showDubbingSelector,
    handleChangeDubbingInfo,
    initVoiceInfo,
  };
}
