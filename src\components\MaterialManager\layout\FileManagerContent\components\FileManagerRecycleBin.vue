<template>
  <FaFileManagerTable
    :columns="columns"
    :tr-event="{
      click: changeSelect,
      mouseenter: handleMouseenter,
      mouseleave: handleMouseleave,
    }"
    @select="changeSelect"
  >
    <template #[SORT_BY_KEY.NAME]="{ text, record, index }">
      <div class="file-manager-demo--recycle-bin flex ai-center">
        <FaDesignerIcon
          v-if="isFolder(record)"
          class="file-manager-demo--recycle-bin-icon"
          type="document"
        />
        <FaDesignerIcon
          v-else-if="checkOwnerFilePresetPreview(record)"
          class="file-manager-demo--recycle-bin-icon"
          :type="iconType(record)"
        />
        <div v-else class="file-manager-demo--recycle-bin-img">
          <div :style="{ 'background-image': `url(${getImgUrl(record)})` }" />
        </div>
        <div class="file-manager-demo--recycle-bin-text flex-1">
          {{ text }}
        </div>
        <div
          class="file-manager-demo--recycle-bin-control"
          :style="{ opacity: controlId === record[INFO_KEYS.ID] ? 1 : 0 }"
        >
          <span
            class="file-manager-demo--recycle-bin-btn"
            @click.stop="handleClick('restore', index)"
            >还原</span
          >
          <span
            class="file-manager-demo--recycle-bin-btn"
            @click.stop="handleClick('delete', index)"
            >删除</span
          >
        </div>
      </div>
    </template>
  </FaFileManagerTable>
</template>

<script>
import {
  FileManager as FaFileManager,
  DesignerIcon as FaDesignerIcon,
} from '@fk/fa-component-cus';
import { SORT_BY_KEY } from '@/components/MaterialManager/constants/index.ts';
import { INFO_KEYS } from '@/constants/material';
import {
  iconType,
  checkOwnerFilePresetPreview,
  getImgUrl,
} from '@/components/MaterialManager/utils/index.ts';
import store from '@/store';
import { isFolder } from '@/utils/resource';

export default {
  name: 'FileManagerRecycleBin',
  components: {
    FaDesignerIcon,
    FaFileManagerTable: FaFileManager.FileManagerTable,
  },

  data() {
    return {
      INFO_KEYS,
      SORT_BY_KEY,

      controlId: '',

      columns: [
        {
          title: '名称',
          width: 220,
          dataIndex: SORT_BY_KEY.NAME,
          scopedSlots: { customRender: SORT_BY_KEY.NAME },
        },
        {
          title: '文件大小',
          width: 200,
          dataIndex: SORT_BY_KEY.FILE_SIZE,
          scopedSlots: { customRender: SORT_BY_KEY.FILE_SIZE },
        },
        {
          title: '上传者',
          width: 200,
          dataIndex: 'uploader',
          scopedSlots: { customRender: 'uploader' },
        },
        {
          title: '删除者',
          width: 200,
          dataIndex: 'deleter',
          scopedSlots: { customRender: 'deleter' },
        },
        {
          title: '删除时间',
          width: 220,
          dataIndex: 'deleteTime',
          scopedSlots: { customRender: 'deleteTime' },
        },
        {
          title: '自动清理时间（无法还原）',
          width: 220,
          dataIndex: 'cleanupTime',
          scopedSlots: { customRender: 'cleanupTime' },
        },
      ],
    };
  },

  methods: {
    isFolder,
    iconType,
    checkOwnerFilePresetPreview,
    getImgUrl,

    changeSelect(record) {
      store.commit('changeSelectById', record);
    },

    handleClick(type, index) {
      store.commit(type, index);
    },

    handleMouseenter(e) {
      this.controlId = e[INFO_KEYS.ID];
    },

    handleMouseleave() {
      this.controlId = '';
    },
  },
};
</script>

<style lang="scss" scoped>
.fa-file-manager--table {
  padding: 0 40px 0 30px;
}

.file-manager-demo--recycle-bin {
  &-icon {
    font-size: 24px;
  }

  &-text {
    padding: 5px 4px;
    margin-left: 8px;
    color: #333;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }

  &-img {
    width: 24px;
    height: 24px;
    padding: 1px;
    border: 1px solid #d9d9d9;
    border-radius: 2px;
    box-sizing: border-box;

    > div {
      height: 100%;
      background-size: contain;
      background-position: center;
      background-repeat: no-repeat;
    }
  }

  &-control {
    transition: 0.3s opacity;
  }

  &-btn {
    margin-left: 16px;
    color: #40a9ff;
    cursor: pointer;
    transition: color 0.3s;

    &:hover {
      color: #3a84fe;
    }

    &:active {
      color: #096dd9;
    }
  }
}
</style>
