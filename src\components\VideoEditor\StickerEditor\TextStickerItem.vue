<template>
  <vue-draggable-resizable
    ref="vdr"
    :parent="isLimit"
    :x="x - paddingOffset"
    :y="y"
    w="auto"
    h="auto"
    :resizable="false"
    @dragging="handleDrag"
    :key="vdrKey"
  >
    <svg
      :width="svgWidth"
      :height="svgHeight"
      :style="{
        'display': 'block',
        'user-select': 'none',
        'overflow': 'visible',
      }"
    >
      <!-- 阴影层：用g包裹多个text模拟阴影 -->
      <g v-if="hasShadow">
        <!-- 文字实体层 -->
        <text
          v-for="(line, idx) in textLines"
          :key="'solid-shadow-' + idx"
          :x="getShadowX(idx)"
          :y="getShadowY(idx)"
          dominant-baseline="central"
          :text-anchor="'start'"
          :font-size="fontSize"
          :font-family="currfontFamily"
          :fill="svgTextStyle.shadowColor"
          :paint-order="svgTextStyle['paint-order']"
        >
          {{ line }}
        </text>
        <!-- 描边层 -->
        <text
          v-for="(line, idx) in textLines"
          :key="'stroke-shadow-' + idx"
          :x="getShadowX(idx)"
          :y="getShadowY(idx)"
          dominant-baseline="central"
          :text-anchor="'start'"
          :font-size="fontSize"
          :font-family="currfontFamily"
          :fill="svgTextStyle.shadowColor"
          stroke-linecap="round"
          stroke-linejoin="round"
          :stroke="svgTextStyle.shadowStrokeColor"
          :stroke-width="svgTextStyle.shadowStrokeWidth"
          :paint-order="svgTextStyle['paint-order']"
          :mask="`url(#clip-${textId}-${idx})`"
        >
          {{ line }}
        </text>
        <!-- 裁剪层 -->
        <mask
          :id="`clip-${textId}-${idx}`"
          v-for="(line, idx) in textLines"
          :key="'mask-' + idx"
        >
          <rect x="-100" y="-1000" width="800" height="1600" fill="white" />
          <text
            :key="'clip-shadow-' + idx"
            :x="getShadowX(idx)"
            :y="getShadowY(idx)"
            dominant-baseline="central"
            :text-anchor="'start'"
            :font-size="fontSize"
            :font-family="currfontFamily"
            fill="#000000"
            :paint-order="svgTextStyle['paint-order']"
          >
            {{ line }}
          </text>
        </mask>
      </g>
      <!-- 主文本层：用g包裹多个text -->
      <g>
        <!-- 如果有背景色，先渲染rect -->
        <template v-for="(_line, idx) in textLines">
          <!-- 空格需要渲染背景，换行不渲染 -->
          <rect
            v-if="svgTextStyle['background-color'] && _line !== ''"
            :x="getBgX(idx)"
            :y="getBgY(idx)"
            :width="widthPerLine[idx] + textPaddingWidth || 0"
            :height="lineHeight"
            :fill="svgTextStyle['background-color']"
            :key="'rect-' + idx"
          />
        </template>
        <!-- 主文本 -->
        <template v-for="(line, idx) in textLines">
          <text
            :key="idx"
            :x="getContentX(idx)"
            :y="getContentY(idx)"
            dominant-baseline="central"
            :text-anchor="'start'"
            :font-size="fontSize"
            :font-family="currfontFamily"
            :fill="svgTextStyle.fill"
            :stroke="svgTextStyle.stroke"
            stroke-linecap="round"
            stroke-linejoin="round"
            :stroke-width="svgTextStyle['stroke-width']"
            :paint-order="svgTextStyle['paint-order']"
          >
            {{ line }}
          </text>
        </template>
      </g>
    </svg>
  </vue-draggable-resizable>
</template>

<script lang="ts" setup>
import { TextSticker } from '@/types';
import { computed, ref, watch } from 'vue';
// 文档在这：https://mauricius.github.io/vue-draggable-resizable/#/
import VueDraggableResizable from 'vue-draggable-resizable';
import {
  getFontPresetStyle,
  measureTextWidth,
  pxToSize,
  sizeToPx,
} from './utils';
import {
  getTextShadowX,
  getTextShadowY,
  getTextBgX,
  getTextBgY,
  getTextContentX,
  getTextContentY,
} from './utils/textCoordinateCalculator';
import { useFontInfo } from './hook/useFontInfo';
import { loadFont } from '@/utils/loadFont';
import { currentEditorType } from '@/constants/project';
import {
  currentSpace,
  currentSetting,
  globalTextStickerItemSize,
} from '@/components/ImageTextEditor/hook/useWorkInfo';
import { logger } from '@/utils/logger';
import { cloneDeep, throttle } from 'lodash-es';

const { fontMap } = useFontInfo();
const currfontFamily = computed(() => {
  const font = fontMap.value.get(props.textInfo.fileName.split('.')[0]);
  return (font ? font.fontFamily + ', ' : '') + 'default-font';
});

const textId = new Date().getTime();

const props = withDefaults(
  defineProps<{
    /** 当前花字 */
    textInfo: TextSticker;
    /** 父容器高度 */
    parentHeight?: number | string;
    /**是否限制拖动 */
    isLimit?: boolean;
  }>(),
  {
    isLimit: false,
  },
);

/** log 信息 */
const logTextInfo = throttle(() => {
  const newConf = cloneDeep(props.textInfo);
  logger.debug('花字当前设置：', {
    ...newConf,
    fontFile: `D:/sc/scportal-web/public/fonts/${newConf.fileName}`,
  });
}, 1000);

/** 拖拽事件 */
const handleDrag = (...$event: [number, number]) => {
  const offsetX =
    currentEditorType == 'image' ? currentSetting.value?.space?.h ?? 0 : 0;
  const adjustedX = $event[0] - offsetX + paddingOffset.value; // 需要加上额外偏移量（存数据时，减掉的textOffset，要加回来）
  props.textInfo.x = pxToSize(adjustedX);

  const offsetY =
    currentEditorType == 'image' ? currentSetting.value?.space?.v ?? 0 : 0;
  props.textInfo.y = pxToSize($event[1] - offsetY);
  logTextInfo();
};

const x = computed(() => {
  const _x = sizeToPx(props.textInfo.x);
  // 图文编辑，后端对于单图是根据图片左上角定位x y的，前端需要加上留白距离才是渲染的x y
  return currentEditorType == 'image'
    ? _x + (currentSetting.value?.space?.h ?? 0)
    : _x;
});
const y = computed(() => {
  const _y = sizeToPx(props.textInfo.y);
  // 图文编辑，后端对于单图是根据图片左上角定位x y的，前端需要加上留白距离才是渲染的x y
  return currentEditorType == 'image'
    ? _y + (currentSetting.value?.space?.v ?? 0)
    : _y;
});

const fontSize = computed(() => sizeToPx(props.textInfo.fontSize || 0));
const lineHeight = computed(() => fontSize.value * 1.25);
// 计算文本行，并将每行中的空格替换为unicode格式
const textLines = computed(() =>
  (props.textInfo.text || '')
    .split('\n')
    .map((line: string) => line.replace(/ /g, '\u00A0')),
);

/** 每行文字的宽度 */
const widthPerLine = ref<number[]>([]);
/** 每行文字的高度信息（包含ascent/descent） */
const lineMetrics = ref<{ ascent: number; descent: number; height: number }[]>(
  [],
);

/** 最长文本宽度 */
const longestLineWidth = ref(0);

/** 单个字高 */
const charHeight = ref(0);

/** 文本左右内边距倍数 */
const TEXT_PADDING_RATIO = 0.6;

/** SVG宽度，取最长文本宽度 */
const svgWidth = computed(() => {
  return longestLineWidth.value + textPaddingWidth.value;
});

const svgHeight = computed(
  () => lineHeight.value * (textLines.value.length || 1),
);

const svgTextStyle = computed(() => {
  return getFontPresetStyle(props.textInfo);
});

const hasShadow = computed(() => !!svgTextStyle.value.filterId);

// 内边距宽度（统一给所有的花字加上左右内边距）
const textPaddingWidth = computed(() => {
  return TEXT_PADDING_RATIO * fontSize.value;
});

// 计算额外偏移量
const paddingOffset = computed(() => {
  return textPaddingWidth.value / 2;
});

/** 坐标计算上下文，简化函数调用 */
const coordinateContext = computed(() => ({
  svgWidth: svgWidth.value,
  widthPerLine: widthPerLine.value,
  textPaddingWidth: textPaddingWidth.value,
  paddingOffset: paddingOffset.value,
  textAnchor: textAnchor.value,
  svgTextStyle: svgTextStyle.value,
  lineHeight: lineHeight.value,
}));

/** 简化的坐标计算函数包装器 */
const getShadowX = (idx: number) =>
  getTextShadowX(idx, coordinateContext.value);
const getShadowY = (idx: number) =>
  getTextShadowY(idx, coordinateContext.value);
const getBgX = (idx: number) => getTextBgX(idx, coordinateContext.value);
const getBgY = (idx: number) => getTextBgY(idx, coordinateContext.value);
const getContentX = (idx: number) =>
  getTextContentX(idx, coordinateContext.value);
const getContentY = (idx: number): number =>
  getTextContentY(idx, coordinateContext.value);

const vdrKey = ref(0);

// 监听文本、字体、字号、父容器高度变化，重新初始化组件并测量文本宽度
// todo parentHeight需要监听？
watch(
  () => [
    props.textInfo.text,
    props.textInfo.fontName,
    props.textInfo.fontSize,
    currfontFamily.value,
    currentSpace.value,
    props.parentHeight,
  ],
  async () => {
    // 主动加载字体，确保字体已注册
    const font = fontMap.value.get(props.textInfo.fileName.split('.')[0]);
    if (font) {
      await loadFont(font);
    }
    // 计算每行文本的宽度和高度信息
    const metricsArr: { ascent: number; descent: number; height: number }[] =
      [];
    widthPerLine.value = await Promise.all(
      textLines.value.map(async (line: string) => {
        // 获取宽度和高度信息
        const { width, height, ascent, descent } = await measureTextWidth(
          line,
          currfontFamily.value,
          fontSize.value,
        );
        metricsArr.push({ ascent, descent, height });
        charHeight.value = height;
        return width;
      }),
    );
    lineMetrics.value = metricsArr;
    longestLineWidth.value = Math.max(...widthPerLine.value);
    // 当文本内容、字号或父容器高度变化时，重新初始化组件
    vdrKey.value += 1;
    // 测试log
    logTextInfo();
  },
  { immediate: true },
);

watch(
  () => [svgWidth.value, svgHeight.value],
  newSize => {
    // 减去border宽度和内边距偏移量
    globalTextStickerItemSize.value = {
      width: newSize[0] + 2 - paddingOffset.value,
      height: newSize[1] + 2,
    };
  },
);

const textAnchor = computed(() => {
  switch (props.textInfo.align) {
    case 'left':
      return 'start';
    case 'right':
      return 'end';
    case 'center':
    default:
      return 'middle';
  }
});
</script>

<style>
@import 'vue-draggable-resizable/dist/VueDraggableResizable.css';
</style>

<style lang="scss" scoped>
.vdr {
  @apply b-assist cursor-move;
}
::v-deep {
  .handle {
    @apply b-assist;
    &.handle-tl {
      @apply top-[-1px] left-[-1px];
    }
    &.handle-tr {
      @apply top-[-1px] right-[-1px];
    }
    &.handle-bl {
      @apply bottom-[-1px] left-[-1px];
    }
    &.handle-br {
      @apply bottom-[-1px] right-[-1px];
    }
    &.handle-tm {
      @apply top-[-1px];
    }
    &.handle-ml {
      @apply left-[-1px];
    }
    &.handle-mr {
      @apply right-[-1px];
    }
    &.handle-bm {
      @apply bottom-[-1px];
    }
  }
}
</style>
