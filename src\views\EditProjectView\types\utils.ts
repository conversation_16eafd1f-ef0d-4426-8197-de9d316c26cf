/**
 * @fileoverview 项目编辑视图的通用高级工具类型和辅助函数定义。
 * @description 此文件包含用于文件类型过滤、异步操作处理、事件处理、条件类型判断以及基本几何图形等场景的工具类型和函数。
 *              注意：原表单相关的工具类型已移至 `form.ts`。
 * <AUTHOR>
 * @since 1.0.0
 */

import type { MediaType, FileStatus } from './base';
import { FILE_STATUS, MEDIA_TYPES } from './base';
import type { FileInfo } from './file';

// ============= 文件处理工具类型 =============

/**
 * @description 根据媒体类型（MediaType）从文件信息数组中筛选特定类型的文件。
 * @template T - 继承自 `FileInfo` 的文件信息类型。
 * @template M - 媒体类型字符串，如 'image' 或 'video'。
 * @typedef {T extends { mediaType: M } ? T : never} FilterFilesByMediaType
 * @example
 * type AllFiles = FileInfo[];
 * type ImagesOnly = FilterFilesByMediaType<FileInfo, typeof MEDIA_TYPES.IMAGE>; // 将得到所有 mediaType 为 MEDIA_TYPES.IMAGE 的 FileInfo
 */
export type FilterFilesByMediaType<
  T extends FileInfo,
  M extends MediaType,
> = T extends {
  mediaType: M;
}
  ? T
  : never;

/**
 * @description 特指媒体类型为 MEDIA_TYPES.IMAGE 的文件信息类型。
 * @typedef {FilterFilesByMediaType<FileInfo, typeof MEDIA_TYPES.IMAGE>} ImageFile
 * @see {@link FileInfo}
 * @see {@link FilterFilesByMediaType}
 */
export type ImageFile = FilterFilesByMediaType<
  FileInfo,
  typeof MEDIA_TYPES.IMAGE
>;

/**
 * @description 特指媒体类型为 MEDIA_TYPES.VIDEO 的文件信息类型。
 * @typedef {FilterFilesByMediaType<FileInfo, typeof MEDIA_TYPES.VIDEO>} VideoFile
 * @see {@link FileInfo}
 * @see {@link FilterFilesByMediaType}
 */
export type VideoFile = FilterFilesByMediaType<
  FileInfo,
  typeof MEDIA_TYPES.VIDEO
>;

/**
 * @description 根据文件状态（FileStatus）从文件信息数组中筛选特定状态的文件。
 * @template T - 继承自 `FileInfo` 的文件信息类型。
 * @template S - 文件状态枚举值，如 `FILE_STATUS.NORMAL`。
 * @typedef {T extends { status: S } ? T : never} FilterFilesByStatus
 * @example
 * type NormalFiles = FilterFilesByStatus<FileInfo, typeof FILE_STATUS.NORMAL>;
 */
export type FilterFilesByStatus<
  T extends FileInfo,
  S extends FileInfo['status'],
> = T extends {
  status: S;
}
  ? T
  : never;

/**
 * @description 特指状态为 `FILE_STATUS.NORMAL` (正常/已完成) 的文件信息类型。
 * @typedef {FilterFilesByStatus<FileInfo, typeof FILE_STATUS.NORMAL>} CompletedFile
 * @see {@link FileInfo}
 * @see {@link FilterFilesByStatus}
 * @see {@link FILE_STATUS.NORMAL}
 */
export type CompletedFile = FilterFilesByStatus<
  FileInfo,
  typeof FILE_STATUS.NORMAL
>;

/**
 * @description 特指状态为 `FILE_STATUS.TEMP` (上传中/临时) 的文件信息类型。
 * @typedef {FilterFilesByStatus<FileInfo, typeof FILE_STATUS.TEMP>} UploadingFile
 * @see {@link FileInfo}
 * @see {@link FilterFilesByStatus}
 * @see {@link FILE_STATUS.TEMP}
 */
export type UploadingFile = FilterFilesByStatus<
  FileInfo,
  typeof FILE_STATUS.TEMP
>;

/**
 * @description 特指状态为 `FILE_STATUS.RECYCLE` (回收站) 或 `FILE_STATUS.DELETE` (已删除) 的文件信息类型，通常视为无效文件。
 * @typedef {FilterFilesByStatus<FileInfo, typeof FILE_STATUS.RECYCLE | typeof FILE_STATUS.DELETE>} InvalidFile
 * @see {@link FileInfo}
 * @see {@link FilterFilesByStatus}
 * @see {@link FILE_STATUS.RECYCLE}
 * @see {@link FILE_STATUS.DELETE}
 */
export type InvalidFile = FilterFilesByStatus<
  FileInfo,
  typeof FILE_STATUS.RECYCLE | typeof FILE_STATUS.DELETE
>;

/**
 * @description 检查给定的文件状态是否表示文件已失效（即在回收站或已删除）。
 * @function isInvalidFileStatus
 * @param {FileStatus} status - 要检查的文件状态。
 * @returns {boolean} 如果文件状态为 `FILE_STATUS.RECYCLE` 或 `FILE_STATUS.DELETE`，则返回 `true`，否则返回 `false`。
 */
export function isInvalidFileStatus(status: FileStatus): boolean {
  return status === FILE_STATUS.RECYCLE || status === FILE_STATUS.DELETE;
}

// ============= 通用工具类型 =============

/**
 * @description 将普通函数类型转换为返回Promise的异步函数类型。
 * @template T - 原始函数类型。
 * @typedef {(...args: Parameters<T>) => Promise<ReturnType<T>>} AsyncFunction
 */
export type AsyncFunction<T extends (...args: unknown[]) => unknown> = (
  ...args: Parameters<T>
) => Promise<ReturnType<T>>;

/**
 * @description 通用事件处理器函数类型。
 * @template T - 事件对象的类型，默认为 `unknown`。
 * @typedef {(event: T) => void | Promise<void>} EventHandler
 */
export type EventHandler<T = unknown> = (event: T) => void | Promise<void>;

/**
 * @description 通用回调函数类型。
 * @template T - 回调函数接收的参数类型，默认为 `void`。
 * @template R - 回调函数的返回值类型，默认为 `void`。
 * @typedef {(result: T) => R} Callback
 */
export type Callback<T = void, R = void> = (result: T) => R;

/**
 * @description 通用错误处理器函数类型。
 * @typedef {(error: Error) => void | Promise<void>} ErrorHandler
 */
export type ErrorHandler = (error: Error) => void | Promise<void>;

// ============= 条件类型工具 =============

/**
 * @description 条件类型：如果类型 `T` 可以赋值给类型 `U`，则返回类型 `Y`，否则返回类型 `N`。
 * @template T - 待检查的类型。
 * @template U - 目标类型。
 * @template Y - `T` 可赋值给 `U` 时返回的类型，默认为 `T`。
 * @template N - `T` 不可赋值给 `U` 时返回的类型，默认为 `never`。
 * @typedef {T extends U ? Y : N} IfExtends
 */
export type IfExtends<T, U, Y = T, N = never> = T extends U ? Y : N;

/**
 * @description 条件类型：如果类型 `T` 和类型 `U` 完全相同，则返回类型 `Y`，否则返回类型 `N`。
 * @template T - 待检查的类型。
 * @template U - 目标类型。
 * @template Y - `T` 与 `U` 相同时返回的类型，默认为 `T`。
 * @template N - `T` 与 `U` 不相同时返回的类型，默认为 `never`。
 * @typedef {(<G>() => G extends T ? 1 : 2) extends (<G>() => G extends U ? 1 : 2) ? Y : N} IfEquals
 */
export type IfEquals<T, U, Y = T, N = never> = (<G>() => G extends T
  ? 1
  : 2) extends <G>() => G extends U ? 1 : 2
  ? Y
  : N;

/**
 * @description 类型断言工具：确保类型 `T` 是类型 `U` 的子类型。如果是，则返回 `T`，否则返回 `never`。
 * @template T - 待断言的类型。
 * @template U - 期望的父类型。
 * @typedef {T extends U ? T : never} Assert
 */
export type Assert<T, U> = T extends U ? T : never;

/**
 * @description 检查类型 `T` 是否为函数类型。
 * @template T - 待检查的类型。
 * @typedef {T extends (...args: unknown[]) => unknown ? true : false} IsFunction
 */
export type IsFunction<T> = T extends (...args: unknown[]) => unknown
  ? true
  : false;

/**
 * @description 检查类型 `T` 是否为纯对象类型（非数组、非函数）。
 * @template T - 待检查的类型。
 * @typedef {T extends object ? (T extends unknown[] ? false : true) : false} IsObject
 */
export type IsObject<T> = T extends object
  ? T extends unknown[]
    ? false
    : true
  : false;

/**
 * @description 检查类型 `T` 是否为数组类型。
 * @template T - 待检查的类型。
 * @typedef {T extends unknown[] ? true : false} IsArray
 */
export type IsArray<T> = T extends unknown[] ? true : false;

/**
 * @description 检查类型 `T` 是否为字符串类型。
 * @template T - 待检查的类型。
 * @typedef {T extends string ? true : false} IsString
 */
export type IsString<T> = T extends string ? true : false;

/**
 * @description 检查类型 `T` 是否为数字类型。
 * @template T - 待检查的类型。
 * @typedef {T extends number ? true : false} IsNumber
 */
export type IsNumber<T> = T extends number ? true : false;

// ============= 几何类型 =============

/**
 * @description 表示二维平面上的一个点坐标。
 * @interface Point
 */
export type Point = {
  /** X轴坐标 */
  x: number;
  /** Y轴坐标 */
  y: number;
};

/**
 * @description 表示一个二维对象的尺寸（宽度和高度）。
 * @interface Size
 */
export type Size = {
  /** 宽度 */
  width: number;
  /** 高度 */
  height: number;
};

/**
 * @description 表示一个矩形区域，由一个点（通常是左上角顶点）和尺寸定义。
 * @typedef {Point & Size} Rect
 * @see {@link Point}
 * @see {@link Size}
 */
export type Rect = Point & Size;

// 注意：表单相关的工具类型（如ExtractFieldNames、FormValues、FormDataType等）
// 已移至 form.ts 文件中，以保持表单系统类型的统一性
