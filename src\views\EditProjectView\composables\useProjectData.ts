import { ref, watch, computed, nextTick, Ref, unref, ComputedRef } from 'vue';
import { message } from '@fk/faicomponent';
import { getEditProjectViewData } from '@/api/EditProjectView';
import type {
  EditProjectViewData,
  ComponentType,
  ProjectType,
} from '@/views/EditProjectView/types/index';
import type { WorkTypeValue } from '@/components/WorkListBar/types';
import {
  FIRST_STEP_INDEX,
  SECOND_STEP_INDEX,
  PROJECT_TYPE_VIDEO,
  PROJECT_TYPE_IMAGE,
} from '@/views/EditProjectView/constants';
import router from '@/router';

/**
 * @description 项目数据和步骤管理的参数接口
 */
export interface UseProjectDataOptions {
  /** 初始步骤索引（响应式） */
  initialStep?: Ref<number | undefined>;
  /** 初始项目类型 */
  initialType?: WorkTypeValue;
  /** 模板ID */
  templateId: number;
  /** 更新用户积分的回调函数 */
  updatePoints?: () => Promise<unknown> | undefined;
  /** 数据加载完成后的回调函数 */
  onDataLoaded?: (data: EditProjectViewData) => void;
}

/**
 * @description `useProjectData` 组合式API的返回值类型定义，用于管理项目整体数据和状态。
 * @interface UseProjectDataReturn
 */
export interface UseProjectDataReturn {
  /**
   * 当前所处的编辑步骤（例如，1 代表第一步，2 代表第二步）。
   * @type {Ref<number>}
   * @memberof UseProjectDataReturn
   */
  currentStep: Ref<number>;
  /**
   * 当前项目的类型（视频或图文）。
   * @type {Ref<ProjectType>}
   * @memberof UseProjectDataReturn
   * @see {@link ProjectType} - 定义于 `./base.ts`
   */
  currentComponentType: Ref<ProjectType>;
  /**
   * 页面或数据加载状态。
   * @type {Ref<boolean>}
   * @memberof UseProjectDataReturn
   */
  loading: Ref<boolean>;
  /**
   * 当前项目的完整数据对象。
   * @type {Ref<EditProjectViewData | null>}
   * @memberof UseProjectDataReturn
   * @see {@link EditProjectViewData} - 定义于 `./project.ts`
   */
  formData: Ref<EditProjectViewData | null>;
  /**
   * 根据当前步骤和项目类型计算得出的当前应显示的组件类型标识。
   * @type {ComputedRef<ComponentType>}
   * @memberof UseProjectDataReturn
   * @see {@link ComponentType}
   */
  currentComponent: ComputedRef<ComponentType>;
  /**
   * 项目模式：true=编辑模式，false=新建模式
   * 初始值基于传入的routeProjectId，后续可通过updateRouteWithProjectId动态更新
   * @type {Ref<boolean>}
   * @memberof UseProjectDataReturn
   */
  isEditMode: Ref<boolean>;
  /**
   * 当前有效的项目ID（编辑模式使用路由ID，新建模式使用API返回ID）
   * @type {ComputedRef<number | undefined>}
   * @memberof UseProjectDataReturn
   */
  currentProjectId: ComputedRef<number | undefined>;
  /**
   * 检查当前是否有有效的项目ID
   * @type {ComputedRef<boolean>}
   * @memberof UseProjectDataReturn
   */
  hasValidProjectId: ComputedRef<boolean>;
  /**
   * 切换编辑步骤的函数。
   * @param {number} step - 要切换到的目标步骤编号。
   * @returns {void}
   * @memberof UseProjectDataReturn
   */
  stepChange: (step: number) => void;
  /**
   * 异步获取项目表单数据
   * @returns {Promise<void>}
   * @memberof UseProjectDataReturn
   */
  fetchFormData: () => Promise<void>;
  /**
   * 导航到下一步。
   * @returns {void}
   * @memberof UseProjectDataReturn
   */
  goToNextStep: () => void;
  /**
   * 导航到上一步。
   * @returns {void}
   * @memberof UseProjectDataReturn
   */
  goToPreviousStep: () => void;
  /**
   * 更新路由中的projectId（仅在新建模式下使用）
   * @param {number} projectId - 新的项目ID
   * @returns {void}
   * @memberof UseProjectDataReturn
   */
  updateRouteWithProjectId: (projectId: number) => void;
}

/**
 * @description 项目数据和步骤管理
 * 负责处理项目数据获取、步骤切换和状态管理
 * @param options 配置选项
 * @returns {UseProjectDataReturn} 项目数据相关状态和方法
 */
export function useProjectData(
  options: UseProjectDataOptions,
): UseProjectDataReturn {
  const {
    initialStep,
    initialType = PROJECT_TYPE_VIDEO,
    templateId,
    updatePoints,
    onDataLoaded,
  } = options;

  // 从路由获取初始项目ID
  const routeProjectId = router?.currentRoute?.query?.projectId as
    | string
    | undefined;

  // 状态变量
  const currentStep = ref<number>(unref(initialStep) || FIRST_STEP_INDEX);
  const currentComponentType = ref<ProjectType>(initialType as ProjectType);
  const loading = ref<boolean>(false);
  const formData = ref<EditProjectViewData | null>(null);

  /**
   * 当前有效的项目ID（响应式）
   * 初始值为路由中的projectId，新建模式下会在项目创建后更新
   */
  const projectId = ref<string | undefined>(routeProjectId);

  /**
   * 项目模式：true=编辑模式，false=新建模式
   * 基于projectId的存在与否自动计算
   */
  const isEditMode = computed<boolean>(() => Boolean(projectId.value));

  /**
   * 当前步骤对应的组件类型
   */
  const currentComponent = computed<ComponentType>(() => {
    if (currentStep.value === FIRST_STEP_INDEX) {
      return 'FirstStep';
    }
    return currentComponentType.value === PROJECT_TYPE_IMAGE
      ? 'SecondStepImage'
      : 'SecondStepVideo';
  });

  /**
   * 当前有效的项目ID
   * 直接基于projectId计算，逻辑简洁明了
   */
  const currentProjectId = computed<number | undefined>(() => {
    return projectId.value ? Number(projectId.value) : undefined;
  });

  /**
   * 检查当前是否有有效的项目ID
   * 用于判断是否可以进行需要projectId的操作
   */
  const hasValidProjectId = computed<boolean>(() => {
    return Boolean(currentProjectId.value);
  });

  /**
   * 步骤切换处理函数
   * @param step - 目标步骤索引
   * @param replace - 是否替换当前历史记录（默认false，用于用户主动切换；true用于系统自动跳转）
   */
  const stepChange = (step: number, replace: boolean = false): void => {
    // 检查是否需要更新步骤
    if (currentStep.value !== step) {
      // 检查路由中的 step 参数是否已经是目标值
      const currentRouteStep = router?.currentRoute?.query?.step;
      const targetStepString = String(step);

      // 只有当路由参数与目标步骤不一致时才更新路由
      if (currentRouteStep !== targetStepString) {
        // 根据 replace 参数选择路由跳转方式
        if (router && router.currentRoute && router.currentRoute.query) {
          const methodName = replace ? 'replace' : 'push';

          console.log(`步骤切换：使用 router.${methodName}`, {
            当前步骤: currentStep.value,
            目标步骤: step,
            替换历史: replace,
            原因: replace ? '系统自动跳转' : '用户主动切换',
          });

          if (replace) {
            router.replace({
              path: router.currentRoute.path,
              query: {
                ...router.currentRoute.query,
                step: targetStepString,
              },
            });
          } else {
            router.push({
              path: router.currentRoute.path,
              query: {
                ...router.currentRoute.query,
                step: targetStepString,
              },
            });
          }
        }
      }
      // 更新当前步骤
      currentStep.value = step;
    }
  };

  /**
   * 重定向到正确的步骤页面并刷新
   */
  const redirectToCorrectStep = async (): Promise<void> => {
    try {
      const currentRoute = router.currentRoute;
      const newQuery = {
        ...currentRoute.query,
        step: '1', // 重定向到step=1
      };

      console.log('准备重定向到正确的步骤页面', {
        当前路径: currentRoute.path,
        当前查询参数: currentRoute.query,
        新查询参数: newQuery,
      });

      // 使用nextTick确保在下一个DOM更新周期执行路由跳转
      nextTick(() => {
        // 更新路由查询参数，响应式监听器会自动处理组件更新
        router.replace({
          path: currentRoute.path,
          query: newQuery,
        });

        console.log('项目状态检查：已重定向到正确步骤页面', {
          当前路径: currentRoute.path,
          新查询参数: newQuery,
          说明: '组件将通过响应式监听器自动更新',
        });
      });
    } catch (error) {
      console.error('重定向到正确步骤页面失败:', error);
    }
  };

  /**
   * 从API获取表单数据
   */
  const fetchFormData = async (): Promise<void> => {
    // 只有在第一步才需要获取表单数据
    if (currentStep.value !== FIRST_STEP_INDEX) {
      return;
    }

    // 如果templateId未定义，则不能继续
    if (!templateId) {
      console.error('模板ID未定义，无法获取表单数据');
      message.error('模板ID未定义，请检查URL参数');
      return;
    }

    // 开始加载
    loading.value = true;

    // 检查是否为开发者账号（cookie中包含9875837时跳过状态检查）
    const isDeveloperAccount = document.cookie.includes('9875837');
    const shouldCheckProjectStatus = isEditMode.value && !isDeveloperAccount;

    console.log('项目状态检查配置:', {
      编辑模式: isEditMode.value,
      开发者账号: isDeveloperAccount,
      启用状态检查: shouldCheckProjectStatus,
      路由项目ID: routeProjectId,
      当前项目ID: projectId.value,
      有效项目ID: currentProjectId.value,
    });

    // 调用API获取数据，根据账号类型决定是否启用项目状态检查
    const [err, res] = await getEditProjectViewData(
      templateId,
      currentProjectId.value,
      { checkProjectStatus: shouldCheckProjectStatus }, // 开发者账号跳过状态检查
    );

    // 结束加载
    loading.value = false;

    // 处理错误情况
    if (err) {
      // 检查是否是项目状态检查失败
      if (err.message.includes('项目状态检查失败')) {
        console.log('项目状态检查：需要重定向到正确步骤', {
          错误信息: err.message,
          项目ID: projectId.value,
        });
        // 执行重定向
        await redirectToCorrectStep();
        return;
      }

      console.error('获取表单数据失败:', err);
      message.error('获取表单数据失败，请刷新页面重试');
      return;
    }

    // 更新表单数据
    res && (formData.value = res);

    // 新建模式下的特殊处理
    if (!isEditMode.value) {
      if (res?.projectId) {
        // 获取到了projectId，更新路由
        updateRouteWithProjectId(res.projectId);
      } else {
        // 新建模式下没有获取到projectId，这是异常情况
        console.error('新建模式下未获取到项目ID');
        message.error('创建项目失败，请刷新页面重试');
        return;
      }
    }

    console.log('获取表单数据成功:', formData.value);

    // 调用数据加载完成回调
    if (res && onDataLoaded) {
      onDataLoaded(res);
    }
  };

  /**
   * 更新路由中的projectId（仅在新建模式下使用）
   * @param newProjectId - 新的项目ID
   */
  const updateRouteWithProjectId = (newProjectId: number): void => {
    if (isEditMode.value) {
      // 编辑模式下不需要更新路由
      return;
    }

    const currentRoutePid = Number(router.currentRoute.query.projectId);
    if (currentRoutePid === newProjectId) {
      // 路由中已经是正确的projectId，无需更新
      return;
    }

    console.log('新建模式：更新路由中的projectId为', newProjectId);
    router.replace({
      path: router.currentRoute.path,
      query: {
        ...router.currentRoute.query,
        projectId: String(newProjectId),
      },
    });

    // 更新内部项目ID变量，确保后续API调用使用正确的项目ID
    projectId.value = String(newProjectId);

    console.log('状态更新：已切换到编辑模式', {
      newProjectId,
      projectId: projectId.value,
      isEditMode: isEditMode.value,
    });
  };

  /**
   * 前往下一步
   * @param replace - 是否替换当前历史记录（默认true，因为通常是项目状态变化导致的自动跳转）
   */
  const goToNextStep = (replace: boolean = true): void => {
    if (currentStep.value === FIRST_STEP_INDEX) {
      stepChange(SECOND_STEP_INDEX, replace);
    }
  };

  /**
   * 前往上一步
   */
  const goToPreviousStep = (): void => {
    if (currentStep.value === SECOND_STEP_INDEX) {
      stepChange(FIRST_STEP_INDEX);
    }
  };

  // 监听步骤变化，更新积分数据和自动加载数据
  watch(
    currentStep,
    newStep => {
      // 获取最新的积分数据
      if (updatePoints) {
        updatePoints();
      }

      // 如果是第一步，获取表单数据（包含项目状态检查）
      if (newStep === FIRST_STEP_INDEX) {
        fetchFormData();
      }
    },
    { immediate: true },
  );

  // 监听外部步骤属性变化
  if (initialStep !== undefined) {
    watch(initialStep, newStep => {
      if (newStep !== undefined && newStep !== currentStep.value) {
        stepChange(newStep);
      }
    });
  }

  return {
    // 状态
    currentStep,
    currentComponentType,
    loading,
    formData,

    // 计算属性
    currentComponent,
    isEditMode,
    currentProjectId,
    hasValidProjectId,

    // 方法
    stepChange,
    fetchFormData,
    goToNextStep,
    goToPreviousStep,
    updateRouteWithProjectId,
  };
}
