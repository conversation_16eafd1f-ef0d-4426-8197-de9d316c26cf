/**
 * scrollArea.ts 重构后的功能测试
 * 验证新的参数化函数是否正常工作，以及向后兼容性
 */

import {
  getDefaultScrollAreaStyles,
  DEFAULT_SCROLL_BAR_STYLE,
  DEFAULT_SCROLL_THUMB_STYLE,
  WORK_LIST_SCROLL_THUMB_STYLE,
  SCRIPT_MODAL_SCROLL_THUMB_STYLE,
} from '../scrollArea';

describe('scrollArea 重构功能测试', () => {
  describe('getDefaultScrollAreaStyles 参数化功能', () => {
    test('无参数调用应返回默认样式', () => {
      const result = getDefaultScrollAreaStyles();

      expect(result).toEqual({
        barStyle: DEFAULT_SCROLL_BAR_STYLE,
        thumbStyle: DEFAULT_SCROLL_THUMB_STYLE,
      });
    });

    test('自定义 thumbStyle 应正确合并', () => {
      const customThumbStyle = { right: '-11px' };
      const result = getDefaultScrollAreaStyles({
        thumbStyle: customThumbStyle,
      });

      expect(result.barStyle).toEqual(DEFAULT_SCROLL_BAR_STYLE);
      expect(result.thumbStyle).toEqual({
        ...DEFAULT_SCROLL_THUMB_STYLE,
        ...customThumbStyle,
      });
    });

    test('自定义 barStyle 应正确合并', () => {
      const customBarStyle = { width: '8px' };
      const result = getDefaultScrollAreaStyles({
        barStyle: customBarStyle,
      });

      expect(result.barStyle).toEqual({
        ...DEFAULT_SCROLL_BAR_STYLE,
        ...customBarStyle,
      });
      expect(result.thumbStyle).toEqual(DEFAULT_SCROLL_THUMB_STYLE);
    });

    test('同时自定义 barStyle 和 thumbStyle', () => {
      const customBarStyle = { width: '8px' };
      const customThumbStyle = { right: '0px', backgroundColor: '#bbb' };
      const result = getDefaultScrollAreaStyles({
        barStyle: customBarStyle,
        thumbStyle: customThumbStyle,
      });

      expect(result.barStyle).toEqual({
        ...DEFAULT_SCROLL_BAR_STYLE,
        ...customBarStyle,
      });
      expect(result.thumbStyle).toEqual({
        ...DEFAULT_SCROLL_THUMB_STYLE,
        ...customThumbStyle,
      });
    });
  });

  describe('常量验证', () => {
    test('WORK_LIST_SCROLL_THUMB_STYLE 应基于默认样式扩展', () => {
      expect(WORK_LIST_SCROLL_THUMB_STYLE).toEqual({
        ...DEFAULT_SCROLL_THUMB_STYLE,
        right: '-11px',
      });
    });

    test('SCRIPT_MODAL_SCROLL_THUMB_STYLE 应基于默认样式扩展', () => {
      expect(SCRIPT_MODAL_SCROLL_THUMB_STYLE).toEqual({
        ...DEFAULT_SCROLL_THUMB_STYLE,
        right: '0px',
        backgroundColor: '#bbb',
      });
    });
  });

  describe('类型安全性测试', () => {
    test('应接受正确的样式属性', () => {
      // 这个测试主要验证 TypeScript 类型定义是否正确
      const result = getDefaultScrollAreaStyles({
        barStyle: {
          right: '0',
          borderRadius: '0px',
          backgroundColor: 'transparent',
          width: '0px',
          opacity: 0,
        },
        thumbStyle: {
          borderRadius: '3px',
          backgroundColor: '#d9d9d9',
          width: '6px',
          opacity: 0.75,
          right: '-11px',
        },
      });

      expect(result).toBeDefined();
      expect(typeof result.barStyle).toBe('object');
      expect(typeof result.thumbStyle).toBe('object');
    });
  });
});
