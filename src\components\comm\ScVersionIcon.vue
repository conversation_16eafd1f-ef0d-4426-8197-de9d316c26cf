<template>
  <!-- 当需要权限验证且用户版本不足时显示tooltip -->
  <fa-tooltip
    v-if="needPermissionCheck && !hasPermission"
    placement="top"
    overlayClassName="versionIconOverlayClassName"
    :getPopupContainer="props.getPopupContainer"
  >
    <template slot="title">
      <div class="version-tooltip">
        <span>{{ tooltipMessage }}&nbsp;</span>
        <a
          :href="upgradeUrl"
          target="_blank"
          class="version-tooltip__link"
          @click.stop
        >
          去升级
        </a>
      </div>
    </template>
    <img :src="iconAsset" :class="versionIconClass" />
  </fa-tooltip>
  <!-- 正常显示版本图标 -->
  <img v-else :src="iconAsset" :class="versionIconClass" />
</template>

<script setup lang="ts">
import { computed } from 'vue';
import {
  VERSION,
  VERSION_ICON_ASSET_MAP,
  VERSION_NAME,
} from '@/constants/version.ts';
import { getVersionIconUrl } from '@/utils/version.ts';
import { checkVersionPermission } from '@/utils/versionControl';
import { getExternalDynamicUrl } from '@/constants/system';

const props = defineProps({
  /** 版本 */
  version: {
    type: Number,
    required: true,
  },
  /** 类型 可选值：small、medium、large */
  type: {
    type: String,
    default: 'medium',
  },
  /** 功能要求的最低版本，用于权限验证 */
  requiredVersion: {
    type: Number as () => VERSION,
    required: false,
  },
  /** 自定义提示消息，如果不提供则使用默认格式 */
  customTooltipMessage: {
    type: String,
    required: false,
  },
  /** 弹出层容器函数，用于控制tooltip的渲染位置 */
  getPopupContainer: {
    type: Function,
    required: false,
  },
});

const iconAsset = computed(() => {
  // 当传入了requiredVersion时，优先使用requiredVersion显示对应的版本图标
  const displayVersion = props.requiredVersion ?? props.version;

  const path =
    VERSION_ICON_ASSET_MAP?.[displayVersion as VERSION]?.[
      props.type as 'small' | 'medium' | 'large'
    ];

  if (path) {
    return getVersionIconUrl(path);
  }
  return '';
});

/**
 * 是否需要进行权限检查
 */
const needPermissionCheck = computed(() => {
  return props.requiredVersion !== undefined;
});

/**
 * 用户是否有权限使用该功能
 */
const hasPermission = computed(() => {
  if (!needPermissionCheck.value) return true;
  return checkVersionPermission(props.requiredVersion as VERSION);
});

/**
 * Tooltip提示消息
 */
const tooltipMessage = computed(() => {
  if (!needPermissionCheck.value || hasPermission.value) return '';

  // 如果提供了自定义提示消息，直接使用
  if (props.customTooltipMessage) {
    return props.customTooltipMessage;
  }

  // 否则使用默认格式
  const requiredVersionName = VERSION_NAME[props.requiredVersion as VERSION];
  return `开启该功能需要升级至${requiredVersionName}`;
});

/**
 * 升级链接URL
 */
const upgradeUrl = computed(() => {
  return getExternalDynamicUrl().VERSION_BUY;
});

/**
 * 版本图标的动态class
 */
const versionIconClass = computed(() => {
  return `version-icon version-icon--${props.type}`;
});
</script>

<style lang="scss" scoped>
.version-icon {
  /* 尺寸相关 */
  @apply h-19px w-auto;
}

.version-icon--small {
  /* 尺寸相关 */
  @apply h-17px w-18px;
}

.version-icon--medium {
  /* 尺寸相关 */
  @apply h-19px w-auto;
}

.version-icon--large {
}
</style>

<style lang="scss">
.versionIconOverlayClassName {
  .fa-tooltip-inner {
    /* 外观相关 */
    @apply rounded-6px bg-[rgba(0,0,0,0.8)];
  }

  .version-tooltip {
    /* 布局相关 */
    @apply flex items-center;
    /* 文字相关 */
    @apply text-white text-14px;

    .version-tooltip__link {
      /* 文字相关 */
      @apply text-white font-600;
      /* 外观相关 */
      @apply underline;
      /* 交互相关 */
      @apply cursor-pointer;
    }
  }
}
</style>
