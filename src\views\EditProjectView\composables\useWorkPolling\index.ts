/**
 * @fileoverview 智能轮询功能 Composable
 * @description 提供作品状态的智能轮询功能，支持动态间隔调整和自动启停
 * 基于事件驱动架构，将状态变化分解为基础事件进行统一处理
 */

import { ref, computed, watch, onUnmounted, onMounted } from 'vue';
import { WorkItem } from '@/types';
import {
  getWorkStatusInfo,
  isPollingWork,
  WorkStatusInfo,
} from '@/constants/workStatus';
import {
  getWorkListByIdWithTransform,
  getWorkListWithTransform,
} from '@/api/EditProjectView/work';
import { logger } from '@/utils/logger';
// 导入重构后的模块
import { WorkEventManager, WorkStateAnalyzer, PollingController } from './core';
import { POLLING_TRIGGER_TYPE } from './constants';
import { PAGE_LEAVE_THRESHOLDS } from './config';
import {
  calculateMaxProgress,
  calculatePollingInterval,
  createPollingContext,
  validateOptions,
  registerEventHandlers,
} from './utils';
import type {
  UseWorkPollingOptions,
  UseWorkPollingReturn,
  StateMap,
  ProgressMap,
} from './types';

/**
 * 智能轮询功能 Composable
 * @param options 轮询配置选项
 * @returns 轮询相关的状态和方法
 */
export function useWorkPolling<T extends WorkItem>(
  options: UseWorkPollingOptions<T>,
): UseWorkPollingReturn {
  const { workList, projectId, projectType, eventHandlers } = options;

  // 参数验证
  validateOptions(projectId, projectType);

  // ==================== 事件系统初始化 ====================
  const eventManager = new WorkEventManager();
  const stateAnalyzer = new WorkStateAnalyzer(eventManager);

  // 注册事件处理器
  registerEventHandlers(eventManager, eventHandlers);

  // ==================== 轮询状态管理 ====================
  const isPageVisible = ref(true);
  const pageLeaveTime = ref<number | null>(null);

  // 防重复调用标志
  const isPollingInProgress = ref(false);

  // 检测需要轮询的作品
  const pollingWorks = computed(() =>
    workList.value.filter(work => {
      const statusInfo = getWorkStatusInfo(work);
      return isPollingWork(statusInfo);
    }),
  );

  // 是否需要轮询
  const needsPolling = computed(
    () => pollingWorks.value.length > 0 && isPageVisible.value,
  );

  // 创建轮询控制器
  const pollingController = new PollingController(
    projectId,
    projectType,
    () => pollingWorks.value.length,
  );

  // ==================== 轮询逻辑 ====================

  /**
   * 获取轮询间隔
   */
  const getPollingInterval = (): number => {
    const maxProgress = calculateMaxProgress(pollingWorks.value);
    return calculatePollingInterval({
      projectType,
      maxProgress,
      pollingWorksCount: pollingWorks.value.length,
    });
  };

  /**
   * 执行轮询更新
   */
  const performPollingUpdate = async (
    triggerType: string = POLLING_TRIGGER_TYPE.AUTO,
  ): Promise<void> => {
    const pollingWorkIds = pollingWorks.value.map(work => work.id);
    if (pollingWorkIds.length === 0) {
      return;
    }

    // 防止重复调用：如果已经有轮询请求在进行中，则跳过本次调用
    if (isPollingInProgress.value) {
      logger.debug('⚠️ 轮询请求已在进行中，跳过本次调用', {
        触发类型: triggerType,
        轮询作品数量: pollingWorkIds.length,
        项目ID: projectId,
      });
      return;
    }

    const currentInterval = getPollingInterval();
    const maxProgress = calculateMaxProgress(pollingWorks.value);

    // 记录轮询前的状态
    const { currentStates, currentProgress } = recordCurrentStates();

    // 记录轮询开始
    logPollingStart(triggerType, pollingWorkIds, currentInterval, maxProgress);

    const startTime = performance.now();

    // 设置轮询进行中标志
    isPollingInProgress.value = true;

    try {
      // 执行轮询专用API请求
      const [err, res] = await getWorkListByIdWithTransform({
        projectId,
        idList: pollingWorkIds,
      });

      const duration = performance.now() - startTime;

      if (err || !res?.workList) {
        logPollingError(triggerType, err, duration);
        return;
      }

      // 使用状态分析器分析变化并触发事件
      const updatedWorks = res.workList as T[];
      await stateAnalyzer.analyzeAndEmitEvents(
        updatedWorks,
        currentStates,
        currentProgress,
        `轮询更新-${triggerType}`,
      );

      // 记录轮询完成
      logPollingComplete(triggerType, updatedWorks.length, duration);
    } finally {
      // 无论成功还是失败，都要清除轮询进行中标志
      isPollingInProgress.value = false;
    }
  };

  /**
   * 执行页面恢复时的智能数据更新
   */
  const performPageResumeUpdate = async (): Promise<void> => {
    if (!pageLeaveTime.value) {
      logger.debug('📱 页面恢复：无离开时间记录，跳过更新');
      return;
    }

    const leaveTime = pageLeaveTime.value;
    const currentTime = Date.now();
    const leaveDuration = currentTime - leaveTime;
    const leaveDurationSeconds = Math.round(leaveDuration / 1000);

    logger.debug('📱 页面恢复检测', {
      离开时长: `${leaveDurationSeconds}秒`,
      离开时间: new Date(leaveTime).toLocaleTimeString(),
      恢复时间: new Date(currentTime).toLocaleTimeString(),
      更新阈值: `${PAGE_LEAVE_THRESHOLDS.IMMEDIATE_UPDATE / 1000}秒`,
    });

    // 如果离开时间小于阈值，不进行更新
    if (leaveDuration < PAGE_LEAVE_THRESHOLDS.IMMEDIATE_UPDATE) {
      logger.debug('📱 页面恢复：离开时间较短，跳过数据更新');
      return;
    }

    await updateAllWorksOnPageResume(leaveDurationSeconds);
    pageLeaveTime.value = null;
  };

  /**
   * 页面恢复时更新所有作品
   */
  const updateAllWorksOnPageResume = async (
    leaveDurationSeconds: number,
  ): Promise<void> => {
    // 防止重复调用：如果已经有轮询请求在进行中，则跳过本次调用
    if (isPollingInProgress.value) {
      logger.debug('⚠️ 页面恢复更新：轮询请求已在进行中，跳过本次调用', {
        项目ID: projectId,
        离开时长: `${leaveDurationSeconds}秒`,
      });
      return;
    }

    const startTime = performance.now();
    logger.debug('📱 页面恢复：开始更新作品列表和详情数据');

    // 设置轮询进行中标志
    isPollingInProgress.value = true;

    try {
      const allWorkIds = workList.value.map(work => work.id);
      if (allWorkIds.length === 0) return;

      const [err, res] = await getWorkListWithTransform({
        projectId,
        pageNow: 1,
        limit: allWorkIds.length,
        idList: allWorkIds,
      });

      const duration = performance.now() - startTime;

      if (!err && res?.workList) {
        const updatedWorks = res.workList as T[];

        // 页面恢复时需要记录当前状态，以便检测状态变化
        const { currentStates, currentProgress } = recordCurrentStates();

        await stateAnalyzer.analyzeAndEmitEvents(
          updatedWorks,
          currentStates,
          currentProgress,
          POLLING_TRIGGER_TYPE.PAGE_RESUME,
        );

        logger.debug('✅ 页面恢复更新完成', {
          触发类型: POLLING_TRIGGER_TYPE.PAGE_RESUME,
          离开时长: `${leaveDurationSeconds}秒`,
          更新作品数量: updatedWorks.length,
          耗时: `${duration.toFixed(2)}ms`,
        });
      } else {
        logger.debug('❌ 页面恢复更新失败', {
          触发类型: POLLING_TRIGGER_TYPE.PAGE_RESUME,
          错误信息: err?.message || '响应数据为空',
          耗时: `${(performance.now() - startTime).toFixed(2)}ms`,
        });
      }
    } catch (error) {
      logger.debug('❌ 页面恢复更新异常', {
        触发类型: POLLING_TRIGGER_TYPE.PAGE_RESUME,
        错误信息: error instanceof Error ? error.message : '未知错误',
        耗时: `${(performance.now() - startTime).toFixed(2)}ms`,
      });
    } finally {
      // 无论成功还是失败，都要清除轮询进行中标志
      isPollingInProgress.value = false;
    }
  };

  // ==================== 页面可见性处理 ====================

  /**
   * 处理页面可见性变化
   */
  const handleVisibilityChange = (): void => {
    const isVisible = !document.hidden;
    const previousVisible = isPageVisible.value;
    isPageVisible.value = isVisible;

    logger.debug('👁️ 页面可见性变化', {
      当前状态: isVisible ? '可见' : '隐藏',
      之前状态: previousVisible ? '可见' : '隐藏',
      轮询状态: pollingController.isPolling.value ? '进行中' : '已停止',
      轮询作品数量: pollingWorks.value.length,
      时间戳: new Date().toLocaleTimeString(),
    });

    if (isVisible && !previousVisible) {
      // 页面从隐藏变为可见
      logger.debug('📱 页面变为可见，恢复轮询并检查是否需要更新数据');
      performPageResumeUpdate().catch(error => {
        logger.debug('❌ 页面恢复数据更新失败', {
          错误信息: error instanceof Error ? error.message : '未知错误',
        });
      });
    } else if (!isVisible && previousVisible) {
      // 页面从可见变为隐藏
      logger.debug('📱 页面变为隐藏，暂停轮询并记录离开时间', {
        离开时间: new Date().toLocaleTimeString(),
        当前轮询状态: pollingController.isPolling.value ? '进行中' : '已停止',
      });
      pageLeaveTime.value = Date.now();
    }
  };

  // ==================== 辅助函数 ====================

  /**
   * 记录当前状态和进度
   */
  const recordCurrentStates = (): {
    currentStates: StateMap;
    currentProgress: ProgressMap;
  } => {
    const currentStates = new Map<number, WorkStatusInfo>();
    const currentProgress = new Map<number, number>();

    pollingWorks.value.forEach(work => {
      const statusInfo = getWorkStatusInfo(work);
      currentStates.set(work.id, statusInfo);
      currentProgress.set(work.id, work.progress || 0);
    });

    return { currentStates, currentProgress };
  };

  /**
   * 记录轮询开始日志
   */
  const logPollingStart = (
    triggerType: string,
    pollingWorkIds: number[],
    currentInterval: number,
    maxProgress: number,
  ): void => {
    logger.debug('🔄 轮询更新开始', {
      ...createPollingContext(
        triggerType,
        projectId,
        projectType,
        pollingWorks.value.length,
        isPageVisible.value,
      ),
      作品ID列表: pollingWorkIds,
      当前轮询间隔: `${currentInterval}ms`,
      最大进度: `${maxProgress.toFixed(1)}%`,
    });
  };

  /**
   * 记录轮询错误日志
   */
  const logPollingError = (
    triggerType: string,
    err: { message?: string } | null,
    duration: number,
  ): void => {
    logger.debug('❌ 轮询更新失败', {
      触发类型: triggerType,
      错误信息: err?.message || '响应数据为空',
      耗时: `${duration.toFixed(2)}ms`,
    });
  };

  /**
   * 记录轮询完成日志
   */
  const logPollingComplete = (
    triggerType: string,
    updateCount: number,
    duration: number,
  ): void => {
    logger.debug('✅ 轮询更新完成', {
      触发类型: triggerType,
      更新作品数量: updateCount,
      耗时: `${duration.toFixed(2)}ms`,
      下次轮询间隔: `${getPollingInterval()}ms`,
    });
  };

  // ==================== 生命周期管理 ====================

  // 监听页面可见性变化
  onMounted(() => {
    isPageVisible.value = !document.hidden;
    document.addEventListener('visibilitychange', handleVisibilityChange);
  });

  // 监听作品列表变化，自动控制轮询启停
  watch(
    needsPolling,
    (needs, oldNeeds) => {
      if (needs && !pollingController.isPolling.value) {
        pollingController.startPolling(
          POLLING_TRIGGER_TYPE.STATUS_CHANGE,
          () => performPollingUpdate(POLLING_TRIGGER_TYPE.AUTO),
          getPollingInterval,
          () => needsPolling.value,
        );
      } else if (!needs && pollingController.isPolling.value) {
        pollingController.stopPolling();
      }

      // 记录轮询需求变化
      if (needs !== oldNeeds) {
        logger.debug('🎯 轮询需求变化', {
          ...createPollingContext(
            '需求变化',
            projectId,
            projectType,
            pollingWorks.value.length,
            isPageVisible.value,
          ),
          轮询需求: needs ? '需要轮询' : '无需轮询',
          轮询状态: pollingController.isPolling.value ? '进行中' : '已停止',
          变化方向: needs ? '开始轮询' : '停止轮询',
        });
      }
    },
    { immediate: true },
  );

  // 组件卸载时清理资源
  onUnmounted(() => {
    pollingController.cleanup();
    document.removeEventListener('visibilitychange', handleVisibilityChange);
    eventManager.clear();

    logger.debug('🧹 轮询资源清理完成', {
      项目ID: projectId,
      清理时间: new Date().toLocaleTimeString(),
    });
  });

  // ==================== 公共方法 ====================

  /**
   * 手动启动轮询
   */
  const startPolling = (triggerType?: string): void => {
    pollingController.startPolling(
      triggerType || POLLING_TRIGGER_TYPE.MANUAL,
      () => performPollingUpdate(triggerType || POLLING_TRIGGER_TYPE.MANUAL),
      getPollingInterval,
      () => needsPolling.value,
    );
  };

  /**
   * 手动停止轮询
   */
  const stopPolling = (): void => {
    pollingController.stopPolling();
  };

  return {
    isPolling: pollingController.isPolling,
    pollingWorks,
    needsPolling,
    isPageVisible,
    isPollingTimedOut: pollingController.isPollingTimedOut,
    pollingStartTime: pollingController.pollingStartTime,
    startPolling,
    stopPolling,
  };
}

// 导出类型
export type { UseWorkPollingOptions, UseWorkPollingReturn } from './types';
