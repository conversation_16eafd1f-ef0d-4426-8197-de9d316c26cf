import { FILE_TYPES } from '@/constants/fileType';

/**
 * 资源信息(图片、视频)
 */
export interface FileData {
  /** 资源id，（aid下自增），上传过程中临时值是string */
  id: number;
  /** 资源名称 */
  name: string;
  /**  资源类型（0~20：预留系统使用，21+：用户使用） */
  type: number;
  /** 素材文件夹id（0：默认，临时文件夹：999999，1：系统，>1：用户）文件夹id可以同时做音乐资源的分类 */
  folderId: number;
  /** 文件大小（单位Byte） */
  fileSize: number;
  /** 文件类型（图片、视频、音频） */
  fileType: FILE_TYPES;
  /** 创建时间 */
  createTime: number;
  /** 更新时间 */
  updateTime: number;
  /** 后端原始资源ID（同时用于判断是否文件夹，有值即为资源） */
  resId: string;
  /** 视频额外数据，如视频封面、时长等 */
  extra: ExtraData;
  /** 标志位（0x1：无法切片） */
  flag?: number;
  /** 资源URL 前端新增 */
  url: string;
  /** 文件后缀 前端新增 */
  suffix?: string;
  /** 文件上传进度 前端新增 */
  percent?: number;
}

/**
 * @description 视频额外字段
 */
export interface ExtraData {
  /** 封面id字符串 */
  cover?: string;
  /** 视频封面图类型 自定义字段，用于前端展示 */
  coverType?: FILE_TYPES;
  /** 视频时长（单位秒） */
  duration?: number;
}

/**
 * 文件夹信息
 */
export interface FolderData {
  /** 文件夹id，（aid下自增） */
  id: number;
  /** 文件夹名 */
  name: string;
  /** 父文件夹id，0为根目录 */
  parentId?: number;
  /** 创建时间 */
  createTime?: number;
  /** 更新时间 */
  updateTime?: number;
  /** 是否是文件夹 */
  resId?: string;
}

/** 文件联合类型 */
export type MaterialData = FileData | FolderData;
