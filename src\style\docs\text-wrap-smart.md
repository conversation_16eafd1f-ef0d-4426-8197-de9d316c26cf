# sc-text-wrap-smart 文字换行处理工具类

## 概述

`sc-text-wrap-smart` 是一个 UnoCSS shortcut，用于处理文本的智能换行显示，特别适用于需要保留空格、换行符，并且要求英文单词不被断开的场景。

## 样式组合

```css
sc-text-wrap-smart = whitespace-pre-wrap + break-words + overflow-wrap-break-word
```

## 功能特性

1. **保留空格和换行符** (`whitespace-pre-wrap`)

   - 保留文本中的空格字符
   - 保留文本中的换行符 `\n`
   - 自动换行以适应容器宽度

2. **智能单词换行** (`break-words`)

   - 英文单词会整个换行，不会被断开
   - 只在单词边界处换行
   - 保持文本的可读性

3. **极端情况处理** (`overflow-wrap-break-word`)
   - 当单个单词本身就超出容器宽度时，才会在单词内部断行
   - 防止内容溢出容器

## 使用场景

- 用户输入的文本内容显示
- 多行文本预览
- 聊天消息显示
- 文章内容展示
- 任何需要保留格式的文本显示

## 使用方法

### 直接使用 shortcut

```vue
<template>
  <div class="sc-text-wrap-smart">
    {{ userContent }}
  </div>
</template>
```

### 在 BEM 类中使用（推荐）

```vue
<template>
  <div class="content-display">
    {{ userContent }}
  </div>
</template>

<style scoped>
.content-display {
  /* 文字换行处理 */
  @apply sc-text-wrap-smart;
  /* 其他样式 */
  @apply text-14px text-gray-800;
}
</style>
```

## 实际效果

### 输入文本示例

```text
这是一段包含
换行符的文本

还有    多个空格    的内容

以及很长的英文单词 supercalifragilisticexpialidocious 和普通英文 hello world
```

### 显示效果

- ✅ 换行符会正常换行
- ✅ 多个空格会被保留
- ✅ 英文单词 `hello` 和 `world` 会整个换行
- ✅ 超长单词 `supercalifragilisticexpialidocious` 在必要时会在单词内部断行

## 项目中的使用示例

### 图文作品预览

```vue
<!-- src/components/TemplateView/ImageWorkPreview.vue -->
<div class="text-title font-semibold text-[14px] sc-text-wrap-smart">
  {{ title }}
</div>
<div class="text-text text-[12px] sc-text-wrap-smart pb-[10px]">
  {{ content }}
</div>
```

### 素材内容显示

```vue
<!-- MaterialBox.vue -->
<style scoped>
.material-box__content > pre {
  /* 文字换行处理 */
  @apply sc-text-wrap-smart;
}
</style>
```

## 注意事项

1. **性能考虑**：对于大量文本内容，建议配合虚拟滚动使用
2. **容器宽度**：确保父容器有明确的宽度限制
3. **字体影响**：不同字体可能会影响换行效果
4. **移动端适配**：在小屏幕设备上测试换行效果

## 相关工具类

- `scrollbar-default`: 默认滚动条样式
- `scrollbar-small`: 小号滚动条样式
- `btn-special`: 特殊按钮样式

## 更新日志

- 2025-01-23: 创建 `sc-text-wrap-smart` shortcut，替代项目中重复的文字换行样式组合
