# 智能轮询系统

## 📋 系统概述

智能轮询系统是作品生成页面的核心功能，提供自动状态更新、进度监控、页面可见性控制和超时管理等完整的轮询解决方案。该系统能够实时监控作品的生成进度，智能调整轮询频率，并在各种场景下保持数据的一致性和准确性。

**当前版本**: v6.0.0 (模块化架构)  
**维护状态**: 架构稳定，功能完善

## 🚀 核心特性

### 智能频率调整

- **视频项目**: 根据进度调整轮询间隔（5 秒/10 秒/3 秒）
- **图文项目**: 更频繁的轮询间隔（4 秒/2 秒）
- **进度策略**: 基于最大进度决定轮询频率

### 自动控制

- 有作品生成中时自动启动轮询
- 所有作品完成时自动停止轮询
- 页面卸载时自动清理资源

### 页面可见性控制

- 页面隐藏时暂停轮询节省资源
- 页面显示时智能恢复轮询
- 根据离开时间决定是否更新数据

### 超时管理

- 5 分钟自动超时停止
- 超时状态跟踪
- 重启时重置计时器

## 🔧 快速使用

### 基本用法

```typescript
import { useWorkPolling } from '@/views/EditProjectView/composables/useWorkPolling';

const { startPolling, stopPolling, isPolling } = useWorkPolling({
  workList: cachedItems,
  projectId,
  projectType,
  eventHandlers: {
    onDataUpdated: async event => {
      console.log('数据更新:', event.updatedWorks);
    },
    onProgressChanged: async event => {
      console.log('进度变化:', event.currentProgress);
    },
    onStatusChanged: async event => {
      // 使用辅助函数简化状态检查
      if (isCompletionStatusChange(event)) {
        console.log('作品完成:', event.workId);
      }
      if (isFailureStatusChange(event)) {
        console.log('作品失败:', event.workId);
      }
    },
  },
});
```

### 事件类型

- `progress:changed`: 进度变化事件
- `work:status:changed`: 作品状态变化事件
- `work:completed`: 作品完成事件
- `polling:started`: 轮询开始事件
- `polling:stopped`: 轮询停止事件

## 🏗️ 技术架构

### 模块化结构

```text
useWorkPolling/
├── config/          # 配置管理
├── constants/       # 常量定义
├── types/          # 类型定义
├── core/           # 核心逻辑
└── utils/          # 工具函数
```

### 核心组件

- **EventManager**: 事件管理器
- **StateAnalyzer**: 状态分析器
- **PollingController**: 轮询控制器

## 🧪 测试环境

### 单元测试

- 位置: `src/views/EditProjectView/composables/__tests__/useWorkPolling*.test.ts`
- 完整的测试覆盖
- 事件系统测试
- 状态分析测试

### 运行测试

```bash
# 运行智能轮询相关测试
npm test -- useWorkPolling
```

## 📚 文档导航

### 核心文档

- **[完整技术指南](./技术文档/智能轮询-完整技术指南.md)** - 详细的技术实现和架构设计
- **[完整更新日志](./更新日志/智能轮询-完整更新日志.md)** - 版本历史和变更记录
- **[视频 50%进度检查](./功能特性/智能轮询-视频项目50%进度检查功能.md)** - 特殊功能说明

### 重要文件位置

- **核心实现**: `src/views/EditProjectView/composables/useWorkPolling/`
- **集成使用**: `src/views/EditProjectView/composables/useInfiniteWorkList.ts`
- **单元测试**: `src/views/EditProjectView/composables/__tests__/useWorkPolling*.test.ts`

## 🔍 问题排查

### 常见问题

1. **类型错误**: 检查 TypeScript 类型导入
2. **测试失败**: 运行 `npm test -- useWorkPolling`
3. **功能异常**: 检查事件处理器注册

### 调试工具

- 浏览器控制台日志
- 单元测试验证
- 类型检查: `npm run type-check`

## ⚠️ 重要提醒

**架构稳定**: 当前 v6.0.0 架构经过充分验证，不建议大幅重构。

**扩展优先**: 新功能应在现有模块基础上扩展。

**测试保障**: 任何修改都必须确保所有测试通过。

---

**维护团队**: 前端开发团队  
**最后更新**: 2025-01-15
