/**
 * @fileoverview 智能轮询类型定义入口文件
 * @description 统一导出所有类型定义
 */

// 事件相关类型
export type {
  BaseWorkEvent,
  ProgressChangedEvent,
  StatusChangedEvent,
  DataUpdatedEvent,
  WorkEvent,
  EventHandler,
} from './events';

// 配置相关类型
export type { UseWorkPollingOptions, UseWorkPollingReturn } from './config';

// 轮询相关类型
export type {
  PollingContext,
  StateMap,
  ProgressMap,
  IntervalCalculationParams,
} from './polling';
