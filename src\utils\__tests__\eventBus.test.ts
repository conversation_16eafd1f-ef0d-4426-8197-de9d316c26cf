/**
 * EventBus 测试
 * 验证重构后的事件机制是否正常工作
 */

import { describe, it, expect, vi, beforeEach } from 'vitest';
import {
  eventBus,
  EVENT_NAMES,
  type WorkRemoveRequestData,
  type WorkSaveRequestData,
} from '../eventBus';

describe('EventBus', () => {
  beforeEach(() => {
    // 清除所有事件监听器
    eventBus.clear();
  });

  describe('基础事件功能', () => {
    it('应该能够注册和触发事件', () => {
      const callback = vi.fn();
      eventBus.on('test-event', callback);

      eventBus.emit('test-event', 'test-data');

      expect(callback).toHaveBeenCalledWith('test-data');
    });

    it('应该能够移除事件监听器', () => {
      const callback = vi.fn();
      eventBus.on('test-event', callback);
      eventBus.off('test-event', callback);

      eventBus.emit('test-event', 'test-data');

      expect(callback).not.toHaveBeenCalled();
    });

    it('应该支持一次性事件监听', () => {
      const callback = vi.fn();
      eventBus.once('test-event', callback);

      eventBus.emit('test-event', 'data1');
      eventBus.emit('test-event', 'data2');

      expect(callback).toHaveBeenCalledTimes(1);
      expect(callback).toHaveBeenCalledWith('data1');
    });
  });

  describe('作品操作事件', () => {
    it('应该能够发送和接收作品删除请求事件', () => {
      const callback = vi.fn();
      eventBus.on(EVENT_NAMES.WORK_REMOVE_REQUEST, callback);

      const requestData: WorkRemoveRequestData = { workId: 123 };
      eventBus.emit(EVENT_NAMES.WORK_REMOVE_REQUEST, requestData);

      expect(callback).toHaveBeenCalledWith(requestData);
    });

    it('应该能够发送和接收作品保存请求事件', () => {
      const callback = vi.fn();
      eventBus.on(EVENT_NAMES.WORK_SAVE_REQUEST, callback);

      const requestData: WorkSaveRequestData = {
        workIds: [123, 456],
        isBatchSave: false,
      };
      eventBus.emit(EVENT_NAMES.WORK_SAVE_REQUEST, requestData);

      expect(callback).toHaveBeenCalledWith(requestData);
    });

    it('应该能够发送作品操作成功事件', () => {
      const callback = vi.fn();
      eventBus.on(EVENT_NAMES.WORK_REMOVE_SUCCESS, callback);

      const successData = { workIds: [123], message: '移除成功' };
      eventBus.emit(EVENT_NAMES.WORK_REMOVE_SUCCESS, successData);

      expect(callback).toHaveBeenCalledWith(successData);
    });

    it('应该能够发送作品操作错误事件', () => {
      const callback = vi.fn();
      eventBus.on(EVENT_NAMES.WORK_REMOVE_ERROR, callback);

      const errorData = {
        workIds: [123],
        error: new Error('删除失败'),
        message: '删除失败',
      };
      eventBus.emit(EVENT_NAMES.WORK_REMOVE_ERROR, errorData);

      expect(callback).toHaveBeenCalledWith(errorData);
    });
  });

  describe('错误处理', () => {
    it('应该捕获回调函数中的错误', () => {
      const consoleSpy = vi
        .spyOn(console, 'error')
        .mockImplementation(() => {});
      const errorCallback = vi.fn(() => {
        throw new Error('回调错误');
      });

      eventBus.on('test-event', errorCallback);
      eventBus.emit('test-event', 'test-data');

      expect(consoleSpy).toHaveBeenCalledWith(
        'EventBus error in event "test-event":',
        expect.any(Error),
      );

      consoleSpy.mockRestore();
    });
  });

  describe('事件名称常量', () => {
    it('应该包含所有必需的事件名称', () => {
      expect(EVENT_NAMES.WORK_REMOVE_REQUEST).toBe('workRemoveRequest');
      expect(EVENT_NAMES.WORK_SAVE_REQUEST).toBe('workSaveRequest');
      expect(EVENT_NAMES.WORK_REMOVE_SUCCESS).toBe('workRemoveSuccess');
      expect(EVENT_NAMES.WORK_REMOVE_ERROR).toBe('workRemoveError');
      expect(EVENT_NAMES.WORK_SAVE_SUCCESS).toBe('workSaveSuccess');
      expect(EVENT_NAMES.WORK_SAVE_ERROR).toBe('workSaveError');
    });
  });
});
