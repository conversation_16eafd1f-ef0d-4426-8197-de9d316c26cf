/**
 * @fileoverview 状态变化检查辅助函数
 * @description 提供简化的状态变化检查逻辑，用于 onStatusChanged 事件处理器
 */

import {
  isPollingWork,
  isAnyCompletedWork,
  isAnyFailedWork,
} from '@/constants/workStatus';
import type { StatusChangedEvent } from '../types';

/**
 * 检查是否为作品完成状态变化
 * @param event 状态变化事件
 * @returns 如果是从轮询状态变为完成状态返回 true，否则返回 false
 */
export function isCompletionStatusChange(event: StatusChangedEvent): boolean {
  const { previousStatusInfo, currentStatusInfo } = event;
  return (
    isPollingWork(previousStatusInfo) && isAnyCompletedWork(currentStatusInfo)
  );
}

/**
 * 检查是否为作品失败状态变化
 * @param event 状态变化事件
 * @returns 如果是从轮询状态变为失败状态返回 true，否则返回 false
 */
export function isFailureStatusChange(event: StatusChangedEvent): boolean {
  const { previousStatusInfo, currentStatusInfo } = event;
  return (
    isPollingWork(previousStatusInfo) && isAnyFailedWork(currentStatusInfo)
  );
}

/**
 * 检查是否为重要的状态变化（完成或失败）
 * @param event 状态变化事件
 * @returns 如果是重要的状态变化返回 true，否则返回 false
 */
export function isCriticalStatusChange(event: StatusChangedEvent): boolean {
  return isCompletionStatusChange(event) || isFailureStatusChange(event);
}

/**
 * 获取状态变化的类型描述
 * @param event 状态变化事件
 * @returns 状态变化类型的描述字符串
 */
export function getStatusChangeType(event: StatusChangedEvent): string {
  if (isCompletionStatusChange(event)) {
    return '作品完成';
  }
  if (isFailureStatusChange(event)) {
    return '作品失败';
  }
  return '状态变化';
}

/**
 * 检查状态变化事件的有效性
 * @param event 状态变化事件
 * @returns 如果事件有效返回 true，否则返回 false
 */
export function isValidStatusChangeEvent(event: StatusChangedEvent): boolean {
  return !!(
    event &&
    event.workId &&
    event.previousStatusInfo &&
    event.currentStatusInfo
  );
}
