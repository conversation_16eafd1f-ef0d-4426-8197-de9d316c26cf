import { VideoWorkItem } from '@/types';
import { ref } from 'vue';
import { VIDEO_EDITOR_CLOSE_STATUS } from '../constants';
import { checkWorkRegenerateInfo } from '@/api/VideoEditor/save';
import { throttle } from 'lodash-es';

/** 原始数据 */
export const originWorkInfo = ref<VideoWorkItem>();
/** 本次修改数据 */
export const workInfo = ref<VideoWorkItem>();
/** 接口加载中 */
export const loading = ref(false);
/** 关闭抽屉时，作品的状态 */
export const closeStatus = ref<
  (typeof VIDEO_EDITOR_CLOSE_STATUS)[keyof typeof VIDEO_EDITOR_CLOSE_STATUS]
>(VIDEO_EDITOR_CLOSE_STATUS.UNMODIFIED);
/** 重新生成所需的点数 */
export const consumePoint = ref(0);
/** 是否需要重新生成 */
export const isNeedMergeVideo = ref<boolean>(false);
/** 更新重新生成所需的点数 */
export const updateConsumePoint = throttle(async () => {
  if (workInfo.value) {
    const [err, res] = await checkWorkRegenerateInfo(workInfo.value);
    if (err) {
      return;
    }
    const { pointCnt, mergeVideo } = res.data;
    consumePoint.value = pointCnt;
    isNeedMergeVideo.value = mergeVideo;
  }
}, 2000);
/** 配音是否失效 */
export const isDubbingInvalid = ref<boolean>(false);
/** 背景音乐是否失效 */
export const isBgmInvalid = ref<boolean>(false);

let isUpdating = false;
let pendingPromise: Promise<void> | null = null;
let lastTimeContent: string | null = null;
/** 立即更新所需的点数 */
export const updateConsumePointNow = async () => {
  // 如果正在更新，则等待上一次更新完成后再返回
  if (isUpdating) {
    // 如果有挂起的Promise，则等待其完成
    if (pendingPromise) {
      await pendingPromise;
    }
    return;
  }
  if (lastTimeContent === JSON.stringify(workInfo.value)) return;
  isUpdating = true;
  pendingPromise = (async () => {
    if (workInfo.value) {
      try {
        const [err, res] = await checkWorkRegenerateInfo(workInfo.value);
        if (!err) {
          const { pointCnt, mergeVideo } = res.data;
          consumePoint.value = pointCnt;
          isNeedMergeVideo.value = mergeVideo;
          lastTimeContent = JSON.stringify(workInfo.value);
        }
      } finally {
        isUpdating = false;
        pendingPromise = null;
      }
    } else {
      isUpdating = false;
      pendingPromise = null;
    }
  })();
  await pendingPromise;
};
