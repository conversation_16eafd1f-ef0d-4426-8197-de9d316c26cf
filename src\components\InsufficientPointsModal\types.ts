/**
 * @description 点数不足弹窗组件的类型定义
 */

/**
 * 点数不足弹窗组件的Props类型
 */
export interface InsufficientPointsModalProps {
  /** 弹窗显示状态 */
  visible: boolean;
}

/**
 * 点数不足弹窗组件的事件类型
 */
export interface InsufficientPointsModalEmits {
  /** 更新弹窗显示状态 */
  'update:visible': (visible: boolean) => void;
  /** 点击充值按钮 */
  'recharge': () => void;
  /** 点击取消按钮 */
  'cancel': () => void;
}

/**
 * 点数不足弹窗组件的配置选项
 */
export interface InsufficientPointsModalOptions {
  /** 弹窗宽度，默认400px */
  width?: number;
  /** 是否可以通过点击遮罩关闭，默认false */
  maskClosable?: boolean;
  /** 是否显示关闭按钮，默认false */
  closable?: boolean;
  /** 自定义提示文本 */
  message?: string;
  /** 充值按钮文本，默认"去充值" */
  rechargeText?: string;
  /** 取消按钮文本，默认"取消" */
  cancelText?: string;
}
