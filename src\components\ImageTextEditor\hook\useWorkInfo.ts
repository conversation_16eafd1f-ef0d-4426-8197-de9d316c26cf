import { ImageWorkItem } from '@/types';
import { ref, computed } from 'vue';
import { IMAGE_TEXT_EDITOR_CLOSE_STATUS } from '../constants';
import { eventBus, EVENT_NAMES } from '@/utils/eventBus';

/** 原始数据 */
export const originWorkInfo = ref<ImageWorkItem | undefined>(undefined);
/** 本次修改数据 */
export const workInfo = ref<ImageWorkItem | undefined>(undefined);
/** 接口加载中 */
export const loading = ref(false);
/** 关闭抽屉时，作品的状态 */
export const closeStatus = ref<
  (typeof IMAGE_TEXT_EDITOR_CLOSE_STATUS)[keyof typeof IMAGE_TEXT_EDITOR_CLOSE_STATUS]
>(IMAGE_TEXT_EDITOR_CLOSE_STATUS.UNMODIFIED);

export const globalPreviewImageBoxSize = ref<{
  width: string;
  height: string;
}>({
  width: '',
  height: '',
});
export const globalTextStickerItemSize = ref<{
  width: number;
  height: number;
}>({
  width: 0,
  height: 0,
});
export const globalImageStickerItemSize = ref<{
  width: number;
  height: number;
}>({
  width: 0,
  height: 0,
});

// 拼图布局到拼图数量的映射
export const LAYOUT_TO_IMAGE_COUNT: Record<number, number> = {
  1: 1, // 单图
  2: 2, // 双图
  3: 4, // 四宫格
  4: 6, // 六宫格
};

// 图片比例type到图片比例的映射
export const RATIO_TO_SCALE: Record<number, number> = {
  1: 0.75, // 4:3
  2: 0.5625, // 16:9
  3: 1, // 1:1
};

/** 当前选中的图片下标 */
export const selectedSettingIndex = ref(0);

/** 当前选中的 setting 数据 */
export const currentSetting = computed(() => {
  return workInfo.value?.setting.graphicList?.[selectedSettingIndex.value];
});

/** 当前选中的 setting 数据是否封面图数据 */
export const isFirstImgSetting = computed(() => {
  return selectedSettingIndex.value == 0;
});

/** 判断是否单图模式 */
export const isSingleImage = computed(
  () => currentSetting.value?.puzzleType === 1,
);

/**
 * 根据puzzleType处理图片列表，保证图片数量符合布局要求
 */
export const puzzleImages = computed(() => {
  const puzzleStyle = currentSetting.value?.puzzleStyle || [];
  const type = currentSetting.value?.puzzleType ?? 1;

  const requiredCount = LAYOUT_TO_IMAGE_COUNT[type] || 1;

  if (puzzleStyle.length === requiredCount) {
    // 如果长度刚好匹配，直接返回
    return puzzleStyle;
  } else if (puzzleStyle.length > requiredCount) {
    // 如果原数组长度大于所需长度，返回截断后的数组(不修改原数组)
    return puzzleStyle.slice(0, requiredCount);
  } else if (puzzleStyle.length > 0) {
    // 如果原数组长度小于所需长度且不为空，用 puzzleStyle 的第一项填充到所需长度
    const result = [...puzzleStyle];
    const firstItem = {
      resId: puzzleStyle[0].resId,
      type: puzzleStyle[0].type,
      isNewCreate: true,
    };
    while (result.length < requiredCount) {
      // 深拷贝 firstItem，确保后续初始化更新,不会联动改到其他拼图的字段
      result.push({ ...firstItem });
    }
    return result;
  }

  // 空数组情况，返回空数组
  return [];
});

export const currentSpace = ref<{
  selectedIndex: number;
  h: number | null;
  v: number | null;
  renderW: number | null;
  renderH: number | null;
  originW: number | null;
}>({
  selectedIndex: 0,
  h: null,
  v: null,
  renderW: null,
  renderH: null,
  originW: null,
});

/**
 * selectedIndex 到初始化的原图宽度的映射
 * key: selectedIndex
 * value: 对应的原图宽度
 */
export const selectedIndexToOriginWMap = new Map<number, number>();
/**
 * selectedIndex 到最新的原图宽度的映射
 * key: selectedIndex
 * value: 对应的原图宽度
 */
export const selectedIndexToNewOriginWMap = new Map<number, number>();

/**
 * 设置当前编辑图片的留白距离
 * @param selectedIndex 侧边栏选中下标
 * @param v 垂直留白距离
 * @param h 水平留白距离
 * @param renderW 图片渲染后的宽度
 * @param renderH 图片渲染后的高度
 * @param originW 图片实际宽度
 */
export const setCurrentSpace = (
  selectedIndex: number,
  h: number,
  v: number,
  renderW: number,
  renderH: number,
  originW: number,
) => {
  currentSpace.value = {
    selectedIndex: selectedIndex,
    h: h,
    v: v,
    renderW: renderW,
    renderH: renderH,
    originW: originW,
  };

  // 更新 selectedIndex 到原图宽度的映射（如果 key 不存在才更新）
  if (!selectedIndexToOriginWMap.has(selectedIndex)) {
    selectedIndexToOriginWMap.set(selectedIndex, originW);
  }
  selectedIndexToNewOriginWMap.set(selectedIndex, originW);
  assignCurrentSpaceToSticker(selectedSettingIndex.value);
  // emit 成功设置currentSpace事件
  eventBus.emit(EVENT_NAMES.CURRENT_SPACE_CHANGE);
};

/**
 * 将 currentSpace 赋值到当前展示的花字对象
 */
export function assignCurrentSpaceToSticker(idx: number) {
  if (workInfo.value?.setting.graphicList[idx]) {
    workInfo.value.setting.graphicList[idx].space = { ...currentSpace.value };
  }
}
