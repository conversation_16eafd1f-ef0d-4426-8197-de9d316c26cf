/**
 * @description EditProjectView 相关的常量定义
 */

// 步骤索引
export const FIRST_STEP_INDEX = 0;
export const SECOND_STEP_INDEX = 1;

// 项目/媒体类型
export const PROJECT_TYPE_VIDEO = 0;
export const PROJECT_TYPE_IMAGE = 1;

// 字段类型
export const FIELD_TYPE_TEXT = 0; // 文本输入
export const FIELD_TYPE_TAGS = 1; // 标签输入
export const FIELD_TYPE_SELECT = 2; // 下拉选择

// 默认配置
export const DEFAULT_RES_FORM_MAX_LENGTH = 5; // 资源表单最大长度

// 交互延迟配置
export const DEBOUNCE_DELAY = 300; // 防抖延迟时间（毫秒）
export const LOADING_DELAY = 300; // 加载状态延迟时间（毫秒）

// 作品状态常量 - 从统一的常量文件导入，确保唯一数据源
export {
  WORK_STATUS,
  POLLING_STATUSES,
  WORK_STATUS_NAMES,
} from '@/constants/workStatus';
export type { WorkStatusType } from '@/constants/workStatus';

// 查看模式常量
export const VIEW_MODE = {
  /** 编辑器弹窗 */
  EDIT: 1,
  /** 预览 */
  PRE_VIEW: 2,
} as const;
