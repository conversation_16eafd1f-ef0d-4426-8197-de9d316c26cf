# 日志系统URL参数控制功能

## 功能概述

日志系统现在支持通过URL查询参数来控制日志的开启和关闭，这为调试和生产环境提供了更灵活的日志控制方式。

## 使用方法

### URL参数控制

在浏览器地址栏中添加查询参数：

- **启用所有日志**: `?log=1`
  ```
  https://your-domain.com/your-page?log=1
  ```

- **禁用所有日志**: `?log=0`
  ```
  https://your-domain.com/your-page?log=0
  ```

### 优先级说明

日志配置的优先级从高到低为：

1. **URL查询参数** (最高优先级)
   - `?log=1` - 启用所有日志
   - `?log=0` - 禁用所有日志

2. **localStorage用户设置**
   - 通过控制台命令设置的配置

3. **默认配置** (最低优先级)
   - 开发环境：启用所有日志
   - 生产环境：只启用警告和错误日志

## 功能特点

### 1. 即时生效
- URL参数修改后刷新页面即可生效
- 无需清除缓存或重新部署

### 2. 覆盖所有设置
- URL参数会覆盖localStorage中的用户设置
- URL参数会覆盖默认的环境配置

### 3. 清晰的状态提示
- 控制台会显示当前使用的日志控制方式
- 不同的控制方式使用不同颜色的提示信息

## 控制台提示信息

### 通过URL参数控制时
```
🎯 日志系统已初始化 (通过URL参数控制)

URL参数 log=1 ✅ 启用所有日志
在控制台中输入 loggerControl.help() 查看可用命令
当前配置：
```

### 使用默认配置时
```
🎯 日志系统已初始化

💡 提示：可通过URL参数控制日志开关
  • ?log=1 启用所有日志
  • ?log=0 禁用所有日志
在控制台中输入 loggerControl.help() 查看可用命令
当前配置：
```

## 使用场景

### 1. 生产环境调试
```
# 在生产环境临时启用日志进行问题排查
https://production-site.com/problematic-page?log=1
```

### 2. 性能测试
```
# 在性能测试时禁用所有日志减少干扰
https://test-site.com/performance-test?log=0
```

### 3. 用户问题复现
```
# 让用户在特定URL下操作以收集详细日志
https://your-site.com/user-issue-page?log=1
```

### 4. 演示环境
```
# 在演示时关闭日志保持控制台整洁
https://demo-site.com/presentation?log=0
```

## 技术实现

### 配置检测逻辑
```typescript
const getLogConfig = () => {
  // 首先检查URL查询参数
  const urlParams = new URLSearchParams(window.location.search);
  const logParam = urlParams.get('log');
  
  if (logParam !== null) {
    const isLogEnabled = logParam === '1';
    return isLogEnabled ? enableAllConfig : disableAllConfig;
  }
  
  // 其次检查localStorage用户设置
  // 最后使用默认配置
};
```

### 状态配置
- **log=1**: 启用所有日志类型，日志级别设为DEBUG
- **log=0**: 禁用所有日志类型，日志级别设为OFF

## 注意事项

1. **参数格式**: 只接受 `log=1` 和 `log=0`，其他值将被忽略
2. **页面刷新**: 修改URL参数后需要刷新页面才能生效
3. **优先级**: URL参数会覆盖所有其他配置，包括控制台命令设置
4. **兼容性**: 支持所有现代浏览器，使用标准的URLSearchParams API

## 更新日志

- **2025-06-20**: 新增URL参数控制功能
  - 支持 `?log=1` 启用所有日志
  - 支持 `?log=0` 禁用所有日志
  - 更新控制台帮助信息
  - 添加状态提示功能
