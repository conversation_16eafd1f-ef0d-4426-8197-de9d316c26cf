<template>
  <div class="video-play-toolbar">
    <!-- 素材库插件内部选中素材绑定的点击事件用的mousedown事件，这里对应使用mousedown事件，阻止事件冒泡，否则点击时会触发选中素材 -->
    <img
      class="opacity-90 hover:opacity-100"
      :style="{
        width: `${btnSize}px`,
        height: `${btnSize}px`,
      }"
      src="@/assets/Material/bofang.svg"
      title="点击播放"
      @mousedown.stop="previewPlay"
    />
  </div>
</template>

<script setup lang="ts">
import { defineProps } from 'vue';
import { showVideoViewer } from '@/components/comm/VideoViewer/index';
import { getMaterialFullUrl } from '@/components/MaterialBasicUpload/utils/index';

const props = defineProps({
  file: {
    type: Object,
    required: true,
  },
  btnSize: {
    type: Number,
    default: 16,
  },
});

const previewPlay = () => {
  const v = props.file;
  const videoSrc = getMaterialFullUrl(v.resId, v.fileType);
  showVideoViewer({
    videoSrc,
  });
};
</script>

<style scoped lang="scss">
.video-play-toolbar {
  @apply flex h-full items-center justify-center;
}
</style>
