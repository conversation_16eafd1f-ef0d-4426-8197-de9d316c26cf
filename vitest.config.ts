/// <reference types="vitest" />
import { defineConfig } from 'vitest/config';
import vue2 from '@vitejs/plugin-vue2';
import path from 'path';
import UnoCSS from 'unocss/vite';

export default defineConfig({
  plugins: [vue2(), UnoCSS()],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, 'src'),
    },
  },
  test: {
    globals: true,
    environment: 'jsdom',
    setupFiles: ['./src/test/setup.ts'],
    include: ['src/**/*.{test,spec}.{js,mjs,cjs,ts,mts,cts,jsx,tsx}'],
    exclude: ['node_modules', 'dist', '.idea', '.git', '.cache', 'src/mock/**'],
    coverage: {
      provider: 'v8',
      reporter: ['text', 'json', 'html'],
      exclude: [
        'node_modules/',
        'src/test/',
        'src/mock/',
        '**/*.d.ts',
        '**/*.config.*',
        '**/types/**',
        'src/main.ts',
        'src/vite-env.d.ts',
      ],
    },
    // 浏览器测试配置（可选）
    browser: {
      enabled: false, // 默认关闭，可通过 --browser 启用
      name: 'chrome',
      headless: true,
      provider: 'webdriverio',
    },
    // 测试超时配置
    testTimeout: 10000,
    hookTimeout: 10000,
    // 报告器配置
    reporters: ['verbose', 'json', 'html'],
    outputFile: {
      json: './test-results/results.json',
      html: './test-results/index.html',
    },
    // 模拟配置
    deps: {
      inline: [
        'element-ui',
        '@fk/fa-component-cus',
        '@fk/faicomponent',
        '@fk/faiupload',
      ],
    },
  },
});
