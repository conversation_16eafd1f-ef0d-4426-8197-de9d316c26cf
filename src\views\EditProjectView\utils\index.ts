import {
  PROJECT_TYPE_VIDEO,
  PROJECT_TYPE_IMAGE,
  DEFAULT_RES_FORM_MAX_LENGTH,
} from '@/views/EditProjectView/constants';
import {
  FieldType,
  FirstStepComponentShape,
  EditProjectViewData,
  InputFormItem,
  FormItemInternal,
  ResFormItem,
  ProjectType,
} from '@/views/EditProjectView/types/index';
import { FormRule, MEDIA_TYPES } from '@/views/EditProjectView/types/index';

// ================ Helper Functions ================

/**
 * 检查表单项是否必填
 * @param rules - 表单验证规则数组
 * @returns 是否必填
 */
function isFormItemRequired(rules?: FormRule[]): boolean {
  return rules?.some((rule: FormRule) => rule.required) || false;
}

/**
 * 获取表单项的默认值
 * @param value - 当前值
 * @returns 处理后的值（空值返回空字符串）
 */
function getFormItemDefaultValue(value: unknown): unknown {
  return value || '';
}

// ================ Main Functions ================

// 为 formatFirstStepDataForSave 的返回值定义一个接口
interface FormattedFirstStepData {
  name: string;
  inputForm: InputFormItem[];
  resForm: ResFormItem[];
  bgMusic: EditProjectViewData['bgMusic'];
  voice: EditProjectViewData['voice'];
  script?: {
    content: string;
    title?: string;
  };
  [key: string]: unknown;
}

/**
 * 创建基础表单项数据 (InputFormItem)
 * @param item - 表单项配置 (FormItemInternal)
 * @param value - 表单项值
 * @param mapTypeFunction - 类型映射函数 (将 item.type 转换为 FieldType)
 * @returns 格式化后的 InputFormItem 数据
 */
export function createInputFormItem(
  item: FormItemInternal,
  value: unknown,
  mapTypeFunction?: (type: string | number) => FieldType,
): InputFormItem {
  const mappedFieldType = mapTypeFunction
    ? mapTypeFunction(item.type)
    : FieldType.TEXT;
  const required =
    typeof item.required === 'boolean'
      ? item.required
      : isFormItemRequired(item.rules);
  return {
    variable: item.prop,
    label: item.label,
    required: required,
    filedType: mappedFieldType,
    placeholder: item.placeholder || '',
    options: item.options || [],
    openAiTip: item.openAiTip || false,
    prompt: item.prompt || [],
    value: getFormItemDefaultValue(value) as InputFormItem['value'],
  };
}

/**
 * 创建资源表单项数据 (ResFormItem)
 * @param item - 表单项配置 (FormItemInternal)
 * @param value - 表单项值 (unknown, expected to be file list)
 * @param index - 表单项索引 (for fallback ID)
 * @returns 格式化后的 ResFormItem 数据
 */
function createResFormItem(
  item: FormItemInternal,
  value: unknown,
  index: number,
): ResFormItem {
  const idSource = item.id;
  let id: number;
  if (typeof idSource === 'number') {
    id = idSource;
  } else if (typeof idSource === 'string' && idSource.trim() !== '') {
    const parsedId = parseInt(idSource, 10);
    id = isNaN(parsedId) ? index + 1 : parsedId;
  } else {
    id = index + 1;
  }

  const itemType: ProjectType =
    item.mediaType === MEDIA_TYPES.VIDEO
      ? PROJECT_TYPE_VIDEO
      : PROJECT_TYPE_IMAGE;
  const required =
    typeof item.required === 'boolean'
      ? item.required
      : isFormItemRequired(item.rules);

  const minLength = isFormItemRequired(item.rules) ? 1 : 0;

  const maxLength =
    typeof item.maxLength === 'number'
      ? item.maxLength
      : DEFAULT_RES_FORM_MAX_LENGTH;

  return {
    id: id,
    type: itemType,
    variable: item.prop,
    label: item.label,
    required: required,
    value: (value || []) as ResFormItem['value'],
    minLength: minLength,
    maxLength: maxLength,
    description: item.description || '',
    openModifyMouth: item.openModifyMouth || false,
    hasModifyMouth: item.hasModifyMouth,
    resId: item.resId,
    resType: item.resType,
    coverId: item.coverId,
    coverType: item.coverType,
  };
}

/**
 * 获取并格式化用于保存的表单数据
 * @description 将表单组件数据格式化为API请求所需格式
 * @param firstStepComponent - 第一步组件实例
 * @returns 格式化后的表单数据 (不含顶层id和type)，失败返回null
 */
export function formatFirstStepDataForSave(
  firstStepComponent: FirstStepComponentShape | null,
): FormattedFirstStepData | null {
  if (!firstStepComponent?.formItems || !firstStepComponent?.fileFormItems) {
    console.error('组件或表单项无效');
    return null;
  }
  const allFormData = firstStepComponent.getAllFormData!();
  if (!allFormData) {
    console.error('获取表单数据失败');
    return null;
  }

  const { basicFormData, filesFormData, bgMusic, voice } = allFormData;

  const inputForm = firstStepComponent.formItems.map(item =>
    createInputFormItem(
      item as FormItemInternal,
      basicFormData[item.prop],
      firstStepComponent.mapComponentTypeToFieldType,
    ),
  );

  const resForm = firstStepComponent.fileFormItems.map((item, index: number) =>
    createResFormItem(
      item as FormItemInternal,
      filesFormData[item.prop],
      index,
    ),
  );

  const formData: FormattedFirstStepData = {
    name: (basicFormData.shopName as string) || '',
    inputForm: inputForm,
    resForm: resForm,
    bgMusic: bgMusic,
    voice: voice,
  };

  // 添加示例脚本（如果存在）
  if (
    firstStepComponent.hasExampleScript &&
    firstStepComponent.exampleScriptContent
  ) {
    formData.script = {
      content: firstStepComponent.exampleScriptContent,
    };
  }
  return formData;
}

// ================ Content Formatter ================

export {
  formatImageWorkContent,
  formatImageWorkContentFromItem,
} from './contentFormatter';
