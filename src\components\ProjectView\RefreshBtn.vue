<template>
  <fa-button class="refresh-btn" @click="handleRefresh">
    <Icon type="huanyihuan" class="refresh-icon" />刷新
  </fa-button>
</template>

<script setup lang="ts">
import { defineEmits } from 'vue';

/**
 * SearchInput 组件 Emits
 * @event search 搜索事件，参数为输入内容
 */
const emit = defineEmits<{
  (e: 'refresh'): void;
}>();

const handleRefresh = (event: Event) => {
  emit('refresh');
  // 按钮被点击后默认会获得焦点，在这里需要点击事件后主动移除焦点
  (event.target as HTMLElement).blur();
};
</script>

<style lang="scss" scoped>
.refresh-btn {
  @apply flex items-center text-text text-[14px] border-[edge];
  .refresh-icon {
    @apply size-[20px] mr-[4px] text-assist;
    transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
  }
  &:hover,
  &:focus {
    @apply text-primary;
    .refresh-icon {
      @apply text-primary;
    }
  }
}
</style>
