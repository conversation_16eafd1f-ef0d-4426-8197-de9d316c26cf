# EditProjectView 开发者账号状态检查跳过功能

## 功能概述

为了方便开发者调试和测试，在 EditProjectView 组件中实现了开发者账号特殊处理功能。当用户的 cookie 中包含特定标识 `9875837` 时，系统会自动跳过项目状态检查，允许开发者在任何项目状态下进行编辑操作。

## 实现原理

### 检测逻辑

```typescript
// 检查是否为开发者账号（cookie中包含9875837时跳过状态检查）
const isDeveloperAccount = document.cookie.includes('9875837');
const shouldCheckProjectStatus = isEditMode.value && !isDeveloperAccount;
```

### 状态检查控制

```typescript
// 调用API获取数据，根据账号类型决定是否启用项目状态检查
const [err, res] = await getEditProjectViewData(
  templateId,
  routeProjectId ? Number(routeProjectId) : undefined,
  { checkProjectStatus: shouldCheckProjectStatus }, // 开发者账号跳过状态检查
);
```

## 功能特点

### ✅ 开发者账号特权

- **跳过状态检查**：不受项目状态限制，可以在任何状态下编辑项目
- **无重定向干扰**：即使项目处于生成中、待保存等状态，也不会被强制重定向
- **完整功能访问**：可以正常使用所有编辑功能，不受状态限制

### ✅ 普通账号保护

- **正常状态检查**：继续执行项目状态检查逻辑
- **安全重定向**：在不合适的状态下会被重定向到正确页面
- **用户体验保障**：确保普通用户的正常使用流程

## 账号类型判断

| 账号类型 | Cookie 条件 | 状态检查 | 重定向行为 |
|----------|-------------|----------|------------|
| **开发者账号** | 包含 `9875837` | ❌ 跳过 | ❌ 不重定向 |
| **普通账号** | 不包含 `9875837` | ✅ 正常检查 | ✅ 正常重定向 |

## 使用场景

### 开发者调试场景

1. **测试不同状态的项目**：可以直接访问处于生成中、待保存等状态的项目
2. **功能开发调试**：不受状态限制，可以专注于功能开发和测试
3. **问题排查**：可以查看和修改任何状态的项目，便于问题定位

### 生产环境保护

1. **普通用户保护**：确保普通用户不会在错误的状态下操作项目
2. **数据安全**：防止用户在项目生成过程中进行编辑操作
3. **流程规范**：维护正常的项目编辑流程

## 技术实现

### 实现位置

**文件路径**: `src/views/EditProjectView/composables/useProjectData.ts`

### 核心代码

```typescript
const fetchFormData = async (): Promise<void> => {
  // ... 其他逻辑 ...

  // 检查是否为开发者账号（cookie中包含9875837时跳过状态检查）
  const isDeveloperAccount = document.cookie.includes('9875837');
  const shouldCheckProjectStatus = isEditMode.value && !isDeveloperAccount;

  console.log('项目状态检查配置:', {
    编辑模式: isEditMode.value,
    开发者账号: isDeveloperAccount,
    启用状态检查: shouldCheckProjectStatus,
    项目ID: routeProjectId,
  });

  // 调用API获取数据，根据账号类型决定是否启用项目状态检查
  const [err, res] = await getEditProjectViewData(
    templateId,
    routeProjectId ? Number(routeProjectId) : undefined,
    { checkProjectStatus: shouldCheckProjectStatus }, // 开发者账号跳过状态检查
  );

  // ... 其他逻辑 ...
};
```

### 日志输出

系统会输出详细的配置信息，便于调试：

```javascript
项目状态检查配置: {
  编辑模式: true,
  开发者账号: true,  // 或 false
  启用状态检查: false, // 或 true
  项目ID: '123'
}
```

## 测试验证

### 测试用例覆盖

1. ✅ **开发者账号（cookie包含9875837）应该跳过状态检查**
2. ✅ **普通账号（cookie不包含9875837）应该正常进行状态检查**
3. ✅ **开发者账号在生成中状态也不会被重定向**

### 测试结果

```text
✓ 开发者账号特殊处理 (3)
  ✓ 开发者账号（cookie包含9875837）应该跳过状态检查
  ✓ 普通账号（cookie不包含9875837）应该正常进行状态检查
  ✓ 开发者账号在生成中状态也不会被重定向
```

## 安全考虑

### Cookie 检测方式

- **简单有效**：使用 `document.cookie.includes('9875837')` 进行检测
- **不易误触**：特定数字标识，不容易与其他 cookie 值冲突
- **易于控制**：开发者可以通过设置/删除 cookie 来控制功能开关

### 权限控制

- **仅影响状态检查**：不影响其他安全验证和权限控制
- **透明日志**：所有操作都有详细日志记录，便于审计
- **可追踪性**：可以通过日志追踪开发者账号的操作行为

## 使用方法

### 开启开发者模式

1. 在浏览器开发者工具中设置 cookie：
   ```javascript
   document.cookie = "dev_flag=9875837; path=/";
   ```

2. 或者在应用中添加包含 `9875837` 的任何 cookie

3. 刷新页面，系统会自动检测并跳过状态检查

### 关闭开发者模式

1. 清除包含 `9875837` 的 cookie
2. 刷新页面，恢复正常的状态检查

## 注意事项

1. **仅用于开发调试**：此功能主要用于开发和调试，不建议在生产环境中长期使用
2. **Cookie 依赖**：功能依赖于浏览器 cookie，确保 cookie 功能正常
3. **日志监控**：建议监控开发者账号的使用情况，确保功能使用合理
4. **安全意识**：虽然跳过了状态检查，但仍需注意数据安全和操作规范

## 相关文件

- `src/views/EditProjectView/composables/useProjectData.ts` - 主要实现文件
- `src/views/EditProjectView/composables/__tests__/useProjectData.projectStatus.test.ts` - 测试文件
- `src/api/EditProjectView/index.ts` - API层状态检查逻辑
