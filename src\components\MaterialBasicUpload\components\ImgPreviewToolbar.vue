<template>
  <!-- 素材库插件内部选中素材绑定的点击事件用的mousedown事件，这里对应使用mousedown事件，阻止事件冒泡，否则点击时会触发选中素材 -->
  <div class="img-preview-toolbar" @mousedown.stop="previewImg">
    <div class="flex justify-center items-center h-full">
      <fa-designer-icon class="preview-icon" type="preview"></fa-designer-icon>
    </div>
  </div>
</template>

<script setup lang="ts">
import { defineProps } from 'vue';
import { showImageViewer } from '@/components/comm/ImageViewer/index';
import { getMaterialFullUrl } from '@/components/MaterialBasicUpload/utils/index';
import { FILE_TYPES } from '@/constants/fileType';

const props = defineProps({
  file: {
    type: Object,
    required: true,
  },
});

const previewImg = () => {
  const v = props.file;
  const imgSrc = getMaterialFullUrl(v.resId, FILE_TYPES.WEBP || v.fileType);
  showImageViewer({
    imgList: [imgSrc],
  });
};
</script>

<style scoped lang="scss">
.img-preview-toolbar {
  @apply absolute left-[5px] right-[5px] bottom-[5px] h-[24px] z-1 bg-[rgba(0,0,0,0.6)];

  .preview-icon {
    @apply items-center color-[#fff];
  }
  &:hover {
    @apply bg-[rgba(0,0,0,1)];
  }
}
</style>
