/**
 * 素材管理器上传临时值存取工具
 * 处理上传过程中的临时文件管理和进度更新
 */
import { MaterialManageInfo } from '@/components/MaterialManager/types/index.ts';
import { getFileUniqueId } from '@/components/MaterialBasicUpload/utils/uploadTempStorage';

/**
 * 创建临时文件数据（素材库用）
 * @param file 文件对象
 * @param currentFolder 当前文件夹ID
 * @returns 临时文件数据
 */
export const createTempMaterialFileData = (
  file: File,
  currentFolder: number,
): MaterialManageInfo => {
  const fileType = -1;
  return {
    id: Math.floor(Math.random() * 100000), // 新生成的图片/视频无法拿到id值, 随机生成用于保证key唯一
    name: file.name,
    type: fileType,
    fileType: fileType,
    fileSize: file.size,
    createTime: new Date().getTime(),
    resId: getFileUniqueId(file),
    extra: {},
    parentId: currentFolder,
    percent: 0,
    children: [],
    select: false,
  };
};
