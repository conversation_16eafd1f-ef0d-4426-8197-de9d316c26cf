<template>
  <div class="test-component">
    <h1 class="test-component__title">{{ title }}</h1>
    <p class="test-component__content">{{ content }}</p>
    <button
      class="test-component__button"
      @click="handleClick"
      :disabled="loading"
    >
      {{ loading ? '加载中...' : '点击我' }}
    </button>
    <div v-if="showMessage" class="test-component__message">
      {{ message }}
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent } from 'vue';

export default defineComponent({
  name: 'TestComponent',
  props: {
    title: {
      type: String,
      default: '默认标题',
    },
    content: {
      type: String,
      default: '默认内容',
    },
  },
  data() {
    return {
      loading: false,
      showMessage: false,
      message: '',
    };
  },
  methods: {
    async handleClick() {
      this.loading = true;
      this.showMessage = false;

      try {
        // 模拟异步操作
        await new Promise(resolve => setTimeout(resolve, 1000));
        this.message = '操作成功！';
        this.showMessage = true;
        this.$emit('success', { message: this.message });
      } catch (error) {
        this.message = '操作失败！';
        this.showMessage = true;
        this.$emit('error', { error });
      } finally {
        this.loading = false;
      }
    },

    reset() {
      this.loading = false;
      this.showMessage = false;
      this.message = '';
    },
  },
});
</script>

<style scoped>
.test-component {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  min-height: 200px;
  padding: 20px;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.test-component__title {
  font-size: 24px;
  font-weight: bold;
  color: #1f2937;
  margin-bottom: 10px;
}

.test-component__content {
  font-size: 16px;
  color: #6b7280;
  margin-bottom: 20px;
  text-align: center;
}

.test-component__button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 10px 20px;
  min-width: 120px;
  background: #3b82f6;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
}

.test-component__button:hover {
  background: #2563eb;
}

.test-component__button:active {
  background: #1d4ed8;
}

.test-component__button:disabled {
  background: #9ca3af;
  cursor: not-allowed;
}

.test-component__message {
  margin-top: 15px;
  padding: 10px;
  background: #dcfce7;
  border: 1px solid #bbf7d0;
  border-radius: 4px;
  font-size: 14px;
  color: #166534;
}
</style>
