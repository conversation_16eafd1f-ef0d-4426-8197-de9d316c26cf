<template>
  <div class="remain-point-detail" v-if="dataSource.length">
    <fa-table
      size="middle"
      class="remain-point-detail__table"
      :columns="dataComlumns"
      :data-source="dataSource"
      :pagination="false"
      skin-type="form-page"
      width="280px"
    >
    </fa-table>
  </div>
</template>
<script lang="ts" setup>
import { ref } from 'vue';
import { message } from '@fk/faicomponent';
import { getRemainPointList } from '@/api/AcctInfoView/index.ts';
const dataComlumns = [
  {
    title: '剩余创作点数',
    dataIndex: 'restPoint',
    width: '140px',
  },
  {
    title: '生效时间',
    dataIndex: 'effectiveTime',
    width: '140px',
  },
  {
    title: '到期时间',
    dataIndex: 'expiredTime',
    width: '140px',
  },
];

const dataSource = ref<
  {
    restPoint: number;
    expiredTime: string;
  }[]
>([]);

const getData = async () => {
  const [err, res] = await getRemainPointList();
  if (err) {
    message.error(err.message || '获取失败');
    dataSource.value = []; // 清空数据
    return;
  }
  dataSource.value = res.data;
};

getData();
</script>

<style lang="scss" scoped>
.remain-point-detail__table {
  margin-top: 16px;
  ::v-deep {
    .fa-table-tbody {
      background-color: #fff;
      // border-radius: 8px;
    }
  }
  ::v-deep {
    .fa-table-skin__specification--form-page .fa-table {
      border-radius: 8px;
    }
    .fa-table-middle
      > .fa-table-content
      > .fa-table-body
      > table
      > .fa-table-thead
      > tr
      > th {
      padding: 10px 16px;
    }
    .fa-table-middle
      > .fa-table-content
      > .fa-table-body
      > table
      > .fa-table-tbody
      > tr
      > td {
      padding: 12px 16px;
    }
  }
}
</style>
