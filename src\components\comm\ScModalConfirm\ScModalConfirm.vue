<template>
  <fa-modal
    :visible="visible"
    :width="width"
    :closable="false"
    :bodyStyle="{ 'padding-top': 0, 'padding-bottom': 0 }"
    centered
    v-bind="$attrs"
    v-on="$listeners"
    class="fa-modal-confirm"
  >
    <div
      class="fa-modal-confirm__container"
      :style="{ minHeight: minHeight + 'px' }"
    >
      <div class="fa-modal-confirm__content">{{ content }}</div>
    </div>
    <template #footer>
      <div class="fa-modal-confirm__footer">
        <fa-button :type="okType" @click="handleOk">{{ okText }}</fa-button>
        <fa-button class="fa-modal-confirm__cancel" @click="handleCancel">{{
          cancelText
        }}</fa-button>
      </div>
    </template>
  </fa-modal>
</template>

<script setup lang="ts">
import { defineProps, defineEmits } from 'vue';

const props = defineProps({
  visible: Boolean,
  content: { type: String, default: '' },
  okText: { type: String, default: '确定' },
  cancelText: { type: String, default: '取消' },
  okType: { type: String, default: 'danger' }, // 'danger' | 'primary'
  width: { type: [Number, String], default: 400 },
  minHeight: { type: [Number, String], default: 135 },
  onOk: Function,
  onCancel: Function,
});

const emit = defineEmits(['close']);

const handleCancel = () => {
  props.onCancel?.();
  close();
};

const handleOk = () => {
  props.onOk?.();
  close();
};
function close() {
  emit('close');
}
</script>

<style lang="scss" scoped>
.fa-modal-confirm {
  :deep(.fa-modal) {
    .fa-btn {
      @apply min-w-[88px] line-height-[38px];
    }
  }

  .fa-modal-confirm__container {
    @apply flex items-center justify-center;

    .fa-modal-confirm__content {
      @apply text-[14px] color-text text-center;
      word-break: break-word;
    }
  }

  .fa-modal-confirm__cancel {
    @apply ml-[15px] hover:(text-title border-assist);
  }
}
</style>
