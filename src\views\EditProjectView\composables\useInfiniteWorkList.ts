/**
 * @fileoverview 无限滚动作品列表的可复用 Composable
 * @description 提供无限滚动作品列表相关的状态和方法
 *
 * 主要功能：
 * - 无限滚动作品列表管理
 * - 智能轮询机制，实时更新作品状态和进度
 * - 进度实时同步：当轮询更新列表项进度时，自动同步到作品详情数据
 * - 作品详情缓存管理
 * - 统一的作品数据更新接口
 *
 * 进度同步机制：
 * 当智能轮询检测到作品进度变化时，会自动将最新的进度和状态同步到
 * 当前选中作品的详情数据中，确保左侧详情面板显示的进度与右侧列表
 * 中的进度保持实时同步，提升用户体验。
 */

import {
  ref,
  watch,
  computed,
  nextTick,
  onMounted,
  onUnmounted,
  Ref,
  ComputedRef,
} from 'vue';
import { WorkItem, VideoWorkItem, ImageWorkItem } from '@/types';
import * as useWorkList from './useWorkList';
import type { UseWorkListReturn } from './useWorkList';
import {
  useInfiniteScroll,
  type InfiniteLoadingError,
} from './useInfiniteScroll';
import { useWorkPolling } from './useWorkPolling/index';
import type {
  UseWorkPollingReturn,
  ProgressChangedEvent,
  DataUpdatedEvent,
  StatusChangedEvent,
} from './useWorkPolling/types';
import {
  isCompletionStatusChange,
  isFailureStatusChange,
  isValidStatusChangeEvent,
  getStatusChangeType,
} from './useWorkPolling/utils';
import { deleteWork } from '@/api/WorkView';
import { getWorkListWithTransform } from '@/api/EditProjectView/work';
import { message } from '@fk/faicomponent';
import { logger } from '@/utils/logger';
import {
  eventBus,
  EVENT_NAMES,
  type WorkRemoveRequestData,
  type WorkSaveRequestData,
} from '@/utils/eventBus';
import router from '@/router';
import { FIRST_STEP_INDEX } from '@/views/EditProjectView/constants';
import { PROJECT_STATUS_NAME } from '@/constants/project';
import {
  getProjectStatusFromStore,
  shouldRestrictProjectAccess,
} from '@/views/EditProjectView/utils/projectStatus';
import {
  MEDIA_TYPES,
  type MediaType,
} from '@/views/EditProjectView/types/base';
import {
  PROJECT_TYPE_VIDEO,
  PROJECT_TYPE_IMAGE,
} from '@/views/EditProjectView/constants';

// ==================== 常量定义 ====================

/** 数据操作类型枚举 */
enum WorkListOperation {
  /** 合并更新（轮询、保存、编辑后） */
  MERGE = 'merge',
  /** 删除作品 */
  REMOVE = 'remove',
}

// ==================== 辅助函数 ====================

/**
 * 将媒体类型转换为项目类型
 * @param mediaType 媒体类型
 * @returns 项目类型（0: 视频, 1: 图文）
 */
function convertMediaTypeToProjectType(mediaType: MediaType): number {
  // 处理边界情况：确保媒体类型有效
  if (!mediaType || typeof mediaType !== 'string') {
    logger.debug('⚠️ 无效的媒体类型，默认使用视频项目类型', {
      传入类型: mediaType,
      默认类型: '视频',
    });
    return PROJECT_TYPE_VIDEO;
  }

  return mediaType === MEDIA_TYPES.IMAGE
    ? PROJECT_TYPE_IMAGE
    : PROJECT_TYPE_VIDEO;
}

// ==================== 类型定义 ====================

/** 作品更新选项接口 */
interface UpdateWorkItemsOptions<T extends WorkItem = WorkItem> {
  /** 是否清除指定作品的详情缓存 */
  clearDetailCache?: boolean;
  /** 是否完整刷新作品列表（会清空所有缓存并回到第一页） */
  refreshFullList?: boolean;
  /** 操作上下文描述，用于日志记录 */
  context?: string;
  /** 是否重新获取当前选中作品的详情 */
  refreshCurrentDetail?: boolean;
  /** 更新完成后的回调函数 */
  onSuccess?: (updatedWorks?: T[]) => void;
  /** 更新失败后的回调函数 */
  onError?: (error: Error) => void;
  /** 是否跳过空ID列表的检查（用于完整刷新场景） */
  allowEmptyIds?: boolean;
}

export interface UseInfiniteWorkListReturn<T extends WorkItem> {
  /** 当前选中的作品ID */
  currentWorkId: Ref<number>;
  /** 是否正在加载数据 */
  isLoading: Ref<boolean>;
  /** 作品列表数据 */
  workList: Ref<WorkItem[]>;
  /** 显示的作品列表（用于无限滚动） */
  displayedWorkList: Ref<WorkItem[]>;
  /** 当前选中的作品数据（包含实时同步的进度信息） */
  currentWork: Ref<T | undefined>;
  /** 当前作品的进度（仅视频作品有此属性，支持实时同步） */
  currentProgress: ComputedRef<number>;
  /** 获取作品列表数据 */
  fetchWorkList: () => Promise<[Error | null, void | null]>;
  /** 选中的作品ID列表 */
  selectedWorkIds: Ref<number[]>;
  /** 处理作品选择变更事件 */
  handleSelectChange: (ids: number[]) => void;
  /** 分页相关 */
  currentPage: Ref<number>;
  pageSize: Ref<number>;
  totalItems: Ref<number>;
  /** 成功生成的作品数量 */
  sucNum: Ref<number>;
  /** 是否正在加载更多 */
  isLoadingMore: ComputedRef<boolean>;
  /** 是否已加载全部 */
  hasLoadedAll: ComputedRef<boolean>;
  /** 处理滚动到底部事件 */
  handleScrollEnd: () => Promise<void>;
  /** 重试加载 */
  retryLoad: () => Promise<void>;
  /** 处理页码变更事件 */
  onPageChange: (page: number) => Promise<[Error | null, void | null]>;
  /** 是否有加载错误 */
  hasLoadingError: ComputedRef<boolean>;
  /** 加载错误信息 */
  loadingError: ComputedRef<InfiniteLoadingError | null>;
  /** 是否可以重试 */
  canRetryLoading: ComputedRef<boolean>;
  /** 作品详情相关 */
  isLoadingDetail: Ref<boolean>;
  /** 切换到指定作品并获取详情 */
  switchToWork: (workId: number) => Promise<[Error | null, void | null]>;
  /** 第一页加载状态 */
  isFirstPageLoading: ComputedRef<boolean>;
  /** 加载更多状态（非第一页） */
  isLoadingMoreData: ComputedRef<boolean>;
  /** 删除作品方法（通过 provide/inject 提供给子组件） */
  removeWork: (workId: number) => Promise<boolean>;
  /** 统一的作品更新方法 */
  updateWorkItems: (
    workIds: number[],
    options?: {
      clearDetailCache?: boolean;
      refreshFullList?: boolean;
      context?: string;
      refreshCurrentDetail?: boolean;
      onSuccess?: (updatedWorks?: T[]) => void;
      onError?: (error: Error) => void;
      allowEmptyIds?: boolean;
    },
  ) => Promise<void>;
  /** 刷新作品列表（用于批量保存后的完整刷新） */
  refreshWorkList: () => Promise<[Error | null, void | null]>;
  /** 轮询相关功能（包含进度实时同步机制） */
  polling: UseWorkPollingReturn;
  /** 保存后更新作品列表数据（向后兼容） */
  handleSaveUpdate: (workIds: number[], isBatchSave?: boolean) => Promise<void>;
  /** 编辑后更新作品数据（向后兼容） */
  handleEditUpdate: (workId: number) => Promise<void>;
  /** 生成完成后更新作品数据（向后兼容） */
  handleGenerateUpdate: (workId: number) => Promise<void>;
}

/**
 * 视频作品列表管理（带无限滚动）
 */
export function useInfiniteVideoWorkList(
  projectId: number,
): UseInfiniteWorkListReturn<VideoWorkItem> {
  return useInfiniteWorkListImpl<VideoWorkItem>(projectId, MEDIA_TYPES.VIDEO);
}

/**
 * 图文作品列表管理（带无限滚动）
 */
export function useInfiniteImageWorkList(
  projectId: number,
): UseInfiniteWorkListReturn<ImageWorkItem> {
  return useInfiniteWorkListImpl<ImageWorkItem>(projectId, MEDIA_TYPES.IMAGE);
}

/**
 * 工厂函数：根据作品类型获取对应的作品列表管理函数
 * @template T 作品类型，继承自 WorkItem
 * @param type 作品类型标识符
 * @returns 对应类型的作品列表管理函数
 */
function getWorkListComposable<T extends WorkItem>(
  type: MediaType,
): (projectId: number) => UseWorkListReturn<T> {
  // 根据类型返回对应的 composable 函数
  // 这里使用类型断言是安全的，因为我们确保了类型的一致性
  return type === MEDIA_TYPES.VIDEO
    ? (useWorkList.useVideoWorkList as (
        projectId: number,
      ) => UseWorkListReturn<T>)
    : (useWorkList.useImageWorkList as (
        projectId: number,
      ) => UseWorkListReturn<T>);
}

/**
 * 无限滚动作品列表管理的内部实现
 * @template T 作品类型，继承自 WorkItem
 * @param projectId 项目ID
 * @param type 作品类型标识符
 * @returns 无限滚动作品列表管理的状态和方法
 */
function useInfiniteWorkListImpl<T extends WorkItem>(
  projectId: number,
  type: MediaType,
): UseInfiniteWorkListReturn<T> {
  // 使用工厂函数获取对应类型的作品列表管理函数
  const workListComposableFn = getWorkListComposable<T>(type);

  // 调用基础作品列表管理函数，获取基础功能
  const baseWorkListState = workListComposableFn(projectId);

  // 解构基础作品列表管理的状态和方法
  const {
    currentWorkId,
    isLoading,
    workList,
    fetchWorkList,
    selectedWorkIds,
    handleSelectChange,
    currentPage,
    pageSize,
    totalItems,
    sucNum,
    handlePageChange,
    isLoadingDetail,
    switchToWork,
    currentWork,
    currentProgress,
    clearWorkDetailCacheEntry,
    clearAllWorkDetailCache,
    hasWorkDetailCache,
    batchUpdateWorkProgress,
  } = baseWorkListState;

  // 使用无限滚动组件
  const {
    cachedItems,
    stateManager,
    hasLoadedAll,
    addItemsToCache,
    resetCache,
    triggerLoadMore,
    triggerRetry,
  } = useInfiniteScroll<T>(totalItems);

  // ==================== 数据更新核心逻辑 ====================

  /**
   * 统一的缓存和选中状态重置函数
   * @description
   * 将缓存重置逻辑统一到一个函数中，确保重置逻辑的一致性和可维护性
   * 包含：重置缓存、清空选中状态、重置当前作品ID
   */
  const resetCacheAndSelection = (): void => {
    // 1. 重置无限滚动缓存
    resetCache();

    // 2. 清空选中状态，让 fetchWorkList 自动选中第一个作品
    selectedWorkIds.value = [];

    // 3. 重置当前作品ID，确保 fetchWorkList 会重新选择第一个作品
    currentWorkId.value = -1;
  };

  // 监听页码变化，处理缓存重置和选中状态
  watch(currentPage, newPageNumber => {
    if (newPageNumber === 1) {
      // 当切换到第一页时，使用统一的重置函数
      resetCacheAndSelection();
    }
  });

  // 监听工作列表数据变化，处理缓存更新
  // workList 始终只包含当前页的数据，无论第几页都是追加到缓存
  watch(
    workList,
    newWorkListData => {
      // 将当前页的数据添加到缓存中
      // 如果是第一页，缓存已经在 currentPage 的 watch 中被重置（或在 refreshWorkList 中直接重置）
      // 如果是其他页，则追加到现有缓存中
      addItemsToCache(newWorkListData as T[]);
    },
    { immediate: true },
  );

  // ==================== 核心数据更新方法 ====================

  /**
   * 更新缓存中的作品数据（智能合并）
   * @param updatedItems 更新的作品数据
   */
  const mergeUpdatedItemsToCache = (updatedItems: T[]): void => {
    const updatedMap = new Map(updatedItems.map(item => [item.id, item]));

    cachedItems.value = cachedItems.value.map(item =>
      updatedMap.has(item.id) ? updatedMap.get(item.id)! : item,
    );

    // 保持选中状态：过滤掉已删除的作品ID
    selectedWorkIds.value = selectedWorkIds.value.filter(id =>
      cachedItems.value.some(item => item.id === id),
    );
  };

  /**
   * 统一的数据更新入口
   * @param data 新的作品数据或作品ID（删除操作时）
   * @param operation 操作类型
   */
  const updateWorkList = (
    data: T[] | number,
    operation: WorkListOperation,
  ): void => {
    switch (operation) {
      case WorkListOperation.MERGE:
        // 合并更新（轮询、保存、编辑后更新）
        mergeUpdatedItemsToCache(data as T[]);
        break;
      case WorkListOperation.REMOVE:
        // 删除作品
        handleWorkRemoved(data as number);
        break;
      default:
        logger.warn(`未知的操作类型: ${operation}`);
    }
  };

  // ==================== 滚动加载处理 ====================

  /**
   * 创建错误信息对象
   * @param message 错误消息
   * @param original 原始错误对象
   * @returns 格式化的错误信息
   */
  const createLoadingError = (
    message: string,
    original?: Error,
  ): InfiniteLoadingError => ({
    type: 'network',
    message,
    retryable: true,
    original,
  });

  /**
   * 处理页面加载结果
   * @param err 错误对象
   * @param errorMessage 错误消息
   */
  const handleLoadResult = (err: Error | null, errorMessage: string): void => {
    if (err) {
      stateManager.setError(
        createLoadingError(err.message || errorMessage, err),
      );
      return;
    }

    stateManager.setSuccess();
    if (hasLoadedAll.value) {
      stateManager.setAllLoaded();
    }
  };

  /**
   * 处理滚动到底部事件
   */
  const handleScrollEnd = async (): Promise<void> => {
    if (!stateManager.canLoadMore.value || hasLoadedAll.value) {
      return;
    }

    triggerLoadMore();
    const nextPage = currentPage.value + 1;
    const [err] = await handlePageChange(nextPage);
    handleLoadResult(err, '加载失败');
  };

  /**
   * 重试加载
   */
  const retryLoad = async (): Promise<void> => {
    if (!stateManager.canRetry.value) {
      return;
    }

    triggerRetry();
    const [err] = await handlePageChange(currentPage.value);
    handleLoadResult(err, '重试失败');
  };

  // ==================== 页面状态计算属性 ====================

  /**
   * 处理页码变更事件
   * @param page 页码
   * @returns Promise<[Error | null, void | null]>
   */
  const onPageChange = async (
    page: number,
  ): Promise<[Error | null, void | null]> => {
    return await handlePageChange(page);
  };

  // 加载状态相关计算属性
  const isFirstPageLoading = computed(
    () =>
      stateManager.isLoading.value ||
      (isLoading.value && currentPage.value === 1),
  );

  const isLoadingMoreData = computed(() => stateManager.isLoadingMore.value);
  const hasLoadingError = computed(() => stateManager.hasError.value);
  const loadingError = computed(() => stateManager.error.value);
  const canRetryLoading = computed(() => stateManager.canRetry.value);

  // ==================== 作品删除逻辑 ====================

  /**
   * 检查是否触发了防死循环保护机制
   * @description
   * 防止因后端异常导致的死循环跳转问题：
   * 1. 当项目状态为生成中但作品列表返回空时，会触发跳转到第一步
   * 2. 然后 checkProjectStatus 又会跳转回预览页，形成死循环
   *
   * @param projectId 项目ID
   * @returns {boolean} 是否应该阻止跳转
   */
  const shouldPreventRedirectLoop = (projectId: string): boolean => {
    try {
      // 防护参数
      const REDIRECT_THRESHOLD = 1; // 60秒内允许的最大跳转次数
      const REDIRECT_TIMEOUT_MS = 60 * 1000; // 60秒重置计时器
      const STORAGE_KEY = 'adi_redirect_protection';

      // 从 sessionStorage 获取跳转历史
      const redirectHistoryStr = sessionStorage.getItem(STORAGE_KEY);
      const redirectHistory = redirectHistoryStr
        ? JSON.parse(redirectHistoryStr)
        : {};

      // 获取当前项目的跳转记录
      const projectRecord = redirectHistory[projectId] || {
        redirectCount: 0,
        lastRedirectTime: 0,
      };

      const now = Date.now();
      const timeSinceLastRedirect = now - projectRecord.lastRedirectTime;

      // 如果超过超时时间，重置计数器
      if (timeSinceLastRedirect > REDIRECT_TIMEOUT_MS) {
        projectRecord.redirectCount = 0;
      }

      // 更新跳转记录
      projectRecord.redirectCount += 1;
      projectRecord.lastRedirectTime = now;

      // 更新存储
      redirectHistory[projectId] = projectRecord;
      sessionStorage.setItem(STORAGE_KEY, JSON.stringify(redirectHistory));

      // 检查是否触发防护机制
      if (projectRecord.redirectCount > REDIRECT_THRESHOLD) {
        console.warn('⚠️ 检测到潜在的跳转死循环，已启动防护机制', {
          项目ID: projectId,
          跳转次数: projectRecord.redirectCount,
          时间窗口: `${REDIRECT_TIMEOUT_MS / 1000}秒`,
          防护措施: '停止自动跳转',
          建议操作: '手动刷新页面或联系技术支持',
        });
        return true; // 阻止跳转
      }

      return false; // 允许跳转
    } catch (error) {
      console.error('跳转防护检查失败:', error);
      return false; // 出错时默认允许跳转，避免阻塞用户操作
    }
  };

  /**
   * 处理作品列表为空时的跳转逻辑
   * @description 当删除最后一个作品后，自动跳转到项目编辑的第一步页面
   * @description 包含项目状态检查机制，避免与 checkProjectStatus 函数产生冲突导致的死循环问题
   * @description 包含防死循环保护机制，避免因后端异常导致的无限跳转
   */
  const handleEmptyWorkListRedirect = (): void => {
    try {
      // 获取当前路由信息
      const currentRoute = router.currentRoute;

      // 检查当前是否在预览模式（第二步）
      const currentStep = Number(currentRoute.query?.step) || 0;
      if (currentStep === FIRST_STEP_INDEX) {
        // 如果已经在第一步，无需跳转
        console.log('当前已在第一步，无需跳转');
        return;
      }

      // 项目状态检查：确保项目状态允许跳转到第一页
      const projectId = currentRoute.query?.projectId as string;
      if (projectId && !isProjectStateAllowRedirect(projectId)) {
        console.warn('项目状态不允许跳转到第一步，跳过自动跳转', {
          项目ID: projectId,
          当前步骤: currentStep,
        });
        return;
      }

      // 防死循环保护：检查是否触发了防护机制
      if (projectId && shouldPreventRedirectLoop(projectId)) {
        console.warn('检测到频繁跳转，已阻止自动跳转', {
          项目ID: projectId,
          当前步骤: currentStep,
          建议: '请手动刷新页面或联系技术支持',
        });
        // 可以在这里添加用户提示，例如显示一个友好的提示信息
        return;
      }

      // 构建跳转到第一步的路由参数
      const newQuery = {
        ...currentRoute.query,
        step: String(FIRST_STEP_INDEX),
      };

      console.log('准备跳转到第一步页面', {
        当前步骤: currentStep,
        目标步骤: FIRST_STEP_INDEX,
        项目ID: projectId,
        项目状态检查: projectId ? '已通过' : '跳过（新建项目）',
      });

      // 使用 nextTick 确保在下一个 DOM 更新周期执行路由跳转
      nextTick(() => {
        // 更新路由查询参数，响应式监听器会自动处理组件更新
        router.replace({
          path: currentRoute.path,
          query: newQuery,
        });

        console.log('作品列表为空，已自动跳转到第一步页面', {
          当前步骤: currentStep,
          目标步骤: FIRST_STEP_INDEX,
          说明: '组件将通过响应式监听器自动更新',
        });
      });
    } catch (error) {
      console.error('跳转到第一步页面失败:', error);
    }
  };

  /**
   * 检查项目状态是否允许重定向到第一步
   *
   * @description
   * 当作品列表为空时，需要自动返回上一步（产品需求）。
   * 但需要根据项目状态判断是否允许跳转，避免与 checkProjectStatus 函数产生冲突。
   *
   * 重要业务逻辑说明：
   * 1. 当项目状态处于生成中时，作品列表接口不会返回空数组
   * 2. 后端在作品列表为空时返回 res.data 为空数组，不返回项目状态信息
   * 3. 项目状态为 undefined 时，说明后端没有返回状态信息，此时应该允许跳转
   * 4. 项目状态为 null 时，说明明确获取到了空状态，此时不允许跳转
   * 5. 项目状态为具体数值时，需要通过 shouldRestrictProjectAccess 判断是否允许访问
   *
   * @param projectId 项目ID
   * @returns 是否允许重定向到第一步
   */
  const isProjectStateAllowRedirect = (projectId: string): boolean => {
    try {
      // 从 Vuex store 获取项目状态
      const projectStatus = getProjectStatusFromStore(projectId);
      if (projectStatus === undefined) {
        console.log('项目状态未知（undefined），允许跳转到第一步', {
          项目ID: projectId,
          业务逻辑: '当前项目状态为生成中，状态作品列表最少有一个作品',
          原因: '当前作品列表返回空数组',
        });
        return true;
      }

      if (projectStatus === null) {
        console.log('项目状态为 null，不允许跳转', {
          项目ID: projectId,
          说明: '明确获取到null状态，可能存在业务限制',
        });
        return false;
      }
      const shouldRestrictAccess = shouldRestrictProjectAccess(projectStatus);
      const isAllowed = !shouldRestrictAccess;

      console.log('项目状态检查结果', {
        项目ID: projectId,
        当前状态: projectStatus,
        状态名称: PROJECT_STATUS_NAME[projectStatus] || '未知状态',
        是否应该限制访问: shouldRestrictAccess,
        是否允许跳转: isAllowed,
        判断逻辑: '使用 shouldRestrictProjectAccess 统一判断',
      });

      return isAllowed;
    } catch (error) {
      console.error('项目状态检查失败，默认允许跳转:', error);
      // 发生错误时默认允许跳转，避免阻塞用户操作
      return true;
    }
  };

  /**
   * 删除作品方法
   * @param workId 要删除的作品ID
   * @returns 删除是否成功
   */
  const removeWork = async (workId: number): Promise<boolean> => {
    const startTime = performance.now();
    const isCurrentWork = currentWorkId.value === workId;

    const operationParams = {
      列表数据更新方式: '移除指定作品',
      详情缓存清除范围: '清除指定作品缓存',
      是否修改选中作品: isCurrentWork ? '是（智能选择下一个）' : '否',
      是否重新获取详情数据: '否',
      删除作品ID: workId,
    };

    logger.operationStart('删除作品', operationParams);

    // 调用删除 API
    const [err] = await deleteWork(workId);

    if (err) {
      logger.operationError('删除作品', err, performance.now() - startTime);
      message.error(err.message || '删除失败');
      return false;
    }

    // 使用统一的数据更新方法处理删除
    updateWorkList(workId, WorkListOperation.REMOVE);

    const result = {
      列表数据更新方式: '移除指定作品',
      详情缓存清除范围: '已清除指定作品缓存',
      是否修改选中作品: operationParams.是否修改选中作品,
      是否重新获取详情数据: '否',
      删除作品ID: workId,
      剩余作品数: cachedItems.value.length,
    };

    logger.operationEnd('删除作品', result, performance.now() - startTime);
    message.success('移除成功');

    // 检查删除后列表是否为空，如果为空则触发跳转到第一步
    if (cachedItems.value.length === 0) {
      handleEmptyWorkListRedirect();
    }

    return true;
  };

  // ==================== 智能选择逻辑 ====================

  /**
   * 查找智能选择的下一个作品ID
   * @param removedIndex 被删除作品的索引位置
   * @returns 新选中的作品ID，如果没有可选作品则返回undefined
   */
  const findNextSelectedWorkId = (removedIndex: number): number | undefined => {
    const currentList = cachedItems.value;

    // 优先选择下一个作品（删除后原来的下一个作品现在在相同索引位置）
    if (removedIndex < currentList.length && currentList[removedIndex]) {
      return currentList[removedIndex].id;
    }

    // 其次选择前一个作品
    if (removedIndex > 0 && currentList[removedIndex - 1]) {
      return currentList[removedIndex - 1].id;
    }

    return undefined;
  };

  /**
   * 处理作品删除后的智能选择逻辑
   * @param removedWorkId 被删除的作品ID
   * @param removedIndex 被删除作品的索引位置
   */
  const handleSmartSelection = (
    removedWorkId: number,
    removedIndex: number,
  ): void => {
    // 只有当删除的是当前选中的作品时才需要重新选择
    if (!selectedWorkIds.value.includes(removedWorkId)) {
      return;
    }

    const newSelectedWorkId = findNextSelectedWorkId(removedIndex);

    if (newSelectedWorkId) {
      handleSelectChange([newSelectedWorkId]);
    } else {
      handleSelectChange([]);
    }
  };

  /**
   * 处理作品删除后的逻辑
   * @param removedWorkId 被删除的作品ID
   */
  const handleWorkRemoved = (removedWorkId: number): void => {
    // 1. 清除被删除作品的详情缓存
    clearWorkDetailCacheEntry(removedWorkId);

    // 2. 查找被删除作品在列表中的位置
    const removedIndex = cachedItems.value.findIndex(
      item => item.id === removedWorkId,
    );

    if (removedIndex === -1) {
      return;
    }

    // 3. 从显示列表中移除被删除的作品
    cachedItems.value = cachedItems.value.filter(
      item => item.id !== removedWorkId,
    );

    // 4. 更新总数量
    totalItems.value = Math.max(0, totalItems.value - 1);

    // 5. 处理智能作品选择逻辑
    handleSmartSelection(removedWorkId, removedIndex);
  };

  // ==================== 统一作品更新方法 ====================

  /**
   * 更新作品列表数据
   *
   * @description
   * 根据业务场景选择合适的更新策略：
   *
   * **常见使用场景：**
   * - 🔄 作品状态变化（生成中→已完成）→ 局部更新
   * - ✏️ 编辑作品信息 → 局部更新 + 清除作品详情缓存
   * - 💾 批量操作完成 → 完整刷新
   * - 🗑️ 删除作品 → 完整刷新
   *
   * **两种更新模式：**
   * - **局部更新**：只更新指定作品，保持当前页面位置
   * - **完整刷新**：重新加载整个列表，回到第一页
   *
   * @param workIds 要更新的作品ID数组
   * @param options 更新配置选项
   * @param options.refreshFullList 是否完整刷新列表（默认：false）
   * @param options.clearDetailCache 是否清除指定作品的详情缓存（默认：false）
   * @param options.refreshCurrentDetail 是否刷新当前选中作品详情（默认：false）
   * @param options.allowEmptyIds 是否允许空ID数组（默认：false）
   * @param options.context 操作上下文，用于日志记录（默认：'更新作品'）
   * @param options.onSuccess 成功回调函数
   * @param options.onError 失败回调函数
   *
   * @example
   * ```typescript
   * // 🔄 作品生成完成，更新状态
   * await updateWorkItems([123]);
   *
   * // ✏️ 编辑作品后，更新列表数据并清除作品详情缓存
   * await updateWorkItems([123], {
   *   clearDetailCache: true,        // 清除该作品的详情缓存
   *   refreshCurrentDetail: true     // 重新获取当前作品详情
   * });
   *
   * // 💾 批量操作后，完整刷新列表
   * await updateWorkItems([], {
   *   refreshFullList: true,
   *   allowEmptyIds: true
   * });
   *
   * // 🗑️ 删除作品后，完整刷新
   * await updateWorkItems([123], {
   *   refreshFullList: true
   * });
   * ```
   */
  const updateWorkItems = async (
    workIds: number[],
    options: UpdateWorkItemsOptions<T> = {},
  ): Promise<void> => {
    const {
      clearDetailCache = false,
      refreshFullList = false,
      context = '更新作品',
      refreshCurrentDetail = false,
      onSuccess,
      onError,
      allowEmptyIds = false,
    } = options;

    const startTime = performance.now();

    // 核心操作维度日志
    const operationParams = {
      列表数据更新方式: refreshFullList ? '完整刷新' : '局部更新',
      详情缓存清除范围: refreshFullList
        ? '清空所有缓存'
        : clearDetailCache
        ? '清除指定作品缓存'
        : '不清除缓存',
      是否修改选中作品: refreshFullList ? '是（重置为第一个）' : '否',
      是否重新获取详情数据: refreshCurrentDetail ? '是' : '否',
      作品数量: workIds.length,
    };

    logger.operationStart(context, operationParams);

    // 参数验证：根据更新模式检查作品ID列表
    if (!refreshFullList && workIds.length === 0) {
      logger.warn(`${context} 跳过`, {
        原因: '局部更新模式下作品ID列表不能为空',
      });
      logger.endGroup();
      return;
    }

    if (refreshFullList && workIds.length === 0 && !allowEmptyIds) {
      logger.warn(`${context} 跳过`, {
        原因: '完整刷新模式下需要设置 allowEmptyIds: true 才能使用空ID列表',
      });
      logger.endGroup();
      return;
    }

    // 如果需要刷新整个列表（批量保存、生成完成等场景）
    if (refreshFullList) {
      const [err] = await refreshWorkList();
      // 注意：
      // 1. refreshWorkList 已经调用了 clearAllWorkDetailCache()，所以不需要再清除缓存
      // 2. 完整刷新模式下，workIds 参数实际上不会被使用
      // 3. refreshWorkList 会重置所有状态并回到第一页
      const endTime = performance.now();
      const duration = endTime - startTime;

      if (err) {
        logger.operationError(context, err, duration);
        onError?.(err);
        return;
      }

      const result = {
        列表数据更新方式: '完整刷新',
        详情缓存清除范围: '已清空所有缓存',
        是否修改选中作品: '是（重置为第一个）',
        是否重新获取详情数据: '否',
        更新后作品总数: cachedItems.value.length,
      };

      logger.operationEnd(context, result, duration);
      onSuccess?.();
      return;
    }

    try {
      // 如果需要清除详情缓存
      if (clearDetailCache) {
        workIds.forEach(id => clearWorkDetailCacheEntry(id));
      }

      // 获取更新后的作品数据
      const [err, res] = await getWorkListWithTransform({
        projectId,
        pageNow: 1,
        limit: workIds.length,
        idList: workIds,
      });

      if (err || !res?.workList) {
        const error = new Error(err?.message || '获取作品数据失败');
        logger.operationError(context, error, performance.now() - startTime);
        onError?.(error);
        return;
      }

      // 使用合并操作更新作品状态
      const updatedWorks = res.workList as T[];
      updateWorkList(updatedWorks, WorkListOperation.MERGE);

      // 同步更新 sucNum（轮询更新时需要保持 sucNum 与接口返回值一致）
      if (res.sucNum !== undefined) {
        sucNum.value = res.sucNum;
      }

      // 如果当前选中的作品在更新列表中，重新获取详情
      if (refreshCurrentDetail && workIds.includes(currentWorkId.value)) {
        nextTick(() => {
          // 在切换前再次清除缓存，防止竞态条件导致的缓存重新加载
          clearWorkDetailCacheEntry(currentWorkId.value);
          switchToWork(currentWorkId.value);
        });
      }

      const endTime = performance.now();
      const duration = endTime - startTime;

      const result = {
        列表数据更新方式: '局部更新',
        详情缓存清除范围: clearDetailCache ? '清除指定作品缓存' : '不清除缓存',
        是否修改选中作品: '否',
        是否重新获取详情数据:
          refreshCurrentDetail && workIds.includes(currentWorkId.value)
            ? '是'
            : '否',
        更新作品数量: updatedWorks.length,
        sucNum更新: res.sucNum !== undefined ? `${sucNum.value}` : '未更新',
      };

      logger.operationEnd(context, result, duration);
      onSuccess?.(updatedWorks);
    } catch (error) {
      const err = error instanceof Error ? error : new Error('未知错误');
      logger.operationError(context, err, performance.now() - startTime);
      onError?.(err);
    }
  };

  /**
   * 保存后更新作品列表数据
   * @param workIds 保存的作品ID列表
   * @param isBatchSave 是否为批量保存
   */
  const handleSaveUpdate = async (
    workIds: number[],
    isBatchSave = false,
  ): Promise<void> => {
    await updateWorkItems(workIds, {
      refreshFullList: isBatchSave,
      clearDetailCache: isBatchSave,
      allowEmptyIds: isBatchSave, // 批量保存时允许空ID列表（全选状态）
      context: isBatchSave ? '批量保存作品' : '保存作品',
    });
  };

  /**
   * 编辑后更新作品数据
   * 编辑后保存会导致作品状态流转到重新生成中状态，需要同时更新作品列表和详情数据
   * @param workId 编辑的作品ID
   */
  const handleEditUpdate = async (workId: number): Promise<void> => {
    await updateWorkItems([workId], {
      clearDetailCache: true,
      refreshCurrentDetail: true,
      context: '编辑作品',
    });
  };

  /**
   * 刷新作品列表（用于批量保存后的完整刷新）
   * 清空所有作品详情缓存并重新刷新作品列表，清空选中状态
   * @returns Promise<[Error | null, void | null]> 使用 await-to 范式返回错误状态
   */
  const refreshWorkList = async (): Promise<[Error | null, void | null]> => {
    // 1. 清空所有作品的详情缓存
    clearAllWorkDetailCache();

    // 2. 使用统一的重置函数，确保缓存和选中状态的一致性重置
    resetCacheAndSelection();

    // 3. 回到第一页并重新获取作品列表数据
    const [err] = await onPageChange(1);

    if (err) {
      return [err, null];
    }

    // 4. 检查刷新后列表是否为空，如果为空则触发跳转到第一步
    // 使用 nextTick 确保数据已经更新完成
    nextTick(() => {
      if (cachedItems.value.length === 0) {
        handleEmptyWorkListRedirect();
      }
    });

    return [null, null];
  };

  /**
   * 生成完成后更新作品数据
   * 生成完成后需要清空所有作品详情缓存并重新刷新作品列表
   * @param workId 生成完成的作品ID
   */
  const handleGenerateUpdate = async (workId: number): Promise<void> => {
    await updateWorkItems([workId], {
      refreshFullList: true,
      allowEmptyIds: true, // 允许空ID列表，因为完整刷新不依赖具体的作品ID
      context: '生成完成作品',
    });
  };

  /**
   * 轮询更新-作品完成时的回调函数
   * 当作品从"生成中"状态变为"已完成"状态时，清除并重新获取作品详情
   * @param workId 完成的作品ID
   */
  const handleWorkCompleted = (workId: number): void => {
    // 使用统一的更新方法处理作品完成
    updateWorkItems([workId], {
      clearDetailCache: true,
      refreshCurrentDetail: currentWorkId.value === workId,
      context: '轮询更新-作品完成',
    });
  };

  /**
   * 轮询更新-作品失败时的回调函数
   * 当作品从"生成中"状态变为"生成失败"状态时，停止轮询并清除详情缓存
   * @param workId 失败的作品ID
   */
  const handleWorkFailed = (workId: number): void => {
    logger.debug('🚫 作品生成失败，清除详情缓存', {
      作品ID: workId,
      是否为当前选中作品: currentWorkId.value === workId,
      操作: '清除详情缓存并更新列表',
      上下文: '轮询更新-作品失败',
    });

    // 使用统一的更新方法处理作品失败
    updateWorkItems([workId], {
      clearDetailCache: true,
      refreshCurrentDetail: currentWorkId.value === workId,
      context: '轮询更新-作品失败',
    });
  };

  /**
   * 轮询更新-视频作品50%进度时的回调函数
   * 当视频作品进度达到50%时，更新作品详情数据以获取最新的元数据和状态信息
   * @param workId 达到50%进度的作品ID
   */
  const handleVideoWork50PercentProgress = (workId: number): void => {
    logger.debug('🎯 视频作品50%进度，更新详情数据', {
      作品ID: workId,
      是否为当前选中作品: currentWorkId.value === workId,
      操作: '清除详情缓存并重新获取',
      上下文: '轮询更新-50%进度',
    });

    // 使用统一的更新方法处理50%进度更新
    updateWorkItems([workId], {
      clearDetailCache: true,
      refreshCurrentDetail: currentWorkId.value === workId,
      context: '轮询更新-50%进度',
    });
  };

  /**
   * 同步进度到作品详情数据
   * 当轮询更新列表项进度时，同步进度到对应的作品详情数据中
   * @param updatedWorks 更新后的作品数据
   */
  const syncProgressToWorkDetail = (updatedWorks: T[]): void => {
    const progressUpdates: Array<{ workId: number; progress: number }> = [];
    const syncDetails: Array<{
      workId: number;
      oldProgress: number;
      newProgress: number;
    }> = [];

    // 遍历所有更新的作品，收集需要同步进度的作品
    updatedWorks.forEach(updatedWork => {
      // 检查该作品是否有详情缓存
      if (!hasWorkDetailCache(updatedWork.id)) {
        return; // 没有缓存则跳过
      }

      // 获取当前作品详情（如果是当前选中的作品）
      const currentWorkDetail =
        updatedWork.id === currentWorkId.value ? currentWork.value : null;

      // 如果是当前选中的作品，检查进度是否发生变化
      if (currentWorkDetail) {
        const oldProgress = currentWorkDetail.progress;
        const newProgress = updatedWork.progress;

        if (oldProgress !== newProgress) {
          progressUpdates.push({
            workId: updatedWork.id,
            progress: newProgress,
          });

          syncDetails.push({
            workId: updatedWork.id,
            oldProgress,
            newProgress,
          });
        }
      } else {
        // 对于非当前选中的作品，直接添加到更新列表
        progressUpdates.push({
          workId: updatedWork.id,
          progress: updatedWork.progress,
        });
      }
    });

    // 批量更新进度
    if (progressUpdates.length > 0) {
      batchUpdateWorkProgress(progressUpdates);

      logger.debug('🔄 同步进度到作品详情缓存', {
        同步作品数量: progressUpdates.length,
        当前选中作品ID: currentWorkId.value,
        同步详情: syncDetails.length > 0 ? syncDetails : '批量更新，无详细对比',
        同步方式: 'batchUpdateWorkProgress',
      });
    }
  };

  /**
   * 轮询数据更新处理器
   * @description
   * 处理轮询获取的作品数据，包括进度同步和列表更新
   * 注意：轮询系统已经获取了最新数据，这里只需要更新缓存，不需要再次发起API请求
   *
   * @param updatedWorks 轮询获取的最新作品数据
   */
  const handlePollingDataUpdate = (updatedWorks: T[]): void => {
    // 参数验证
    if (!Array.isArray(updatedWorks) || updatedWorks.length === 0) {
      return;
    }

    logger.debug('🔄 轮询数据更新处理器：直接更新缓存', {
      更新作品数量: updatedWorks.length,
      作品ID列表: updatedWorks.map(work => work.id),
      上下文: '轮询数据更新-避免重复API调用',
    });

    // 1. 先同步进度到作品详情数据（实时进度更新）
    syncProgressToWorkDetail(updatedWorks);

    // 2. 直接更新列表缓存数据（避免重复API调用）
    // 轮询系统已经获取了最新数据，这里只需要合并到缓存中
    updateWorkList(updatedWorks, WorkListOperation.MERGE);
  };

  // 50%进度跟踪状态（避免重复触发）
  const triggered50PercentWorks = ref<Set<number>>(new Set());

  // 集成智能轮询功能，使用事件驱动架构
  const polling = useWorkPolling({
    workList: cachedItems,
    projectId,
    projectType: convertMediaTypeToProjectType(type),
    eventHandlers: {
      onDataUpdated: async (event: DataUpdatedEvent) => {
        handlePollingDataUpdate(event.updatedWorks as T[]);
      },
      onStatusChanged: async (event: StatusChangedEvent) => {
        /**
         * 统一的状态变化处理逻辑
         *
         * 此处理器负责监听所有作品状态变化，并根据状态变化类型执行相应的业务逻辑：
         * 1. 作品完成：从"生成中"状态变为"已完成"状态时，清除详情缓存并更新列表
         * 2. 作品失败：从"生成中"状态变为"生成失败"状态时，停止轮询并清除详情缓存
         * 3. 其他状态变化：可以在此处添加更多状态变化的处理逻辑
         *
         * 使用辅助函数简化状态检查逻辑，提高代码可读性和可维护性
         */

        // 验证事件有效性
        if (!isValidStatusChangeEvent(event)) {
          logger.warn('⚠️ 收到无效的状态变化事件', { event });
          return;
        }

        const { workId } = event;
        const changeType = getStatusChangeType(event);

        logger.debug('📊 处理状态变化事件', {
          作品ID: workId,
          变化类型: changeType,
          事件详情: event,
        });

        // 处理作品完成状态变化
        if (isCompletionStatusChange(event)) {
          logger.debug('✅ 作品生成完成，执行完成处理逻辑', { 作品ID: workId });
          handleWorkCompleted(workId);
        }

        // 处理作品失败状态变化
        if (isFailureStatusChange(event)) {
          logger.debug('❌ 作品生成失败，执行失败处理逻辑', { 作品ID: workId });
          handleWorkFailed(workId);
        }

        // 可以在此处添加其他状态变化的处理逻辑
        // 例如：重新生成状态、编辑状态等
      },
      onProgressChanged: async (event: ProgressChangedEvent) => {
        // 检查是否触发了视频50%进度里程碑
        // 条件：视频项目 && 进度从小于50%变为大于等于50% && 未曾触发过
        const currentProjectType = convertMediaTypeToProjectType(type);
        if (
          currentProjectType === PROJECT_TYPE_VIDEO &&
          event.previousProgress < 50 &&
          event.currentProgress >= 50 &&
          !triggered50PercentWorks.value.has(event.workId)
        ) {
          // 标记为已触发，避免重复触发
          triggered50PercentWorks.value.add(event.workId);
          handleVideoWork50PercentProgress(event.workId);
        }

        // 这里可以添加其他进度变化的处理逻辑
        // 例如：更新进度条、发送进度统计等
      },
    },
  });

  // ==================== EventBus 事件处理 ====================

  /**
   * 处理作品删除请求
   */
  const handleWorkRemoveRequest = (...args: unknown[]) => {
    const data = args[0] as WorkRemoveRequestData;
    if (!data || typeof data.workId !== 'number') {
      console.error('Invalid work remove request data:', data);
      return;
    }

    removeWork(data.workId).then(success => {
      const eventData = {
        workIds: [data.workId],
        message: success ? '删除成功' : '删除失败',
      };

      if (success) {
        eventBus.emit(EVENT_NAMES.WORK_REMOVE_SUCCESS, eventData);
      } else {
        eventBus.emit(EVENT_NAMES.WORK_REMOVE_ERROR, {
          ...eventData,
          error: new Error('删除失败'),
        });
      }
    });
  };

  /**
   * 处理作品保存请求
   */
  const handleWorkSaveRequest = async (...args: unknown[]) => {
    const data = args[0] as WorkSaveRequestData;
    if (!data || !Array.isArray(data.workIds)) {
      console.error('Invalid work save request data:', data);
      return;
    }

    try {
      await handleSaveUpdate(data.workIds, data.isBatchSave);
      eventBus.emit(EVENT_NAMES.WORK_SAVE_SUCCESS, {
        workIds: data.workIds,
        message: '保存成功',
      });
    } catch (error) {
      eventBus.emit(EVENT_NAMES.WORK_SAVE_ERROR, {
        workIds: data.workIds,
        error: error instanceof Error ? error : new Error('保存失败'),
        message: '保存失败',
      });
    }
  };

  // 注册和清理事件监听器
  onMounted(() => {
    eventBus.on(EVENT_NAMES.WORK_REMOVE_REQUEST, handleWorkRemoveRequest);
    eventBus.on(EVENT_NAMES.WORK_SAVE_REQUEST, handleWorkSaveRequest);
  });

  onUnmounted(() => {
    eventBus.off(EVENT_NAMES.WORK_REMOVE_REQUEST, handleWorkRemoveRequest);
    eventBus.off(EVENT_NAMES.WORK_SAVE_REQUEST, handleWorkSaveRequest);
  });

  return {
    currentWorkId,
    isLoading,
    workList,
    displayedWorkList: cachedItems,
    currentWork,
    currentProgress,
    fetchWorkList,
    selectedWorkIds,
    handleSelectChange,
    currentPage,
    pageSize,
    totalItems,
    sucNum,
    // 无限滚动状态
    isLoadingMore: stateManager.isLoadingMore,
    hasLoadedAll,
    handleScrollEnd,
    retryLoad,
    onPageChange,
    isLoadingDetail,
    switchToWork,
    isFirstPageLoading,
    isLoadingMoreData,
    // 错误处理状态
    hasLoadingError,
    loadingError,
    canRetryLoading,
    // 作品操作方法
    removeWork,
    updateWorkItems,
    refreshWorkList,
    // 向后兼容的方法（建议使用 updateWorkItems）
    handleSaveUpdate,
    handleEditUpdate,
    handleGenerateUpdate,
    // 轮询功能
    polling,
  };
}
