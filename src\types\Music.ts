export interface Music {
  /** 资源id */
  resId: string;
  /** 名称 */
  name: string;
  /** 时长 */
  duration: string;
  /** 封面 */
  cover: {
    resId: string;
    resType: number;
  };
  /** 链接 */
  link: string;
}

export interface Dubbing {
  /** 配音员id */
  voiceId: string;
  /** 分类名 */
  typeName: string;
  /** 分类名 */
  categoryName: string;
  /** 配音员名称 */
  name: string;
  /** 配音员预览链接 */
  link: string;
  /** 配音员封面 */
  cover: {
    resId: string;
    resType: number;
  };
  /** 额外类型 */
  extraType?: string;
}
