/**
 * @fileoverview 智能轮询配置相关类型定义
 * @description 定义轮询配置选项和返回值类型
 */

import { Ref } from 'vue';
import { WorkItem } from '@/types';
import {
  EventHandler,
  DataUpdatedEvent,
  ProgressChangedEvent,
  StatusChangedEvent,
} from './events';

/**
 * 轮询配置选项接口
 */
export interface UseWorkPollingOptions<T extends WorkItem> {
  /** 作品列表的响应式引用 */
  workList: Ref<T[]>;
  /** 项目ID */
  projectId: number;
  /** 项目类型（0: 视频, 1: 图文） */
  projectType: number;
  /** 事件处理器配置 */
  eventHandlers: {
    /** 数据更新事件处理器 */
    onDataUpdated: EventHandler<DataUpdatedEvent>;
    /** 进度变化事件处理器（必需，用于处理包括50%进度在内的所有进度变化） */
    onProgressChanged: EventHandler<ProgressChangedEvent>;
    /** 状态变化事件处理器（必需，用于处理完成、失败等所有状态变化） */
    onStatusChanged: EventHandler<StatusChangedEvent>;
  };
}

/**
 * 轮询返回值接口
 */
export interface UseWorkPollingReturn {
  /** 是否正在轮询 */
  isPolling: Ref<boolean>;
  /** 需要轮询的作品列表 */
  pollingWorks: Ref<WorkItem[]>;
  /** 是否需要轮询 */
  needsPolling: Ref<boolean>;
  /** 页面是否可见 */
  isPageVisible: Ref<boolean>;
  /** 轮询是否已超时 */
  isPollingTimedOut: Ref<boolean>;
  /** 轮询开始时间 */
  pollingStartTime: Ref<number | null>;
  /** 手动启动轮询 */
  startPolling: (triggerType?: string) => void;
  /** 手动停止轮询 */
  stopPolling: () => void;
}
