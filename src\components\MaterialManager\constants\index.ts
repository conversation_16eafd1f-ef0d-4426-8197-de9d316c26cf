import { MIMETYPE_KEY } from '@/constants/material';
/**
 * 排序字段
 */
export enum SORT_BY_KEY {
  NAME = 'name',
  FILE_SIZE = 'fileSize',
  CREATE_TIME = 'createTime',
  DELETE_TIME = 'deleteTime',
  UPLOAD_ACCOUNT = 'uploadAccount',
  DELETE_ACCOUNT = 'deleteAccount',
}

/**
 * MIME类型枚举
 */
export const MIME_ICON_TYPE = {
  // 视频类
  [MIMETYPE_KEY.MP4]: 'video',
  [MIMETYPE_KEY.MOV]: 'video',
  // 图片类
  [MIMETYPE_KEY.JPG]: 'image',
  [MIMETYPE_KEY.JPEG]: 'image',
  [MIMETYPE_KEY.PNG]: 'image',
  [MIMETYPE_KEY.GIF]: 'image',
  [MIMETYPE_KEY.BMP]: 'image',
};

/**
 * 可预览文件类型
 * @type {Array}
 */
export const MIMETYPE_PRESET_PREVIEW_LIST = [
  MIMETYPE_KEY.DOC,
  MIMETYPE_KEY.DOCX,
  MIMETYPE_KEY.XLS,
  MIMETYPE_KEY.XLSX,
  MIMETYPE_KEY.PPT,
  MIMETYPE_KEY.PPTX,
  MIMETYPE_KEY.PDF,
  MIMETYPE_KEY.MP3,
  MIMETYPE_KEY.M4A,
  // MIMETYPE_KEY.MP4,
  MIMETYPE_KEY.FLV,
  MIMETYPE_KEY.SWF,
];

/** 页面状态 */
export const STATUS_DEF = {
  INIT: 1,
  INIT_404: 2,
  SEARCHED: 3,
  SEARCHED_404: 4,
};

/**
 * 搜索历史记录缓存key
 */
export const FM_HISTORY_LIST_KEY = 'fm_history_list_key';
