import store from '@/store';
import { Font } from '@/types';

/** 已加载字体集合，防止重复加载 */
const loadedFonts = new Set<string>();

/** 进行中的字体加载任务，保证同一字体“单飞” */
const inflight = new Map<string, Promise<void>>();

const isDev = import.meta.env.MODE === 'development';

// 用于兜底的默认字体，有些字体缺失某个字时用这种字体代替
export const DEFAULT_FONT: Font = {
  fileName: 'SourceHanSansCN-Medium.ttf',
  fontName: '思源黑体 CN Medium',
  fontFamily: 'SourceHanSansCN-Medium',
  styleCategory: 'SourceHanSansCN',
  name: '思源黑体-Medium',
  pricingCategory: 'free',
  languageCategory: 'zh',
};

// 用于添加花字时的默认字体
export const FLOWER_FONT: Font = {
  fileName: 'DouyinSansBold.ttf',
  fontName: '抖音美好体',
  fontFamily: 'DouyinSansBold',
  styleCategory: '',
  name: '抖音美好体',
  pricingCategory: 'free',
  languageCategory: 'zh',
};

/** 引导默认/花字字体的加载，只执行一次，失败可重试 */
let bootstrapPromise: Promise<void> | null = null;

/** 生成字体去重键（如需区分同族不同文件，可改为 `${font.fontFamily}:${font.fileName}`） */
function fontKey(font: Font): string {
  return font.fontFamily;
}

/** 实际加载单个字体的帮助函数，带“单飞”与失败重试能力 */
async function loadFontFace(font: Font, fontDomain: string): Promise<void> {
  const key = fontKey(font);

  // 已成功加载则直接返回
  if (loadedFonts.has(key)) return;

  // 有进行中的任务则复用
  const running = inflight.get(key);
  if (running) return running;

  const task = (async () => {
    // 基于文件后缀可选添加 format 提示，这里简单按 ttf 处理
    const src = `url(${fontDomain}/${font.fileName})`;
    const fontFace = new FontFace(font.fontFamily, src);
    await fontFace.load();
    document.fonts.add(fontFace);
    loadedFonts.add(key);
    console.log(`字体 ${font.fontFamily} 加载成功`);
  })()
    .catch(error => {
      console.error(`加载字体失败: ${error}`);
      // 失败后清理，便于后续重试
      throw error;
    })
    .finally(() => {
      inflight.delete(key);
    });

  inflight.set(key, task);
  return task;
}

/** 动态加载字体，防止重复加载 */
export async function loadFont(font: Font): Promise<void> {
  /** 字体资源域名 */
  const fontDomain = isDev
    ? '/fonts'
    : store.state.system.scPortalResRoot + '/css/fonts';

  // 只在首次调用时并行加载默认与花字字体；失败会清空以便下次重试
  if (!bootstrapPromise) {
    bootstrapPromise = (async () => {
      await Promise.all([
        loadFontFace(DEFAULT_FONT, fontDomain),
        loadFontFace(FLOWER_FONT, fontDomain),
      ]);
    })().catch(e => {
      bootstrapPromise = null;
      throw e;
    });
  }

  // 等待引导完成后，再加载目标字体
  await bootstrapPromise;
  await loadFontFace(font, fontDomain);
}
