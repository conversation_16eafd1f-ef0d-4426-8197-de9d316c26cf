# 删除最后作品自动跳转功能说明

## 功能概述

当用户在视频项目或图文项目的预览页面中删除作品列表中的最后一个作品项后，系统会自动跳转回到项目编辑流程的第一步页面。

## 适用场景

- **视频项目预览页面**：VideoProjectView 组件的预览模式（第二步）
- **图文项目预览页面**：ImageProjectView 组件的预览模式（第二步）

## 触发条件

1. 当前作品列表中的所有作品都被删除完毕（列表长度为 0）
2. 删除操作成功完成后立即触发
3. 支持单个删除和批量删除两种场景

## 实现方案

### 1. 核心实现位置

**文件**：`src/views/EditProjectView/composables/useInfiniteWorkList.ts`

### 2. 主要修改内容

#### 2.1 添加跳转处理函数

```typescript
/**
 * 处理作品列表为空时的跳转逻辑
 * @description 当删除最后一个作品后，自动跳转到项目编辑的第一步页面
 */
const handleEmptyWorkListRedirect = (): void => {
  try {
    // 获取当前路由信息
    const currentRoute = router.currentRoute;

    // 检查当前是否在预览模式（第二步）
    const currentStep = Number(currentRoute.query.step) || 0;
    if (currentStep === FIRST_STEP_INDEX) {
      // 如果已经在第一步，无需跳转
      return;
    }

    // 构建跳转到第一步的路由参数
    const newQuery = {
      ...currentRoute.query,
      step: String(FIRST_STEP_INDEX),
    };

    // 执行路由跳转
    router.replace({
      path: currentRoute.path,
      query: newQuery,
    });

    console.log('作品列表为空，已自动跳转到第一步页面');
  } catch (error) {
    console.error('跳转到第一步页面失败:', error);
  }
};
```

#### 2.2 单个删除场景处理

在 `removeWork` 方法中添加空列表检查：

```typescript
const removeWork = async (workId: number): Promise<boolean> => {
  // ... 原有删除逻辑 ...

  // 检查删除后列表是否为空，如果为空则触发跳转到第一步
  if (cachedItems.value.length === 0) {
    handleEmptyWorkListRedirect();
  }

  return true;
};
```

#### 2.3 批量删除场景处理

在 `refreshWorkList` 方法中添加空列表检查：

```typescript
const refreshWorkList = async (): Promise<[Error | null, void | null]> => {
  // ... 原有刷新逻辑 ...

  // 检查刷新后列表是否为空，如果为空则触发跳转到第一步
  // 使用 nextTick 确保数据已经更新完成
  nextTick(() => {
    if (cachedItems.value.length === 0) {
      handleEmptyWorkListRedirect();
    }
  });

  return [null, null];
};
```

### 3. 删除场景覆盖

#### 3.1 单个删除

- **触发方式**：通过 EventBus 发送 `WORK_REMOVE_REQUEST` 事件
- **处理流程**：`handleWorkRemoveRequest` → `removeWork` → 检查空列表 → 跳转
- **适用情况**：普通作品的单个删除操作

#### 3.2 重新生成完成状态删除

- **触发方式**：通过 EventBus 发送 `WORK_REMOVE_REQUEST` 事件（与普通删除相同）
- **处理流程**：`handleWorkRemoveRequest` → `removeWork` → 检查空列表 → 跳转
- **适用情况**：重新生成完成状态的作品删除（统一使用普通删除流程）

## 技术细节

### 1. 路由跳转逻辑

- **保持参数**：保留当前路由的所有查询参数（projectId、templateId 等）
- **仅修改步骤**：只将 `step` 参数修改为 `0`（第一步索引）
- **跳转方式**：使用 `router.replace` 避免产生历史记录

### 2. 错误处理

- **边界检查**：检查当前步骤，避免在第一步时重复跳转
- **异常捕获**：使用 try-catch 包装跳转逻辑，防止路由错误影响删除操作
- **日志记录**：记录跳转成功和失败的日志信息

### 3. 时序控制

- **单个删除**：删除成功后立即检查
- **批量删除**：使用 `nextTick` 确保列表数据更新完成后再检查

## 兼容性说明

### 1. 现有功能兼容

- **不影响正常删除流程**：跳转逻辑作为额外功能，不干扰原有删除逻辑
- **保持错误处理**：删除失败时不会触发跳转
- **维持状态管理**：不影响作品选择、缓存清理等现有机制

### 2. 项目编辑工作流兼容

- **路由参数保持**：确保跳转后项目编辑流程能正常继续
- **状态重置**：跳转到第一步时会触发相应的状态重置逻辑
- **数据获取**：第一步页面会重新获取项目数据

## 测试场景

### 1. 单个删除测试

1. 在预览页面中只保留一个作品
2. 删除该作品
3. 验证是否自动跳转到第一步页面

### 2. 批量删除测试

1. 在预览页面中选择所有作品进行批量删除
2. 验证是否自动跳转到第一步页面

### 3. 边界情况测试

1. 在第一步页面时删除作品（不应跳转）
2. 删除失败时（不应跳转）
3. 删除后仍有剩余作品时（不应跳转）

## 注意事项

1. **仅在预览模式触发**：只有在第二步（预览页面）删除最后一个作品时才会跳转
2. **保持项目状态**：跳转不会影响项目的保存状态和编辑进度
3. **用户体验**：跳转是静默进行的，用户会看到页面自然切换到第一步
4. **错误恢复**：如果跳转失败，用户仍可手动切换到第一步页面
