/**
 * 浏览器特定的全屏 API 类型定义
 */
interface BrowserDocument extends Document {
  webkitFullscreenEnabled?: boolean;
  mozFullScreenEnabled?: boolean;
  msFullscreenEnabled?: boolean;
  webkitFullscreenElement?: Element | null;
  mozFullScreenElement?: Element | null;
  msFullscreenElement?: Element | null;
  webkitExitFullscreen?: () => Promise<void>;
  mozCancelFullScreen?: () => Promise<void>;
  msExitFullscreen?: () => Promise<void>;
}

interface BrowserElement extends HTMLElement {
  webkitRequestFullscreen?: () => Promise<void>;
  mozRequestFullScreen?: () => Promise<void>;
  msRequestFullscreen?: () => Promise<void>;
}

/**
 * 格式化时间为 MM:SS 格式
 * @param seconds 时间（秒）
 * @returns 格式化后的时间字符串
 */
export function formatTime(seconds: number): string {
  if (isNaN(seconds) || !isFinite(seconds) || seconds < 0) {
    return '00:00';
  }

  const mins = Math.floor(seconds / 60);
  const secs = Math.floor(seconds % 60);

  return `${mins.toString().padStart(2, '0')}:${secs
    .toString()
    .padStart(2, '0')}`;
}

/**
 * 计算进度百分比
 * @param current 当前值
 * @param total 总值
 * @returns 百分比值（0-100）
 */
export function calculatePercent(current: number, total: number): number {
  if (total <= 0 || current < 0) return 0;
  return Math.min((current / total) * 100, 100);
}

/**
 * 从事件中获取进度位置
 * @param event 鼠标事件
 * @param element 目标元素
 * @returns 位置比例（0-1）
 */
export function getProgressFromEvent(
  event: MouseEvent,
  element: HTMLElement,
): number {
  const rect = element.getBoundingClientRect();
  let position = (event.clientX - rect.left) / rect.width;
  return Math.max(0, Math.min(1, position));
}

/**
 * 获取缓冲进度
 * @param buffered TimeRanges对象
 * @param duration 视频总时长
 * @returns 缓冲进度百分比（0-100）
 */
export function getBufferedPercent(
  buffered: TimeRanges,
  duration: number,
): number {
  if (!buffered || buffered.length === 0 || duration <= 0) return 0;
  return calculatePercent(buffered.end(buffered.length - 1), duration);
}

/**
 * 检查浏览器是否支持全屏
 * @returns 是否支持全屏
 */
export function isFullscreenEnabled(): boolean {
  const doc = document as BrowserDocument;
  return (
    doc.fullscreenEnabled ||
    doc.webkitFullscreenEnabled ||
    doc.mozFullScreenEnabled ||
    doc.msFullscreenEnabled ||
    false
  );
}

/**
 * 检查元素是否处于全屏状态
 * @returns 是否全屏
 */
export function isFullscreen(): boolean {
  const doc = document as BrowserDocument;
  return !!(
    doc.fullscreenElement ||
    doc.webkitFullscreenElement ||
    doc.mozFullScreenElement ||
    doc.msFullscreenElement
  );
}

/**
 * 请求元素全屏
 * @param element 目标元素
 * @returns Promise
 */
export async function requestFullscreen(element: HTMLElement): Promise<void> {
  const el = element as BrowserElement;
  if (el.requestFullscreen) {
    return el.requestFullscreen();
  } else if (el.webkitRequestFullscreen) {
    return el.webkitRequestFullscreen();
  } else if (el.mozRequestFullScreen) {
    return el.mozRequestFullScreen();
  } else if (el.msRequestFullscreen) {
    return el.msRequestFullscreen();
  }
}

/**
 * 退出全屏
 * @returns Promise
 */
export async function exitFullscreen(): Promise<void> {
  const doc = document as BrowserDocument;
  if (doc.exitFullscreen) {
    return doc.exitFullscreen();
  } else if (doc.webkitExitFullscreen) {
    return doc.webkitExitFullscreen();
  } else if (doc.mozCancelFullScreen) {
    return doc.mozCancelFullScreen();
  } else if (doc.msExitFullscreen) {
    return doc.msExitFullscreen();
  }
}
