import { INFO_KEYS } from '@/constants/material';
import { ExtraData } from '@/types/Material';
/**
 * 文件类型枚举
 */
export enum FileType {
  /** 全部 */
  ALL = 0,
  /** 图片 */
  IMAGE = 1,
  /** 视频 */
  VIDEO = 2,
  /** 文件夹 */
  FOLDER = 3,
}

// 定义类型
export interface MaterialManageInfo {
  [INFO_KEYS.ID]: number; // 文件id
  [INFO_KEYS.NAME]: string; // 文件名称
  [INFO_KEYS.TYPE]?: number; // 文件类型
  [INFO_KEYS.CHILDREN]?: MaterialManageInfo[]; // 子文件夹
  [INFO_KEYS.SELECT]?: boolean; // 是否选中
  [INFO_KEYS.CREATE_TIME]?: number; // 文件创建时间
  [INFO_KEYS.FILE_SIZE]?: number; // 文件大小
  [INFO_KEYS.FILE_TYPE]: number; // 文件类型
  [INFO_KEYS.PARENT_ID]?: string | number; // 父文件夹id
  [INFO_KEYS.EXTRA]?: ExtraData; // 文件额外信息
  [INFO_KEYS.RES_ID]?: string; // 文件资源id
  [INFO_KEYS.FILE_PERCENT]?: number; // 文件上传进度
  [key: string]: unknown; // 其他信息
}
