import type { ImageWorkItem } from '@/types/Work';

/**
 * 格式化图文作品的内容显示
 * @description 将script.content与script.hashtags进行拼接显示
 * @param script - 图文作品的脚本对象
 * @param defaultContent - 当内容为空时的默认显示文本
 * @returns 格式化后的内容字符串
 */
export function formatImageWorkContent(
  script: ImageWorkItem['script'],
  defaultContent: string = '暂无内容',
): string {
  if (!script) return defaultContent;

  const content = script.content || '';
  const hashtags = script.hashtags || [];

  // 如果没有hashtags，直接返回content或默认值
  if (hashtags.length === 0) {
    return content || defaultContent;
  }

  // 将hashtags数组用空格连接
  const hashtagsText = hashtags.join(' ');

  // 如果有content，用换行符连接；否则只返回hashtags
  return content ? `${content}\n${hashtagsText}` : hashtagsText;
}

/**
 * 格式化图文作品的内容显示（从完整的作品对象中提取）
 * @description 从ImageWorkItem对象中提取script并格式化内容
 * @param workItem - 图文作品对象
 * @param defaultContent - 当内容为空时的默认显示文本
 * @returns 格式化后的内容字符串
 */
export function formatImageWorkContentFromItem(
  workItem: ImageWorkItem | undefined,
  defaultContent: string = '暂无内容',
): string {
  return formatImageWorkContent(workItem?.script, defaultContent);
}
