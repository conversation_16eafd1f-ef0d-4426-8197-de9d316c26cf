import {
  PLAYER_DEFAULT_CONFIG,
  PLAYER_STYLE,
} from '@/views/EditProjectView/pages/SecondStepVideo/components/VideoPlayer/constants/player.constants.ts';

/**
 * 视频播放器配置
 */
export const playerConfig = {
  /**
   * 播放器基础配置
   */
  player: {
    /** 自动播放 */
    autoplay: false,
    /** 显示控制栏 */
    controls: false,
    /** 响应式布局 */
    responsive: true,
    /** 流式布局 */
    fluid: false,
    /** 视频比例 */
    // aspectRatio: PLAYER_DEFAULT_CONFIG.ASPECT_RATIO,
    /** 预加载 - 设置为none，只有用户点击播放时才加载 */
    preload: 'none',
    /** 行内播放 */
    playsinline: true,
    /** 禁用下载功能 */
    controlsList: 'nodownload',
    /** 禁用用户点击切换播放行为 */
    userActions: {
      click: false,
    },
  },

  /**
   * 样式配置
   */
  style: {
    /** 最大宽度 */
    maxWidth: PLAYER_STYLE.MAX_WIDTH,
    /** 小屏幕最大宽度 */
    maxWidthSmall: PLAYER_STYLE.MAX_WIDTH_SMALL,
    /** 小屏幕断点 */
    smallScreenBreakpoint: PLAYER_STYLE.SMALL_SCREEN_BREAKPOINT,
  },

  /**
   * 控制配置
   */
  controls: {
    /** 控制栏自动隐藏时间 */
    hideDelay: PLAYER_DEFAULT_CONFIG.CONTROLS_HIDE_DELAY,
    /** 默认音量 */
    defaultVolume: PLAYER_DEFAULT_CONFIG.DEFAULT_VOLUME,
  },

  /**
   * 事件配置
   */
  events: {
    /** 需要监听的事件列表 */
    listeners: [
      'loadedmetadata',
      'loadeddata',
      'timeupdate',
      'progress',
      'play',
      'pause',
      'ended',
      'seeking',
      'seeked',
      'error',
    ],
  },
} as const;
