/// <reference types="vite/client" />

declare module '*.vue' {
  import Vue from 'vue';
  export default typeof Vue;
}

// vue-draggable-resizable 没有类型声明文件，手动添加一个
// 文档在这：https://mauricius.github.io/vue-draggable-resizable/#/
declare module 'vue-draggable-resizable' {
  import Vue from 'vue';
  export default typeof Vue;
}

declare module 'vuex' {
  export * from 'vuex/types/index.d.ts';
  export * from 'vuex/types/helpers.d.ts';
  export * from 'vuex/types/logger.d.ts';
  export * from 'vuex/types/vue.d.ts';
}
