# ICONS 文档

## 图标库

我们使用 **Codesign** 图标库，图标库地址为：[https://codesign.qq.com/app/icon/543819364371142/detail](https://codesign.qq.com/app/icon/543819364371142/detail)。

## 图标组件

项目中以图标组件的方式使用图标，图标组件路径为：`src/components/comm/Icon.vue`。该组件已全局引入，可以直接在任意位置使用。

### 使用示例

```vue
<template>
  <div>
    <!-- 使用图标组件，其中xxx是图标名 -->
    <Icon type="xxx" />
  </div>
</template>
```

### 图标组件实现

图标组件内部通过 **SVG** 的方式加载和渲染图标，确保图标的清晰度和可扩展性。

## 图标源文件

图标的源文件位于：`src/assets/iconfont.js`。如果需要更新图标，只需替换该文件即可完成更新。

### 更新步骤

1. 从 [Codesign 图标库](https://codesign.qq.com/app/icon/543819364371142/detail) 下载最新的图标文件；
2. 替换项目中的 `src/assets/iconfont.js` 文件；
