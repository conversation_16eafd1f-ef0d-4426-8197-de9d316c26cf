# 数据保护功能使用指南

## 概述

数据保护功能通过 `useDataProtection` composable 提供了一个简洁易用的 API，整合了浏览器保护、EventBus 通信、表单变更检测等功能，防止用户意外丢失未保存的数据。

## 主要优势

### 相比旧版本的改进

1. **统一入口**：所有数据保护功能集中在一个 composable 中
2. **简化 API**：无需手动管理 EventBus 事件监听
3. **自动化管理**：自动处理生命周期、事件注册/注销
4. **更好的类型支持**：完整的 TypeScript 类型定义
5. **向后兼容**：保持现有功能的完整性

### 功能特性

- ✅ **浏览器刷新/关闭保护**：自动拦截浏览器操作
- ✅ **退出确认保护**：通过 EventBus 与其他组件通信
- ✅ **表单变更检测**：智能检测各种表单元素变更
- ✅ **用户操作记录**：记录标签删除和 AI 建议标签添加操作
- ✅ **静默期机制**：防止保存后误触发
- ✅ **KeepAlive 支持**：完整支持 Vue 缓存场景
- ✅ **防抖优化**：避免频繁的变更检测

## 基本用法

### 1. 在步骤组件中使用

```typescript
// src/views/EditProjectView/pages/FirstStep/index.vue

import { useDataProtection } from '@/composables/useDataProtection';

export default defineComponent({
  setup() {
    // 使用统一的数据保护功能
    const dataProtection = useDataProtection({
      stepIndex: 0, // 当前步骤索引
      enableBrowserProtection: true, // 启用浏览器保护
      enableExitProtection: true, // 启用退出确认保护
    });

    // 解构需要的方法和状态
    const {
      hasUnsavedChanges,
      resetUnsavedChanges,
      setUnsavedChanges,
      reactivate: reactivateUnsavedChanges,
    } = dataProtection;

    return {
      hasUnsavedChanges,
      resetUnsavedChanges,
      setUnsavedChanges,
      reactivateUnsavedChanges,
    };
  },
});
```

### 2. 在其他组件中检查未保存更改

```typescript
// src/views/EditProjectView/components/ProjectHeader/index.vue

import { checkUnsavedChangesBeforeLeave } from '@/composables/useDataProtection';

export default defineComponent({
  setup(props) {
    const checkUnsavedChanges = async (): Promise<boolean> => {
      // 仅在第一步时检查
      if (props.currentStep !== 0) {
        return false;
      }

      // 使用工具函数检查未保存更改
      return checkUnsavedChangesBeforeLeave(props.currentStep);
    };

    const goHome = async () => {
      const hasUnsavedChanges = await checkUnsavedChanges();
      if (hasUnsavedChanges) {
        const confirmed = window.confirm('您有未保存的更改，确定要离开吗？');
        if (!confirmed) return;
      }

      // 执行跳转逻辑
      router.push('/home');
    };

    return { goHome };
  },
});
```

## 配置选项

### DataProtectionOptions

```typescript
interface DataProtectionOptions {
  /** 当前步骤索引，用于 EventBus 通信 */
  stepIndex?: number;
  /** 是否启用浏览器刷新/关闭保护 */
  enableBrowserProtection?: boolean;
  /** 是否启用退出确认保护（EventBus） */
  enableExitProtection?: boolean;
  /** 防抖延迟时间（毫秒），默认 300ms */
  debounceDelay?: number;
  /** 静默期时长（毫秒），默认 1000ms */
  silentPeriodDuration?: number;
}
```

### 配置示例

```typescript
// 仅启用浏览器保护（不参与 EventBus 通信）
const dataProtection = useDataProtection({
  enableBrowserProtection: true,
  enableExitProtection: false,
});

// 自定义防抖和静默期时长
const dataProtection = useDataProtection({
  stepIndex: 1,
  debounceDelay: 500, // 500ms 防抖
  silentPeriodDuration: 2000, // 2秒静默期
});

// 完整配置
const dataProtection = useDataProtection({
  stepIndex: 0,
  enableBrowserProtection: true,
  enableExitProtection: true,
  debounceDelay: 300,
  silentPeriodDuration: 1000,
});
```

## 返回接口

### DataProtectionReturn

```typescript
interface DataProtectionReturn {
  /** 是否有未保存的更改（只读） */
  hasUnsavedChanges: Readonly<Ref<boolean>>;
  /** 是否已初始化（只读） */
  isInitialized: Readonly<Ref<boolean>>;
  /** 是否处于静默期（只读） */
  isSilentPeriod: Readonly<Ref<boolean>>;
  /** 手动设置未保存状态 */
  setUnsavedChanges: (value: boolean) => void;
  /** 重置未保存状态（保存后调用） */
  resetUnsavedChanges: () => void;
  /** 初始化数据保护功能 */
  initialize: () => void;
  /** 清理数据保护功能 */
  cleanup: () => void;
  /** 重新激活（KeepAlive 场景） */
  reactivate: () => void;
}
```

## 用户操作记录功能

### 功能说明

数据保护系统内置了用户操作记录功能，能够自动监听并记录用户在表单中的关键操作行为，包括标签删除和 AI 建议标签添加操作。

### 支持的操作类型

#### 1. 标签删除操作记录

**触发条件：** 用户点击 selectTags 类型组件中的标签删除按钮（`.fa-select-selection__choice__remove`）

**记录内容：**

```javascript
{
  操作类型: '删除操作',
  表单项: '字段标签名称',
  删除内容: '被删除的标签内容',
  时间戳: '2024-01-01T12:00:00.000Z'
}
```

#### 2. AI 建议标签添加操作记录

**触发条件：** 用户点击 AI 推荐标签（`.ai-suggestion__tag`）进行添加

**记录内容：**

```javascript
{
  操作类型: '新增操作',
  表单项: '字段标签名称',
  添加内容: '被添加的标签内容',
  来源: 'AI建议',
  时间戳: '2024-01-01T12:00:00.000Z'
}
```

### 查看操作记录

操作记录会通过 `console.log` 输出到浏览器控制台：

1. 打开浏览器开发者工具
2. 切换到 Console 标签页
3. 执行标签删除或 AI 标签添加操作
4. 查看相应的日志输出（以 `🚀 ADI-LOG ~` 开头）

### 技术实现

- **事件监听：** 使用 document 级别的事件委托，监听 click 事件
- **DOM 解析：** 从事件目标元素中提取操作相关的数据信息
- **状态触发：** 记录操作后会触发数据保护机制，设置未保存状态
- **性能优化：** 与现有的防抖和静默期机制完全兼容

## 常见使用场景

### 1. 保存成功后重置状态

```typescript
const saveProject = async () => {
  // 执行保存逻辑
  const [err, res] = await saveProjectData(formData);

  if (!err) {
    // 保存成功后重置未保存状态
    resetUnsavedChanges();
    message.success('保存成功');
  }
};
```

### 2. 手动设置未保存状态

```typescript
const handleFormChange = () => {
  // 在特定操作后手动设置未保存状态
  setUnsavedChanges(true);
};

const handleFormReset = () => {
  // 重置表单后清除未保存状态
  setUnsavedChanges(false);
};
```

### 3. KeepAlive 场景处理

```typescript
// 组件会自动处理 KeepAlive 场景，无需手动调用
// 但如果需要手动重新激活：
onActivated(() => {
  reactivateUnsavedChanges();
});
```

## 迁移指南

### 从旧版本迁移

#### 旧版本代码

```typescript
// 旧版本：需要手动管理多个部分
import { useUnsavedChanges } from './composables/useUnsavedChanges';
import { eventBus, EVENT_NAMES } from '@/utils/eventBus';

const { hasUnsavedChanges, resetUnsavedChanges } = useUnsavedChanges();

// 手动管理 EventBus 事件
const handleCheckUnsavedChanges = (...args: unknown[]) => {
  // 复杂的事件处理逻辑
};

onMounted(() => {
  eventBus.on(EVENT_NAMES.CHECK_UNSAVED_CHANGES, handleCheckUnsavedChanges);
});

onUnmounted(() => {
  eventBus.off(EVENT_NAMES.CHECK_UNSAVED_CHANGES, handleCheckUnsavedChanges);
});
```

#### 新版本代码

```typescript
// 新版本：一行代码搞定
import { useDataProtection } from '@/composables/useDataProtection';

const { hasUnsavedChanges, resetUnsavedChanges } = useDataProtection({
  stepIndex: 0,
  enableBrowserProtection: true,
  enableExitProtection: true,
});
```

### 迁移步骤

1. **替换导入**：将 `useUnsavedChanges` 替换为 `useDataProtection`
2. **移除 EventBus 代码**：删除手动的 EventBus 事件管理代码
3. **更新配置**：根据需要配置 `DataProtectionOptions`
4. **测试功能**：确保所有数据保护功能正常工作

## 注意事项

1. **stepIndex 参数**：只有需要参与 EventBus 通信的组件才需要设置
2. **生命周期管理**：composable 会自动管理生命周期，无需手动调用
3. **静默期机制**：保存后会自动进入静默期，避免误触发
4. **性能考虑**：使用防抖机制优化性能，避免频繁检测
5. **浏览器兼容性**：现代浏览器会忽略自定义 beforeunload 消息
6. **操作记录功能**：用户操作记录会自动启用，通过控制台查看详细日志

## 常见问题解答

### Q: 保存后仍然提示有未保存更改怎么办？

A: 这通常是由于保存操作后的 UI 更新触发了变更检测。新版本通过静默期机制解决了这个问题：

- 保存成功后会自动进入 1 秒静默期
- 静默期内的表单变更事件会被忽略
- 确保调用 `resetUnsavedChanges()` 方法重置状态

### Q: 如何在其他步骤中使用数据保护？

A: 只需要在组件中使用 `useDataProtection` 并设置对应的 `stepIndex`：

```typescript
// 第二步组件中
const dataProtection = useDataProtection({
  stepIndex: 1, // 第二步的索引
  enableBrowserProtection: true,
  enableExitProtection: true,
});
```

### Q: 如何自定义防抖延迟和静默期时长？

A: 通过配置选项进行自定义：

```typescript
const dataProtection = useDataProtection({
  debounceDelay: 500, // 500ms 防抖延迟
  silentPeriodDuration: 2000, // 2秒静默期
});
```

### Q: 如何查看用户操作记录？

A: 用户操作记录会自动输出到浏览器控制台：

1. 打开浏览器开发者工具（F12）
2. 切换到 Console 标签页
3. 执行标签删除或 AI 标签添加操作
4. 查看以 `🚀 ADI-LOG ~` 开头的日志记录

操作记录包含操作类型、表单项、操作内容和时间戳等详细信息。

## 相关文件

### 代码文件

- `src/composables/useDataProtection.ts` - 主要实现文件
- `src/views/EditProjectView/pages/FirstStep/index.vue` - 使用示例
- `src/views/EditProjectView/components/ProjectHeader/index.vue` - 检查示例

### 相关文档

- `docs/ADI/编辑项目视图/功能特性/数据保护功能使用指南.md` - 本文档
