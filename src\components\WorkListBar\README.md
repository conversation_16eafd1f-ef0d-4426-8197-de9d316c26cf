# WorkListBar 组件

作品列表栏组件，用于显示和管理项目中的作品列表，支持选择、删除、分页等功能。

## 组件结构

```
WorkListBar/
├── index.vue                    # 主组件
├── WorkListBarItem.vue         # 作品项组件
├── EmptyState.vue              # 空状态组件
├── types.ts                    # 类型定义
├── __tests__/                  # 测试文件
│   └── WorkListBar.test.ts     # 单元测试
└── README.md                   # 本文档
```

## 核心功能

### 1. 作品列表展示

- 支持视频和图片两种作品类型
- 无限滚动加载
- 分页管理
- 空状态处理

### 2. 作品选择管理

- 单选模式
- 全选/取消全选
- 状态同步机制

### 3. 作品删除功能 ⭐

- 智能选择下一个作品
- 保持分页状态
- 缓存清理
- 无刷新删除

## 删除功能详细说明

### 删除流程架构

```mermaid
sequenceDiagram
    participant User as 用户
    participant WLI as WorkListBarItem
    participant WLB as WorkListBar
    participant Parent as 父组件
    participant Manager as useInfiniteWorkList
    participant <PERSON><PERSON> as useWorkDetail

    User->>WLI: 点击删除按钮
    WLI->>WLI: 确认删除操作
    WLI->>Manager: removeWork(workId)
    Manager->>Manager: 调用删除API
    Manager->>Cache: clearWorkDetailCacheEntry(workId)
    Manager->>Manager: 更新displayedWorkList
    Manager->>Manager: selectNextWork(智能选择)
    Manager->>Parent: selectedWorkIds更新
    Parent->>WLB: :selectedWorkIds="[newId]"
    WLB->>WLB: 状态同步
    WLB->>WLI: :isSelected="true"
    WLI->>User: 显示新选中状态
```

### 智能选择逻辑

删除作品后，系统会自动选择下一个合适的作品：

#### 选择优先级

1. **删除中间作品** → 选择上一个作品
2. **删除第一个作品** → 选择下一个作品
3. **删除最后一个作品** → 选择上一个作品
4. **删除唯一作品** → 清空选择

### 状态同步机制

为了确保删除后的选中状态正确显示，组件实现了完整的状态同步机制：

#### 数据流向

```text
useInfiniteWorkList.selectedWorkIds
    ↓ (props)
WorkListBar.selectedIds
    ↓ (computed)
WorkListBarItem.isSelected
```

#### 同步逻辑

```typescript
// WorkListBar 组件中的状态同步
watch(
  () => props.selectedWorkIds,
  (newSelectedIds, oldSelectedIds) => {
    // 避免不必要的更新
    if (JSON.stringify(newSelectedIds) === JSON.stringify(oldSelectedIds)) {
      return;
    }

    // 标记正在从父组件更新，避免循环通知
    isUpdatingFromParent.value = true;

    // 同步父组件的选中状态到本地状态
    if (newSelectedIds && newSelectedIds.length > 0) {
      selectedIds.value = [...newSelectedIds];
      if (isAllSelected.value) {
        isAllSelected.value = false;
      }
    } else {
      selectedIds.value = [];
    }

    // 重置标记
    setTimeout(() => {
      isUpdatingFromParent.value = false;
    }, 0);
  },
  { immediate: true, deep: true },
);
```

### 性能优化

#### 1. 统一删除流程

- 所有删除操作（包括重新生成完成状态）统一使用普通删除流程
- 通过 EventBus 发送 `WORK_REMOVE_REQUEST` 事件
- 不重新请求作品列表 API，直接从本地列表中移除作品
- 保持分页状态和滚动位置

#### 2. 缓存管理

- 删除作品详情缓存：`clearWorkDetailCacheEntry(workId)`
- 避免内存泄漏
- 确保数据一致性

#### 3. 防重复操作

```typescript
const isDeleting = ref(false);

const removeWork = async (workId: number) => {
  if (isDeleting.value) {
    console.warn('作品正在删除中，请勿重复操作');
    return false;
  }

  isDeleting.value = true;
  try {
    // 删除逻辑
  } finally {
    isDeleting.value = false;
  }
};
```

## 使用方式

### 基本用法

```vue
<template>
  <WorkListBar
    :workList="displayedWorkList"
    :selectedWorkIds="selectedWorkIds"
    :workListBarType="WorkType.VIDEO"
    :loading="isLoading"
    :currentPage="currentPage"
    :pageSize="pageSize"
    :totalItems="totalItems"
    :isLoadingMore="isLoadingMore"
    :hasLoadedAll="hasLoadedAll"
    @select-change="handleSelectChange"
    @all-select-change="handleAllSelectChange"
    @scroll-end="handleScrollEnd"
    @remove-work="handleRemoveWork"
  />
</template>

<script>
import { useInfiniteVideoWorkList } from '@/composables';

export default {
  setup() {
    const {
      displayedWorkList,
      selectedWorkIds,
      handleSelectChange,
      handleRemoveWork,
      // ... 其他状态和方法
    } = useInfiniteVideoWorkList(projectId);

    return {
      displayedWorkList,
      selectedWorkIds,
      handleSelectChange,
      handleRemoveWork,
    };
  },
};
</script>
```

### Props 说明

| 属性 | 类型 | 必填 | 默认值 | 说明 |
| --- | --- | --- | --- | --- |
| workList | `WorkItem[]` | 是 | `[]` | 作品列表数据 |
| selectedWorkIds | `number[]` | 是 | `[]` | 选中的作品 ID 列表 |
| workListBarType | `WorkTypeValue` | 是 | `WorkType.VIDEO` | 作品类型 |
| loading | `boolean` | 否 | `false` | 加载状态 |
| currentPage | `number` | 否 | `1` | 当前页码 |
| pageSize | `number` | 否 | `10` | 每页条数 |
| totalItems | `number` | 否 | `0` | 总条数 |
| isLoadingMore | `boolean` | 否 | `false` | 是否正在加载更多 |
| hasLoadedAll | `boolean` | 否 | `false` | 是否已加载全部 |

### Events 说明

| 事件              | 参数                 | 说明         |
| ----------------- | -------------------- | ------------ |
| select-change     | `ids: number[]`      | 选中状态变化 |
| all-select-change | `isAll: boolean`     | 全选状态变化 |
| scroll-end        | -                    | 滚动到底部   |
| remove-work       | `removeData: object` | 删除作品事件 |
| refresh           | -                    | 刷新列表     |

## 故障排查

### 常见问题

1. **删除后没有自动选中新作品**

   - 检查 `selectedWorkIds` prop 是否正确传递
   - 查看控制台是否有错误日志
   - 确认 `handleRemoveWork` 方法是否正确调用

2. **选中状态显示错误**

   - 检查 `isSelected` 计算逻辑
   - 确认 `selectedIds` 数组内容
   - 排查是否有重复的作品 ID

3. **状态同步延迟**
   - 检查 `isUpdatingFromParent` 标记
   - 确认 `setTimeout` 是否正确执行
   - 验证 Vue 响应式更新

### 调试工具

组件提供了完整的状态同步机制，可以通过 Vue DevTools 查看组件状态变化。

## 技术实现细节

### 删除功能的关键技术点

#### 1. 事件传递优化

**问题**：原始实现中事件传递链路过长（6 层），导致代码复杂且难以维护。

**解决方案**：使用 `provide/inject` 模式简化事件传递：

```typescript
// 父组件提供统一的作品操作接口
const workListActions = {
  removeWork,
  updateWorkItems,
  handleSaveUpdate,
  handleEditUpdate,
  handleGenerateUpdate,
  refreshWorkList,
};
provide('workListActions', workListActions);

// WorkListBarItem 直接注入使用
const workListActions = inject('workListActions');
const removeWork = workListActions?.removeWork;
```

#### 2. 状态同步的竞态条件处理

**问题**：删除操作和状态更新可能产生竞态条件。

**解决方案**：使用标记和异步队列：

```typescript
const isUpdatingFromParent = ref(false);

// 避免循环更新
watch(selectedIds, newIds => {
  if (!isAllSelected.value && !isUpdatingFromParent.value) {
    emit('select-change', newIds);
  }
});
```

#### 3. 内存管理

**问题**：删除作品后可能存在内存泄漏。

**解决方案**：完整的清理机制：

```typescript
const removeWork = async (workId: number) => {
  // 1. API删除
  await deleteWork(workId);

  // 2. 清理详情缓存
  clearWorkDetailCacheEntry(workId);

  // 3. 更新列表数据
  displayedWorkList.value = displayedWorkList.value.filter(
    item => item.id !== workId,
  );

  // 4. 智能选择下一个作品
  selectNextWork(workId);
};
```

### 架构设计原则

#### 1. 单一职责原则

- `WorkListBar`：负责列表展示和状态管理
- `WorkListBarItem`：负责单个作品项的展示和操作
- `useInfiniteWorkList`：负责数据管理和业务逻辑

#### 2. 数据流单向性

```text
数据源 → useInfiniteWorkList → 父组件 → WorkListBar → WorkListBarItem
```

#### 3. 错误边界处理

```typescript
// 边界检查
if (!workId || workId <= 0) {
  console.warn('无效的作品ID:', workId);
  return false;
}

// 存在性检查
const workExists = displayedWorkList.value.some(item => item.id === workId);
if (!workExists) {
  console.warn('作品不存在于当前列表中:', workId);
  return false;
}
```

## 性能监控

### 关键指标

- **删除响应时间**：< 200ms
- **状态同步延迟**：< 50ms
- **内存使用**：删除后无明显增长
- **UI 流畅度**：60fps

### 监控方法

```javascript
// 性能监控示例
console.time('removeWork');
await removeWork(workId);
console.timeEnd('removeWork');

// 内存监控
console.log('Memory usage:', performance.memory);
```

## 扩展指南

### 添加新的删除策略

```typescript
// 扩展删除策略
enum DeleteStrategy {
  SMART = 'smart', // 智能选择
  FIRST = 'first', // 总是选择第一个
  LAST = 'last', // 总是选择最后一个
  NONE = 'none', // 不自动选择
}

const selectNextWork = (removedWorkId: number, strategy: DeleteStrategy) => {
  switch (strategy) {
    case DeleteStrategy.SMART:
      return smartSelectLogic(removedWorkId);
    case DeleteStrategy.FIRST:
      return workList.value[0]?.id || null;
    // ... 其他策略
  }
};
```

### 添加删除确认机制

```typescript
// 可配置的删除确认
interface DeleteConfig {
  requireConfirm: boolean;
  confirmMessage?: string;
  allowBatchDelete?: boolean;
}
```

## 更新日志

### v2.0.0 (2024-12)

- ✨ 新增智能删除功能
- ✨ 优化状态同步机制
- ✨ 添加缓存清理逻辑
- 🐛 修复删除后选中状态不同步问题
- 📝 完善文档和测试
- 🚀 性能优化：避免不必要的列表刷新
- 🔧 重构事件传递机制

### v1.0.0

- 🎉 初始版本
- ✨ 基础作品列表功能
- ✨ 选择和分页功能

---

## 贡献指南

### 开发流程

1. Fork 项目
2. 创建功能分支
3. 编写代码和测试
4. 提交 Pull Request

### 代码规范

- 遵循项目的 ESLint 配置
- 使用 TypeScript 严格模式
- 添加适当的注释和文档
- 确保测试覆盖率

### 提交信息格式

```text
type(scope): description

feat(WorkListBar): 添加智能删除功能
fix(WorkListBar): 修复状态同步问题
docs(WorkListBar): 更新README文档
```
