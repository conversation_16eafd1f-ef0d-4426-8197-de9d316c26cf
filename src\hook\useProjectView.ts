import { ref, computed } from 'vue';
import {
  getProjectList,
  getProjectDetailInfo,
  updateProjectName,
  copyProject,
  deleteProject,
} from '@/api/ProjectView/index';
import { ProjectData } from '@/types/Project';
import { PROJECT_STATUS, PROJECT_STATUS_LIST } from '@/constants/project';
import { message } from '@fk/faicomponent';
import { showConfirmModal } from '@/components/comm/ScModalConfirm/index';
import { debounce } from 'lodash-es';

// States
export const tabList = ref(PROJECT_STATUS_LIST);
export const status = ref(PROJECT_STATUS.ALL);
export const spinning = ref(false);
export const searchText = ref('');
export const currentPage = ref(1);
export const pageSize = ref(11);
export const total = ref(0);
export const projectList = ref<ProjectData[]>([]);
export const isShowDetailModal = ref(false);
export const isShowEmptyState = ref(true);
export const currentProject = ref<ProjectData | null>(null);

// Computed properties
export const queryParams = computed(() => {
  return {
    pageNow: currentPage.value,
    limit: pageSize.value,
    // 按照状态筛选
    status: status.value === -1 ? undefined : status.value,
    // 按照项目名称筛选
    name: searchText.value ? searchText.value : undefined,
  };
});

// 重置相关状态
export const resetProjectViewState = () => {
  status.value = PROJECT_STATUS.ALL;
  searchText.value = '';
  currentPage.value = 1;
  pageSize.value = 11;
  isShowEmptyState.value = false;
};

// 获取项目列表
export const fetchProjectList = async (showSpinning: boolean = true) => {
  if (showSpinning) spinning.value = true;
  const [err, res] = await getProjectList(queryParams.value);
  if (showSpinning) spinning.value = false;
  if (err) {
    message.error(err.message || '获取项目列表失败');
    return;
  }
  const data = res.data || []; // 后端返回的项目列表数据

  // 如果当前页没有数据，回退到上一页（场景：当前在最后一页，删除当前页面所有项目，分页条需要更新，页面显示当前最后一页的数据）
  if (currentPage.value !== 1 && data.length === 0) {
    currentPage.value -= 1;
    await fetchProjectList(); // 重新获取数据
    return;
  }
  // 更新项目列表和总数
  projectList.value = data;
  total.value = res.total || 0;

  const { status, name } = queryParams.value;
  if (res.total === 0 && (status !== undefined || name !== '')) {
    isShowEmptyState.value = true;
  } else {
    isShowEmptyState.value = false;
  }
};

// 切换状态标签
export const handleTabChange = (newStatus: number) => {
  status.value = newStatus;
  currentPage.value = 1;
  fetchProjectList();
};

// 处理搜索项目名称
export const handleSearchText = (val: string) => {
  if (val === searchText.value) return;
  searchText.value = val;
  currentPage.value = 1;
  fetchProjectList();
};

// 刷新项目列表
export const handleRefresh = () => {
  fetchProjectList();
};

// 处理项目点击
export const handleShowDetail = async (project: ProjectData) => {
  const [err, res] = await getProjectDetailInfo(project.id || 0);
  if (err) {
    message.error(err.message || '获取项目详情失败');
    return;
  }
  currentProject.value = res.data;
  isShowDetailModal.value = true;
};

// 处理修改项目名
export const handleUpdateProjectName = async (params: {
  id: number;
  name: string;
}) => {
  spinning.value = true;
  const [err, res] = await updateProjectName(params);
  spinning.value = false;
  if (err) {
    message.error(err.message || '修改失败');
    return;
  }
  message.success(res.msg || '修改成功');
  fetchProjectList();
};

// 处理复制项目
export const handleCopy = async (project: ProjectData) => {
  spinning.value = true;
  const [err, res] = await copyProject(project.id);
  spinning.value = false;
  if (err) {
    message.error(err.message || '复制失败');
    return;
  }
  message.success(res.msg || '复制成功');
  fetchProjectList();
};

/**
 * 获取项目不同状态下的删除文案
 * @param status
 * @returns
 */
const getDeleteConfirmcontent = (status: number) => {
  const textMap: Record<number, string> = {
    [PROJECT_STATUS.DRAFT]: '删除后，项目将无法恢复，确定删除该项目吗？', // 草稿
    [PROJECT_STATUS.GENERATING]:
      '当前项目有正在生成的视频，删除后将全部丢失，确定删除该项目吗？', // 生成中
    [PROJECT_STATUS.TO_BE_SAVED]:
      '当前项目有待保存的作品，删除后将永久丢失，确定删除该项目吗？', // 待保存
    [PROJECT_STATUS.COMPLETED]: '删除后，项目将无法恢复，确定删除该项目吗？', // 已完成
  };
  return textMap[status];
};

// 处理删除项目的确认弹窗
export const handleDelete = (project: ProjectData) => {
  currentProject.value = project;
  const content = getDeleteConfirmcontent(project.status);
  showConfirmModal({
    content,
    okType: 'danger',
    onOk: () => {
      handleDeleteConfirm();
    },
  });
};

// 处理删除项目
const handleDeleteConfirm = async () => {
  spinning.value = true;
  if (!currentProject.value) {
    spinning.value = false;
    console.error('错误信息：当前项目为空，无法删除');
    return;
  }
  const [err, res] = await deleteProject(currentProject.value.id || 0);
  spinning.value = false;
  if (err) {
    message.error(err.message || '删除失败');
    return;
  }
  message.success(res.msg || '删除成功');
  fetchProjectList();
};

// 处理分页变化
export const handlePageChange = (page: number) => {
  currentPage.value = page;
  fetchProjectList();
};

// 处理每页条数变化
export const handleSizeChange = (size: number) => {
  pageSize.value = size;
  currentPage.value = 1;
  fetchProjectList();
};

// 添加和移除页面可见性变化监听器
export const setupVisibilityListener = () => {
  const handler = debounce(() => {
    if (document.visibilityState === 'visible') {
      fetchProjectList(false);
    }
  }, 100);

  document.addEventListener('visibilitychange', handler);

  // 返回清理函数
  return () => document.removeEventListener('visibilitychange', handler);
};
