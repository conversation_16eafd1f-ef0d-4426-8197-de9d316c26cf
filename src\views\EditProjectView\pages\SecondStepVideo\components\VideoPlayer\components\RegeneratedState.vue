<template>
  <div class="regenerated-state">
    <div class="regenerated-state__color-blocks">
      <div
        class="regenerated-state__color-block regenerated-state__color-block--1"
      ></div>
      <div
        class="regenerated-state__color-block regenerated-state__color-block--2"
      ></div>
      <div
        class="regenerated-state__color-block regenerated-state__color-block--3"
      ></div>
      <div
        class="regenerated-state__color-block regenerated-state__color-block--4"
      ></div>
      <div
        class="regenerated-state__color-block regenerated-state__color-block--black regenerated-state__color-block--black-1"
      ></div>
      <div
        class="regenerated-state__color-block regenerated-state__color-block--black regenerated-state__color-block--black-2"
      ></div>
      <div
        class="regenerated-state__color-block regenerated-state__color-block--black regenerated-state__color-block--black-3"
      ></div>
      <div
        class="regenerated-state__color-block regenerated-state__color-block--black regenerated-state__color-block--black-4"
      ></div>
    </div>
    <div class="regenerated-state__glass-layer"></div>
    <div class="regenerated-state__content">
      <div class="regenerated-state__icon">
        <img
          :src="regenerated0"
          alt="星星图标"
          class="regenerated-state__star regenerated-state__star--0"
        />
        <img
          :src="regenerated1"
          alt="星星图标"
          class="regenerated-state__star regenerated-state__star--1"
        />
      </div>
      <div class="regenerated-state__text">
        视频生成完毕，点击播放预览您的新视频
      </div>
      <div class="regenerated-state__button">
        <button @click="handlePreviewClick">预览最新视频</button>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent } from 'vue';
import regenerated0 from '@/assets/EditProject/generating.svg';
import regenerated1 from '@/assets/EditProject/generating.svg';

export default defineComponent({
  name: 'RegeneratedState',
  emits: ['preview-video'],
  setup(_, { emit }) {
    const handlePreviewClick = () => {
      emit('preview-video');
    };

    return {
      regenerated0,
      regenerated1,
      handlePreviewClick,
    };
  },
});
</script>

<style lang="scss" scoped>
.regenerated-state {
  /* 布局相关 */
  @apply zi-regenerated-state flex items-center justify-center relative;
  /* 尺寸相关 */
  @apply h-full overflow-hidden p-20px;
  /* 最小宽度限制 */
  min-width: 220px;
  /* 宽高比例 9:16，高度100%，宽度自动计算 */
  aspect-ratio: 9 / 16;
  /* 显示模式 contain 效果 */
  object-fit: contain;
}

.regenerated-state__color-blocks {
  /* 布局相关 */
  @apply absolute top-0 left-0;
  /* 尺寸相关 */
  @apply w-full h-full;
}

.regenerated-state__color-block {
  /* 布局相关 */
  @apply absolute;
  /* 外观相关 */
  @apply rounded-50%;
  filter: blur(30px);
}

.regenerated-state__color-block--1 {
  /* 尺寸相关 */
  width: 40%;
  height: 40%;
  top: 10%;
  left: 10%;
  /* 外观相关 */
  background: radial-gradient(
    circle,
    rgba(255, 40, 40, 0.8) 0%,
    rgba(255, 40, 40, 0) 70%
  );
}

.regenerated-state__color-block--2 {
  /* 尺寸相关 */
  width: 50%;
  height: 50%;
  bottom: 5%;
  right: 5%;
  /* 外观相关 */
  background: radial-gradient(
    circle,
    rgba(255, 220, 0, 0.8) 0%,
    rgba(255, 220, 0, 0) 70%
  );
}

.regenerated-state__color-block--3 {
  /* 尺寸相关 */
  width: 30%;
  height: 30%;
  top: 20%;
  right: 20%;
  /* 外观相关 */
  background: radial-gradient(
    circle,
    rgba(40, 200, 80, 0.8) 0%,
    rgba(40, 200, 80, 0) 70%
  );
}

.regenerated-state__color-block--4 {
  /* 尺寸相关 */
  width: 45%;
  height: 45%;
  bottom: 10%;
  left: 15%;
  /* 外观相关 */
  background: radial-gradient(
    circle,
    rgba(145, 40, 255, 0.8) 0%,
    rgba(145, 40, 255, 0) 70%
  );
}

.regenerated-state__color-block--black {
  /* 外观相关 */
  background: radial-gradient(
    circle,
    rgba(0, 0, 0, 0.7) 0%,
    rgba(0, 0, 0, 0) 70%
  );
  filter: blur(30px);
}

.regenerated-state__color-block--black-1 {
  /* 尺寸相关 */
  width: 60%;
  height: 30%;
  top: -5%;
  left: -10%;
}

.regenerated-state__color-block--black-2 {
  /* 尺寸相关 */
  width: 40%;
  height: 40%;
  bottom: -10%;
  right: -5%;
}

.regenerated-state__color-block--black-3 {
  /* 尺寸相关 */
  width: 35%;
  height: 35%;
  top: -8%;
  right: -5%;
}

.regenerated-state__color-block--black-4 {
  /* 尺寸相关 */
  width: 45%;
  height: 25%;
  bottom: -5%;
  left: -8%;
}

.regenerated-state__glass-layer {
  /* 布局相关 */
  @apply absolute top-0 left-0;
  /* 尺寸相关 */
  @apply w-full h-full;
  /* 外观相关 */
  @apply bg-black bg-opacity-60;
  backdrop-filter: blur(30px);
  -webkit-backdrop-filter: blur(30px);
}

.regenerated-state__content {
  /* 布局相关 */
  @apply flex flex-col items-center relative;
  /* 文字相关 */
  @apply text-white;
  /* 动画相关 */
  animation: fadeIn 0.3s ease-out forwards;
}

.regenerated-state__icon {
  /* 布局相关 */
  @apply relative;
  /* 尺寸相关 */
  @apply w-56px h-56px;
  /* 动画相关 */
  animation: fadeInScale 0.3s ease-out forwards;
}

.regenerated-state__star {
  /* 布局相关 */
  @apply absolute top-0 left-0;
  /* 尺寸相关 */
  @apply w-full h-full;
  /* 过渡相关 */
  transition: opacity 0.3s ease-in-out;
}

.regenerated-state__star--0 {
  /* 外观相关 */
  @apply opacity-100;
  /* 动画相关 */
  animation: twinkle 2s ease-in-out infinite;
}

.regenerated-state__star--1 {
  /* 外观相关 */
  @apply opacity-0;
  /* 动画相关 */
  animation: twinkle 2s ease-in-out infinite reverse;
}

.regenerated-state__text {
  /* 布局相关 */
  @apply text-center;
  /* 尺寸相关 */
  margin-top: 30px;
  /* 文字相关 */
  @apply text-white font-normal text-16px;
  /* 动画相关 */
  animation: fadeInUp 0.3s ease-out 0.1s both;
}

.regenerated-state__button {
  /* 尺寸相关 */
  margin-top: 24px;
  /* 动画相关 */
  animation: fadeInUp 0.3s ease-out 0.2s both;
}

.regenerated-state__button button {
  /* 布局相关 */
  @apply text-center;
  /* 尺寸相关 */
  @apply min-w-160px h-40px px-25px py-10px;
  /* 外观相关 */
  @apply rounded-8px;
  background: linear-gradient(92.19deg, #105fff 0%, #7923f9 100%);
  /* 文字相关 */
  @apply text-white font-bold text-14px;
  /* 过渡相关 */
  transition: box-shadow 0.2s ease;
}

.regenerated-state__button button:hover {
  /* 外观相关 */
  box-shadow: 0 2px 8px rgba(72, 98, 234, 0.4);
}

.regenerated-state__button button:active {
  /* 外观相关 */
  box-shadow: 0 1px 4px rgba(72, 98, 234, 0.3);
}

/* 动画定义 */
@keyframes twinkle {
  0% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0;
    transform: scale(0.92);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes fadeInScale {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
