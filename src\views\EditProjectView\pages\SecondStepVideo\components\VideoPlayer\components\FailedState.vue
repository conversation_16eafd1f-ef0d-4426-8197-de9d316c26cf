<template>
  <div class="failed-state">
    <div class="failed-state__glass-layer"></div>
    <div class="failed-state__content">
      <div class="failed-state__icon">
        <img
          :src="workFailedIcon"
          alt="失败图标"
          class="failed-state__icon-img"
        />
      </div>
      <div class="failed-state__title">{{ finalErrTitle }}</div>
      <div class="failed-state__description">失败原因：{{ errMsg }}</div>
    </div>
    <div v-if="showRetryButton" class="failed-state__footer">
      <fa-button
        class="failed-state__btn special-dark__btn"
        @click="handleRetry"
        >恢复旧作品</fa-button
      >
    </div>
  </div>
</template>

<script setup lang="ts">
import workFailedIcon from '@/assets/EditProject/workFailedIcon.svg';
import { PROJECT_TYPE_VIDEO } from '@/views/EditProjectView/constants';
import { computed } from 'vue';

interface Props {
  /** 错误标题 */
  errTitle?: string;
  /** 错误信息 */
  errMsg?: string;
  /** 是否显示重试按钮 */
  showRetryButton?: boolean;
  /** 作品类型（0=视频，1=图文） */
  workType?: number;
  /** 是否需要扣除点数（true=需要扣点，false=不需要扣点） */
  pinchPoint?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  errMsg: '生成失败',
  showRetryButton: false,
  workType: PROJECT_TYPE_VIDEO, // 默认为视频类型以保持向后兼容
});

// 根据是否需要扣点计算默认错误标题
const defaultErrTitle = computed(() => {
  const shouldShowPointsRefund = props.pinchPoint === true;

  if (shouldShowPointsRefund) {
    return '作品生成失败，创作点数已自动返还';
  } else {
    return '作品生成失败';
  }
});

// 最终使用的错误标题
const finalErrTitle = computed(() => {
  return props.errTitle || defaultErrTitle.value;
});

const emit = defineEmits(['retry']);
const handleRetry = () => {
  emit('retry');
};
</script>

<style lang="scss" scoped>
.failed-state {
  /* 布局相关 */
  @apply zi-failed-state flex flex-col items-center relative;
  /* 尺寸相关 */
  @apply h-full p-20px;
  /* 滚动相关 */
  @apply overflow-y-auto;
  /* 最小宽度限制 */
  min-width: 220px;
  /* 内容居中 */
  justify-content: safe center;
  /* 宽高比例 9:16，高度100%，宽度自动计算 */
  aspect-ratio: 9 / 16;
  /* 显示模式 contain 效果 */
  object-fit: contain;
  /* 外观相关 - 使用与LoadingPreview相同的背景 */
  background-image: url('@/assets/EditProject/loading-new.svg');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
}

.failed-state__glass-layer {
  /* 布局相关 */
  @apply absolute top-0 left-0;
  /* 尺寸相关 */
  @apply w-full h-full;
  /* 外观相关 */
}

.failed-state__content {
  /* 布局相关 */
  @apply w-full flex flex-col items-center relative;
  /* 文字相关 */
  @apply text-white;
  /* 动画相关 */
  animation: fadeIn 0.3s ease-out forwards;
}

.failed-state__icon {
  /* 布局相关 */
  @apply relative;
  /* 尺寸相关 */
  @apply w-56px h-56px;
  /* 动画相关 */
  animation: fadeInScale 0.3s ease-out forwards;
}

.failed-state__icon-img {
  /* 尺寸相关 */
  @apply w-full h-full;
  /* 外观相关 */
  @apply opacity-100;
}

.failed-state__title {
  /* 布局相关 */
  @apply text-center;
  /* 尺寸相关 */
  margin-top: 24px;
  /* 文字相关 */
  @apply text-white font-normal text-16px;
  /* 动画相关 */
  animation: fadeInUp 0.3s ease-out 0.1s both;
}

.failed-state__description {
  /* 布局相关 */
  @apply w-full text-center;
  /* 尺寸相关 */
  margin-top: 16px;
  /* 文字相关 */
  @apply text-white font-normal text-13px sc-text-wrap-smart;
  /* 动画相关 */
  animation: fadeInUp 0.3s ease-out 0.2s both;
}

.failed-state__footer {
  /* 动画相关 */
  animation: fadeInUp 0.3s ease-out 0.3s both;
}

.failed-state__btn {
  /* 尺寸相关 */
  @apply w-[160px] mt-[16px];
}

/* 动画定义 */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes fadeInScale {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
