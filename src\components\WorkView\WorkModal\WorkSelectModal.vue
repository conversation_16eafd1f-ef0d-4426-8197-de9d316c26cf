<template>
  <WorkBaseModal :isShow.sync="isShow" :width="1024" @cancel="handleClose">
    <div v-if="workData" class="flex h-[684px]">
      <div class="preview-left gap-[24px]">
        <div class="flex flex-col">
          <div class="preview-title">新视频预览</div>
          <div class="preview-wrapper">
            <VideoWorkPreview
              ref="newVideoRef"
              class="preview-content"
              :src="videoData.video?.id"
              :type="videoData.video?.type"
              :coverId="videoData.videoCover?.id"
              :loop="false"
            />
          </div>
        </div>
        <transition name="fade">
          <div v-if="isShowOldVideo">
            <div class="preview-title">旧视频预览</div>
            <div class="preview-wrapper">
              <VideoWorkPreview
                ref="oldVideoRef"
                class="preview-content"
                :src="videoRelData.video?.id"
                :type="videoRelData.video?.type"
                :coverId="videoRelData.videoCover?.id"
                :loop="false"
              />
            </div>
          </div>
        </transition>
      </div>
      <div
        class="preview-right"
        :style="{ width: isShowOldVideo ? '272px' : '450px' }"
      >
        <div class="text-center">
          <div class="operation-video-btn" @click="toggleOldVideo">
            {{ isShowOldVideo ? '收起' : '对比' }}旧视频
          </div>
          <div class="operation-title">是否保存此次生成的内容？</div>
          <div class="operation-btn-group">
            <fa-button type="primary" @click="handleSaveNewVideo"
              >保存新视频</fa-button
            >
            <fa-button @click="handleSaveAllVideo">保存并保留旧视频</fa-button>
          </div>
        </div>
      </div>
    </div>
  </WorkBaseModal>
</template>

<script setup lang="ts">
import { defineProps, defineEmits, ref, nextTick } from 'vue';
import { selectWork } from '@/api/WorkView/index';
import { VideoWorkItem } from '@/types/Work';
import { message } from '@fk/faicomponent';
import WorkBaseModal from './WorkBaseModal.vue';
import VideoWorkPreview from '@/components/TemplateView/VideoWorkPreview.vue';
import { useWorkModal } from '../hook/useWorkModal';
import { showSaveSuccessMessage } from '@/utils/messageHelpers';

// 定义组件属性
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  workId: {
    type: Number || undefined,
  },
  useCustomSuccessMessage: {
    type: Boolean,
    default: false,
  },
});

// 定义组件事件
const emit = defineEmits(['update:visible', 'edit', 'save-new', 'save-all']);

const {
  isShow,
  workData,
  newVideoRef,
  oldVideoRef,
  stopVideo,
  startVideo,
  handleClose: innerHandleClose,
  videoData,
  videoRelData,
} = useWorkModal(
  props,
  emit as unknown as (event: string, ...args: unknown[]) => void,
);

const handleClose = () => {
  innerHandleClose();
};

const isShowOldVideo = ref(false); // 是否显示旧视频

// 方法定义
/**
 * 切换旧视频显示状态
 */
const toggleOldVideo = () => {
  isShowOldVideo.value = !isShowOldVideo.value;
  nextTick(() => {
    if (isShowOldVideo.value) {
      startVideo(oldVideoRef);
    } else {
      stopVideo(oldVideoRef);
    }
  });
};

/**
 * 保存新作品
 */
const handleSaveNewVideo = async () => {
  const success = await saveWorkApi();
  if (success) {
    emit('save-new');
    handleClose();
  }
};

/**
 * 保存新旧作品
 */
const handleSaveAllVideo = async () => {
  const success = await saveWorkApi(true);
  if (success) {
    emit('save-all');
    handleClose();
  }
};

/**
 * 保存作品API
 * @param isSaveAll 是否同时保存旧视频
 * @returns 保存是否成功
 */
const saveWorkApi = async (isSaveAll = false): Promise<boolean> => {
  const videoWork = workData.value as VideoWorkItem;
  const preset: {
    newId: string;
    oldId?: string;
  } = {
    newId: videoWork?.id?.toString() || '', // 确保 id 为字符串
  };
  // 新作品：id，旧作品：data.relWorkId
  if (isSaveAll) {
    preset.oldId = videoWork?.data?.relWorkId?.toString();
  }
  const [err] = await selectWork(preset);
  if (err) {
    message.error(err.message || '保存失败');
    return false;
  }

  // 根据 props 决定使用哪种保存成功提示
  if (props.useCustomSuccessMessage) {
    showSaveSuccessMessage();
  } else {
    message.success('保存成功！');
  }
  return true;
};
</script>

<style lang="scss" scoped>
.preview-left {
  @apply flex-1 flex h-full items-center justify-center;
  .preview-wrapper {
    @apply h-[576px] overflow-hidden rounded-[12px];
    .preview-content {
      @apply max-w-[324px] w-[324px] h-full bg-black rounded-[12px];
    }
  }
}
.preview-right {
  @apply relative flex flex-col items-center justify-center;
  @apply bg-white rounded-[16px];
  .operation-video-btn {
    @apply tracking-3px text-12px text-assist border-1 border-solid border-edge rounded-14px bg-white;
    @apply absolute top-50% left-0  p-[15px_2px_12px_2px] cursor-pointer;
    writing-mode: tb;
    transform: translate(-50%, -50%);
    &:hover {
      @apply text-subText border-[#bfbfbf];
    }
  }
  .operation-title {
    @apply mb-[48px] text-[16px] text-title font-bold;
  }
  .operation-btn-group {
    @apply flex flex-col m-auto gap-[24px] w-[200px];
  }
}
</style>

<style lang="scss"></style>
