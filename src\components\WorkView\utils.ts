import store from '@/store';
import { FILE_TYPES, FILE_EXTENSIONS } from '@/constants/fileType';
import { message } from '@fk/faicomponent';
import { getResourceToken } from '@/api/WorkView/index';

/**
 * 获取鉴权url
 * @param resId 资源id
 * @param resType 资源类型
 * @param token 鉴权token
 * @returns 鉴权url
 */
export function getAuthenticationUrl(
  resId: string,
  resType: FILE_TYPES,
  token: string,
) {
  const scUsrResAuthRoot = store.state.system.scUsrResAuthRoot;
  const aid = store.state.user.aid;
  const ext = FILE_EXTENSIONS[resType];
  // 先用&拼接所有参数
  const params = `aid=${aid}&timestamp=${Date.now()}&token=${token}`;
  // 将所有&编码为%26
  const encodedParams = params.replace(/&/g, '%26');
  return `${scUsrResAuthRoot}/${aid}/${resType}/0/${resId}.${ext}?${encodedParams}`;
}

/**
 * 通过 fetch+blob 方式下载图片/视频文件，兼容主流浏览器
 * @param {string} downloadPath - 文件的 URL 路径
 * @param {string} fileName - 文件名
 * @returns {Promise<void>} - Promise 对象，表示下载操作的完成
 * 某些浏览器（如 Chrome、Safari）对跨域 mp4 直链的 <a download> 支持有限，尤其是资源未设置 Content-Disposition: attachment 时，会直接打开而不是下载。
 */
export async function downloadFile(downloadPath: string, filename: string) {
  try {
    const res = await fetch(downloadPath, { mode: 'cors' });
    if (!res.ok) throw new Error('下载失败');
    const blob = await res.blob();
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    a.style.display = 'none';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);
    return Promise.resolve();
  } catch (_e) {
    console.log(_e);
    message.error('资源下载失败，请检查链接或网络');
    cachedToken = null; // 下载失败的场景，重置缓存
    return Promise.reject();
  }
}

let cachedToken: string | null = null;
let tokenExpireAt: number = 0;
/**
 * 获取有效的资源token（资源 token 有效期 5 分钟，5 分钟内多次下载只需请求一次 token，超时后自动刷新）
 * @param force 是否强制获取新的token
 * @returns 有效的资源token
 */
export async function getValidResourceToken(force = false) {
  const now = Date.now();
  if (!force && cachedToken && now < tokenExpireAt) {
    return cachedToken;
  }
  const [err, res] = await getResourceToken();
  if (err) {
    message.error('下载失败，无法获取资源凭证：' + err.message);
    throw err;
  }
  cachedToken = res.data?.token;
  tokenExpireAt = now + 5 * 60 * 1000 - 10 * 1000; // 5分钟-10秒，预留缓冲
  return cachedToken;
}

/**
 * 判断作品是否未关联项目（即关联的项目被删除了）
 * @param projectId 项目ID
 * @returns 是否未关联项目
 */
export function isUnrelatedProject(projectId: number) {
  return projectId === 0;
}
