<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <style>
      #skeleton-app {
        position: fixed;
        top: 0;
        left: 0;
        width: 100vw;
        min-width: 1366px;
        height: 100vh;
        z-index: 300;
        background-color: #fff;
        display: flex;
        flex-direction: column;
      }
      .ske-loading-bar {
        background: linear-gradient(
          90deg,
          #f2f2f2 25%,
          #e6e6e6 37%,
          #f2f2f2 63%
        );
        background-size: 400% 100%;
        animation: skeloading 1.4s infinite;
      }
      @keyframes skeloading {
        0% {
          background-position: 100% 50%;
        }
        100% {
          background-position: 0 50%;
        }
      }
      .ske-header {
        position: relative;
        height: 70px;
        min-height: 70px;
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: space-between;
        padding: 0 32px;
        box-shadow: 0px 1px 6px 0px rgba(0, 0, 0, 0.06);
      }
      .ske-header-left .ske-loading-bar {
        width: 156px;
        height: 36px;
        border-radius: 8px;
      }
      .ske-header-midd .ske-loading-bar {
        width: 600px;
        height: 40px;
        border-radius: 20px;
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translateX(-50%) translateY(-50%);
      }
      @media screen and (max-width: 1439px) {
        .ske-header-midd .ske-loading-bar {
          width: 480px;
        }
      }
      .ske-header-right {
        display: flex;
        flex-direction: row;
        align-items: center;
      }
      .ske-header-right-iconbox {
        display: flex;
        flex-direction: row;
        align-items: center;
        gap: 0 24px;
      }
      .ske-header-right-iconbox .ske-loading-bar {
        width: 24px;
        height: 24px;
        border-radius: 6px;
      }
      .ske-header-right-payBox .ske-loading-bar {
        width: 140px;
        height: 39px;
        border-radius: 19.5px;
        margin-right: 32px;
      }
      .ske-header-right-vericon {
        margin-left: 50px;
      }
      .ske-header-right-vericon .ske-loading-bar {
        width: 54px;
        height: 24px;
        border-radius: 6px;
      }
      .ske-main {
        flex: 1;
        display: flex;
        flex-direction: row;
      }
      .ske-sidebar {
        box-sizing: border-box;
        width: 200px;
        padding: 24px 32px;
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 16px 0;
        border-right: 1px solid #edeef2;
      }
      .ske-sidebar .ske-loading-bar {
        width: 136px;
        height: 32px;
        border-radius: 6px;
      }
      .ske-content {
        flex: 1;
        padding: 20px;
        background: #f3f3f5;
      }
      .ske-content-wrap {
        height: 100%;
        min-height: 644px;
        background-color: #fff;
        padding: 26px 32px;
        border-radius: 16px;
      }
      .ske-content-type .ske-loading-bar {
        height: 20px;
        width: 100%;
        border-radius: 6px;
        margin-bottom: 24px;
      }
      .ske-content-switch .ske-loading-bar {
        width: 164px;
        height: 40px;
        border-radius: 20px;
        margin-top: 26px;
      }
      .ske-content-template-list {
        display: grid;
        margin-top: 24px;
        padding-bottom: 32px;
        gap: 24px;
        border-bottom-left-radius: 16px;
        border-bottom-right-radius: 16px;
      }
      .ske-content-template {
        width: 100%;
        display: flex;
        flex-direction: column;
        align-items: flex-start;
      }
      .ske-content-template .ske-loading-bar {
        width: 100% !important;
        position: relative;
        aspect-ratio: 210 / 373;
        max-width: 210px;
        overflow: hidden;
        border-radius: 12px;
      }

      @media screen and (max-width: 1430px) {
        .template-list {
          grid-template-columns: repeat(5, minmax(0, 1fr));
        }
      }

      @media screen and (min-width: 1431px) and (max-width: 1664px) {
        .template-list {
          grid-template-columns: repeat(6, minmax(0, 1fr));
        }
      }

      @media screen and (min-width: 1665px) {
        .template-list {
          grid-template-columns: repeat(7, minmax(0, 1fr));
        }
      }

      html[data-skeleton='hide'] #skeleton-app {
        display: none;
      }

      #common-skeleton-app {
        position: fixed;
        top: 0;
        left: 0;
        width: 100vw;
        min-width: 1366px;
        height: 100vh;
        z-index: 300;
        background-color: #fff;
        display: none;
        flex-direction: column;
      }

      html[data-skeleton='common'] #common-skeleton-app {
        display: flex;
      }

      html[data-skeleton='common'] #skeleton-app {
        display: none;
      }

      #common-skeleton-app .ske-header {
        width: 100%;
        height: 76px;
        padding: 0 32px;
        box-sizing: border-box;
        display: flex;
        align-items: center;
        justify-content: space-between;
        border-bottom: 1px solid #edeef2;
      }

      #common-skeleton-app .ske-main {
        flex: 1;
        display: flex;
        flex-direction: row;
      }

      #common-skeleton-app .ske-sidebar {
        box-sizing: border-box;
        width: 200px;
        padding: 24px 32px;
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 16px 0;
        border-right: 1px solid #edeef2;
      }

      #common-skeleton-app .ske-sidebar .ske-loading-bar {
        width: 136px;
        height: 32px;
        border-radius: 6px;
      }

      #common-skeleton-app .ske-content {
        flex: 1;
        padding: 20px;
        background: #f3f3f5;
      }

      #common-skeleton-app .ske-content .ske-loading-bar {
        width: 100%;
        height: 100%;
        min-height: 644px;
        background-color: #fff;
        border-radius: 16px;
      }
    </style>
    <title></title>
    <script>
      var COMMON_SKELETON_ROUTES = ['/meta', '/acct-info', '/project', '/work'];

      function arrayContains(arr, item) {
        for (var i = 0; i < arr.length; i++) {
          if (arr[i] === item) return true;
        }
        return false;
      }

      function parseRoute(route) {
        if (!route) return { path: '/' };

        var fullPath = route;
        if (fullPath.indexOf('/#/') === 0) {
          fullPath = fullPath.substring(2);
        } else if (fullPath.indexOf('#/') === 0) {
          fullPath = fullPath.substring(1);
        }

        if (!fullPath || fullPath === '/') return { path: '/' };

        var queryIndex = fullPath.indexOf('?');
        var path =
          queryIndex !== -1 ? fullPath.substring(0, queryIndex) : fullPath;

        return { path: path };
      }

      function updateSkeletonState() {
        var hash = window.location.hash;
        var routeInfo = parseRoute(hash);
        var path = routeInfo.path;

        if (path === '/') {
          document.documentElement.removeAttribute('data-skeleton');
        } else if (arrayContains(COMMON_SKELETON_ROUTES, path)) {
          document.documentElement.setAttribute('data-skeleton', 'common');
        } else {
          document.documentElement.setAttribute('data-skeleton', 'hide');
        }
      }

      window.addEventListener('hashchange', updateSkeletonState);

      updateSkeletonState();
    </script>
  </head>

  <body>
    <div id="skeleton-app">
      <div class="ske-header">
        <div class="ske-header-left">
          <div class="ske-loading-bar"></div>
        </div>
        <div class="ske-header-midd">
          <div class="ske-loading-bar"></div>
        </div>
        <div class="ske-header-right">
          <div class="ske-header-right-payBox">
            <div class="ske-loading-bar"></div>
          </div>
          <div class="ske-header-right-iconbox">
            <div class="ske-loading-bar"></div>
            <div class="ske-loading-bar"></div>
          </div>
          <div class="ske-header-right-vericon">
            <div class="ske-loading-bar"></div>
          </div>
        </div>
      </div>
      <div class="ske-main">
        <div class="ske-sidebar">
          <div class="ske-loading-bar"></div>
          <div class="ske-loading-bar"></div>
          <div class="ske-loading-bar"></div>
          <div class="ske-loading-bar"></div>
          <div class="ske-loading-bar"></div>
        </div>
        <div class="ske-content">
          <div class="ske-content-wrap">
            <div class="ske-content-type">
              <div class="ske-loading-bar"></div>
              <div class="ske-loading-bar"></div>
            </div>
            <div class="ske-content-switch">
              <div class="ske-loading-bar"></div>
            </div>
            <div class="ske-content-template-list template-list">
              <div class="ske-content-template">
                <div class="ske-loading-bar"></div>
              </div>
              <div class="ske-content-template">
                <div class="ske-loading-bar"></div>
              </div>
              <div class="ske-content-template">
                <div class="ske-loading-bar"></div>
              </div>
              <div class="ske-content-template">
                <div class="ske-loading-bar"></div>
              </div>
              <div class="ske-content-template">
                <div class="ske-loading-bar"></div>
              </div>
              <div class="ske-content-template">
                <div class="ske-loading-bar"></div>
              </div>
              <div class="ske-content-template">
                <div class="ske-loading-bar"></div>
              </div>
              <div class="ske-content-template">
                <div class="ske-loading-bar"></div>
              </div>
              <div class="ske-content-template">
                <div class="ske-loading-bar"></div>
              </div>
              <div class="ske-content-template">
                <div class="ske-loading-bar"></div>
              </div>
              <div class="ske-content-template">
                <div class="ske-loading-bar"></div>
              </div>
              <div class="ske-content-template">
                <div class="ske-loading-bar"></div>
              </div>
              <div class="ske-content-template">
                <div class="ske-loading-bar"></div>
              </div>
              <div class="ske-content-template">
                <div class="ske-loading-bar"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div id="common-skeleton-app">
      <div class="ske-header">
        <div class="ske-header-left">
          <div class="ske-loading-bar"></div>
        </div>
        <div class="ske-header-midd">
          <div class="ske-loading-bar"></div>
        </div>
        <div class="ske-header-right">
          <div class="ske-header-right-payBox">
            <div class="ske-loading-bar"></div>
          </div>
          <div class="ske-header-right-iconbox">
            <div class="ske-loading-bar"></div>
            <div class="ske-loading-bar"></div>
          </div>
          <div class="ske-header-right-vericon">
            <div class="ske-loading-bar"></div>
          </div>
        </div>
      </div>
      <div class="ske-main">
        <div class="ske-sidebar">
          <div class="ske-loading-bar"></div>
          <div class="ske-loading-bar"></div>
          <div class="ske-loading-bar"></div>
          <div class="ske-loading-bar"></div>
          <div class="ske-loading-bar"></div>
        </div>
        <div class="ske-content">
          <div class="ske-loading-bar"></div>
        </div>
      </div>
    </div>

    <div id="app"></div>

    <script>
      window._resRoot = '<%=_resRoot%>';
    </script>
    <script type="module" src="/src/main.ts"></script>
  </body>
</html>
