import { defineConfig, presetAttributify, presetUno } from 'unocss';
import transformerDirectives from '@unocss/transformer-directives';
import { presetScrollbar } from 'unocss-preset-scrollbar';

import type { UserConfig } from 'unocss';

const Z_INDEX_LAYERS = {
  // **Block**: 普通元素/小交互组件 (0-100)
  'custom-upload__preview-icon': 1,
  'custom-upload__video-duration': 2,
  'custom-upload__delete-button': 10,
  'media-file-item__progress-overlay': 1,
  'media-file-item__regenerated-tag': 10,
  'media-file-item__work-id': 10,
  'error-state': 10,
  'regenerated-state': 10,
  'video-controls': 10,

  // 侧边栏基础层级
  'sidebar': 20,

  // **Block**: float/吸顶/吸底 (101-200)
  // 头部导航
  'header': 101,

  // 底部导航
  'footer': 101,

  // **Block**: 交互元素/大交互组件 (201-300)
  // 下拉菜单
  'dropdown': 201,

  // 工具提示
  'tooltip': 210,

  // 弹出框
  'popover': 220,

  // **Block**: 弹窗 (1001-2000)
  // 普通模态框
  'modal': 1001,

  // **Block**: loading/分享蒙层 (2001-3000)
  // 加载遮罩
  'loading': 2001,

  // **Block**: 其它/AD (3001-9999)
  // 通知提示
  'notification': 3001,

  // 广告浮层
  'ad-float': 3100,
} as const;

export default defineConfig(<UserConfig>{
  presets: [presetUno(), presetAttributify(), presetScrollbar()],
  transformers: [transformerDirectives()],
  theme: {
    colors: {
      primary: '#3261FD', // 主色：重要信息、黑灰色元素hover态
      secondary: '#003AFB', // 次色
      success: '#19BE6B', // 成功色
      warning: '#FF9900', // 警告色
      danger: '#FA3534', // 危险色
      title: '#111111', // 标题颜色，用于主要标题文字
      text: '#333333', // 正文字体颜色，用于正文内容
      subText: '#666666', // 次正文颜色，用于次要文字
      assist: '#999999', // 辅助文字颜色，用于辅助性文字
      disabledText: '#BFBFBF', // 不可用文字颜色，用于不可用状态的文字
      edge: '#D9D9D9', // 边框颜色，用于边框线条
      divider: '#E8E8E8', // 分割线颜色，用于分割线
      background: '#F3F3F5', // 背景颜色，用于页面背景
      disabledBackground: '#F5F5F5', // 不可用背景颜色，用于不可用状态的背景
    },
    backgroundImage: {
      primary: 'linear-gradient(92.19deg, #105fff 0%, #7923f9 100%)', // 主色渐变
      primaryActive: 'linear-gradient(92.19deg, #0050f2 0%, #6e00ff 100%)', // 次色渐变
      primaryDisable:
        'linear-gradient(92.19deg,rgba(16, 95, 255, 0.5) 0%,rgba(121, 35, 249, 0.5) 100%)', // 特殊渐变色不可用态
    },
  },
  rules: [
    [
      new RegExp(`^zi-(${Object.keys(Z_INDEX_LAYERS).join('|')})(-[1-9])?$`),
      match => {
        const [, group, level] = match;
        let zIndex = Z_INDEX_LAYERS[group];
        if (!zIndex) throw new Error(`没有定义名称为 '${group}' 的z-index值`);
        if (isNaN(zIndex))
          throw new Error(`'${group}' 的 z-index 值不是一个数字。`);
        if (level) zIndex += Number(level.substring(1));
        return { 'z-index': zIndex };
      },
    ],
  ],
  shortcuts: {
    // 紫蓝色渐变按钮
    'btn-special':
      'text-white border-none !bg-gradient-to-r from-[#105fff] to-[#7923f9] hover:text-white hover:from-[#0050f2] hover:to-[#6e00ff] focus:text-white',
    'btn-special-disabled':
      'text-white! border-none bg-gradient-to-r from-[#105fff] to-[#7923f9] cursor-not-allowed opacity-40 hover:from-[#105fff] hover:to-[#7923f9]',
    // 文字换行处理
    'sc-text-wrap-smart':
      'whitespace-pre-wrap break-words overflow-wrap-break-word',
    // 默认滚动条样式
    'scrollbar-default':
      // 自定义滚动条整体样式
      'scrollbar scrollbar-w-6px scrollbar-h-6px ' +
      // 滚动条hover时背景色
      'scrollbar:bg-transparent scrollbar:hover:bg-[#e8e8e8] ' +
      // 滚动条滑块样式
      'scrollbar-thumb:bg-[#BBBBBB] scrollbar-thumb:w-6px scrollbar-thumb:h-6px scrollbar-thumb:rounded-4px ' +
      // 滑块hover
      'scrollbar-thumb:hover:bg-[#999] ' +
      // 滑块active
      'scrollbar-thumb:active:bg-[#666666]',
    // 小号滚动条
    'scrollbar-small':
      'scrollbar scrollbar-w-4px scrollbar-h-4px' +
      'scrollbar:bg-transparent scrollbar:hover:bg-[#e8e8e8] ' +
      'scrollbar-thumb:bg-[#BBBBBB] scrollbar-thumb:w-4px scrollbar-thumb:h-4px scrollbar-thumb:rounded-2px ' +
      'scrollbar-thumb:hover:bg-[#999] ' +
      'scrollbar-thumb:active:bg-[#666666]',
  },
});
