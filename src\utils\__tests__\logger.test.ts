/**
 * Logger 工具测试
 * 验证日志记录功能是否正常工作
 */

import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';

describe('Logger Utils', () => {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  let consoleSpy: any;

  beforeEach(() => {
    // 模拟 console 方法
    consoleSpy = {
      log: vi.spyOn(console, 'log').mockImplementation(() => {}),
      warn: vi.spyOn(console, 'warn').mockImplementation(() => {}),
      error: vi.spyOn(console, 'error').mockImplementation(() => {}),
      info: vi.spyOn(console, 'info').mockImplementation(() => {}),
    };
  });

  afterEach(() => {
    // 恢复 console 方法
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    Object.values(consoleSpy).forEach((spy: any) => spy.mockRestore());
  });

  describe('基础日志功能', () => {
    it('应该能够记录普通日志', () => {
      console.log('测试日志');
      expect(consoleSpy.log).toHaveBeenCalledWith('测试日志');
    });

    it('应该能够记录警告日志', () => {
      console.warn('测试警告');
      expect(consoleSpy.warn).toHaveBeenCalledWith('测试警告');
    });

    it('应该能够记录错误日志', () => {
      console.error('测试错误');
      expect(consoleSpy.error).toHaveBeenCalledWith('测试错误');
    });

    it('应该能够记录信息日志', () => {
      console.info('测试信息');
      expect(consoleSpy.info).toHaveBeenCalledWith('测试信息');
    });
  });

  describe('日志格式化', () => {
    it('应该能够记录对象', () => {
      const testObj = { name: '测试', value: 123 };
      console.log(testObj);
      expect(consoleSpy.log).toHaveBeenCalledWith(testObj);
    });

    it('应该能够记录数组', () => {
      const testArray = [1, 2, 3, '测试'];
      console.log(testArray);
      expect(consoleSpy.log).toHaveBeenCalledWith(testArray);
    });

    it('应该能够记录多个参数', () => {
      console.log('参数1', '参数2', 123);
      expect(consoleSpy.log).toHaveBeenCalledWith('参数1', '参数2', 123);
    });
  });

  describe('错误处理', () => {
    it('应该能够记录 Error 对象', () => {
      const error = new Error('测试错误');
      console.error(error);
      expect(consoleSpy.error).toHaveBeenCalledWith(error);
    });

    it('应该能够记录错误堆栈', () => {
      const error = new Error('测试错误');
      error.stack = '测试堆栈信息';
      console.error(error.stack);
      expect(consoleSpy.error).toHaveBeenCalledWith('测试堆栈信息');
    });
  });
});
