import { ref, Ref } from 'vue';
import { message } from '@fk/faicomponent';
import { saveWorks } from '@/api/EditProjectView/work';
import { getWorkStatusInfo, isRecompletedWork } from '@/constants/workStatus';
import { eventBus, EVENT_NAMES } from '@/utils/eventBus';
import { showSaveSuccessMessage } from '@/utils/messageHelpers';

/**
 * @description `useSecondStep` 组合式API的返回值类型定义。
 * @interface UseSecondStepReturn
 */
export interface UseSecondStepReturn {
  /**
   * 在第二步中选中的作品ID列表。
   * @type {Ref<number[]>}
   * @memberof UseSecondStepReturn
   */
  selectedWorkIds: Ref<number[]>;
  /**
   * 保存至作品库操作的加载状态。
   * @type {Ref<boolean>}
   * @memberof UseSecondStepReturn
   */
  saveLoading: Ref<boolean>;
  /**
   * 全选状态（用于分页场景下的全选操作）。
   * @type {Ref<boolean>}
   * @memberof UseSecondStepReturn
   */
  isAllSelected: Ref<boolean>;
  /**
   * 处理第二步中作品选中状态变更的函数。
   * @param {number[]} ids - 新选中的作品ID数组。
   * @returns {void}
   * @memberof UseSecondStepReturn
   */
  handleSelectedWorksChange: (ids: number[]) => void;
  /**
   * 处理全选状态变更的函数。
   * @param {boolean} isAll - 是否全选。
   * @returns {void}
   * @memberof UseSecondStepReturn
   */
  handleAllSelectChange: (isAll: boolean) => void;
  /**
   * 处理保存作品至用户作品库的函数。
   * @param {number} [projectId] - 项目ID。
   * @param {Array<{ id: number; status: number; [key: string]: unknown }>} [workList] - 当前作品列表（用于检查作品状态）。
   * @returns {Promise<void>}
   * @memberof UseSecondStepReturn
   */
  handleSaveToWorks: (
    projectId?: number,
    workList?: Array<{ id: number; status: number; [key: string]: unknown }>,
  ) => Promise<void>;
}

/**
 * @description 第二步相关的业务逻辑
 * @returns {UseSecondStepReturn} 第二步相关状态和方法
 */
export function useSecondStep(): UseSecondStepReturn {
  // 状态
  const selectedWorkIds = ref<number[]>([]);
  const saveLoading = ref<boolean>(false);
  const isAllSelected = ref<boolean>(false);

  /**
   * 处理选中作品变更事件
   * @param ids - 选中的作品ID列表
   */
  const handleSelectedWorksChange = (ids: number[]): void => {
    selectedWorkIds.value = ids;
  };

  /**
   * 处理全选状态变更事件
   * @param isAll - 是否全选
   */
  const handleAllSelectChange = (isAll: boolean): void => {
    isAllSelected.value = isAll;
  };

  /**
   * 处理保存至我的作品按钮点击事件
   * @param projectId - 项目ID
   * @param workList - 当前作品列表（用于检查作品状态）
   */
  const handleSaveToWorks = async (
    projectId?: number,
    workList?: Array<{ id: number; status: number; [key: string]: unknown }>,
  ): Promise<void> => {
    // 检查是否满足保存条件：有选择的作品 OR 全选状态
    if (!selectedWorkIds.value?.length && !isAllSelected.value) {
      message.warning('请选择需要保存的作品');
      return;
    }

    // 非全选情况下，检查是否有重新生成完成状态的视频作品
    if (!isAllSelected.value && selectedWorkIds.value?.length && workList) {
      // 获取当前作品列表，检查选中的作品中是否有重新生成完成状态
      const selectedWorks = workList.filter(work =>
        selectedWorkIds.value.includes(work.id),
      );

      // 查找第一个重新生成完成状态的视频作品
      // 图文作品不需要新旧选择弹窗，即使有editAgainGraphic标识
      const regeneratedWork = selectedWorks.find(work => {
        const statusInfo = getWorkStatusInfo(work);
        // 只有视频作品（type === 0）才检查重新生成完成状态
        return work.type === 0 && isRecompletedWork(statusInfo);
      });

      // 如果找到重新生成完成状态的视频作品，显示新旧选择弹窗
      if (regeneratedWork) {
        eventBus.emit(EVENT_NAMES.SHOW_NEW_OLD_CHOICE_MODAL, regeneratedWork);
        return;
      }
    }

    saveLoading.value = true;
    message.loading('正在保存作品...');

    if (!projectId) {
      message.error('项目ID为空，无法保存作品');
      saveLoading.value = false;
      return;
    }

    const [err, _res] = await saveWorks({
      workIds: isAllSelected.value ? [] : selectedWorkIds.value,
      projectId,
      isAll: isAllSelected.value,
    });

    setTimeout(() => (saveLoading.value = false), 300);

    if (err) {
      console.error('保存作品失败:', err);
      message.error(err.message || '保存作品失败，请重试');
      return;
    }

    // 显示保存成功提示，包含跳转到"我的作品"的链接
    showSaveSuccessMessage();

    // 使用 EventBus 通知第二步组件更新作品列表
    eventBus.emit(EVENT_NAMES.WORK_SAVE_REQUEST, {
      workIds: isAllSelected.value ? [] : selectedWorkIds.value,
      isBatchSave: isAllSelected.value,
    });
  };

  return {
    // 状态
    selectedWorkIds,
    saveLoading,
    isAllSelected,

    // 方法
    handleSelectedWorksChange,
    handleAllSelectChange,
    handleSaveToWorks,
  };
}
