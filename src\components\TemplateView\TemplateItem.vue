<template>
  <div class="template-item">
    <ScImg
      class="cover-img"
      :src="templateInfo.cover.resId"
      belong="oss"
      :max-width="371"
      fit="cover"
    >
      <div slot="error">
        <img
          src="@/assets/common/imgEmpty.webp"
          alt="默认封面"
          class="w-full h-full object-cover"
        />
      </div>
    </ScImg>
    <div class="type-tag">
      <Icon class="w-[16px] h-[16px] mr-[4px]" :type="typeIcon" />
      {{ typeName }}
    </div>
    <div class="name-wrapper">
      <div class="line-clamp-2 sc-text-wrap-smart">
        <span v-if="isStaff">[{{ templateInfo.id }}]</span>
        {{ templateInfo.name }}
      </div>
    </div>
    <!-- hover 显示层 -->
    <div class="hover-layer" @click="handleClickPreview">
      <div class="absolute bottom-0 p-[16px] left-0 right-0">
        <div
          class="text-[15px] text-white mb-[8px] sc-text-wrap-smart line-clamp-8"
        >
          {{ templateInfo.name }}
        </div>
        <div class="hover-layer-tags">
          <span class="tag-item" v-show="!!industryName.length">{{
            industryName
          }}</span>
          <span class="tag-item" v-show="!!sceneName.length">{{
            sceneName
          }}</span>
        </div>
        <FaButton type="primary" class="w-full" @click="handleClickPreview"
          >预览模板</FaButton
        >
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { defineProps } from 'vue';
import { PROJECT_TYPE, PROJECT_TYPE_NAME } from '@/constants/project';
import ScImg from '../comm/ScImg.vue';
import { Template } from '@/types';
import store from '@/store';

const isStaff = store.state.user?.isStaff || false;

const props = defineProps<{
  /** 模板信息 */
  templateInfo: Template;
}>();
const typeName = PROJECT_TYPE_NAME[props.templateInfo.type];
const sceneName =
  store.state.system?.sceneMap?.[props.templateInfo.scene] || '';
const industryName =
  store.state.system?.industryMap?.[props.templateInfo.industry] || '';
const typeIcon =
  props.templateInfo.type === PROJECT_TYPE.VIDEO ? 'shipin' : 'tuwen';

const emit = defineEmits<{
  /**
   * 触发预览事件
   * @param templateInfo 模板信息
   */
  (e: 'preview', templateInfo: Template): void;
}>();

const handleClickPreview = () => {
  emit('preview', props.templateInfo);
};
</script>

<style scoped lang="scss">
.template-item {
  .name-wrapper {
    @apply absolute text-white font-bold text-[15px] bottom-0 left-0 right-0 px-[16px] pt-[16px] pb-[12px];
    background: linear-gradient(180deg, #00000000 0%, #000000cc 100%);
  }
  .type-tag {
    @apply absolute top-[8px] right-[8px] bg-[#000000c0] text-white text-[12px] font-bold px-[8px] py-[4px] rounded-full flex items-center justify-center;
  }
  @apply rounded-[12px] max-w-[210px] overflow-hidden relative aspect-[210/373];
  .hover-layer {
    @apply select-none cursor-pointer absolute top-0 left-0 w-full h-full transition-opacity duration-400 ease-in-out opacity-0;
    background: linear-gradient(180deg, #00000033 0%, #000000cc 100%);
    .hover-layer-tags {
      @apply flex items-center justify-start mb-[8px];
      .tag-item {
        @apply bg-background text-text text-[13px] px-[8px] py-[4px] rounded-[4px] mr-[8px];
      }
    }
  }
  .cover-img {
    @apply w-full h-full transition-transform duration-400 ease-in-out;
  }
  &:hover {
    .hover-layer {
      @apply opacity-100;
    }
    .cover-img {
      @apply scale-105;
    }
  }
}
</style>
