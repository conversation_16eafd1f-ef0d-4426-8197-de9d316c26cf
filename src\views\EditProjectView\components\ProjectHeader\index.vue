<template>
  <header class="project-header">
    <!-- 左侧返回按钮区域 -->
    <div class="project-header__side">
      <fa-button class="project-header__button" @click="goHome">
        <Icon
          class="project-header__button-icon w-[10px] h-[10px]"
          type="jiantou_zuo"
        />
        <span class="project-header__button-text">退出</span>
      </fa-button>
    </div>

    <!-- 中间导航区域 -->
    <NavProject
      class="project-header__nav"
      :currentStep="currentStep"
      :projectId="projectId"
      :allowStepChange="!!projectId"
      @stepChange="onStepChange"
    />

    <!-- 右侧保存按钮区域 -->
    <div class="project-header__side">
      <fa-button
        v-if="showSaveButton"
        class="project-header__button project-header__button--save"
        @click="saveProject"
        :loading="saveLoading"
      >
        <template v-if="!saveLoading">
          <Icon
            class="project-header__button-icon w-[20px] h-[20px]"
            type="baocun"
          />
          <span class="project-header__button-text">保存</span>
        </template>
      </fa-button>
    </div>
  </header>
</template>

<script lang="ts">
import { defineComponent, computed, getCurrentInstance } from 'vue';
import NavProject from '@/views/EditProjectView/components/NavProject/index.vue';
import { PAGE_SOURCE, PAGE_SOURCE_RETURN_PATH } from '@/constants/navigation';
import { FIRST_STEP_INDEX } from '@/views/EditProjectView/constants';
import { checkUnsavedChangesBeforeLeave } from '@/composables/useDataProtection';

/**
 * @description 项目视图头部组件
 */
export default defineComponent({
  name: 'ProjectHeader',
  components: { NavProject },
  props: {
    /**
     * 当前步骤索引
     */
    currentStep: {
      type: Number,
      required: true,
    },
    /**
     * 保存按钮加载状态
     */
    saveLoading: {
      type: Boolean,
      default: false,
    },
    /**
     * 项目ID，用于步骤导航的条件跳转控制
     */
    projectId: {
      type: [String, Number],
      default: null,
    },
  },
  setup(props, { emit }) {
    const instance = getCurrentInstance();

    /**
     * 是否显示保存按钮
     */
    const showSaveButton = computed(() => props.currentStep !== 1);

    /**
     * 检查是否有未保存的更改
     * @returns Promise<boolean> 是否有未保存的更改
     */
    const checkUnsavedChanges = (): Promise<boolean> => {
      // 仅在第一步（上传原料页）时检查
      if (props.currentStep !== FIRST_STEP_INDEX) {
        return Promise.resolve(false);
      }

      // 使用统一的工具函数检查未保存更改
      return checkUnsavedChangesBeforeLeave(props.currentStep);
    };

    /**
     * 显示数据保护确认对话框
     * @returns Promise<boolean> 用户是否确认离开
     */
    const showUnsavedChangesConfirm = (): Promise<boolean> => {
      return new Promise(resolve => {
        // 使用浏览器原生确认对话框
        const confirmed = window.confirm(
          '您有未保存的更改，确定要离开吗？离开后将丢失未保存的内容。',
        );
        resolve(confirmed);
      });
    };

    /**
     * 智能返回到来源页面（带数据保护）
     */
    const goHome = async () => {
      // 检查是否有未保存的更改
      const hasUnsavedChanges = await checkUnsavedChanges();
      if (hasUnsavedChanges) {
        const confirmed = await showUnsavedChangesConfirm();
        if (!confirmed) {
          // 用户取消离开
          return;
        }
      }

      // 执行正常的页面跳转逻辑
      const router = instance?.proxy?.$router;
      const route = instance?.proxy?.$route;

      if (router?.push && route) {
        const from = route.query.from as PAGE_SOURCE;

        // 根据来源参数决定返回位置，使用映射对象
        const returnPath =
          PAGE_SOURCE_RETURN_PATH[from] ||
          PAGE_SOURCE_RETURN_PATH[PAGE_SOURCE.TEMPLATE];
        router.push(returnPath);
      }
    };

    /**
     * 触发保存项目事件
     */
    const saveProject = () => {
      emit('save-project');
    };

    /**
     * 转发步骤切换事件
     * @param step - 目标步骤索引
     */
    const onStepChange = (step: number) => {
      emit('step-change', step);
    };

    return {
      showSaveButton,
      goHome,
      saveProject,
      onStepChange,
      checkUnsavedChanges, // 暴露给测试使用
    };
  },
});
</script>

<style lang="scss" scoped>
.project-header {
  /* 布局相关 */
  @apply flex items-center justify-between;
  /* 尺寸相关 */
  @apply w-full h-72px;
  /* 间距相关 */
  @apply px-16px;
}

.project-header__side {
  /* 尺寸相关 */
  @apply w-110px;
}

.project-header__nav {
  /* 布局相关 */
  @apply flex justify-center;
  /* 尺寸相关 */
  @apply flex-1;
}

.project-header__button {
  /* 布局相关 */
  @apply flex items-center justify-center;
}

.project-header__button--save {
  /* 尺寸相关 */
  @apply w-110px;
}

.project-header__button-icon {
  /* 间距相关 */
  @apply mr-4px;
}

.project-header__button-icon--save {
  /* 尺寸相关 */
  @apply w-15px h-15px;
}
</style>
