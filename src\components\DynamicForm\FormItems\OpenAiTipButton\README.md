# OpenAI 推荐词组件核心逻辑

## 核心机制

### 批量获取与缓存机制

- 一次性获取 30 个推荐词
- 自动去重处理
- 存储在缓存池中供后续使用

### 分批显示机制

- 每次只显示 3 个推荐词
- 通过"换一换"按钮循环显示
- **智能补充机制**：当剩余推荐词不足 3 个时，从头补充确保总是显示 3 个
- 缓存显示完毕后自动重新获取

### 依赖字段监听

- 监听指定依赖字段的值变化
- 依赖条件满足时自动获取推荐词
- 依赖字段变化时智能处理缓存

## 显示控制

**组件显示条件**：

- 组件未被禁用
- 曾经获得过焦点
- 曾经成功加载过数据
- 正在加载中或有推荐词显示

**"换一换"按钮显示条件**：

- 组件未被禁用
- 依赖条件已满足
- 有当前显示的推荐词或缓存中有推荐词

## 依赖字段变化处理

**情况 1：未获得焦点** → 立即清空缓存并重新获取

**情况 2：已获得焦点** → 标记变化，保持显示，用户点击"换一换"时更新

## 工作流程

### 初始化流程

1. 组件挂载 → 初始化依赖字段缓存
2. 获得焦点 → 标记曾经获得过焦点
3. 满足依赖条件 → 自动获取推荐词
4. API 返回数据 → 去重处理 → 存入缓存池
5. 显示前 3 个推荐词 → 标记已成功加载

### "换一换"流程

1. 用户点击"换一换"按钮
2. 检查是否需要重新获取推荐词
3. 如需要：调用 API 获取新数据
4. 如不需要：显示缓存中的下一批
5. 如缓存已显示完：重新获取新推荐词

### 依赖字段变化流程

1. 依赖字段值发生变化 → 触发监听器
2. 检查依赖条件是否从"不满足"变为"满足"
3. 如果刚满足且从未加载过 → 自动获取推荐词
4. 根据是否获得焦点分别处理缓存
5. 更新依赖字段缓存

## 换一换按钮逻辑

**重新获取条件**（满足任一条件即重新获取）：

- 缓存池为空
- 依赖字段已发生变化
- 显示索引为 0 且当前无显示内容

**操作流程**：

1. 检查是否需要重新获取
2. 如需要：调用 API 获取新推荐词
3. 如不需要：显示缓存中的下一批推荐词

## 常见问题

**Q: 依赖字段变化后推荐词为什么没立即更新？** A: 已获得焦点时保持显示连续性，点击"换一换"才更新

**Q: 什么时候自动获取推荐词？** A: 依赖条件从不满足变为满足时；获得焦点且满足条件且未加载过时

**Q: "换一换"按钮什么时候显示？** A: 未禁用 + 依赖条件满足 + 有推荐词可显示时

## 最佳实践

### 使用建议

- **依赖字段设置**：确保依赖字段能提供足够的上下文信息
- **性能考虑**：避免频繁修改依赖字段值，利用组件的防抖机制
- **用户体验**：依赖字段变化时组件会保持显示连续性

### 常见配置

```vue
<!-- 基础用法 -->
<OpenAiTipButton
  field-name="description"
  :context-data="formData"
  :dependency-fields="['title', 'category']"
  :fields-config="{ type: 'textarea' }"
/>

<!-- 模板格式 -->
<OpenAiTipButton
  field-name="content"
  :context-data="formData"
  :dependency-fields="['根据#title#和#category#生成内容建议']"
  :fields-config="{ type: 'input' }"
/>
```

### 注意事项

- 组件会在依赖条件满足时自动获取推荐词
- 已获得焦点后，依赖字段变化不会立即清空显示
- 推荐词会自动去重，避免重复内容
- 缓存机制可减少不必要的 API 调用

---

**最后更新：2025-08-05**
