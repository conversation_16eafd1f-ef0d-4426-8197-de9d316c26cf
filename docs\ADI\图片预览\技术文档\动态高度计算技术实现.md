# ImagePreview 动态高度计算示例

## 功能演示

以下是 ImagePreview 组件动态轮播图高度计算功能的具体示例：

### 示例 1：正方形图片（1:1 比例）

**图片尺寸**：800px × 800px  
**计算过程**：

```
动态高度 = 378px × (800px / 800px) = 378px × 1 = 378px
```

**结果**：轮播图高度为 378px

### 示例 2：横向图片（16:9 比例）

**图片尺寸**：1920px × 1080px  
**计算过程**：

```
动态高度 = 378px × (1080px / 1920px) = 378px × 0.5625 = 212.625px
```

**结果**：轮播图高度为 212.625px

### 示例 3：纵向图片（9:16 比例）

**图片尺寸**：1080px × 1920px **计算过程**：

```
动态高度 = 378px × (1920px / 1080px) = 378px × 1.778 = 672px
```

**限制处理**：由于计算结果 672px 超过最大限制 504px **结果**：轮播图高度被限制为 504px

### 示例 4：极宽图片（超宽比例）

**图片尺寸**：2000px × 100px  
**计算过程**：

```
动态高度 = 378px × (100px / 2000px) = 378px × 0.05 = 18.9px
```

**限制处理**：由于计算结果 18.9px 低于最小限制 378px **结果**：轮播图高度被限制为 378px

### 示例 5：图片加载失败

**情况**：第一张图片无法加载或获取尺寸失败  
**处理**：

```javascript
firstImageSize.value = null;
```

**结果**：轮播图高度回退为默认值 "78%"

## 代码示例

### 基本使用

```vue
<template>
  <ImagePreview :workInfo="workInfo" />
</template>

<script setup>
import ImagePreview from '@/views/EditProjectView/pages/SecondStepImage/components/ImagePreview/index.vue';

const workInfo = {
  script: {
    title: '测试标题',
    content: '测试内容',
  },
  data: {
    graphic: [
      { id: 'image-1', type: 1 }, // 第一张图片，用于计算高度
      { id: 'image-2', type: 1 },
      { id: 'image-3', type: 1 },
    ],
  },
  status: 'completed',
};
</script>
```

### 高度计算逻辑

```typescript
// 轮播图容器固定宽度
const CAROUSEL_CONTAINER_WIDTH = 378;

// 动态计算的轮播图最大高度
const dynamicCarouselMaxHeight = computed(() => {
  // 检查是否有图片和尺寸信息
  if (!firstImageSize.value || !imgListCal.value.length) {
    return '78%'; // 默认高度
  }

  const { width, height } = firstImageSize.value;

  // 防止除零错误
  if (width <= 0) {
    return '78%';
  }

  // 计算宽高比和动态高度
  const aspectRatio = height / width;
  const dynamicHeight = CAROUSEL_CONTAINER_WIDTH * aspectRatio;

  // 应用高度限制
  const minHeight = 378;
  const maxHeight = 504;
  const clampedHeight = Math.max(minHeight, Math.min(maxHeight, dynamicHeight));

  return `${clampedHeight}px`;
});
```

## 实际效果对比

### 修改前（固定高度）

```vue
<ImageWorkPreview
  carousel-max-height="78%"
  <!-- 其他属性 -->
/>
```

- 所有图片使用相同的固定高度
- 可能导致图片变形或显示不完整
- 无法适应不同比例的图片

### 修改后（动态高度）

```vue
<ImageWorkPreview
  :carousel-max-height="dynamicCarouselMaxHeight"
  <!-- 其他属性 -->
/>
```

- 根据第一张图片的实际比例计算高度
- 保持图片原始比例，避免变形
- 自动适应不同尺寸的图片内容

## 测试场景

### 1. 正常场景测试

```javascript
// 测试数据
const testCases = [
  {
    name: '正方形图片',
    imageSize: { width: 800, height: 800 },
    expectedHeight: '378px',
  },
  {
    name: '横向图片',
    imageSize: { width: 1920, height: 1080 },
    expectedHeight: '212.625px',
  },
  {
    name: '纵向图片',
    imageSize: { width: 1080, height: 1920 },
    expectedHeight: '504px', // 被限制
  },
];
```

### 2. 边界情况测试

```javascript
// 边界测试
const edgeCases = [
  {
    name: '空图片列表',
    imgList: [],
    expectedHeight: '78%',
  },
  {
    name: '图片加载失败',
    firstImageSize: null,
    expectedHeight: '78%',
  },
  {
    name: '宽度为零',
    imageSize: { width: 0, height: 600 },
    expectedHeight: '78%',
  },
];
```

## 性能考虑

### 1. 计算属性缓存

- 使用 Vue 的 `computed` 确保只在依赖变化时重新计算
- 避免不必要的重复计算

### 2. 异步图片加载

- 图片尺寸获取不阻塞界面渲染
- 使用 Promise 包装图片加载过程

### 3. 错误处理

- 图片加载失败时快速回退到默认值
- 避免因网络问题影响用户体验

## 兼容性说明

- **向后兼容**：当无法获取图片尺寸时，自动使用原有的默认高度
- **渐进增强**：在支持的环境中提供更好的体验，在不支持的环境中保持基本功能
- **错误恢复**：任何异常情况都会优雅地回退到安全的默认状态
