/**
 * @description 差异化表单校验功能测试
 * 测试保存模式和生成模式的校验差异
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';

// 模拟 useForm composable 的部分功能
import { VALIDATION_TRIGGER } from '@/views/EditProjectView/types/index';
import type { FormRule } from '@/views/EditProjectView/types/index';

/**
 * 模拟校验模式
 */
const VALIDATION_MODE = {
  GENERATE: 'generate', // 生成模式：完整校验
  SAVE: 'save', // 保存模式：仅最大长度校验
} as const;

let currentValidationMode: string = VALIDATION_MODE.GENERATE;

/**
 * 设置校验模式
 */
const setValidationMode = (mode: string): void => {
  currentValidationMode = mode;
};

/**
 * 创建必填验证规则（测试版本）
 */
const createRequiredRule = (
  _label: string,
  _componentType: string,
): FormRule => {
  return {
    trigger: VALIDATION_TRIGGER.MAGIC,
    triggerAuto: true,
    validator: (
      _rule: FormRule,
      value: unknown,
      callback: (error?: Error) => void,
    ) => {
      // 保存模式跳过必填校验
      if (currentValidationMode === VALIDATION_MODE.SAVE) {
        console.log('保存模式：跳过必填校验');
        return callback();
      }

      // 统一的空值检查逻辑
      const isEmpty = Array.isArray(value)
        ? value.length === 0
        : !value || String(value).trim() === '';

      if (isEmpty) {
        callback(new Error('输入不能为空'));
      } else {
        callback();
      }
    },
  };
};

/**
 * 创建长度验证规则（测试版本）
 */
const createLengthRule = (
  label: string,
  minLength?: number,
  maxLength?: number,
): FormRule => {
  return {
    trigger: VALIDATION_TRIGGER.MAGIC,
    triggerAuto: true,
    min: minLength,
    max: maxLength,
    validator: (
      _rule: FormRule,
      value: unknown,
      callback: (error?: Error) => void,
    ) => {
      const valueStr = String(value || '');
      const length = valueStr.length;

      // 保存模式下跳过最小长度校验
      const shouldCheckMinLength =
        currentValidationMode !== VALIDATION_MODE.SAVE;

      if (shouldCheckMinLength && minLength && length < minLength) {
        callback(new Error(`${label}不能少于${minLength}个字符`));
      } else if (maxLength && length > maxLength) {
        callback(new Error(`输入超出${maxLength}个字符限制`));
      } else {
        callback();
      }
    },
  };
};

describe('差异化表单校验功能', () => {
  beforeEach(() => {
    // 重置校验模式
    setValidationMode(VALIDATION_MODE.GENERATE);
  });

  describe('必填校验规则', () => {
    it('生成模式下应该进行必填校验', async () => {
      setValidationMode(VALIDATION_MODE.GENERATE);
      const rule = createRequiredRule('测试字段', 'text');

      const mockCallback = vi.fn();
      rule.validator!(rule, '', mockCallback);

      expect(mockCallback).toHaveBeenCalledWith(new Error('输入不能为空'));
    });

    it('保存模式下应该跳过必填校验', async () => {
      setValidationMode(VALIDATION_MODE.SAVE);
      const rule = createRequiredRule('测试字段', 'text');

      const mockCallback = vi.fn();
      rule.validator!(rule, '', mockCallback);

      expect(mockCallback).toHaveBeenCalledWith();
    });

    it('生成模式下有值时应该通过校验', async () => {
      setValidationMode(VALIDATION_MODE.GENERATE);
      const rule = createRequiredRule('测试字段', 'text');

      const mockCallback = vi.fn();
      rule.validator!(rule, '有效值', mockCallback);

      expect(mockCallback).toHaveBeenCalledWith();
    });
  });

  describe('长度校验规则', () => {
    it('生成模式下应该进行最小长度校验', async () => {
      setValidationMode(VALIDATION_MODE.GENERATE);
      const rule = createLengthRule('测试字段', 5, 20);

      const mockCallback = vi.fn();
      rule.validator!(rule, '123', mockCallback);

      expect(mockCallback).toHaveBeenCalledWith(
        new Error('测试字段不能少于5个字符'),
      );
    });

    it('保存模式下应该跳过最小长度校验', async () => {
      setValidationMode(VALIDATION_MODE.SAVE);
      const rule = createLengthRule('测试字段', 5, 20);

      const mockCallback = vi.fn();
      rule.validator!(rule, '123', mockCallback);

      expect(mockCallback).toHaveBeenCalledWith();
    });

    it('两种模式下都应该进行最大长度校验', async () => {
      // 生成模式
      setValidationMode(VALIDATION_MODE.GENERATE);
      const rule1 = createLengthRule('测试字段', 5, 10);

      const mockCallback1 = vi.fn();
      rule1.validator!(rule1, '这是一个超过十个字符的长文本', mockCallback1);

      expect(mockCallback1).toHaveBeenCalledWith(
        new Error('输入超出10个字符限制'),
      );

      // 保存模式
      setValidationMode(VALIDATION_MODE.SAVE);
      const rule2 = createLengthRule('测试字段', 5, 10);

      const mockCallback2 = vi.fn();
      rule2.validator!(rule2, '这是一个超过十个字符的长文本', mockCallback2);

      expect(mockCallback2).toHaveBeenCalledWith(
        new Error('输入超出10个字符限制'),
      );
    });

    it('符合长度要求时应该通过校验', async () => {
      setValidationMode(VALIDATION_MODE.GENERATE);
      const rule = createLengthRule('测试字段', 5, 20);

      const mockCallback = vi.fn();
      rule.validator!(rule, '这是一个有效的文本', mockCallback);

      expect(mockCallback).toHaveBeenCalledWith();
    });
  });

  describe('校验模式切换', () => {
    it('应该能正确切换校验模式', () => {
      expect(currentValidationMode).toBe(VALIDATION_MODE.GENERATE);

      setValidationMode(VALIDATION_MODE.SAVE);
      expect(currentValidationMode).toBe(VALIDATION_MODE.SAVE);

      setValidationMode(VALIDATION_MODE.GENERATE);
      expect(currentValidationMode).toBe(VALIDATION_MODE.GENERATE);
    });
  });
});
