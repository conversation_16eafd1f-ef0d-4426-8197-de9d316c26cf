import { POST, POST_JSON } from '@/api/request';

/**
 * 获取成员列表
 * @param params 请求参数
 * @param params.pageNow 当前页
 * @param params.limit 每页条数
 * @param params.desc 是否倒序
 * @param params.sortKey 排序字段
 * @returns 成员列表
 * */
export const getMemberList = (params: {
  /** 当前页 */
  pageNow: number;
  /** 每页条数 */
  limit: number;
  /** 是否倒序 */
  desc: boolean;
  /** 排序字段 */
  sortKey: string;
}) => {
  return POST_JSON<
    {
      /** sid */
      sid: number;
      /** 成员名称 */
      sacct: string;
      /** 成员权限(0-无,1-超级管理员,2-管理员) */
      authBit: number;
    }[]
  >('/api/scStaff/getStaffList', JSON.stringify(params));
};

/**
 * 设置成员权限
 * @param sid 成员sid
 * @param cancel 是否取消权限 true-取消（无权限） false-设置（管理员）
 * @returns
 */
export const setMemberAuth = (sid: number, cancel: boolean) => {
  return POST<{
    /** 是否成功 */
    success: boolean;
  }>('/api/scStaff/setAuth', {
    sid,
    cancel,
  });
};

/**
 * 获取点数明细
 */
export const getPointDetail = (params: {
  type: number[]; // 类型
  pageNow: number; // 当前页
  limit: number; // 每页条数
  startDate?: number; // 开始时间
  endDate?: number; // 结束时间
}) => {
  return POST_JSON<
    {
      id: string | number;
      createTime: number;
      typeStr: string;
      relatedStr: string;
      point: number;
      balance: number;
    }[]
  >('/api/optLog/getList4Point', JSON.stringify(params));
};

/**
 * 获取剩余创作点数列表
 */
export const getRemainPointList = () => {
  return POST_JSON<
    {
      /** 剩余点数 */
      restPoint: number;
      /** 到期时间 */
      expiredTime: string;
    }[]
  >('/api/optLog/getList4PointSchedule', JSON.stringify({}));
};
