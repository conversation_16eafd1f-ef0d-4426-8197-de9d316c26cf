import { getMaterialFullUrl } from '@/components/MaterialBasicUpload/utils/index.ts';
import { FILE_TYPES } from '@/constants/fileType';
import { Dubbing, Music } from '@/types';

export interface ApiMusic {
  /** 音乐资源唯一id */
  id: number;
  /** 所属用户id */
  aid: number;
  /** 资源id */
  resId: string;
  /** 音乐名称 */
  name: string;
  /** 资源类型，13表示音乐 */
  type: number;
  /** 状态，0为正常 */
  status: number;
  /** 标记字段，0为无特殊标记 */
  flag: number;
  /** 所属文件夹id */
  folderId: number;
  /** 文件大小，单位字节 */
  fileSize: number;
  /** 文件类型，55为音乐文件类型 */
  fileType: number;
  /** 被引用次数 */
  referCnt: number;
  /** 额外信息 */
  extra: {
    /** 时长，单位是秒 */
    duration: number;
    /** 封面资源id */
    cover: string;
    /** 配音员id，''表示无配音 */
    voiceId: string;
    /** 配音类型，空字符串表示无配音类型 */
    voiceType: string;
  };
  /** 创建时间，时间戳（毫秒） */
  createTime: number;
  /** 更新时间，时间戳（毫秒） */
  updateTime: number;
  /** 是否为文件夹，false表示不是文件夹 */
  folder: boolean;
}

export function transformMusicFromApiToMusic(apiMusic: ApiMusic): Music {
  return {
    resId: apiMusic.resId,
    name: apiMusic.name,
    cover: {
      resId: apiMusic.extra.cover,
      resType: FILE_TYPES.WEBP,
    },
    duration: (() => {
      const minutes = Math.floor(apiMusic.extra.duration / 60);
      const seconds = apiMusic.extra.duration % 60;
      const pad = (num: number) => num.toString().padStart(2, '0');
      return `${pad(minutes)}:${pad(seconds)}`;
    })(),
    link: getMaterialFullUrl(apiMusic.resId, apiMusic.fileType, 'oss'),
  };
}

export interface ApiDubbing {
  /** 配音资源主键id */
  id: number;
  /** 所属应用id */
  aid: number;
  /** 资源id */
  resId: string;
  /** 配音名称 */
  name: string;
  /** 资源类型，14为配音 */
  type: number;
  /** 状态，0为正常 */
  status: number;
  /** 标记字段，0为无特殊标记 */
  flag: number;
  /** 所属文件夹id */
  folderId: number;
  /** 文件大小，单位字节 */
  fileSize: number;
  /** 文件类型，85为配音文件类型 */
  fileType: number;
  /** 被引用次数 */
  referCnt: number;
  /** 分类名 */
  categoryName: string;
  /** 额外信息 */
  extra: {
    /** 配音员封面资源id*/
    cover: string;
    /** 配音员封面资源type */
    coverType: number;
    /** 时长 */
    duration: number;
    /** 类型名 */
    categoryName: string;
    /** 配音员id */
    voiceType: string;
    /** 额外类型 */
    extraType?: string;
  };
  /** 创建时间，时间戳（毫秒） */
  createTime: number;
  /** 更新时间，时间戳（毫秒） */
  updateTime: number;
  /** 是否为文件夹，false表示不是文件夹 */
  folder: boolean;
}

export function transformDubbingFromApiToDubbing(
  apiDubbing: ApiDubbing,
): Dubbing {
  return {
    voiceId: apiDubbing.extra.voiceType,
    name: apiDubbing.name,
    cover: {
      resId: apiDubbing.extra.cover,
      resType: FILE_TYPES.WEBP,
    },
    typeName: apiDubbing.extra.categoryName || '未知',
    categoryName: apiDubbing.categoryName || '',
    link: getMaterialFullUrl(apiDubbing.resId, apiDubbing.fileType, 'oss'),
    extraType: apiDubbing.extra.extraType,
  };
}
