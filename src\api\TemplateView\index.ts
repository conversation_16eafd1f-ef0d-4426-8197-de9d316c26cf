import { GET, POST, POST_JSON, RequestResult } from '@/api/request';
import { PROJECT_TYPE } from '@/constants/project';
import { Template } from '@/types';

/**
 * 获取行业和场景列表
 * @returns 行业和场景列表
 */
export const getIndustryAndScene = async (): Promise<
  RequestResult<{
    industry: {
      id: number;
      name: string;
    }[];
    scene: {
      id: number;
      name: string;
    }[];
  }>
> => {
  // 调用接口，获取原始数据
  const [err, res] = await GET<{
    /** 行业原始列表 */
    industryList: {
      key: number;
      value: string;
    }[];
    /** 场景原始列表 */
    sceneList: {
      key: number;
      value: string;
    }[];
  }>('/api/template/getConfInfo');
  if (err) return [err, null];
  // 数据清洗
  const newRes = {
    data: {
      industry: res.data.industryList.map(
        (item: { key: number; value: string }) => ({
          id: item.key,
          name: item.value,
        }),
      ),
      scene: res.data.sceneList.map((item: { key: number; value: string }) => ({
        id: item.key,
        name: item.value,
      })),
    },
    msg: res.msg,
    rt: res.rt,
    success: res.success,
    total: res.total,
  };
  return [err, newRes];
};

export interface TemplateQuery extends Record<string, unknown> {
  /** 行业id */
  industry: number;
  /** 场景id */
  scene: number;
  /** 模板类型 */
  type: PROJECT_TYPE;
  /** 搜索关键词 */
  search: string;
  /** 页码 */
  page: number;
  /** 是否仅查看内部上架模板 */
  internalTemplate: boolean;
}

/**
 * 获取模板列表
 * @param query 模板查询参数
 * @returns 模板列表
 */
export const getTemplate = (query: TemplateQuery) => {
  return POST_JSON<Template[]>('/api/template/getTemplateList', {
    type: query.type,
    keyWord: query.search,
    industry: query.industry,
    scene: query.scene,
    pageNow: query.page,
    internalTemplate: query.internalTemplate,
    limit: 20,
  });
};

/**
 * 创建项目
 * @param templateId 模板ID
 * @returns 项目创建结果
 */
export const createProject = (templateId: string) => {
  return POST<{ id: string }>('/api/createProject', { templateId });
};

/**
 * 获取模板预览信息
 * @param templateId 模板ID
 * @returns 模板预览信息
 */
export const getTemplatePreviewInfo = (templateId: string) => {
  return GET<Template>('/api/template/getTempPreviewInfo', { id: templateId });
};

/**
 * 获取模板状态
 * @param templateId 模板ID
 * @returns 模板状态
 */
export const getTemplateStatus = (templateId: string) => {
  return GET<{
    /** 原型是否可用 */
    isCanUseProto: boolean;
    /** 模板是否可用 */
    isCanUseTemplate: boolean;
  }>('/api/template/getTemplateStatus', {
    id: templateId,
  });
};
