# 轮询接口优化记录

## 概述

将智能轮询功能的接口调用从通用的 `/api/work/getList` 改为专门的轮询接口 `/api/work/getListById`，以提高轮询性能和准确性。

## 修改详情

### 1. 新增类型定义

#### 文件：`src/api/EditProjectView/types/request.ts`

新增轮询专用接口请求参数类型：

```typescript
/**
 * 轮询专用接口请求参数
 * @description 用于 /api/work/getListById 接口的参数定义
 */
export interface GetWorkListByIdParams {
  /** 项目ID（必填） */
  projectId: number;
  /** 作品ID列表（必填，数组格式） */
  idList: number[];
}
```

### 2. 新增 API 接口函数和代码复用优化

#### 文件：`src/api/EditProjectView/work.ts`

**新增轮询专用接口函数：**

```typescript
/**
 * @description 轮询专用接口 - 根据作品ID列表获取作品数据
 */
export const getWorkListById = async (params: GetWorkListByIdParams) => {
  return POST_JSON<WorkListResponse['data']>('/api/work/getListById', params);
};
```

**重构数据转换逻辑，提取通用函数：**

```typescript
/**
 * @description 通用的作品列表数据转换函数
 * @param res API响应数据
 * @param projectId 项目ID，用于更新Vuex状态
 * @param updateStore 是否更新Vuex状态，默认为true
 */
const transformWorkListResponse = (
  res: { data?: WorkListResponse['data'] } | null,
  projectId: number,
  updateStore: boolean = true,
) => {
  // 统一的数据转换逻辑
  // 包含作品列表转换、Vuex状态更新等
};
```

**优化后的接口函数：**

```typescript
// 轮询专用接口，不更新Vuex状态
export const getWorkListByIdWithTransform = async (
  params: GetWorkListByIdParams,
) => {
  const [err, res] = await getWorkListById(params);
  if (err) return [err, null] as const;

  const result = transformWorkListResponse(res, params.projectId, false);
  return [null, result] as const;
};

// 通用接口，更新Vuex状态
export const getWorkListWithTransform = async (params: GetWorkListParams) => {
  const [err, res] = await getWorkList(params);
  if (err) return [err, null] as const;

  const result = transformWorkListResponse(res, params.projectId, true);
  return [null, result] as const;
};
```

### 3. 修改轮询逻辑

#### 文件：`src/views/EditProjectView/composables/useWorkPolling/index.ts`

修改 `performPollingUpdate` 函数中的 API 调用：

**修改前：**

```typescript
const [err, res] = await getWorkListWithTransform({
  projectId,
  pageNow: 1,
  limit: pollingWorkIds.length,
  idList: pollingWorkIds,
});
```

**修改后：**

```typescript
const [err, res] = await getWorkListByIdWithTransform({
  projectId,
  idList: pollingWorkIds,
});
```

## 优化效果

### 1. 接口参数简化

- **移除不必要参数**：去除了 `pageNow` 和 `limit` 参数
- **专用化设计**：接口专门为轮询场景设计，参数更精简

### 2. 性能提升

- **减少数据传输**：只传输必要的项目 ID 和作品 ID 列表
- **提高查询效率**：后端可以针对轮询场景进行优化
- **避免不必要的状态更新**：轮询时不更新 Vuex 状态，减少性能开销

### 3. 代码质量提升

- **职责分离**：轮询逻辑使用专用接口，与分页查询逻辑分离
- **语义明确**：接口名称明确表达轮询用途
- **代码复用**：提取通用转换函数，消除重复代码
- **维护性增强**：数据转换逻辑集中管理，便于维护和修改

### 4. 架构优化

- **统一转换逻辑**：所有作品列表接口使用相同的数据转换函数
- **灵活的状态管理**：可控制是否更新 Vuex 状态，适应不同使用场景
- **更好的扩展性**：新增类似接口时可直接复用转换逻辑

## 兼容性保证

### 1. 返回数据格式

- 新接口返回数据格式与原接口完全一致
- 通过 `transformWorkListResponse` 通用函数确保数据转换逻辑一致
- 两个接口函数共享相同的转换逻辑，保证数据结构统一

### 2. 错误处理

- 保持现有的 `[err, res]` 错误处理格式
- 错误处理机制不变

### 3. 页面恢复功能

- 页面恢复时的全量更新仍使用原 `getWorkListWithTransform` 接口
- 确保页面恢复功能不受影响

## 测试验证

### 1. 功能测试

- [ ] 轮询功能正常工作
- [ ] 状态更新正确
- [ ] 错误处理正常

### 2. 性能测试

- [ ] 轮询响应时间
- [ ] 网络请求优化效果

### 3. 兼容性测试

- [ ] 现有功能不受影响
- [ ] 页面恢复功能正常

## 注意事项

1. **接口依赖**：确保后端 `/api/work/getListById` 接口已实现
2. **数据一致性**：新接口返回数据结构需与原接口保持一致
3. **错误处理**：新接口的错误响应格式需与现有接口一致

---

**修改时间**：2025-01-22  
**修改人员**：前端开发团队  
**影响范围**：智能轮询系统
