<!-- 动态表单组件 -->
<template>
  <div class="dynamic-form">
    <fa-form-model
      ref="dynamicFormRef"
      :model="formData"
      :label-col="formConfig?.labelCol || DEFAULT_LABEL_COL"
      :wrapper-col="formConfig?.wrapperCol || DEFAULT_WRAPPER_COL"
      :layout="formConfig?.layout || DEFAULT_LAYOUT"
      @submit.native.prevent="handleSubmit"
    >
      <!-- 表单项循环渲染 -->
      <template v-for="item in formItems">
        <!-- 表单项 -->
        <DynamicFormItem
          :key="item.prop"
          :form-item="item"
          :form-items="formItems"
          :form-values="formData"
          :form-config="formConfig"
          :rules="formRules[item.prop]"
          @upload-click="handleUploadClick"
          @file-delete="handleFileDelete"
          @replace="handleReplace"
          @field-change="handleFieldChange"
        >
          <!-- 为每个自定义插槽透传插槽内容 -->
          <template
            v-if="item.type === 'slot' && item.slotName"
            v-slot:[item.slotName]="slotData"
          >
            <slot :name="item.slotName" v-bind="slotData"></slot>
          </template>
        </DynamicFormItem>
      </template>

      <!-- 操作按钮 -->
      <fa-form-model-item v-if="showButtons" class="dynamic-form__actions">
        <fa-button v-if="showResetBtn" @click="handleReset">
          {{ resetBtnText }}
        </fa-button>
        <fa-button type="primary" html-type="submit" :loading="submitLoading">
          {{ submitBtnText }}
        </fa-button>
      </fa-form-model-item>
    </fa-form-model>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, watch, onMounted, type PropType } from 'vue';
import { message } from '@fk/faicomponent';
import DynamicFormItem from '@/components/DynamicForm/FormItems/DynamicFormItem.vue';
import { cloneDeep } from 'lodash-es';
import {
  DEFAULT_LABEL_COL,
  DEFAULT_WRAPPER_COL,
  DEFAULT_LAYOUT,
  DEFAULT_SUBMIT_TEXT,
  DEFAULT_RESET_TEXT,
} from './constants';
import type {
  FormItemInternal,
  DynamicFormMethods,
  DynamicFormProps,
  FormEventParams,
  FormRule,
} from '@/views/EditProjectView/types/index';

/**
 * @description fa-form-model 组件的字段类型定义
 * @interface FormModelField
 */
interface FormModelField {
  /** 字段属性名 */
  prop: string;
  /** 其他字段属性 */
  [key: string]: unknown;
}

/**
 * @description fa-form-model 组件实例类型定义
 * @interface FormModelInstance
 */
interface FormModelInstance {
  /** 表单字段数组 */
  fields?: FormModelField[];
  /** 验证表单方法 */
  validate: (callback: (valid: boolean) => void) => void;
  /** 验证单个字段方法 */
  validateField: (prop: string) => void;
  /** 重置表单字段方法 */
  resetFields: () => void;
  /** 重置单个字段方法 */
  resetField: (prop: string) => void;
  /** 清除验证信息方法 */
  clearValidate: (props?: string | string[]) => void;
  /** 其他属性 */
  [key: string]: unknown;
}

/**
 * 动态表单组件
 * @description 根据配置动态生成表单，支持多种表单控件类型，提供表单验证、数据管理等功能
 * @example
 * ```vue
 * <DynamicForm
 *   ref="formRef"
 *   :form-config="formConfig"
 *   :form-items="formItems"
 *   :initial-values="initialValues"
 *   :show-buttons="true"
 *   :show-reset-btn="true"
 *   @submit="handleSubmit"
 *   @upload-click="handleUploadClick"
 * />
 * ```

 * @since 1.0.0
 */
export default defineComponent({
  name: 'DynamicForm',
  components: {
    DynamicFormItem,
  },
  props: {
    /**
     * 表单配置
     * @description 包含表单的布局、样式等全局配置信息
     * @default {}
     */
    formConfig: {
      type: Object as PropType<DynamicFormProps['formConfig']>,
      default: () => ({}),
    },
    /**
     * 表单项配置数组
     * @description 定义表单中每个字段的类型、验证规则、属性等配置
     * @default []
     */
    formItems: {
      type: Array as PropType<FormItemInternal[]>,
      default: () => [],
    },
    /**
     * 表单初始值
     * @description 设置表单字段的初始值，key为字段名，value为初始值
     * @default {}
     */
    initialValues: {
      type: Object as PropType<Record<string, unknown>>,
      default: () => ({}),
    },
    /**
     * 是否显示操作按钮区域
     * @description 控制是否显示表单底部的提交、重置等操作按钮
     * @default false
     */
    showButtons: {
      type: Boolean,
      default: false,
    },
    /**
     * 是否显示重置按钮
     * @description 在showButtons为true时，控制是否显示重置按钮
     * @default false
     */
    showResetBtn: {
      type: Boolean,
      default: false,
    },
    /**
     * 提交按钮文字
     * @description 自定义提交按钮的显示文字
     * @default "提交"
     */
    submitBtnText: {
      type: String,
      default: DEFAULT_SUBMIT_TEXT,
    },
    /**
     * 重置按钮文字
     * @description 自定义重置按钮的显示文字
     * @default "重置"
     */
    resetBtnText: {
      type: String,
      default: DEFAULT_RESET_TEXT,
    },
  },
  emits: {
    /**
     * 表单提交事件
     * @description 表单通过验证后触发，传递表单数据和回调函数
     * @param formData 表单数据对象
     * @param callback 提交完成后的回调函数
     */
    'submit': (
      _formData: Record<string, unknown>,
      _callback: (success?: boolean, message?: string) => void,
    ) => true,
    /**
     * 表单重置事件
     * @description 点击重置按钮或调用重置方法时触发
     */
    'reset': () => true,
    /**
     * 文件上传点击事件
     * @description 点击文件上传按钮时触发，传递上传相关信息
     * @param data 上传事件数据，包含字段名、媒体类型等信息
     */
    'upload-click': (_data: FormEventParams['upload']) => true,
    /**
     * 文件删除事件
     * @description 删除已上传文件时触发
     * @param data 删除事件数据，包含字段名、文件信息等
     */
    'file-delete': (_data: FormEventParams['fileDelete']) => true,
    /**
     * 文件替换事件
     * @description 替换已上传文件时触发
     * @param data 替换事件数据，包含原文件信息、字段名等
     */
    'replace': (_data: FormEventParams['replace']) => true,
  },
  setup(props, { emit, expose }) {
    // ============= 响应式数据 =============

    /**
     * 表单引用
     * @description 用于访问底层fa-form-model表单组件的实例
     */
    const dynamicFormRef = ref<FormModelInstance | null>(null);

    /**
     * 表单数据
     * @description 存储表单中所有字段的当前值
     */
    const formData = ref<Record<string, unknown>>({});

    /**
     * 表单验证规则
     * @description 存储每个字段的验证规则配置
     */
    const formRules = ref<Record<string, FormRule[]>>({});

    /**
     * 提交加载状态
     * @description 控制提交按钮的加载状态显示
     */
    const submitLoading = ref(false);

    /**
     * 已验证过的字段集合
     * @description 记录哪些字段已经进行过验证，用于动态添加验证触发器
     */
    const validatedFields = ref<Set<string>>(new Set());

    // ============= 方法定义 =============

    /**
     * 初始化表单数据
     * @description 根据表单项配置和初始值设置表单数据的初始状态
     * @private
     */
    const initFormData = (): void => {
      const data: Record<string, unknown> = {};

      // 从表单项配置中提取字段
      props.formItems.forEach(item => {
        if (item.prop) {
          // 优先使用传入的初始值，否则使用表单项的默认值
          const value =
            props.initialValues[item.prop] !== undefined
              ? props.initialValues[item.prop]
              : item.defaultValue;

          data[item.prop] = value;
        }
      });

      formData.value = data;
    };

    /**
     * 初始化表单校验规则
     * @description 根据表单项配置生成验证规则，并处理已验证字段的触发器
     * @private
     */
    const initFormRules = (): void => {
      const rules: Record<string, FormRule[]> = {};

      props.formItems.forEach(item => {
        if (item.prop && item.rules) {
          // 深拷贝规则，避免修改原始配置
          rules[item.prop] = cloneDeep(item.rules);

          // 如果字段已经验证过，为规则添加触发器
          if (validatedFields.value.has(item.prop)) {
            // 先将规则添加到formRules中，以便addFieldTrigger可以找到它们
            formRules.value[item.prop] = rules[item.prop];
            addFieldTrigger(item.prop);
          }
        }
      });

      // 更新formRules
      formRules.value = { ...formRules.value, ...rules };

      // 移除不再存在的字段规则
      Object.keys(formRules.value).forEach(key => {
        const fieldExists = props.formItems.some(item => item.prop === key);
        if (!fieldExists) {
          delete formRules.value[key];
        }
      });
    };

    /**
     * 处理表单提交
     * @description 验证表单并触发提交事件，显示相应的加载状态和错误提示
     * @public
     */
    const handleSubmit = (): void => {
      if (!dynamicFormRef.value) return;

      dynamicFormRef.value.validate((valid: boolean) => {
        if (!valid) {
          message.error('表单验证失败，请检查填写内容');
          return;
        }

        submitLoading.value = true;
        emit('submit', { ...formData.value }, handleSubmitCallback);
      });
    };

    /**
     * 提交回调处理函数
     * @description 处理表单提交完成后的状态更新和消息提示
     * @param success 提交是否成功
     * @param messageText 提示消息文本
     * @private
     */
    const handleSubmitCallback = (success = true, messageText = ''): void => {
      submitLoading.value = false;

      if (success) {
        if (messageText) {
          message.success(messageText);
        }
      } else {
        message.error(messageText || '提交失败');
      }
    };

    /**
     * 重置表单
     * @description 重置所有表单字段到初始状态，清除验证状态
     * @public
     */
    const handleReset = (): void => {
      if (!dynamicFormRef.value) return;

      dynamicFormRef.value.resetFields();
      // 重置时清空已验证字段记录
      validatedFields.value.clear();
      emit('reset');
    };

    /**
     * 获取表单数据
     * @description 返回当前表单数据的副本
     * @returns 表单数据副本
     * @public
     */
    const getFormData = (): Record<string, unknown> => {
      return { ...formData.value };
    };

    /**
     * 设置表单数据
     * @description 更新表单数据，支持合并或完全覆盖模式
     * @param data 要设置的数据对象
     * @param override 是否完全覆盖现有数据，默认为false（合并模式）
     * @public
     */
    const setFormData = (
      data: Record<string, unknown>,
      override = false,
    ): void => {
      if (!data || typeof data !== 'object') return;

      if (override) {
        // 完全替换表单数据
        formData.value = { ...data };
      } else {
        // 合并表单数据
        Object.keys(data).forEach(key => {
          formData.value[key] = data[key];
        });
      }
    };

    /**
     * 验证表单
     * @description 验证整个表单，返回验证结果的Promise
     * @returns Promise<boolean> 验证结果，true表示验证通过
     * @public
     */
    const validate = (): Promise<boolean> => {
      return new Promise(resolve => {
        if (!dynamicFormRef.value) {
          resolve(false);
          return;
        }

        dynamicFormRef.value.validate((valid: boolean) => {
          // 记录所有已验证字段
          if (!valid && dynamicFormRef.value?.fields) {
            dynamicFormRef.value.fields.forEach((field: FormModelField) => {
              if (field.prop) {
                validatedFields.value.add(field.prop);

                // 为已验证的字段动态添加触发器
                addFieldTrigger(field.prop);
              }
            });
          }
          resolve(valid);
        });
      });
    };

    /**
     * 验证单个字段
     * @description 对指定字段进行验证
     * @param prop 要验证的字段名
     * @public
     */
    const validateField = (prop: string): void => {
      if (dynamicFormRef.value && prop) {
        dynamicFormRef.value.validateField(prop);
      }
    };

    /**
     * 处理字段值变化
     * @description 当表单项字段值发生变化时触发验证，只对已经验证过的字段进行重新验证
     * @param prop 发生变化的字段名
     * @private
     */
    const handleFieldChange = (prop: string): void => {
      // 只有该字段已经验证过，才进行重新验证
      if (validatedFields.value.has(prop)) {
        // 为该字段动态添加触发器
        addFieldTrigger(prop);

        // 验证该字段
        validateField(prop);
      }
    };

    /**
     * 为字段动态添加验证触发器
     * @description 为已验证过的字段添加change和blur触发器，实现实时验证
     * @param prop 字段名
     * @private
     */
    const addFieldTrigger = (prop: string): void => {
      // 检查该字段是否存在rules
      if (formRules.value[prop]) {
        // 遍历字段的所有规则
        formRules.value[prop].forEach(rule => {
          // 如果规则没有trigger或trigger为空，添加change触发器
          if (rule.triggerAuto) {
            rule.trigger = ['change', 'blur'];
          }
        });
      }
    };

    /**
     * 重置特定字段
     * @description 重置指定字段到初始状态，清除该字段的验证状态
     * @param prop 要重置的字段名
     * @public
     */
    const resetField = (prop: string): void => {
      if (dynamicFormRef.value && prop) {
        dynamicFormRef.value.resetField(prop);
        validatedFields.value.delete(prop);
      }
    };

    /**
     * 清除校验错误信息
     * @description 清除指定字段或所有字段的验证错误信息
     * @param props 字段名、字段名数组或不传（清除所有字段）
     * @public
     */
    const clearValidate = (props?: string | string[]): void => {
      if (!dynamicFormRef.value) return;

      if (props) {
        dynamicFormRef.value.clearValidate(props);
        // 如果是清除单个字段，同时从validatedFields中移除
        if (typeof props === 'string') {
          validatedFields.value.delete(props);
        } else if (Array.isArray(props)) {
          props.forEach(prop => validatedFields.value.delete(prop));
        }
      } else {
        dynamicFormRef.value.clearValidate();
        // 清除所有验证记录
        validatedFields.value.clear();
      }
    };

    /**
     * 检查字段是否已经被验证过
     * @description 检查指定字段是否已经进行过验证，用于判断是否需要重新验证
     * @param prop 字段名
     * @returns 是否已验证过
     * @public
     */
    const hasFieldBeenValidated = (prop: string): boolean => {
      return validatedFields.value.has(prop);
    };

    /**
     * 处理文件上传点击事件
     * @description 向父组件传递文件上传点击事件，由父组件处理实际的上传逻辑
     * @param data 上传相关数据，包含字段名、媒体类型等信息
     * @public
     */
    const handleUploadClick = (data: FormEventParams['upload']): void => {
      // 向上传递上传点击事件，由父组件处理实际上传逻辑
      emit('upload-click', data);
    };

    /**
     * 处理文件删除事件
     * @description 向父组件传递文件删除事件
     * @param data 删除相关数据，包含字段名、文件信息等
     * @public
     */
    const handleFileDelete = (data: FormEventParams['fileDelete']): void => {
      // 向上传递文件删除事件
      emit('file-delete', data);
    };

    /**
     * 处理文件替换事件
     * @description 向父组件传递文件替换事件
     * @param data 替换相关数据，包含原文件信息、字段名等
     * @public
     */
    const handleReplace = (data: FormEventParams['replace']): void => {
      // 向上传递更换素材事件
      emit('replace', data);
    };

    // ============= 监听器 =============

    /**
     * 监听表单项配置变化
     * @description 当表单项配置发生变化时，重新初始化验证规则和字段默认值
     */
    watch(
      () => props.formItems,
      newItems => {
        // 重新初始化表单规则
        initFormRules();

        // 检查是否有新增的字段，如果有则初始化其值
        newItems.forEach(item => {
          if (item.prop && formData.value[item.prop] === undefined) {
            // 设置新字段的默认值
            const value =
              props.initialValues[item.prop] !== undefined
                ? props.initialValues[item.prop]
                : item.defaultValue;

            formData.value[item.prop] = value;
          }
        });
      },
      { deep: true },
    );

    // ============= 生命周期 =============

    /**
     * 组件挂载生命周期
     * @description 组件挂载时初始化表单数据和验证规则
     */
    onMounted(() => {
      initFormData();
      initFormRules();
    });

    // ============= 暴露给父组件的方法 =============

    /**
     * 暴露给父组件调用的方法接口
     * @description 实现DynamicFormMethods接口，提供表单操作的标准API
     */
    const exposedMethods: DynamicFormMethods = {
      getFormData,
      setFormData,
      validate,
      validateField,
      handleReset,
      clearValidate,
      resetField,
      hasFieldBeenValidated,
    };

    expose(exposedMethods);

    // ============= 返回模板所需数据 =============

    return {
      // 引用
      dynamicFormRef,

      // 响应式数据
      formData,
      formRules,
      submitLoading,

      // 常量
      DEFAULT_LABEL_COL,
      DEFAULT_WRAPPER_COL,
      DEFAULT_LAYOUT,

      // 方法
      handleSubmit,
      handleUploadClick,
      handleFileDelete,
      handleReplace,
      handleFieldChange,

      // 对外方法
      ...exposedMethods,
    };
  },
});
</script>

<style lang="scss" scoped>
.dynamic-form {
  /* 表单操作按钮区 */
  &__actions {
    @apply mt-24px text-right;

    :deep(.fa-button + .fa-button) {
      @apply ml-8px;
    }
  }

  :deep(.fa-input-lg) {
    @apply text-14px;
  }

  :deep(.fa-select-lg) {
    @apply text-14px;
  }
}
</style>
