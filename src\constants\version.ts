/** 版本枚举 */
export enum VERSION {
  /** 免费版 */
  FREE = 0,
  /** 基础版 */
  BASIC = 3,
  /** 专业版 */
  PRO = 7,
}

/** 版本名称 */
export const VERSION_NAME: { [key in VERSION]: string } = {
  [VERSION.FREE]: '免费版',
  [VERSION.BASIC]: '基础版',
  [VERSION.PRO]: '专业版',
};

/** 版本图标资源 */
export const VERSION_ICON_ASSET_MAP: {
  [key in VERSION]: {
    large: string;
    medium: string;
    small: string;
  };
} = {
  [VERSION.FREE]: {
    small: '/src/assets/common/version/small/free.svg',
    medium: '/src/assets/common/version/medium/free.svg',
    large: '/src/assets/common/version/large/free.svg',
  },
  [VERSION.BASIC]: {
    small: '/src/assets/common/version/small/basic.svg',
    medium: '/src/assets/common/version/medium/basic.svg',
    large: '/src/assets/common/version/large/basic.svg',
  },
  [VERSION.PRO]: {
    small: '/src/assets/common/version/small/pro.svg',
    medium: '/src/assets/common/version/medium/pro.svg',
    large: '/src/assets/common/version/large/pro.svg',
  },
};

/** 是否为免费版本 */
export const IS_FREE_VERSION = (version: VERSION): boolean => {
  return version === VERSION.FREE;
};

/**
 * 是否为最高版本
 */
export const IS_MAX_VERSION = (version: VERSION): boolean => {
  return version === VERSION.PRO;
};

/** 版本数量额度限制key */
export enum VERSION_NUM_KEY {
  /** 成员权限 */
  MEMBER_PERMISSION = 'MEMBER_PERMISSION',
}

/** 版本数量限制 */
export const VERSION_NUM_LIMIT_MAP: {
  [key in VERSION_NUM_KEY]: {
    [key in VERSION]: { limit: number; msg: string };
  };
} = {
  /** 成员权限 */
  [VERSION_NUM_KEY.MEMBER_PERMISSION]: {
    [VERSION.FREE]: {
      limit: 5,
      msg: `${
        VERSION_NAME[VERSION.FREE]
      }最多可支持5个成员协作，如需开放更多成员协作，需要升级版本`,
    },
    [VERSION.BASIC]: {
      limit: 20,
      msg: `${
        VERSION_NAME[VERSION.BASIC]
      }最多可支持20个成员协作，如需开放更多成员协作，需要升级版本`,
    },
    [VERSION.PRO]: {
      limit: 50,
      msg: '成员数量已达上限',
    },
  },
};

/** 单个视频文件上传大小限制（单位：MB） */
export const VERSION_UPLOAD_VIDEO_LIMIT_MAP: Record<
  VERSION,
  { limit: number; msg: string }
> = {
  [VERSION.FREE]: {
    limit: 10,
    msg: `${
      VERSION_NAME[VERSION.FREE]
    }单次上传文件大小限制为10MB。如需上传更大文件请升级至更高版本`,
  },
  [VERSION.BASIC]: {
    limit: 100,
    msg: `${
      VERSION_NAME[VERSION.BASIC]
    }单次上传文件大小限制为100MB。如需上传更大文件请升级至更高版本`,
  },
  [VERSION.PRO]: {
    limit: 200,
    msg: '单个文件大小不能超过200MB',
  },
};

/** 单个图片文件上传大小限制（单位：MB） */
export const VERSION_UPLOAD_IMAGE_LIMIT_MAP: Record<
  VERSION,
  { limit: number; msg: string }
> = {
  [VERSION.FREE]: {
    limit: 10,
    msg: `${
      VERSION_NAME[VERSION.FREE]
    }单次上传图片大小限制为10MB。如需上传更大文件请升级至更高版本`,
  },
  [VERSION.BASIC]: {
    limit: 30,
    msg: '单个图片大小不能超过30MB',
  },
  [VERSION.PRO]: {
    limit: 30,
    msg: '单个图片大小不能超过30MB',
  },
};

/** 总容量限制提示（总容量拿后端返回的参数判断） */
export const VERSION_CAPACITY_LIMIT_TIP: { [key in VERSION]: string } = {
  [VERSION.FREE]: `${
    VERSION_NAME[VERSION.FREE]
  }云存储空间限制为500MB。如需上传更多文件请升级至更高版本`, // 免费版 500MB
  [VERSION.BASIC]: `${
    VERSION_NAME[VERSION.BASIC]
  }云存储空间限制为10GB。如需上传更多文件，需要升级版本`, // 基础版 10GB = 10240MB
  [VERSION.PRO]: '云存储空间容量已达上限，请清除部分文件', // 专业版 500GB = 512000MB
};
