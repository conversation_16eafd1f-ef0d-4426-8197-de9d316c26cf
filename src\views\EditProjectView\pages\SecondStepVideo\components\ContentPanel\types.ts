/**
 * 内容面板相关类型定义
 */

/**
 * 内容面板操作事件参数接口
 */
export interface ContentPanelActionEventParam {
  /** 操作类型 */
  type: string;
  /** 操作动作 */
  action: string;
  /** 附加数据 */
  data?: Record<string, unknown>;
}

/**
 * 音乐信息接口
 */
export interface MusicInfo {
  /** 音乐名称 */
  name: string;
  /** 音乐时长 */
  duration: string;
  /** 音乐资源ID */
  resId: string;
  /** 音乐音量 */
  volume: number;
}

/**
 * 配音信息接口
 */
export interface VoiceInfo {
  /** 配音员名称 */
  name: string;
  /** 配音员性别 */
  gender: string;
  /** 配音员头像 */
  avatar: string;
  /** 配音员ID */
  voiceId: string;
  /** 语速 */
  speed: number;
}

/**
 * 花字标签类型
 */
export type FontTag = {
  styleId: number;
};

export interface WorkData {
  script?: {
    title?: string;
    content?: string;
    segments?: Array<{
      module: string;
      content: string;
      beginTime?: number;
      length?: number;
      highlighted?: string[];
    }>;
  };
  bgMusic?: MusicInfo;
  voice?: VoiceInfo;
}

export interface FontStyle {
  fontStyle: string;
  fontSize: string;
  color: string;
  border: string;
  shadow: string;
}

export interface FontIdsMap {
  [key: number]: FontStyle;
}
