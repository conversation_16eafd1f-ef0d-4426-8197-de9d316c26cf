<template>
  <fa-modal
    id="videoViewer"
    :visible="visible"
    wrapClassName="videoViewer"
    :width="630"
    mask
    centered
    :closable="false"
    :footer="null"
    :destroyOnClose="true"
    :maskCloseable="false"
    @cancel="handleCancel"
  >
    <!-- 关闭按钮直接放在根节点，fixed定位 -->
    <div class="global-close-btn" v-if="isShow" @click="handleCancel">
      <Icon type="guanbi-tancong" class="size-[12px] text-white" />
    </div>
    <div class="viderPlayer" :style="containerStyle">
      <ScVideo
        :loop="true"
        :src="videoSrc"
        @loadedmetadata="onLoadedMetadata"
      ></ScVideo>
    </div>
  </fa-modal>
</template>

<script setup lang="ts">
import { defineProps, defineEmits, ref, computed } from 'vue';
import ScVideo from '../ScVideo.vue';

defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  videoSrc: {
    type: String,
    default: '',
  },
});
const emit = defineEmits(['close']);

const aspectRatio = ref(16 / 9);

const isShow = ref(false);

/**
 * 动态 aspect-ratio，宽高自适应视频实际比例
 */
const containerStyle = computed(() => ({
  aspectRatio: `${aspectRatio.value}`,
}));

/**
 * 监听 loadedmetadata 事件，自动根据视频实际宽高比调整容器比例，横屏/竖屏/正方形视频都能最大化展示且不变形
 * @param e
 */
function onLoadedMetadata(e: Event) {
  const video = e.target as HTMLVideoElement;
  if (video && video.videoWidth && video.videoHeight) {
    aspectRatio.value = video.videoWidth / video.videoHeight;
  }
  setTimeout(() => {
    isShow.value = true;
  }, 300);
}

function handleCancel() {
  emit('close');
}
</script>

<style lang="scss">
#videoViewer {
  .fa-modal-mask {
    @apply bg-[rgba(0,0,0,0.8)];
    filter: alpha(opacity=80);
  }
  .fa-modal-body {
    @apply flex justify-center p-0;
  }
  .fa-modal-content {
    @apply bg-transparent shadow-none;
  }
  .fa-modal-content,
  .fa-modal {
    @apply static;
  }

  .global-close-btn {
    @apply fixed top-[35px] right-[35px] size-[40px] rounded-full bg-[rgba(0,0,0,0.3)] cursor-pointer z-[9999];
    @apply flex justify-center items-center;
    &:hover {
      @apply bg-[rgba(0,0,0,0.5)];
    }
  }
  .viderPlayer {
    @apply max-w-full max-h-[90vh] min-w-[100px] bg-transparent flex justify-center items-center overflow-hidden;
    .video-js,
    video {
      @apply max-w-full max-h-full object-contain bg-transparent;
    }
  }
}
</style>
