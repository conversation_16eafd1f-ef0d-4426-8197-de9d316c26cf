import { ref, computed } from 'vue';
import type { ErrorHandlingConfig } from '../types';
import { logger } from '@/utils/logger';

/**
 * 错误处理Hook
 * @description 统一管理播放器错误状态和重试逻辑
 */
export function useErrorHandling(config: ErrorHandlingConfig = {}) {
  const {
    maxRetries = 3,
    retryDelay = 1000,
    showErrorMessage: _showErrorMessage = true,
    onError,
  } = config;

  // 错误状态
  const error = ref<Error | null>(null);
  const retryCount = ref(0);
  const isRetrying = ref(false);

  // 计算属性
  const hasError = computed(() => error.value !== null);
  const canRetry = computed(() => retryCount.value < maxRetries);
  const errorMessage = computed(() => {
    if (!error.value) return '';

    // 根据错误类型返回用户友好的错误信息
    const errorType = error.value.message;
    const errorMessages: Record<string, string> = {
      MEDIA_ERR_ABORTED: '视频加载被中断，请重试',
      MEDIA_ERR_NETWORK: '网络错误，请检查网络连接',
      MEDIA_ERR_DECODE: '视频解码失败，请尝试其他格式',
      MEDIA_ERR_SRC_NOT_SUPPORTED: '不支持的视频格式',
      TIMEOUT: '加载超时，请重试',
    };

    return errorMessages[errorType] || '视频加载失败，请稍后重试';
  });

  /**
   * 设置错误
   * @param err 错误对象
   */
  const setError = (err: Error) => {
    error.value = err;
    logger.operationError('VideoPlayer Error', err);

    // 调用自定义错误处理函数
    onError?.(err);
  };

  /**
   * 清除错误
   */
  const clearError = () => {
    error.value = null;
    retryCount.value = 0;
    isRetrying.value = false;
  };

  /**
   * 重试操作
   * @param retryFn 重试函数
   */
  const retry = async (retryFn: () => Promise<void> | void) => {
    if (!canRetry.value || isRetrying.value) return;

    isRetrying.value = true;
    retryCount.value++;

    try {
      // 延迟重试
      await new Promise(resolve => setTimeout(resolve, retryDelay));

      // 执行重试函数
      await retryFn();

      // 重试成功，清除错误
      clearError();
    } catch (err) {
      // 重试失败，设置新错误
      setError(err as Error);
    } finally {
      isRetrying.value = false;
    }
  };

  /**
   * 重置错误状态
   */
  const reset = () => {
    clearError();
  };

  return {
    // 状态
    error,
    hasError,
    canRetry,
    isRetrying,
    retryCount,
    errorMessage,

    // 方法
    setError,
    clearError,
    retry,
    reset,
  };
}
