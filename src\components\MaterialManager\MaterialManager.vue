<template>
  <div class="w-full h-[calc(100vh-110px)] bg-white rounded-[16px]">
    <FaFileManager
      prefix="fa"
      :info-list="sortedFmInfoList"
      :info-selects="infoSelects"
      :info-keys="INFO_KEYS"
    >
      <template #sidebar>
        <FileManagerSidebar />
      </template>

      <template #toolbar>
        <FileManagerToolBar v-if="category === 'file'" />
      </template>

      <template #header>
        <FileManagerHeader
          v-if="category === 'file' && currDirPath.length > 1"
        />
        <FileManagerRecycleTip
          v-if="category !== 'file'"
          @handleDeleteLog="showDeleteLog = true"
        />
      </template>

      <template #default>
        <FileManagerContent />

        <FileManagerMoveModal />

        <FileManagerDeleteLog v-model="showDeleteLog" />
      </template>

      <template #footer>
        <FileManagerFooter />
      </template>
    </FaFileManager>
  </div>
</template>

<script>
import { FileManager as FaFileManager } from '@fk/fa-component-cus';
import FileManagerSidebar from '@/components/MaterialManager/layout/FileManagerSidebar/FileManagerSidebar.vue';
import FileManagerToolBar from '@/components/MaterialManager/layout/FileManagerToolBar/FileManagerToolBar.vue';
import FileManagerHeader from '@/components/MaterialManager/layout/FileManagerHeader/FileManagerHeader.vue';
import FileManagerContent from '@/components/MaterialManager/layout/FileManagerContent/FileManagerContent.vue';
import FileManagerFooter from '@/components/MaterialManager/layout/FileManagerFooter/FileManagerFooter.vue';
import FileManagerMoveModal from '@/components/MaterialManager/components/FileManagerMoveModal.vue';
import FileManagerDeleteLog from '@/components/MaterialManager/components/FileManagerDeleteLog.vue';
import FileManagerRecycleTip from '@/components/MaterialManager/components/FileManagerRecycleTip.vue';
import store from '@/store';
import { INFO_KEYS } from '@/constants/material';
import { defineComponent } from 'vue';

export default defineComponent({
  name: 'FileManager',
  components: {
    FaFileManager,
    FileManagerSidebar,
    FileManagerToolBar,
    FileManagerHeader,
    FileManagerContent,
    FileManagerFooter,
    FileManagerMoveModal,
    FileManagerDeleteLog,
    FileManagerRecycleTip,
  },

  data() {
    return {
      showDeleteLog: false,
      INFO_KEYS,
    };
  },

  computed: {
    category() {
      return store.state.meta.category;
    },
    infoSelects() {
      return store.getters.selects;
    },
    sortedFmInfoList() {
      return store.getters.sortedFmInfoList;
    },
    currDirPath() {
      return store.getters.currDirPath;
    },
  },

  created() {
    this.initData();
  },

  methods: {
    async initData() {
      try {
        await Promise.all([
          store.dispatch('materialUpload/updateSpaceUsage'),
          store.dispatch('updateFolderList'),
          store.dispatch('updateFolderContent'),
        ]);
        console.log('素材库数据初始化加载完成');
      } catch (error) {
        console.error('素材库数据初始化加载完成', error);
      }
    },
  },
});
</script>
<style lang="scss" scoped>
::v-deep {
  .fa-breadcrumb {
    > span {
      .fa-breadcrumb-link {
        vertical-align: bottom;
        &:hover {
          @apply text-[#3a84fe];
        }
      }

      &:last-child {
        .fa-breadcrumb-link {
          &:hover {
            @apply text-[#333];
          }
        }
      }
    }
  }
}
</style>
<style lang="scss">
.fa-file-manager {
  @apply w-full h-full;
  .fa-btn,
  .fa-select-selection,
  .fa-input,
  .fa-pagination-simple .fa-pagination-simple-pager input {
    @apply rounded-[6px];
  }
  .fa-file-manager-input-disabled {
    > div {
      color: #999;
    }
  }
}
</style>
