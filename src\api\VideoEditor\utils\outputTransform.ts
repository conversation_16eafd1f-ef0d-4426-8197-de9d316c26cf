import { ApiWorkInfo } from '@/api/EditProjectView/types';
import { WorkItem } from '@/types';
import { PROJECT_TYPE_VIDEO } from '@/views/EditProjectView/constants';
// 新增类型导入
import type {
  ApiVideoScript,
  ApiVideoSetting,
  ApiVideoData,
  ApiImageScript,
  ApiImageTextSetting,
  ApiImageTextData,
  ApiTextStyle,
  ApiImageStyle,
} from '@/api/EditProjectView/types/response';

export function transformWorkInfoToApiWorkInfo(
  workInfo: WorkItem,
): ApiWorkInfo {
  // 通用字段
  const base: Partial<ApiWorkInfo> = {
    id: workInfo.id,
    aid: workInfo.aid,
    projectId: workInfo.projectId,
    type: workInfo.type,
    name: workInfo.name,
    size: workInfo.size ?? 0,
    flag: workInfo.flag ?? 0,
    progress: workInfo.progress,
    status: workInfo.status,
    createTime: Number(workInfo.createTime),
    updateTime: workInfo.updateTime ? Number(workInfo.updateTime) : 0,
    title: workInfo.title,
    content: workInfo.content,
    subtitle: workInfo.subtitle ?? {},
  };

  // 视频类型
  if (workInfo.type === PROJECT_TYPE_VIDEO) {
    const videoWork = workInfo as import('@/types').VideoWorkItem;
    // 处理脚本
    const script: ApiVideoScript = {
      title: videoWork.script.title,
      hashtags: videoWork.script.hashtags,
      segments:
        videoWork.script?.segments?.map(seg => ({
          module: seg.module,
          content: seg.content,
          highlighted: seg.highlighted,
          beginTime: seg.beginTime,
          endTime: seg.beginTime + seg.length,
        })) || [],
    };
    // 处理设置
    const setting: ApiVideoSetting = {
      bgm: {
        open: videoWork.setting?.bgMusic?.open ?? false,
        resId: videoWork.setting?.bgMusic?.resId ?? '',
        volume: videoWork.setting?.bgMusic?.vol ?? 75,
      },
      voice: {
        open: videoWork.setting?.voice?.open ?? false,
        voiceType: videoWork.setting?.voice?.voiceId ?? '',
        speed: videoWork.setting?.voice?.speed ?? 1.0,
        extraType: videoWork.setting?.voice?.extraType,
      },
      style: (videoWork.setting?.style || []).map(
        (style): ApiTextStyle | ApiImageStyle => {
          if (style.type === 'text') {
            return {
              type: 'text',
              x: style.x,
              y: style.y,
              id: style.styleId,
              text: style.text,
              align: style.align,
              fontName: style.fontName,
              fileName: style.fileName,
              fontSize: style.fontSize,
              color: style.color,
              strokeColor: style.strokeColor,
              strokeWidth: style.strokeWidth,
              shadowColor: style.shadowColor,
              shadowX: style.shadowX,
              shadowY: style.shadowY,
              shadowStrokeColor: style.shadowStrokeColor,
              shadowStrokeWidth: style.shadowStrokeWidth,
              boxColor: style.boxColor,
            };
          } else {
            return {
              type: 'image',
              x: style.x,
              y: style.y,
              resId: style.resId ?? '',
              resType: style.resType || 0,
              width: style.width,
              height: style.height,
            };
          }
        },
      ),
    };
    // 处理数据
    const data: ApiVideoData = {
      fullCover: {
        id: videoWork.coverImg,
        type: videoWork.coverImgType ?? 0,
      },
      baseCover: {
        id: videoWork.coverImg,
        type: videoWork.coverImgType ?? 0,
      },
      fullVideo: {
        id: videoWork.contentUrl || '',
        type: videoWork.coverImgType ?? 0,
      },
      baseVideo: {
        id: videoWork.contentUrl || '',
        type: videoWork.coverImgType ?? 0,
        duration: videoWork.duration,
      },
      graphic: [],
      relWorkId: videoWork.relWorkId,
    };
    return {
      ...base,
      script,
      setting,
      data,
    } as ApiWorkInfo;
  }
  // 图文类型
  else {
    const imageWork = workInfo as import('@/types').ImageWorkItem;
    // 处理脚本
    const script: ApiImageScript = {
      title: (imageWork.script?.title || '').trim(),
      hashtags: imageWork.script?.hashtags || [],
      content: imageWork.script?.content || '',
    };
    // 处理设置
    const setting: ApiImageTextSetting = {
      graphicList: (imageWork.setting.graphicList || []).map(item => ({
        id: item.id,
        puzzleType: item.puzzleType,
        ratio: item.ratio,
        puzzleStyle: (item.puzzleStyle || []).map(pz => ({
          // 单图要把坐标重置为0（后端用不上，但是需要跟原始值0进行md5对比）
          x: item.puzzleType != 1 && pz.x ? pz.x : 0,
          y: item.puzzleType != 1 && pz.y ? pz.y : 0,
          scale: pz.scale ?? 0,
          resId: pz.resId ?? '',
          type: pz.type ?? 0,
        })),
        style: (item.style || []).map((style): ApiTextStyle | ApiImageStyle => {
          if (style.type === 'text') {
            return {
              type: 'text',
              x: style.x,
              y: style.y,
              id: style.styleId,
              text: style.text,
              align: style.align,
              fontName: style.fontName,
              fileName: style.fileName,
              fontSize: style.fontSize,
              color: style.color,
              strokeColor: style.strokeColor,
              strokeWidth: style.strokeWidth,
              shadowColor: style.shadowColor,
              shadowX: style.shadowX,
              shadowY: style.shadowY,
              shadowStrokeColor: style.shadowStrokeColor,
              shadowStrokeWidth: style.shadowStrokeWidth,
              boxColor: style.boxColor,
            };
          } else {
            return {
              type: 'image',
              x: style.x,
              y: style.y,
              resId: style.resId ?? '',
              resType: style.resType || 0,
              width: style.width,
              height: style.height,
            };
          }
        }),
      })),
    };
    // 处理数据
    const data: ApiImageTextData = {
      graphic:
        imageWork.data?.graphic?.map(g => ({
          resId: g.resId,
          type: g.type,
        })) || [],
      relWorkId: imageWork.relWorkId,
    };
    return {
      ...base,
      script,
      setting,
      data,
    } as ApiWorkInfo;
  }
}
