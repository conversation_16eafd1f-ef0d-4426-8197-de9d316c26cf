/**
 * 懒加载路由chunk错误处理工具
 * 用于处理生产环境中因部署新版本导致的chunk文件失效问题
 */

interface ChunkErrorHandlerOptions {
  /** 重新加载间隔时间（毫秒），默认10秒 */
  reloadInterval?: number;
  /** 是否显示用户提示，默认false */
  showUserNotification?: boolean;
  /** 自定义错误检测函数 */
  customErrorDetector?: (error: Error) => boolean;
  /** 自定义重新加载前的回调 */
  beforeReload?: () => void;
}

/**
 * 检测是否为chunk加载失败错误
 */
export function isChunkLoadError(error: Error): boolean {
  const errorMessage = error.message.toLowerCase();

  // 常见的chunk加载失败错误模式
  const chunkErrorPatterns = [
    /loading chunk \d* failed/i,
    /failed to fetch dynamically imported module/i,
    /loading css chunk \d* failed/i,
    /chunk load failed/i,
    /network error/i,
  ];

  return chunkErrorPatterns.some(pattern => pattern.test(errorMessage));
}

/**
 * 检查是否在指定时间间隔内已经重新加载过
 */
function shouldSkipReload(reloadInterval: number): boolean {
  const lastReloadTime = sessionStorage.getItem('lastChunkReloadTime');
  if (!lastReloadTime) return false;

  const currentTime = Date.now();
  return currentTime - parseInt(lastReloadTime) <= reloadInterval;
}

/**
 * 显示用户通知
 */
function showNotification(): void {
  // 用户友好的提示信息
  console.info('系统升级完成！正在为您加载最新功能，请稍候...');

  // 可选：显示浏览器原生通知（如果用户允许）
  if ('Notification' in window && Notification.permission === 'granted') {
    new Notification('系统升级完成', {
      body: '新功能已就绪，正在为您更新页面内容...',
      icon: '/favicon.ico',
      tag: 'system-update',
      requireInteraction: false, // 自动消失
      silent: true, // 静默通知，不打扰用户
    });
  }
}

/**
 * 处理chunk加载错误
 */
export function handleChunkError(
  error: Error,
  options: ChunkErrorHandlerOptions = {},
): void {
  const {
    reloadInterval = 10000,
    showUserNotification = false,
    customErrorDetector,
    beforeReload,
  } = options;

  // 使用自定义错误检测器或默认检测器
  const isError = customErrorDetector
    ? customErrorDetector(error)
    : isChunkLoadError(error);

  if (!isError) return;

  console.info('系统资源更新，准备刷新页面获取最新版本');

  // 检查是否应该跳过重新加载
  if (shouldSkipReload(reloadInterval)) {
    console.info('页面刚刚已经刷新过，请稍后再试');
    return;
  }

  // 记录重新加载时间
  sessionStorage.setItem('lastChunkReloadTime', Date.now().toString());

  // 显示用户通知
  if (showUserNotification) {
    showNotification();
  }

  // 执行重新加载前的回调
  if (beforeReload) {
    beforeReload();
  }

  // 延迟刷新，给用户通知一些时间显示
  const delay = showUserNotification ? 1500 : 0;
  setTimeout(() => {
    window.location.reload();
  }, delay);
}

/**
 * 创建Vue Router错误处理器
 */
export function createRouterErrorHandler(
  options: ChunkErrorHandlerOptions = {},
) {
  return (error: Error) => {
    handleChunkError(error, options);
  };
}
