# 临时更新 totalItems 功能

## 功能概述

为了提升用户体验，在作品删除成功后立即更新 WorkListBar 组件中显示的作品总数，而不需要等待完整的列表刷新。

## 问题背景

在普通作品删除操作中，通过 `WORK_REMOVE_REQUEST` 事件删除作品后，虽然后端逻辑已经正确更新了 `totalItems`，但由于不会刷新整个列表，UI 更新可能存在延迟，导致用户看到的总数不能立即反映删除操作的结果。

注意：恢复旧作品场景不需要临时更新，因为它会触发 `REFRESH_WORK_LIST` 事件刷新整个列表，包括 totalItems。

## 解决方案

### 1. 事件机制

新增 `TEMP_UPDATE_TOTAL_ITEMS` 事件，用于临时更新 WorkListBar 的 totalItems 显示：

```typescript
// 事件数据类型
export interface TempUpdateTotalItemsData {
  /** 操作类型：增加或减少 */
  operation: 'decrease' | 'increase';
  /** 变更数量 */
  amount: number;
}
```

### 2. 发送端实现

在 `WorkListBarItem.vue` 中的普通删除成功场景中发送临时更新事件：

```typescript
const handleRemoveSuccess = () => {
  // ... 原有逻辑 ...

  // 临时更新 WorkListBar 的 totalItems 显示
  // 通过 eventBus 发送临时更新事件，确保 UI 立即响应
  eventBus.emit(EVENT_NAMES.TEMP_UPDATE_TOTAL_ITEMS, {
    operation: 'decrease',
    amount: 1,
  });

  // ... 清理逻辑 ...
};
```

### 3. 接收端实现

在 `WorkListBar/index.vue` 中监听并处理临时更新事件：

```typescript
// 监听临时更新 totalItems 事件
const handleTempUpdateTotalItems = (...args: unknown[]) => {
  const data = args[0] as TempUpdateTotalItemsData;

  if (!data || typeof data !== 'object') return;

  if (data.operation === 'decrease') {
    totalItems.value = Math.max(0, totalItems.value - data.amount);
  } else if (data.operation === 'increase') {
    totalItems.value = totalItems.value + data.amount;
  }
};

// 注册事件监听
onMounted(() => {
  eventBus.on(EVENT_NAMES.TEMP_UPDATE_TOTAL_ITEMS, handleTempUpdateTotalItems);
});

// 清理事件监听
onUnmounted(() => {
  eventBus.off(EVENT_NAMES.TEMP_UPDATE_TOTAL_ITEMS, handleTempUpdateTotalItems);
});
```

## 技术特点

### 1. 类型安全

- 使用 TypeScript 接口定义事件数据结构
- 确保编译时类型检查

### 2. 边界保护

- 防止 totalItems 变为负数：`Math.max(0, totalItems.value - data.amount)`
- 参数验证：检查数据有效性

### 3. 内存管理

- 在组件卸载时正确清理事件监听器
- 避免内存泄漏

### 4. 向后兼容

- 不影响现有的数据更新机制
- 临时更新只是 UI 层面的优化

## 使用场景

### 普通作品删除

1. 用户点击删除按钮
2. 发送 `WORK_REMOVE_REQUEST` 事件
3. 删除成功后发送 `TEMP_UPDATE_TOTAL_ITEMS` 事件
4. UI 立即显示更新后的总数
5. 后续的数据同步会确保数据一致性

**注意**：恢复旧作品场景不使用此机制，因为它会触发 `REFRESH_WORK_LIST` 事件刷新整个列表，包括 totalItems。

## 测试覆盖

功能包含完整的单元测试，覆盖以下场景：

- ✅ 减少操作的正确性
- ✅ 增加操作的正确性
- ✅ 边界情况处理（负数保护、无效数据）
- ✅ EventBus 集成测试
- ✅ 实际使用场景模拟

## 性能影响

- **内存占用**：最小化，只增加一个事件监听器
- **执行效率**：高效，直接更新响应式变量
- **网络请求**：无额外网络请求
- **UI 响应**：立即响应，提升用户体验

## 维护注意事项

1. **数据一致性**：临时更新只是 UI 优化，真实数据仍依赖后端同步
2. **事件清理**：确保组件卸载时清理事件监听器
3. **类型安全**：保持事件数据类型定义的准确性
4. **测试维护**：功能变更时同步更新测试用例

## 扩展性

该机制可以轻松扩展到其他需要临时 UI 更新的场景：

- 批量删除操作
- 作品生成完成
- 其他列表数量变化场景

只需要在相应的成功回调中发送 `TEMP_UPDATE_TOTAL_ITEMS` 事件即可。
