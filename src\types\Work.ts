import {
  PROJECT_TYPE_VIDEO,
  PROJECT_TYPE_IMAGE,
} from '@/views/EditProjectView/constants/index';

export enum STYLE_TYPE {
  /** 文字 */
  FONT = 'text',
  /** 图片 */
  PIC = 'image',
}

export interface TextSticker {
  /** 类型 */
  type: 'text';
  /** x轴位置 */
  x: number;
  /** y轴位置 */
  y: number;
  /** 字号 */
  fontSize: number;
  /** 颜色 */
  color: string;
  /** 预设ID */
  styleId: number;
  /** 内容 */
  text: string;
  /** 对齐方式 */
  align: 'left' | 'center' | 'right';
  /** 后端使用的字体名 */
  fontName: string;
  /** 字体文件名, 唯一标识 */
  fileName: string;
  /** 描边颜色 */
  strokeColor?: string;
  /** 描边宽度 */
  strokeWidth?: number;
  /** 阴影填充颜色 */
  shadowColor?: string;
  /** 阴影X轴偏移 */
  shadowX?: number;
  /** 阴影Y轴偏移 */
  shadowY?: number;
  /** 阴影描边颜色 */
  shadowStrokeColor?: string;
  /** 阴影描边宽度 */
  shadowStrokeWidth?: number;
  /** 背景颜色 */
  boxColor?: string;
}

export interface ImageSticker {
  /** 类型 */
  type: 'image';
  /** x轴位置 */
  x: number;
  /** y轴位置 */
  y: number;
  /** 资源ID */
  resId?: string;
  /** 资源类型 */
  resType?: number;
  /** 宽度 */
  width: number;
  /** 高度 */
  height: number;
}

export type StickerSetting = TextSticker | ImageSticker;

/**
 * @description 基础作品类，包含所有类型作品的通用属性
 */
export interface BaseWorkItem {
  /** 作品ID */
  id: number;
  /** 关联作品ID */
  relWorkId?: number;
  /** 账号ID */
  aid: number;
  /** 项目ID */
  projectId: number;
  /** 作品类型（视频：0，图文：1） */
  type: number;
  /** 作品名称 */
  name: string;
  /** 封面图资源ID */
  coverImg: string;
  /** 封面图类型 */
  coverImgType?: number;
  /** 作品文件大小(单位：Byte) */
  size?: number;
  /** 格式化的文件大小 */
  sizeName?: string;
  /** 类型名称 */
  typeName?: string;
  /** 状态名称 */
  statusName?: string;
  /** 项目名称 */
  projectName?: string;
  /** 标记 */
  flag?: number;
  /** 字幕 */
  subtitle?: Record<string, unknown>;
  /** 视频时长（秒） */
  duration: number;
  /** 生成进度（0-100） */
  progress: number;
  /** 作品状态 */
  status: number;
  /** 资源ID列表 */
  resIds?: string[];
  /** 创建时间 */
  createTime: string;
  /** 保存时间 */
  saveTime?: string;
  /** 更新时间 */
  updateTime?: string;
  /** 过期时间 */
  expireTime?: string;
  /** 标题（可选） */
  title?: string;
  /** 内容（可选） */
  content?: string;
  /** 错误信息（生成失败时） */
  errMsg?: string;
  /** 图文是否二次编辑（仅当作品状态为已生成时使用此字段判断是否显示新生成标签） */
  editAgainGraphic?: boolean;
  /** 是否重新编辑（用于统一状态处理系统） */
  editAgain?: boolean;
  /** 是否需要扣除点数（true=需要扣点，false=不需要扣点） */
  pinchPoint?: boolean;
}

/**
 * @description 脚本片段类
 */
export interface ScriptSegment {
  /** 模块名称 */
  module: string;
  /** 开始时间 */
  beginTime: number;
  /** 时长 */
  length: number;
  /** 内容 */
  content: string;
  /** 高亮文本列表 */
  highlighted: string[];
}

/**
 * @description 资源详情接口
 */
export interface ResourceDetail {
  /** 完整资源ID */
  fullId: string;
  /** 完整资源类型 */
  fullType: number;
  /** 基础资源ID */
  baseId?: string;
  /** 基础资源类型 */
  baseType?: number;
}

/**
 * @description 视频作品类
 */
export interface VideoWorkItem extends BaseWorkItem {
  /** 作品类型（固定为视频类型） */
  type: typeof PROJECT_TYPE_VIDEO;
  /** 脚本信息 */
  script: {
    title: string;
    hashtags: string[];
    segments: ScriptSegment[];
  };
  /** 视频设置 */
  setting: {
    /** 背景音乐设置 */
    bgMusic: {
      /** 是否开启背景音乐 */
      open: boolean;
      /** 背景音乐资源ID */
      resId?: string;
      /** 音量（0-100） */
      vol: number;
    };
    /** 配音设置 */
    voice: {
      /** 是否开启配音 */
      open: boolean;
      /** 配音ID */
      voiceId?: string;
      /** 语速（0.5-2.0） */
      speed: number;
      /** 额外类型 */
      extraType?: string;
    };
    /** 样式列表 */
    style: StickerSetting[];
  };
  /** 视频资源 URL */
  contentUrl?: string;
  /** 作品数据详情 */
  data?: {
    /** 关联作品ID */
    relWorkId?: number;
    /** 视频详情 */
    video?: ResourceDetail;
    /** 视频封面详情 */
    videoCover?: ResourceDetail;
    /** 配音ID */
    voiceId?: string;
    /** 错误信息（生成失败时） */
    errMsg?: string;
  };
  /** 关联作品数据详情 */
  relData?: {
    /** 关联作品ID */
    relWorkId?: number;
    /** 视频详情 */
    video?: ResourceDetail;
    /** 视频封面详情 */
    videoCover?: ResourceDetail;
    /** 配音ID */
    voiceId?: string;
  };
}

/**
 * @description 图文作品类
 */
export interface ImageWorkItem extends BaseWorkItem {
  /** 作品类型（固定为图文类型） */
  type: typeof PROJECT_TYPE_IMAGE;
  /** 脚本信息 */
  script?: {
    /** 标题 */
    title?: string;
    /** 内容 */
    content?: string;
    /** 标签 */
    hashtags?: string[];
  };
  /** 图文设置 */
  setting: {
    graphicList: Array<{
      /** 索引值(后端用) */
      id: number;
      /** 拼图类型 */
      puzzleType: number;
      /** 拼图比例 */
      ratio: number;
      /** 拼图样式 */
      puzzleStyle: Array<{
        /** 位置信息-x */
        x?: number;
        /** 位置信息-y */
        y?: number;
        /** 用户设置的缩放比例 */
        scale?: number;
        /** 图片资源id */
        resId: string;
        /** 图片类型  */
        type: number;
        /** 这个拼图是否用户新增的（前端临时变量，用户编辑新增的拼图需要居中渲染） */
        isNewCreate?: boolean;
      }>;
      /** 图片空间信息(前端临时变量) */
      space?: {
        h: number | null;
        v: number | null;
        renderW: number | null;
        renderH: number | null;
        originW: number | null;
      };
      /** 样式设置 */
      style: StickerSetting[];
    }>;
    /** 本次编辑过的最大索引值（前端临时变量，因为会有删除的情况，因此添加次图的时候，新次图的索引需要用本次编辑过索引最大值+1来递增） */
    curMaxId: number;
  };
  /** 图文数据 */
  data?: {
    /** 图文资源列表 */
    graphic?: ResourceBasicInfo[];
    /** 关联作品ID */
    relWorkId?: number;
    /** 错误信息（生成失败时） */
    errMsg?: string;
  };
}

export interface ResourceBasicInfo {
  /** 资源ID */
  resId: string;
  /** 资源类型 */
  type: number;
}

/** 作品类型联合类型 */
export type WorkItem = VideoWorkItem | ImageWorkItem;
