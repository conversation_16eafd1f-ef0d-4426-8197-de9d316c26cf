{"Vue2 Template": {"prefix": "hd-vue", "body": ["<template>", "  <div class=\"$1\"></div>", "</template>", "", "<script>", "export default {", "  name: \"$1\",", "  components: {},", "  props: {},", "  data() {", "    return {};", "  },", "  computed: {},", "  watch: {},", "  created() {},", "  mounted() {},", "  destroyed() {},", "  methods: {},", "};", "</script>", "", "<style lang=\"scss\" scoped></style>", ""], "description": "hd vue 模板"}}