<template>
  <header class="main-view-header">
    <fa-skeleton
      :loading="isShowSkeleton"
      :paragraph="false"
      class="main-view-header__logo-skeleton"
      active
    >
      <img
        width="142"
        height="35"
        src="@/assets/common/logo.svg"
        alt="logo"
        class="main-view-header__logo"
      />
    </fa-skeleton>
    <!-- 基于整个header居中的搜索组件 -->
    <div v-show="isInTemplateView" class="main-view-header__search-container">
      <TemplateSearch />
    </div>
    <TopTools />
  </header>
</template>

<script lang="ts">
import { defineComponent } from 'vue';
import TemplateSearch from '@/components/MainView/TemplateSearch.vue';
import TopTools from '@/components/MainView/TopTools.vue';
import { useSkeleton } from '@/hook/useSkeleton';

export default defineComponent({
  name: 'MainViewHeader',
  components: {
    TemplateSearch,
    TopTools,
  },
  setup() {
    const { isShowSkeleton } = useSkeleton();
    return {
      isShowSkeleton,
    };
  },
  computed: {
    // 使用computed属性来响应式地监听路由变化
    isInTemplateView(): boolean {
      return this.$route.name === 'template';
    },
  },
});
</script>

<style scoped>
.main-view-header {
  /* 布局相关 */
  @apply flex items-center justify-between;
  /* 尺寸相关 */
  @apply min-h-70px h-70px;
  /* 外观相关 */
  @apply bg-white shadow-[0_1px_6px_#0000000f];
  /* 定位相关 */
  @apply relative z-100;
  /* 间距相关 */
  @apply px-32px;
}

.main-view-header__logo-skeleton {
  /* 尺寸相关 */
  @apply w-142px;
}

.main-view-header__logo {
  /* 尺寸相关 */
  @apply w-142px h-auto;
  /* 外观相关 */
  @apply object-cover;
}

.main-view-header__search-container {
  /* 定位相关 */
  @apply absolute left-1/2 top-1/2;
  /* 变换相关 */
  @apply transform -translate-x-1/2 -translate-y-1/2;
}

/* 骨架屏样式 */
::v-deep(
    .main-view-header__logo-skeleton .fa-skeleton-content .fa-skeleton-title
  ) {
  /* 尺寸相关 */
  @apply h-36px;
  /* 外观相关 */
  @apply rounded-8px;
}
</style>
