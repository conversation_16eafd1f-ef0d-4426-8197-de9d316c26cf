<!-- 自定义文件上传组件 -->
<template>
  <div class="custom-upload">
    <!-- 上传按钮 - 当文件列表为空时显示 -->
    <div v-if="!hasFiles" class="custom-upload__area">
      <!-- 上传按钮 -->
      <div
        class="custom-upload__button"
        :class="{ 'custom-upload__button--disabled': disabled }"
        @click="handleUploadClick"
      >
        <fa-icon type="plus" class="custom-upload__button-icon" />
        <div class="custom-upload__button-text">上传</div>
      </div>

      <!-- 示例媒体 -->
      <div class="custom-upload__example">
        <!-- 图片示例 - 在 defaultMediaType 为 image 时显示 -->
        <fa-popover
          v-if="defaultMediaType === mediaTypes.IMAGE && exampleImageUrl"
          v-model="imagePreviewVisible"
          placement="right"
          trigger="hover"
          :destroyTooltipOnHide="true"
          overlayClassName="image-preview-popover"
        >
          <template slot="content">
            <div class="custom-upload__preview-container">
              <ScImg
                v-if="imagePreviewVisible"
                :src="exampleImageUrl"
                alt="示例图片预览"
                fit="cover"
                class="custom-upload__preview-image"
              >
                <template #placeholder>
                  <div class="custom-upload__loading">
                    <fa-spin />
                  </div>
                </template>
              </ScImg>
            </div>
          </template>
          <div class="custom-upload__example-image">
            <ScImg
              :src="exampleImageUrl"
              alt="示例图片"
              @click="enablePreview ? previewExampleImage : undefined"
              :style="{ cursor: enablePreview ? 'pointer' : 'default' }"
              fit="cover"
              :lazy="true"
            >
              <template #placeholder>
                <div class="custom-upload__loading">
                  <fa-spin />
                </div>
              </template>
            </ScImg>

            <!-- 示例图片 -->
            <div class="custom-upload__example-preview">
              <div class="custom-upload__example-preview__icon">
                <Icon type="zhili" class="w-[20px] h-[20px]" />
              </div>
              <p class="custom-upload__example-preview__label">示例图片</p>
            </div>
          </div>
        </fa-popover>

        <!-- 视频示例 - 只在 defaultMediaType 为 video 时显示 -->
        <fa-popover
          v-if="
            defaultMediaType === mediaTypes.VIDEO &&
            exampleVideoUrl &&
            exampleVideoThumbnail
          "
          v-model="videoPreviewVisible"
          placement="right"
          trigger="hover"
          :destroyTooltipOnHide="true"
          overlayClassName="video-preview-popover"
        >
          <template slot="content">
            <div class="custom-upload__preview-container">
              <video
                v-if="videoPreviewVisible"
                :src="exampleVideoUrl"
                controls
                autoplay
                muted
                controlsList="nodownload"
                @contextmenu.prevent
                class="custom-upload__preview-player"
              ></video>
            </div>
          </template>
          <div class="custom-upload__example-video">
            <ScImg
              :src="exampleVideoThumbnail"
              alt="示例视频"
              @click="enablePreview ? previewExampleVideo : undefined"
              :style="{ cursor: enablePreview ? 'pointer' : 'default' }"
              fit="cover"
              :lazy="true"
            >
              <template #placeholder>
                <div class="custom-upload__loading">
                  <fa-spin />
                </div>
              </template>
            </ScImg>
            <!-- 示例视频 -->
            <div class="custom-upload__example-preview">
              <div class="custom-upload__example-preview__icon">
                <Icon type="zhili" class="w-[20px] h-[20px]" />
              </div>
              <p class="custom-upload__example-preview__label">示例视频</p>
            </div>
          </div>
        </fa-popover>
      </div>
    </div>

    <!-- 文件列表 - 当有文件时显示 -->
    <div v-else class="custom-upload__file-list">
      <!-- 上传按钮 - 在有文件且未达到上限时显示 -->
      <div
        v-if="!isMaxLengthReached"
        class="custom-upload__button"
        :class="{ 'custom-upload__button--disabled': disabled }"
        @click="handleUploadClick"
      >
        <fa-icon type="plus" class="custom-upload__button-icon" />
        <div class="custom-upload__button-text">上传</div>
      </div>

      <!-- 动态渲染不同类型的文件项 -->
      <template v-for="(file, index) in fileList">
        <!-- 图片项 -->
        <div
          v-if="getFileType(file) === mediaTypes.IMAGE"
          :key="index"
          class="custom-upload__image-item"
          :class="{
            'custom-upload__image-item--invalid': isInvalidFileStatus(
              file.status,
            ),
          }"
        >
          <div
            v-if="!isInvalidFileStatus(file.status)"
            class="custom-upload__image-wrapper"
            @click="enablePreview ? previewMedia(file) : undefined"
            :style="{ cursor: enablePreview ? 'pointer' : 'default' }"
          >
            <ScImg
              :src="getImageUrl(file)"
              :alt="file.name || '图片'"
              fit="contain"
              :lazy="true"
            >
              <template #placeholder>
                <div class="custom-upload__loading">
                  <fa-spin />
                </div>
              </template>
            </ScImg>
          </div>

          <!-- 失效状态显示 -->
          <div
            v-if="isInvalidFileStatus(file.status)"
            class="custom-upload__invalid-mask"
          >
            <Icon
              type="sucaiyixiao"
              class="w-[24px] h-[24px] custom-upload__invalid-icon"
            />
            <div class="custom-upload__invalid-text">素材已失效</div>
            <div
              class="custom-upload__replace-button select-none"
              @click.stop="handleReplace(index)"
            >
              <Icon type="huanyihuan" class="w-[14px] h-[14px]" /><span
                >更换素材</span
              >
            </div>
          </div>
          <div
            class="custom-upload__delete-button select-none"
            @click.stop="handleDelete(index)"
          >
            <Icon type="shanshu_xiao" class="w-[10px] h-[10px]" />
          </div>
        </div>

        <!-- 视频项 -->
        <div
          v-else-if="getFileType(file) === mediaTypes.VIDEO"
          class="custom-upload__video-item"
          :class="{
            'custom-upload__video-item--invalid': isInvalidFileStatus(
              file.status,
            ),
          }"
        >
          <div
            v-if="!isInvalidFileStatus(file.status)"
            class="custom-upload__video-wrapper"
            @click="enablePreview ? previewMedia(file) : undefined"
            :style="{ cursor: enablePreview ? 'pointer' : 'default' }"
          >
            <div class="custom-upload__video-thumbnail">
              <ScImg
                :src="getVideoThumbnail(file)"
                :alt="file.name || '视频'"
                fit="contain"
                :lazy="true"
              >
                <template #placeholder>
                  <div class="custom-upload__loading">
                    <fa-spin />
                  </div>
                </template>
              </ScImg>
              <div class="custom-upload__video-duration" v-if="file.duration">
                {{ formatDuration(file.duration) }}
              </div>
            </div>
          </div>

          <!-- 失效状态显示 -->
          <div
            v-if="isInvalidFileStatus(file.status)"
            class="custom-upload__invalid-mask"
          >
            <Icon
              type="sucaiyixiao"
              class="w-[24px] h-[24px] custom-upload__invalid-icon"
            />
            <div class="custom-upload__invalid-text">素材已失效</div>
            <div
              class="custom-upload__replace-button select-none"
              @click.stop="handleReplace(index)"
            >
              <Icon type="huanyihuan" class="w-[14px] h-[14px]" /><span
                >更换素材</span
              >
            </div>
          </div>
          <div
            class="custom-upload__delete-button select-none"
            @click.stop="handleDelete(index)"
          >
            <Icon type="shanshu_xiao" class="w-[10px] h-[10px]" />
          </div>
        </div>
      </template>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, computed, type PropType } from 'vue';
import { message } from '@fk/faicomponent';
import { showVideoViewer } from '@/components/comm/VideoViewer/index';
import { showImageViewer } from '@/components/comm/ImageViewer/index';
import ScImg from '@/components/comm/ScImg.vue';
import type { FileInfo, MediaType } from '@/views/EditProjectView/types/index';
import {
  isInvalidFileStatus,
  MEDIA_TYPES,
} from '@/views/EditProjectView/types/index';

/**
 * @component CustomUpload
 * @description 自定义文件上传组件，支持图片和视频文件的上传、预览、删除和管理。
 * @description 提供文件状态管理（例如：有效、失效）、示例媒体展示、文件数量限制等功能。
 * @description 适用于需要定制化文件上传交互和展示的场景。
 * @example
 * ```vue
 * <CustomUpload
 *   :file-list="fileList"
 *   :disabled="false"
 *   :max-length="10"
 *   default-media-type="image"
 *   :example-image-url="exampleImageUrl"
 *   :example-video-url="exampleVideoUrl"
 *   :example-video-thumbnail="exampleVideoThumbnail"
 *   :enable-preview="true"
 *   @upload-click="handleUploadClick"
 *   @delete="handleFileDelete"
 *   @replace="handleFileReplace"
 * />
 * ```
 * @since 1.0.0
 */
export default defineComponent({
  name: 'CustomUpload',
  components: {
    ScImg,
  },
  props: {
    /**
     * @description 当前已上传的文件列表，包含文件信息、状态、缩略图等数据。
     * @type {Array<FileInfo>}
     * @default []
     */
    fileList: {
      type: Array as PropType<FileInfo[]>,
      default: () => [],
    },
    /**
     * @description 控制整个上传组件是否处于禁用状态，禁用时不允许任何操作。
     * @type {boolean}
     * @default false
     */
    disabled: {
      type: Boolean,
      default: false,
    },
    /**
     * @description 允许上传的最大文件数量，达到限制后隐藏上传按钮。
     * @type {number}
     * @default Infinity
     */
    maxLength: {
      type: Number,
      default: Infinity,
    },
    /**
     * @description 上传组件的默认媒体类型，影响"上传"按钮点击时触发的事件参数，以及空状态时示例的展示逻辑。
     * @description 注意：此属性不限制实际 `fileList` 中文件的具体 `mediaType`，仅作为组件行为的默认指导。
     * @type {MediaType}
     * @values 'image', 'video'
     * @default 'image'
     */
    defaultMediaType: {
      type: String as PropType<MediaType>,
      default: MEDIA_TYPES.IMAGE,
      validator: (value: string) =>
        [MEDIA_TYPES.IMAGE, MEDIA_TYPES.VIDEO].includes(value as MediaType),
    },
    /**
     * @description 用于在文件列表为空时展示的示例图片地址。
     * @type {string}
     * @default ''
     */
    exampleImageUrl: {
      type: String,
      default: '',
    },
    /**
     * @description 用于在文件列表为空且 `defaultMediaType` 为 'video' 时展示的示例视频地址。
     * @type {string}
     * @default ''
     */
    exampleVideoUrl: {
      type: String,
      default: '',
    },
    /**
     * @description 示例视频的缩略图地址，用于视频预览展示。
     * @type {string}
     * @default ''
     */
    exampleVideoThumbnail: {
      type: String,
      default: '',
    },
    /**
     * @description 当图片加载失败或无图片时显示的占位符地址
     * @type {string}
     * @default ''
     */
    defaultImagePlaceholder: {
      type: String,
      default: '',
    },
    /**
     * @description 是否启用点击预览功能，控制示例素材和用户上传素材的点击预览，视频popover预览除外
     * @type {boolean}
     * @default false
     */
    enablePreview: {
      type: Boolean,
      default: false,
    },
  },
  emits: {
    /**
     * @description 用户点击上传按钮时触发，传递媒体类型信息给父组件处理实际上传逻辑。
     * @event upload-click
     * @param {{ mediaType: MediaType }} data - 上传事件数据对象。
     * @param {MediaType} data.mediaType - 请求上传的媒体类型 ('image' 或 'video')。
     */
    'upload-click': (_payload: { mediaType: MediaType }) => true,
    /**
     * @description 用户点击删除按钮时触发，传递要删除的文件索引。
     * @event delete
     * @param {{ index: number }} data - 删除事件数据对象。
     * @param {number} data.index - 要删除的文件在列表中的索引。
     */
    'delete': (_payload: { index: number }) => true,
    /**
     * @description 用户点击替换按钮时触发，传递原文件索引和期望的媒体类型。
     * @event replace
     * @param {{ index: number; mediaType: MediaType }} data - 替换事件数据对象。
     * @param {number} data.index - 要替换的原文件在列表中的索引。
     * @param {MediaType} data.mediaType - 希望替换成的媒体类型。
     */
    'replace': (_payload: { index: number; mediaType: MediaType }) => true,
  },
  setup(props, { emit }) {
    // ============= 响应式数据 =============

    /**
     * @description 控制示例视频预览弹窗的显示/隐藏状态。
     * @type {Ref<boolean>}
     */
    const videoPreviewVisible = ref<boolean>(false);

    /**
     * @description 控制示例图片预览弹窗的显示/隐藏状态。
     * @type {Ref<boolean>}
     */
    const imagePreviewVisible = ref<boolean>(false);

    // ============= 计算属性 =============

    /**
     * @description 检查当前文件列表是否已达到或超过最大数量限制。
     * @returns {boolean} 如果已达到或超过限制则为 true，否则为 false。
     */
    const isMaxLengthReached = computed<boolean>(() => {
      return props.fileList && props.fileList.length >= props.maxLength;
    });

    /**
     * @description 检查文件列表是否包含任何文件。
     * @returns {boolean} 如果文件列表不为空则为 true，否则为 false。
     */
    const hasFiles = computed<boolean>(() => {
      return props.fileList && props.fileList.length > 0;
    });

    // ============= 方法定义 =============

    /**
     * @description 根据文件本身的 mediaType 或组件的 defaultMediaType 来判断文件类型。
     * @param {FileInfo} [file] - 可选的文件对象。如果提供，则优先使用其 mediaType。
     * @returns {MediaType} 返回 'image' 或 'video'。
     */
    const getFileType = (file?: FileInfo): MediaType => {
      if (file && file.mediaType) {
        return file.mediaType;
      }
      return props.defaultMediaType;
    };

    /**
     * @description 处理用户点击"上传"按钮的操作。
     * @description 如果组件未被禁用，则向父组件发送 `upload-click` 事件，并附带当前的 `defaultMediaType`。
     */
    const handleUploadClick = (): void => {
      if (props.disabled) return;
      emit('upload-click', { mediaType: props.defaultMediaType });
    };

    /**
     * @description 处理用户点击文件上的"删除"图标的操作。
     * @description 如果组件未被禁用，则向父组件发送 `delete` 事件，并附带被删除的文件索引。
     * @param {number} index - 要删除的文件在 fileList 中的索引。
     */
    const handleDelete = (index: number): void => {
      if (props.disabled) return;
      if (props.fileList && index >= 0 && index < props.fileList.length) {
        emit('delete', { index });
      } else {
        console.warn(
          'CustomUpload: Invalid index for delete operation',
          index,
          props.fileList,
        );
      }
    };

    /**
     * @description 统一的媒体预览方法。
     * @param {FileInfo} file - 要预览的媒体文件对象。
     */
    const previewMedia = (file: FileInfo): void => {
      if (!props.enablePreview || !file) return;
      const effectiveMediaType = getFileType(file); // Now returns image | video

      if (effectiveMediaType === MEDIA_TYPES.VIDEO) {
        if (!file.url) {
          message.error('无法预览：视频链接无效');
          return;
        }
        showVideoViewer({
          videoSrc: file.url,
        });
      } else {
        const imageUrl = file.url || props.defaultImagePlaceholder || '';
        if (!imageUrl) {
          message.error('无法预览：图片链接无效');
          return;
        }
        showImageViewer({
          imgList: [imageUrl],
        });
      }
    };

    /**
     * @description 预览示例图片。
     * @description 如果 `enablePreview` 为 true 且 `exampleImageUrl` 有效，则打开图片预览器显示该图片。
     */
    const previewExampleImage = (): void => {
      if (!props.enablePreview) return;
      if (!props.exampleImageUrl) {
        message.error('示例图片不可用');
        return;
      }
      showImageViewer({
        imgList: [props.exampleImageUrl],
      });
    };

    /**
     * @description 预览示例视频。
     * @description 如果 `enablePreview` 为 true 且 `exampleVideoUrl` 有效，则打开视频预览器显示该视频。
     */
    const previewExampleVideo = (): void => {
      if (!props.enablePreview) return;
      if (!props.exampleVideoUrl) {
        message.error('示例视频不可用');
        return;
      }
      showVideoViewer({
        videoSrc: props.exampleVideoUrl,
      });
    };

    /**
     * @description 格式化视频时长（秒）为 "MM:SS" 格式的字符串。
     * @param {number} seconds - 视频时长（秒）。
     * @returns {string} 格式化后的时长字符串，如 "01:30"。如果输入无效，则返回 "00:00"。
     */
    const formatDuration = (seconds: number): string => {
      if (seconds == null || isNaN(seconds) || seconds < 0) return '00:00';

      const minutes = Math.floor(seconds / 60);
      const remainingSeconds = Math.floor(seconds % 60);
      return `${minutes.toString().padStart(2, '0')}:${remainingSeconds
        .toString()
        .padStart(2, '0')}`;
    };

    /**
     * @description 处理用户点击文件上的"更换素材"按钮的操作。
     * @param {number} index - 要替换的文件在 fileList 中的索引。
     */
    const handleReplace = (index: number): void => {
      if (props.disabled) return;
      if (props.fileList && index >= 0 && index < props.fileList.length) {
        const fileToReplace = props.fileList[index];
        const replacementMediaType: MediaType =
          fileToReplace.mediaType || props.defaultMediaType;
        emit('replace', { index, mediaType: replacementMediaType });
      } else {
        console.warn(
          'CustomUpload: Invalid index for replace operation',
          index,
          props.fileList,
        );
      }
    };

    /**
     * @description 获取图片文件用于 `ScImg` 的 `src` 属性。
     * @description 如果组件的 `defaultMediaType` 配置为 'video'，则优先使用文件的 `thumbUrl`。
     * @description 否则，直接使用文件的 `url`。
     * @param {FileInfo} file - 图片文件对象。
     * @returns {string} 图片的显示URL，如果URL无效则返回空字符串。
     */
    const getImageUrl = (file: FileInfo): string => {
      // 此处的逻辑是：如果整个上传组件是用于视频的(defaultMediaType === MEDIA_TYPES.VIDEO),
      // 那么即使是文件列表中的图片项（理论上不应出现，除非fileList被外部错误填充），也尝试用thumbUrl。
      // 更合理的做法可能是基于 file.mediaType 来判断。
      // 但遵循原逻辑，如果 defaultMediaType 是 video，就认 thumbUrl
      if (props.defaultMediaType === MEDIA_TYPES.VIDEO) {
        return file.thumbUrl || props.defaultImagePlaceholder || '';
      }
      // 否则，对于 image 类型的组件，直接用 url
      return file.url || props.defaultImagePlaceholder || '';
    };

    /**
     * @description 获取视频文件的缩略图URL，用于 `ScImg` 的 `src` 属性。
     * @description 优先使用文件自身的 `thumbUrl`，如果无效，则使用 `defaultImagePlaceholder`。
     * @param {FileInfo} file - 视频文件对象。
     * @returns {string} 视频缩略图的URL，如果都无效则返回空字符串。
     */
    const getVideoThumbnail = (file: FileInfo): string => {
      return file.thumbUrl || props.defaultImagePlaceholder || '';
    };

    // ============= 监听器 =============

    // ============= 返回模板所需数据 =============

    return {
      // 常量
      mediaTypes: MEDIA_TYPES,
      // 响应式数据和方法
      videoPreviewVisible,
      imagePreviewVisible,
      isMaxLengthReached,
      hasFiles,
      getFileType,
      handleUploadClick,
      handleDelete,
      previewMedia,
      previewExampleImage,
      previewExampleVideo,
      formatDuration,
      handleReplace,
      getImageUrl,
      getVideoThumbnail,
      isInvalidFileStatus,
    };
  },
});
</script>

<style lang="scss" scoped>
/* 自定义上传组件样式 */
.custom-upload {
  /* 布局相关 */
  @apply w-full;

  /* 公用组件 - 上传按钮 */
  .custom-upload__button {
    /* 布局相关 */
    @apply flex flex-col justify-center items-center cursor-pointer;
    /* 尺寸相关 */
    @apply w-[104px] h-[104px];
    /* 外观相关 */
    @apply rounded-[8px] bg-white border border-solid border-[#d9d9d9] border-dashed;
    /* 动画相关 */
    @apply transition-all duration-300 ease-in-out;

    &:hover:not(.custom-upload__button--disabled) {
      /* 外观相关 */
      @apply border-[#3261FD];
      .custom-upload__button-icon,
      .custom-upload__button-text {
        @apply text-[#3261FD];
      }
    }

    &.custom-upload__button--disabled {
      /* 交互相关 */
      @apply cursor-not-allowed opacity-60;
      /* 外观相关 */
      @apply bg-[#f5f5f5];
    }

    .custom-upload__button-icon {
      /* 文字相关 */
      @apply text-[#666] text-[20px];
    }

    .custom-upload__button-text {
      /* 尺寸相关 */
      @apply mt-0;
      /* 文字相关 */
      @apply text-[#666] font-normal text-[14px];
    }
  }

  /* 上传区域 */
  .custom-upload__area {
    /* 布局相关 */
    @apply flex items-start;

    /* 示例媒体区域 */
    .custom-upload__example {
      /* 布局相关 */
      @apply ml-16px flex gap-16px;

      /* 示例图片容器 */
      .custom-upload__example-image {
        /* 布局相关 */
        @apply relative cursor-default;
        /* 尺寸相关 */
        @apply w-[104px] h-[104px];
        /* 外观相关 */
        @apply rounded-[8px] bg-[#fff] border border-[#d9d9d9];

        &:hover .custom-upload__example-preview {
          background: linear-gradient(
            180deg,
            rgba(0, 0, 0, 0) 0%,
            rgba(0, 0, 0, 0.9) 100%
          );
        }

        :deep(.el-image) {
          /* 尺寸相关 */
          @apply w-full h-full mx-auto;
          /* 外观相关 */
          @apply rounded-[8px] rounded-bl-[8px] rounded-br-[8px];
        }
      }

      /* 示例视频容器 */
      .custom-upload__example-video {
        /* 布局相关 */
        @apply relative cursor-default;
        /* 尺寸相关 */
        @apply w-[104px] h-[104px];
        /* 外观相关 */
        @apply rounded-[8px] bg-[#fff] border border-[#d9d9d9];

        &:hover .custom-upload__example-preview {
          background: linear-gradient(
            180deg,
            rgba(0, 0, 0, 0) 0%,
            rgba(0, 0, 0, 0.9) 100%
          );
        }

        :deep(.el-image) {
          /* 尺寸相关 */
          @apply w-full h-full mx-auto;
          /* 外观相关 */
          @apply rounded-[8px];
        }
      }
    }
  }

  /* 公用组件 - 示例预览覆盖层 */
  .custom-upload__example-preview {
    /* 布局相关 */
    @apply absolute bottom-0 left-0 right-0 flex items-end justify-center;
    /* 尺寸相关 */
    @apply h-[40px] pb-4px;
    /* 外观相关 */
    @apply rounded-[0_0_8px_8px];
    /* 文字相关 */
    @apply font-normal text-[0] leading-[40px] cursor-default select-none;
    background: linear-gradient(
      180deg,
      rgba(0, 0, 0, 0) 0%,
      rgba(0, 0, 0, 0.8) 100%
    );

    .custom-upload__example-preview__icon {
      /* 布局相关 */
      @apply flex justify-center items-center;
      /* 文字相关 */
      @apply text-white;
      /* Z-index相关 */
      @apply zi-custom-upload__example-preview__icon;
    }

    .custom-upload__example-preview__label {
      /* 尺寸相关 */
      @apply ml-4px;
      /* 文字相关 */
      @apply text-13px text-white lh-20px;
    }
  }

  /* 公用组件 - 失效状态蒙层 */
  .custom-upload__invalid-mask {
    /* 布局相关 */
    @apply absolute top-0 left-0 flex flex-col justify-center items-center;
    /* 尺寸相关 */
    @apply w-full h-full;
    /* 外观相关 */
    @apply bg-white border border-solid border-[#fa3534] rounded-[8px];
    /* 文字相关 */
    @apply text-white;

    &:hover .custom-upload__replace-button {
      /* 布局相关 */
      @apply flex;
    }

    .custom-upload__invalid-icon {
      /* 文字相关 */
      @apply text-[#ffd2d2];
    }

    .custom-upload__invalid-text {
      /* 尺寸相关 */
      @apply mt-[6px];
      /* 文字相关 */
      @apply font-normal text-[12px] text-center text-[#fa3534];
    }
  }

  /* 公用组件 - 更换按钮 */
  .custom-upload__replace-button {
    /* 布局相关 */
    @apply absolute bottom-4px left-4px right-4px items-center justify-center cursor-pointer hidden;
    /* 尺寸相关 */
    @apply p-[4px_8px] h-[24px];
    /* 外观相关 */
    @apply bg-[rgba(0,0,0,0.75)] rounded-[2px];

    span {
      /* 尺寸相关 */
      @apply ml-1;
      /* 文字相关 */
      @apply text-[12px] leading-[22px] text-left text-white;
    }
  }

  /* 公用组件 - 删除按钮 */
  .custom-upload__delete-button {
    /* 布局相关 */
    @apply absolute top-[-5px] right-[-5px] flex justify-center items-center;
    /* 尺寸相关 */
    @apply w-[20px] h-[20px];
    /* 外观相关 */
    @apply bg-[#f5222d] text-white rounded-full opacity-0;
    /* Z-index相关 */
    @apply zi-custom-upload__delete-button;
    /* 动画相关 */
    @apply transition-opacity duration-200 ease-in-out;
  }

  /* 文件列表区域 */
  .custom-upload__file-list {
    /* 布局相关 */
    @apply flex flex-wrap gap-16px;

    /* 图片文件项 */
    .custom-upload__image-item {
      /* 布局相关 */
      @apply relative cursor-pointer;
      /* 尺寸相关 */
      @apply w-[104px] h-[104px];
      /* 外观相关 */
      @apply rounded-[8px] border border-[#d9d9d9];

      &:hover .custom-upload__delete-button {
        /* 外观相关 */
        @apply opacity-100;
      }

      .custom-upload__image-wrapper {
        /* 尺寸相关 */
        @apply w-full h-full;
        /* 外观相关 */
        @apply overflow-hidden rounded-[8px];

        :deep(.el-image) {
          /* 尺寸相关 */
          @apply w-full h-full;
        }
      }
    }

    /* 视频文件项 */
    .custom-upload__video-item {
      /* 布局相关 */
      @apply relative cursor-pointer;
      /* 尺寸相关 */
      @apply w-[104px] h-[104px];
      /* 外观相关 */
      @apply rounded-[8px];

      &:hover .custom-upload__delete-button {
        /* 外观相关 */
        @apply opacity-100;
      }

      &.custom-upload__video-item--invalid {
        /* 尺寸相关 */
        @apply p-0;
      }

      .custom-upload__video-wrapper {
        /* 尺寸相关 */
        @apply w-full h-full p-0;
        /* 外观相关 */
        @apply bg-[#fff] border-1px border-solid border-[#e8e8e8] rounded-[8px];

        .custom-upload__video-thumbnail {
          /* 布局相关 */
          @apply relative;
          /* 尺寸相关 */
          @apply w-full h-full;
          /* 外观相关 */
          @apply overflow-hidden rounded-[6px];

          :deep(.el-image) {
            /* 尺寸相关 */
            @apply w-full h-full;
          }

          .custom-upload__video-duration {
            /* 布局相关 */
            @apply absolute bottom-[2px] right-[2px];
            /* 尺寸相关 */
            @apply p-[2px_6px];
            /* 外观相关 */
            @apply bg-[rgba(0,0,0,0.6)] text-white rounded-[10px];
            /* 文字相关 */
            @apply font-normal text-[12px];
            /* Z-index相关 */
            @apply zi-custom-upload__video-duration;
          }
        }
      }
    }
  }

  /* 公用组件 - 加载状态 */
  .custom-upload__loading {
    /* 布局相关 */
    @apply flex justify-center items-center;
    /* 尺寸相关 */
    @apply w-full h-full;

    &.custom-upload__loading--large {
      /* 尺寸相关 */
      @apply h-[200px] w-[200px] mx-auto;
    }
  }
}
</style>

<style lang="scss">
/* 全局样式 - CustomUpload 组件相关 */

/* 视频预览弹窗 */
.video-preview-popover {
  .fa-popover-inner-content {
    /* 尺寸相关 */
    @apply p-8px;
  }

  .video-preview-container {
    /* 尺寸相关 */
    @apply w-full h-full min-w-196px min-h-336px;
  }

  .video-player {
    /* 尺寸相关 */
    @apply w-[196px] h-[336px];
    /* 外观相关 */
    @apply bg-black;
  }

  .custom-upload__preview-player {
    /* 尺寸相关 */
    @apply w-[189px] h-[336px];
    /* 外观相关 */
    @apply bg-black rounded-[8px];
  }
}

/* 图片预览弹窗 */
.image-preview-popover {
  .fa-popover-inner-content {
    /* 尺寸相关 */
    @apply p-8px;
  }

  .custom-upload__preview-container {
    /* 尺寸相关 */
    @apply w-full h-full min-w-150px min-h-300px;
  }
}

/* 图片预览样式 */
.custom-upload__preview-image {
  /* 尺寸相关 */
  @apply w-[auto] h-300px max-w300px max-h300px min-w-150px;
  /* 外观相关 */
  @apply bg-white rounded-[8px];
}

/* 图片悬停缩放效果 */
.custom-upload__example-image .el-image img,
.custom-upload__example-video .el-image img,
.custom-upload__image-wrapper .el-image img,
.custom-upload__video-thumbnail .el-image img {
  /* 动画相关 */
  transition: transform 0.3s ease-in-out;

  &:hover {
    transform: scale(1.05);
  }
}
</style>
