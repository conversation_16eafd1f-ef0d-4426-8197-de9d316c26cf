import { nextTick, Ref } from 'vue';
import { message as FaMessage } from '@fk/faicomponent';
import { getFolderContent } from '@/api/Material/index';
import {
  INFO_KEYS,
  SORT_MODE_KEY,
  IMG_PREVIEW_KEY_LIST,
} from '@/constants/material';
import { FILE_TYPES, FILE_EXTENSIONS } from '@/constants/fileType';
import { FileData, FolderData } from '@/types/Material';
import { MaterialUploadFile } from '../types';
import { SORT_BY_KEY } from '../constants';
import store from '@/store';

import { UseMaterialBasicUploadStateReturns } from './useMaterialBasicUploadState';
import { getMaterialFullUrl, FileViewTypeValues } from '../utils/index.ts';
import { isFolder } from '@/utils/resource';

// 用于锁定滚动加载状态
let lockTouchGround = false;

// Emits 类型
type EmitFunction = (
  event: 'update:visible' | 'close' | 'file-choose-over',
  ...args: unknown[]
) => void;

// 定义 FaBasicUpload 组件的类型
export interface FaBasicUploadInstance {
  $el: HTMLElement;
  onViewChange: (...args: unknown[]) => void;
  ownerFileViewType: FileViewTypeValues;
}

// 定义返回类型的接口
export interface UseMaterialBasicUploadUtilsReturns {
  // Utils 部分
  checkOwnerFileImgType: (data: FileData) => boolean;
  checkFileChosen: (data: FileData) => boolean;
  listenerScroll: (
    state: UseMaterialBasicUploadStateReturns,
    loadFolderContent: () => Promise<void>,
  ) => void;
  watchOwnerFileViewType: () => void;

  // Actions 部分
  reloadOwnerFile: () => Promise<void>;
  loadFolderContent: () => Promise<void>;
  doFileChooseOver: (data: FileData) => void;
  doFileUnchoose: (data: FileData) => void;
  doFileChoose: (data: FileData) => void;
  doChosenFileUnchoose: (data: MaterialUploadFile) => void;
  handlerFileSort: (_list?: FileData[]) => FileData[];
}

/**
 * 包含工具函数以及封装所有涉及数据修改、API 调用以及复杂业务逻辑的函数
 * @param state 组件状态对象
 * @param emit 事件触发函数
 * @param FaBasicUploadRef 组件实例引用
 * @returns 工具与操作方法集合
 */
export function useMaterialBasicUploadUtils(
  state: UseMaterialBasicUploadStateReturns,
  emit: EmitFunction,
  FaBasicUploadRef: Ref<FaBasicUploadInstance | null>,
): UseMaterialBasicUploadUtilsReturns {
  // 解构在此模块中直接使用的变量
  const {
    folderList,
    fileList,
    loading,
    pageCurrent,
    pageSize,
    fileTotal,
    currentFolder,
    ownerFileSearchKeywords,
    isVideo,
    chosenFileList,
    sortBy,
    sortMode,
    chosenFileKeyList,
  } = state;

  // ============= Utils 部分 =============
  const handleFileListData = (data: FileData[]) => {
    return data.map(v => {
      v.suffix = FILE_EXTENSIONS[v[INFO_KEYS.FILE_TYPE] as FILE_TYPES];
      v.url = getMaterialFullUrl(
        v[INFO_KEYS.RES_ID],
        FILE_TYPES.WEBP || v[INFO_KEYS.FILE_TYPE],
        'user',
        160,
      ); // 图片视频的url
      // 视频类型新增coverType
      if (v.fileType === FILE_TYPES.MP4) {
        v.extra = v.extra || {};
        v.extra.coverType = FILE_TYPES.JPEG;
      }
      return v as FileData;
    });
  };

  /**
   * 检查文件是否为可预览图片类型
   * @param data 文件数据
   * @returns 是否为图片
   */
  const checkOwnerFileImgType = (data: FileData): boolean => {
    return IMG_PREVIEW_KEY_LIST.includes(data[INFO_KEYS.FILE_TYPE]);
  };

  /**
   * 检查文件是否已被选中
   * @param data 文件数据
   * @returns 是否已选中
   */
  const checkFileChosen = (data: FileData): boolean => {
    return chosenFileKeyList.value.includes(data[INFO_KEYS.ID]);
  };

  // 获取当前路径列表
  const setCurrentPathList = (state: UseMaterialBasicUploadStateReturns) => {
    const { showFolderList, currentFolder, rootFolder } = state;
    const list = [];
    const folderListValue = showFolderList.value;
    const folderValue = currentFolder.value;

    // 根寻址算法
    const map = folderListValue.reduce(
      (acc: Record<string, number>, cur: FolderData, index: number) => {
        acc[cur[INFO_KEYS.ID]] = index;
        return acc;
      },
      {},
    );

    let node = folderListValue[map[folderValue]];
    if (node) {
      const isRootFolder = node[INFO_KEYS.ID] === rootFolder.value;
      while (node && !isRootFolder) {
        list.unshift(node);
        const parentId = node[INFO_KEYS.PARENT_ID];
        if (parentId === undefined || !map[parentId]) break;
        node = folderListValue[map[parentId]];
      }

      if (node) {
        // 加上根路径
        list.unshift(node);
      }
    }

    store.commit('materialUpload/setCurrentPathList', list as FolderData[]);
  };

  // 触底
  const touchGround = (loadFolderContent: () => Promise<void>) => {
    console.log('--触底touchGround--', lockTouchGround);
    if (lockTouchGround) return;
    lockTouchGround = true;
    loadFolderContent();
  };

  // 监听触底
  const listenerScroll = (
    state: UseMaterialBasicUploadStateReturns,
    loadFolderContent: () => Promise<void>,
  ) => {
    const { ownerFileViewType } = state;
    nextTick(() => {
      setTimeout(() => {
        const selector: Record<FileViewTypeValues, string> = {
          list: ' div.fa-scrollarea__wrap',
          table: ' div.fa-table-body',
        };
        let scrollDom: HTMLElement | null = null;

        // 使用 FaBasicUploadRef 获取元素
        if (FaBasicUploadRef.value) {
          const baseElement = FaBasicUploadRef.value.$el;
          scrollDom = baseElement.querySelector(
            `div.fa-popup--body div.fa-tabs-tabpane.fa-tabs-tabpane-active > div.fa-basic-upload-owner-file > div.fa-basic-upload-owner-file--content ${
              selector[ownerFileViewType.value as FileViewTypeValues]
            }`,
          );
        }

        if (scrollDom) {
          scrollDom.addEventListener('scroll', _ => {
            const { scrollTop, offsetHeight, scrollHeight } =
              scrollDom as HTMLElement;
            if (scrollTop + offsetHeight >= scrollHeight - 10) {
              touchGround(loadFolderContent);
            }
          });
          // 重置滚动锁
          lockTouchGround = false;
        }
      }, 300);
    });
  };

  // ============= Actions 部分 =============
  const reloadOwnerFile = async () => {
    loading.value = true;
    fileList.value = [];
    setCurrentPathList(state); // Pass state to setCurrentPathList
    folderList.value = [];
    pageCurrent.value = 0;
    fileTotal.value = 0;
    await loadFolderContent();
    loading.value = false;
    listenerScroll(state, loadFolderContent);
  };
  const loadFolderContent = async () => {
    if (
      pageCurrent.value !== 0 &&
      fileTotal.value !== 0 &&
      pageCurrent.value * pageSize.value >= fileTotal.value
    )
      return;

    const preset: {
      parentId: number;
      limit: number;
      page: number;
      type: number;
      name?: string;
    } = {
      parentId: currentFolder.value,
      limit: pageSize.value,
      page: ++pageCurrent.value,
      type: isVideo.value ? 2 : 1,
    };
    if (ownerFileSearchKeywords.value) {
      preset.name = ownerFileSearchKeywords.value;
    }
    const [err, res] = await getFolderContent(preset);
    if (err) {
      FaMessage.error(err.message || '文件加载失败');
      return;
    }
    const { data, totalSize } = res;
    fileTotal.value = totalSize || 0;
    const fileListData = handleFileListData(
      data.filter(item => !isFolder(item)) as FileData[],
    );
    fileList.value.push(...fileListData);
    folderList.value.push(...data.filter(item => isFolder(item)));
    lockTouchGround = false;
  };

  const doFileChooseOver = (data: FileData) => {
    emit('file-choose-over', data);
  };

  const doFileUnchoose = (data: FileData) => {
    const index = chosenFileList.value.findIndex(
      (item: MaterialUploadFile) =>
        item.data[INFO_KEYS.ID] === data[INFO_KEYS.ID],
    );
    if (index > -1) {
      chosenFileList.value.splice(index, 1);
      console.log(`取消选择文件：${data[INFO_KEYS.NAME]}`);
    }
  };

  const doFileChoose = (data: FileData) => {
    chosenFileList.value.push({ type: 'owner', data });
    console.log(`选择文件：${data[INFO_KEYS.NAME]}`);
  };

  const doChosenFileUnchoose = (data: MaterialUploadFile) => {
    doFileUnchoose(data.data);
  };

  // 文件排序
  const handlerFileSort = (_list?: FileData[]) => {
    let newList: FileData[] = [];
    let setSort: (v: FileData[]) => FileData[];
    if (_list) {
      newList = [..._list];
      setSort = (v: FileData[]) => v;
    } else {
      newList = [...fileList.value];
      setSort = (v: FileData[]) => (fileList.value = v);
    }
    let sortModeHandler: (a: number, b: number) => number = (_a, _b) => 0;
    let sortModeHandlerName: (a: string, b: string) => number = (_a, _b) => 0;
    if (sortMode.value === SORT_MODE_KEY.ASCE) {
      sortModeHandler = (a: number, b: number) => a - b;
      sortModeHandlerName = (a: string, b: string) => a.localeCompare(b);
    } else if (sortMode.value === SORT_MODE_KEY.DESC) {
      sortModeHandler = (a: number, b: number) => b - a;
      sortModeHandlerName = (a: string, b: string) => b.localeCompare(a);
    }

    let sortByHandler: (next: FileData, last: FileData) => number = (
      _next,
      _last,
    ) => 0;
    if (sortBy.value === SORT_BY_KEY.Time) {
      sortByHandler = function (next: FileData, last: FileData) {
        return sortModeHandler(
          next[INFO_KEYS.CREATE_TIME],
          last[INFO_KEYS.CREATE_TIME],
        );
      };
    } else if (sortBy.value === SORT_BY_KEY.Size) {
      sortByHandler = function (next: FileData, last: FileData) {
        return sortModeHandler(
          next[INFO_KEYS.FILE_SIZE],
          last[INFO_KEYS.FILE_SIZE],
        );
      };
    } else if (sortBy.value === SORT_BY_KEY.Name) {
      sortByHandler = function (next: FileData, last: FileData) {
        return sortModeHandlerName(next[INFO_KEYS.NAME], last[INFO_KEYS.NAME]);
      };
    }

    newList.sort(sortByHandler);
    setSort(newList);
    return newList;
  };

  const watchOwnerFileViewType = () => {
    const ref = FaBasicUploadRef.value;
    if (!ref) return;
    const { onViewChange } = ref;
    ref.onViewChange = (...args) => {
      onViewChange.call(ref, ...args);
      // 自己的业务
      listenerScroll(state, loadFolderContent);
      store.commit(
        'materialUpload/setOwnerFileViewType',
        ref.ownerFileViewType,
      );
    };
  };

  return {
    // Utils 部分
    checkOwnerFileImgType,
    checkFileChosen,
    listenerScroll,
    watchOwnerFileViewType,

    // Actions 部分
    reloadOwnerFile,
    loadFolderContent,
    doFileChooseOver,
    doFileUnchoose,
    doFileChoose,
    doChosenFileUnchoose,
    handlerFileSort,
  } as const;
}
