import { ref, reactive, onMounted, onBeforeUnmount } from 'vue';
import { day } from '@/utils/dayjs';
import { getProjectList } from '@/api/ProjectView/index';
import router from '@/router';
import {
  WorkQuery,
  getWorkList,
  updateWorkName,
  deleteWork,
  updateWorkFlag,
} from '@/api/WorkView/index';
import { PROJECT_TYPE, PROJECT_TYPE_NAME } from '@/constants/project';
import { PAGE_SOURCE } from '@/constants/navigation';
import {
  getWorkStatusInfo,
  isAnyGeneratingWork,
  isRecompletedWork,
  isNormalCompletedWork,
  isAnyFailedWork,
} from '@/constants/workStatus';
import { debounce } from 'lodash-es';
import { message } from '@fk/faicomponent';
import { showVideoEditor } from '@/components/VideoEditor/index';
import { showImageTextEditor } from '@/components/ImageTextEditor/utils/index';
import {
  WORK_STATUS_TIPS,
  MODAL_TYPE_PREVIEW,
  MODAL_TYPE_SELECT,
} from '@/components/WorkView/constants';
import { isUnrelatedProject } from '@/components/WorkView/utils';
import { restoreOldWork } from '@/api/WorkView/index';
import { WorkViewItem } from '@/components/WorkView/types';
interface UseWorkListProps {
  projectId?: number;
}

// 常量定义
const WORK_LIST_CONSTANTS = {
  // 分页相关
  SELECT_PAGE_SIZE: 20,
  DEFAULT_PAGE_SIZE: 10,
  DEFAULT_PAGE_CURRENT: 1,

  // 排序相关
  DEFAULT_SORT_FIELD: 'saveTime',
  DEFAULT_SORT_ORDER: 'descend',

  // 项目选择相关
  ALL_PROJECTS_ID: 0,
  ALL_PROJECTS_NAME: '全部创作项目',
} as const;

// 表格列常量定义
export const WORK_LIST_COLUMNS = [
  {
    title: '作品名称',
    scopedSlots: { customRender: 'name' },
    width: '23.51%',
  },
  {
    title: '作品类型',
    dataIndex: 'type',
    filters: [
      {
        text: PROJECT_TYPE_NAME[PROJECT_TYPE.IMAGE] || '图文',
        value: PROJECT_TYPE.IMAGE.toString(),
      },
      {
        text: PROJECT_TYPE_NAME[PROJECT_TYPE.VIDEO] || '视频',
        value: PROJECT_TYPE.VIDEO.toString(),
      },
    ],
    filterMultiple: false,
    customRender: (text: PROJECT_TYPE) => {
      return PROJECT_TYPE_NAME[text] || '-';
    },
    width: '11.57%',
  },
  {
    title: '所属项目',
    scopedSlots: { customRender: 'project' },
    width: '14.18%',
  },
  {
    title: '文件大小',
    dataIndex: 'sizeName',
    width: '8.58%',
  },
  {
    title: '最近编辑时间',
    dataIndex: 'updateTime',
    sorter: true,
    customRender: (text: string) => {
      return text ? day(text).format('YYYY-MM-DD HH:mm:ss') : '-';
    },
    width: '14.18%',
  },
  {
    title: '保存时间',
    dataIndex: 'saveTime',
    sorter: true,
    customRender: (text: string) => {
      return text ? day(text).format('YYYY-MM-DD HH:mm:ss') : '-';
    },
    width: '14.18%',
  },
  {
    title: '操作',
    scopedSlots: { customRender: 'action' },
    width: '13.8%',
  },
];

/**
 * 统一错误处理
 * @param err 错误对象
 * @param defaultMsg 默认错误信息
 */
function handleError(errMsg: string, defaultMsg?: string) {
  message.error(errMsg || defaultMsg);
}

type PaginationType = {
  showSizeChanger: boolean;
  showQuickJumper: boolean;
  current: number;
  pageSize: number;
  total: number; // 总数据量
  showTotal: (total: number, range: [number, number]) => string;
};

export function useWorkList(props: UseWorkListProps) {
  // 状态数据
  const spinning = ref<boolean>(false);
  const searchText = ref<string>('');
  const beginTime = ref<number>(0);
  const endTime = ref<number>(0);
  const tableData = ref<Array<WorkViewItem>>([]);
  const pagination: PaginationType = reactive({
    showSizeChanger: true,
    showQuickJumper: true,
    current: WORK_LIST_CONSTANTS.DEFAULT_PAGE_CURRENT,
    pageSize: WORK_LIST_CONSTANTS.DEFAULT_PAGE_SIZE,
    total: 0,
    showTotal: (_: number, _range: [number, number]): string => {
      return `本页共${pageTotal.value}个 总共${pagination.total}个`;
    },
  });
  const pageTotal = ref<number>(0); // 记录当前页数据总数

  const filterType = ref<string>('');
  const sortField = ref<string>(WORK_LIST_CONSTANTS.DEFAULT_SORT_FIELD); // 默认按保存时间排序
  const sortOrder = ref<string>(WORK_LIST_CONSTANTS.DEFAULT_SORT_ORDER); // 默认降序

  const deleteConfirmVisible = ref<Record<number, boolean>>({});
  const editConfirmVisible = ref<Record<number, boolean>>({});
  const hoveredRowKey = ref<number | null>(null);
  const workModalType = ref<string>(''); // 用于控制作品预览弹窗的显示类型 preview/select
  const currentPreviewWorkId = ref<number>();

  // 项目选择相关状态
  const selectedProject = ref<number>(
    props.projectId || WORK_LIST_CONSTANTS.ALL_PROJECTS_ID,
  );
  const projectOptions = ref<Array<{ id: number; name: string }>>([
    {
      id: WORK_LIST_CONSTANTS.ALL_PROJECTS_ID,
      name: WORK_LIST_CONSTANTS.ALL_PROJECTS_NAME,
    },
  ]);
  const selectPageNow = ref<number>(WORK_LIST_CONSTANTS.DEFAULT_PAGE_CURRENT); // 下拉框当前页码
  const selectPageSize = ref<number>(WORK_LIST_CONSTANTS.SELECT_PAGE_SIZE); // 下拉框每页加载数量
  const selectLoading = ref<boolean>(false); // 获取项目列表数据时的加载状态
  const selectHasMore = ref<boolean>(true); // 是否有更多数据

  // 表格列配置
  const columns = WORK_LIST_COLUMNS;

  // 获取项目列表数据（用于下拉框）
  const fetchProjectTypes = async (name: string = '') => {
    if (selectLoading.value || !selectHasMore.value) return; // 避免重复加载或没有更多时加载
    selectLoading.value = true;
    let params = {
      name: name ? name : undefined,
      pageNow: selectPageNow.value,
      limit: selectPageSize.value,
      viewMode: 1,
    };
    // 假设 getProjectList 返回 ProjectListResponse 包含 data: ProjectData[] 和 total
    const [err, res] = await getProjectList(params);
    selectLoading.value = false;
    if (err) {
      handleError(`获取项目列表失败，${err.message}`);
      return;
    }
    const { data } = res;
    if (!data || data.length === 0) {
      selectHasMore.value = false;
      return; // 没有数据则没有更多了
    }

    if (
      !name &&
      selectPageNow.value === WORK_LIST_CONSTANTS.DEFAULT_PAGE_CURRENT
    ) {
      projectOptions.value = [
        {
          id: WORK_LIST_CONSTANTS.ALL_PROJECTS_ID,
          name: WORK_LIST_CONSTANTS.ALL_PROJECTS_NAME,
        },
        ...projectOptions.value,
        ...data,
      ];
    } else {
      projectOptions.value = [...projectOptions.value, ...data];
    }

    selectHasMore.value = data.length === WORK_LIST_CONSTANTS.SELECT_PAGE_SIZE;
    if (selectHasMore.value) {
      selectPageNow.value++; // 准备加载下一页
    }
  };

  // 处理下拉框滚动事件
  const handleSelectPopupScroll = (e: Event) => {
    const { target } = e;
    if (!target) return;

    const el = target as HTMLElement;
    // 判断是否滚动到底部 (可根据需要调整阈值)
    const isAtBottom = el.scrollHeight - el.scrollTop <= el.clientHeight + 50;

    if (isAtBottom && !selectLoading.value && selectHasMore.value) {
      fetchProjectTypes(searchText.value); // 加载下一页，使用当前搜索词
    }
  };

  // 处理下拉框显示状态变化
  const handleSelectDropdownVisibleChange = (visible: boolean) => {
    if (visible) {
      handleSearchSelectText('');
    }
  };

  /**
   * 处理项目选择
   * @description 当用户选择下拉框中的项目时触发
   * @param val 选中的项目ID
   */
  const handleSelectProject = (val: number) => {
    selectedProject.value = val; // 更新选中的项目ID
    pagination.current = WORK_LIST_CONSTANTS.DEFAULT_PAGE_CURRENT;
    fetchTableData();
  };

  // const debouncedfetchProjectTypes = debounce(fetchProjectTypes, 300);
  // 处理下拉框搜索文本变化
  const handleSearchSelectText = (val: string) => {
    selectPageNow.value = WORK_LIST_CONSTANTS.DEFAULT_PAGE_CURRENT; // 重置页码
    selectHasMore.value = true; // 重置是否有更多数据
    projectOptions.value = [];
    fetchProjectTypes(val); // 使用防抖函数获取项目列表数据
  };

  // 获取作品列表数据（用于表格）
  const fetchTableData = async (showSpinning: boolean = true) => {
    if (showSpinning) spinning.value = true;
    // 构建作品列表查询参数，包含项目ID筛选
    const params: WorkQuery = {
      isSave: true, // 后端说作品列表这个值要设置为true
      pageNow: pagination.current,
      limit: pagination.pageSize,
      name: searchText.value ? searchText.value : undefined, // 作品名称
      projectId:
        selectedProject.value != WORK_LIST_CONSTANTS.ALL_PROJECTS_ID
          ? selectedProject.value
          : undefined, // 添加项目ID筛选
      type: filterType.value ? filterType.value : undefined, // 添加作品类型筛选参数
      saveStartTime: beginTime.value ? beginTime.value : undefined,
      saveEndTime: endTime.value ? endTime.value : undefined,
      sortKey: sortField.value
        ? sortField.value
        : WORK_LIST_CONSTANTS.DEFAULT_SORT_FIELD, // 添加排序字段 updateTime-最近编辑时间 saveTime-保存时间
      desc: sortOrder.value ? sortOrder.value === 'descend' : true, // 添加排序顺序 ascend-升序 descend-降序
    };
    const [err, res] = await getWorkList(params);
    if (showSpinning) spinning.value = false;
    if (err) {
      handleError(err.message, '获取作品列表失败');
      return;
    }
    const list: WorkViewItem[] = res.data?.workList || []; // 后端返回的作品列表数据

    // 如果当前页没有数据，回退到上一页（场景：当前在最后一页，删除当前页面所有作品，分页条需要更新，页面显示当前最后一页的数据）
    if (
      pagination.current !== WORK_LIST_CONSTANTS.DEFAULT_PAGE_CURRENT &&
      list.length === 0
    ) {
      pagination.current -= 1;
      await fetchTableData(); // 重新获取数据
      return;
    }

    tableData.value = list.map(item => {
      const statusInfo = getWorkStatusInfo(item);
      return {
        ...item,
        isGenerating: isAnyGeneratingWork(statusInfo), // 是否生成中（图文生成中/视频重新生成中）
        isReCompleted: isRecompletedWork(statusInfo), // 是否重新生成完成（视频重新生成完成，用于视频的新旧作品选择）
        hadFailWork: isAnyFailedWork(statusInfo), // 是否有失败的作品
      };
    });
    pageTotal.value = list.length;
    pagination.total = res.total || 0; // 更新总数据量
  };

  /**
   * 禁用编辑按钮
   * @param data 行数据
   * @returns 是否禁用
   */
  const disabledEdit = (data: WorkViewItem) => {
    // 已经未关联项目，或者状态为生成中，或者状态为重新生成待选择，或者有失败的作品，则禁用编辑按钮
    return (
      isUnrelatedProject(data.projectId) ||
      data.isGenerating ||
      data.isReCompleted ||
      data.hadFailWork
    );
  };

  const disabledDelete = (data: WorkViewItem) => {
    // 状态为生成中，则禁用删除按钮
    return data.isGenerating;
  };

  // 处理行事件
  const handleRowEvents = (record: WorkViewItem) => {
    return {
      props: {},
      on: {
        mouseenter: () => (hoveredRowKey.value = record.id), // 鼠标移入行
        mouseleave: () => (hoveredRowKey.value = null), // 鼠标离开行
      },
    };
  };

  // 监听搜索条件变化
  const handleSearchText = (val: string) => {
    if (val === searchText.value) return;
    searchText.value = val;
    pagination.current = WORK_LIST_CONSTANTS.DEFAULT_PAGE_CURRENT;
    debouncedSearch();
  };

  const handleDateChange = (_: Date, dateStrList: string[]) => {
    beginTime.value = dateStrList[0]
      ? new Date(dateStrList[0] + ' 00:00:00').getTime()
      : 0;
    endTime.value = dateStrList[1]
      ? new Date(dateStrList[1] + ' 23:59:59').getTime()
      : 0;
    pagination.current = WORK_LIST_CONSTANTS.DEFAULT_PAGE_CURRENT;
    fetchTableData();
  };

  // 表格变化处理
  const handleTableChange = (
    page: { current: number; pageSize: number },
    filters: { type: string[] },
    sorter: { field: string; order: string },
  ) => {
    pagination.current = page.current;
    pagination.pageSize = page.pageSize;

    // 处理筛选
    if (filters && filters.type && filters.type.length > 0) {
      filterType.value = filters.type[0]; // 保存选中的筛选值
    } else {
      filterType.value = '';
    }

    // 处理排序
    if (sorter.field) {
      sortField.value = sorter.field;
      sortOrder.value = sorter.order;
    }

    fetchTableData();
  };

  // 操作处理
  const handlePreview = (record: WorkViewItem) => {
    if (!record) return;
    currentPreviewWorkId.value = record.id;

    // 更新作品标识，标识作品已被用户查看
    updateImageWorkFlag(record);

    // 如果是视频作品且状态为重新生成待选择，则打开选择新旧作品弹窗
    const statusInfo = getWorkStatusInfo(record);
    if (record.type === PROJECT_TYPE.VIDEO && isRecompletedWork(statusInfo)) {
      workModalType.value = MODAL_TYPE_SELECT; // 视频重新生成待选择
    } else {
      workModalType.value = MODAL_TYPE_PREVIEW; // 预览模式
    }
  };

  const handleViewTemplate = (record: WorkViewItem) => {
    const type = record.type as PROJECT_TYPE;
    const resolvedPath = router.resolve({
      path: `/${
        type === PROJECT_TYPE.VIDEO ? 'video-project' : 'image-project'
      }`,
      query: {
        projectId: String(record.projectId),
        templateId: String(record.templateId),
        step: String(1), // 跳到项目第二步 预览效果页面
        from: PAGE_SOURCE.WORK, // 添加来源标识：作品列表
      },
    });
    window.open(resolvedPath.href, '_blank');
  };
  /** 编辑：打开视频/图文编辑器 */
  const handleEdit = (record: WorkViewItem) => {
    if (record.isGenerating) {
      // 生成中不支持编辑
      return message.error(record.typeName + WORK_STATUS_TIPS.REGENERATING);
    } else if (record.isReCompleted) {
      // 重新生成完成，没进行新旧作品选择不支持编辑
      return message.error(WORK_STATUS_TIPS.RECOMPLETED);
    }
    if (disabledEdit(record)) return;

    // 更新图文作品标识，标识作品已被用户查看
    updateImageWorkFlag(record);

    record.type == PROJECT_TYPE.VIDEO
      ? showVideoEditor(
          record.relWorkId || record.data?.relWorkId || record.id,
          '',
          handleSaveCbAfterEdit,
        )
      : showImageTextEditor(record.id, '', handleSaveCbAfterEdit);
  };

  // 更新图文作品标识，标识作品已被用户查看
  const updateImageWorkFlag = (record: WorkViewItem) => {
    if (record.type === PROJECT_TYPE.IMAGE && record.editAgainGraphic) {
      updateWorkFlag({ id: record.id });
    }
  };

  // 关闭作品弹窗
  const closeWorkModal = () => {
    workModalType.value = '';
    // 关闭弹窗后刷新列表
    fetchTableData(false);
  };

  // 收起编辑器后刷新列表
  const handleSaveCbAfterEdit = () => {
    fetchTableData(false);
  };

  // 点击禁用的删除按钮
  const handleClickDisabledDeleteBtn = (record: WorkViewItem) => {
    if (record.isGenerating) {
      message.error(record.typeName + WORK_STATUS_TIPS.REGENERATING);
    }
  };

  // 删除作品
  const handleDelete = async (record: WorkViewItem) => {
    if (!record) return;

    // 如果是视频作品且有失败的作品，则恢复旧作品
    if (isVideoWork(record) && record.hadFailWork) {
      await handleFailedStateRestore(record);
      return;
    }
    await deleteWorkById(record.id);
  };

  const deleteWorkById = async (id: number) => {
    if (!id) return;
    const [err] = await deleteWork(id);
    if (err) {
      handleError(err.message, '删除失败');
      return;
    }
    message.success('删除成功');
    fetchTableData();
  };

  // 修改作品名称
  const handleUpdateWorkName = async (params: { id: number; name: string }) => {
    spinning.value = true;
    const [err] = await updateWorkName(params);
    spinning.value = false;
    if (err) {
      handleError(err.message, '修改失败');
      return;
    }
    message.success('修改成功');
    fetchTableData();
  };

  const handleSaveWork = () => {
    workModalType.value = '';
    fetchTableData();
  };

  /** 恢复旧作品 */
  const handleFailedStateRestore = async (record: WorkViewItem) => {
    if (!record) return;

    if (!record.data?.relWorkId) {
      message.error('无法获取有效的关联作品ID，无法恢复旧作品');
      return;
    }
    const [err] = await restoreOldWork({
      oldId: record.data.relWorkId.toString() || '',
    });
    if (err) {
      message.error(err.message || '恢复旧作品失败');
    } else {
      message.success('恢复成功！');
      workModalType.value = '';
      fetchTableData(); // 恢复成功，刷新列表
    }
  };

  // 创建防抖搜索函数
  const debouncedSearch = debounce(fetchTableData, 300);

  // 获取弹出框的容器
  const getPopupContainer = (triggerNode: HTMLElement) => {
    // 返回最近的滚动容器, 弹窗跟随页面滚动
    return triggerNode;
  };

  // 判断是否是视频作品
  const isVideoWork = (record: WorkViewItem) => {
    return record.type === PROJECT_TYPE.VIDEO;
  };

  // 视频作品重新生成完成或者图文作品二次编辑完成，展示【新生成标签】
  const isShowNewGeneratedTag = (record: WorkViewItem) => {
    if (isVideoWork(record)) {
      return record.isReCompleted;
    } else {
      const statusInfo = getWorkStatusInfo(record);
      return record.editAgainGraphic && isNormalCompletedWork(statusInfo);
    }
  };

  // 添加和移除页面可见性变化监听器
  const setupVisibilityListener = () => {
    const handler = debounce(() => {
      if (document.visibilityState === 'visible') {
        // 如果当前没有打开作品弹窗，则刷新列表
        if (!workModalType.value) {
          fetchTableData(false);
        }
      }
    }, 100);

    document.addEventListener('visibilitychange', handler);

    // 返回清理函数
    return () => document.removeEventListener('visibilitychange', handler);
  };

  // 初始化
  onMounted(() => {
    const cleanup = setupVisibilityListener();
    onBeforeUnmount(cleanup);
    fetchProjectTypes();
    fetchTableData();
  });

  return {
    spinning,
    selectedProject,
    selectLoading,
    tableData,
    pagination,
    projectOptions,
    deleteConfirmVisible,
    editConfirmVisible,
    hoveredRowKey,
    workModalType,
    currentPreviewWorkId,
    columns,
    handleSelectPopupScroll,
    handleSelectDropdownVisibleChange,
    handleSelectProject,
    handleSearchSelectText,
    disabledEdit,
    disabledDelete,
    handleRowEvents,
    handleSearchText,
    handleDateChange,
    handleTableChange,
    handlePreview,
    handleViewTemplate,
    handleEdit,
    closeWorkModal,
    handleClickDisabledDeleteBtn,
    handleDelete,
    deleteWorkById,
    handleUpdateWorkName,
    handleSaveWork,
    getPopupContainer,
    isUnrelatedProject,
    fetchTableData,
    handleFailedStateRestore,
    isVideoWork,
    isShowNewGeneratedTag,
  };
}
