/**
 * @fileoverview 项目状态相关工具函数
 * @description 提供项目状态判断和控制相关的工具函数
 *
 * @example
 * // 使用 PROJECT_STATUS 枚举
 * import { PROJECT_STATUS } from '@/constants/project';
 * import { shouldDisableEdit, getProjectStatusFromStore } from './projectStatus';
 *
 * // 检查项目是否为生成中状态
 * const isGenerating = shouldDisableEdit(PROJECT_STATUS.GENERATING); // true
 *
 * // 从 Vuex project 模块获取项目状态
 * const status = getProjectStatusFromStore('project123');
 * if (status === PROJECT_STATUS.GENERATING) {
 *   // 禁用编辑功能
 * }
 *
 * // 也可以直接使用 Vuex store
 * import store from '@/store';
 * store.dispatch('project/setProjectStatus', { projectId: 'project123', status: PROJECT_STATUS.GENERATING });
 */

import { PROJECT_STATUS } from '@/constants/project';
import store from '@/store';

/**
 * 判断项目状态是否为生成中或重新生成中
 * @param projectStatus 项目状态
 * @returns 是否为生成中或重新生成中状态
 */
export function isProjectGenerating(
  projectStatus?: PROJECT_STATUS | number,
): boolean {
  if (projectStatus === undefined || projectStatus === null) {
    return false;
  }

  return projectStatus === PROJECT_STATUS.GENERATING;
}

/**
 * 判断项目状态是否应该禁用编辑功能
 * 只有待保存和已完成状态允许编辑，其他状态都禁止编辑
 * @param projectStatus 项目状态
 * @returns 是否应该禁用编辑功能
 */
export function shouldDisableEdit(
  projectStatus?: PROJECT_STATUS | number,
): boolean {
  if (projectStatus === undefined || projectStatus === null) {
    return true; // 状态未知时禁止编辑
  }

  // 只有待保存和已完成状态允许编辑
  return !(
    projectStatus === PROJECT_STATUS.TO_BE_SAVED ||
    projectStatus === PROJECT_STATUS.COMPLETED
  );
}

/**
 * 判断项目状态能否访问上传原料页（生成中状态不能访问）
 * @param projectStatus 项目状态
 * @returns true=不能访问，false=可以访问
 */
export function shouldRestrictProjectAccess(
  projectStatus?: PROJECT_STATUS | number,
): boolean {
  return isProjectGenerating(projectStatus);
}

/**
 * 从 Vuex store 获取项目状态
 * @param projectId 项目ID
 * @returns 项目状态
 */
export function getProjectStatusFromStore(
  projectId: string,
): PROJECT_STATUS | undefined {
  return store.getters['project/getProjectStatus'](projectId);
}

/**
 * 根据项目ID判断是否应该禁用编辑功能
 * @param projectId 项目ID
 * @returns true=禁用编辑，false=允许编辑
 */
export function shouldDisableEditByProjectId(projectId: string): boolean {
  const projectStatus = getProjectStatusFromStore(projectId);
  return shouldDisableEdit(projectStatus);
}

/**
 * 根据项目ID判断项目状态能否访问上传原料页
 * @param projectId 项目ID
 * @returns true=不能访问，false=可以访问
 */
export function shouldRestrictProjectAccessByProjectId(
  projectId: string,
): boolean {
  const projectStatus = getProjectStatusFromStore(projectId);
  return shouldRestrictProjectAccess(projectStatus);
}

/**
 * 判断项目状态能否访问预览效果页（草稿状态不能访问）
 * @param projectStatus 项目状态
 * @returns true=不能访问，false=可以访问
 */
export function shouldRestrictPreviewAccess(
  projectStatus?: PROJECT_STATUS | number,
): boolean {
  // 草稿状态不能访问预览效果页
  return projectStatus === PROJECT_STATUS.DRAFT;
}

/**
 * 根据项目ID判断项目状态能否访问预览效果页
 * @param projectId 项目ID
 * @returns true=不能访问，false=可以访问
 */
export function shouldRestrictPreviewAccessByProjectId(
  projectId: string,
): boolean {
  const projectStatus = getProjectStatusFromStore(projectId);
  return shouldRestrictPreviewAccess(projectStatus);
}
