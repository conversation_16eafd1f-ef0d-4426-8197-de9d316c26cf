<template>
  <vue-draggable-resizable
    :parent="isLimit"
    :x="x"
    :y="y"
    :w="width"
    :h="height"
    :min-width="20"
    :min-height="20"
    @dragging="handleDrag"
    @resizing="handleResize"
    :on-resize-start="handleResizeStart"
    :lock-aspect-ratio="lockRatio"
    :key="vdrKey"
  >
    <img
      v-if="stickerInfo.resId == ''"
      src="@/assets/common/imgEmpty.webp"
      alt="默认封面"
      class="w-full h-full object-cover"
    />
    <ScImg
      v-else
      class="w-full h-full object-contain"
      :src="stickerInfo.resId || ''"
      :max-width="maxWidth"
    />
  </vue-draggable-resizable>
</template>

<script lang="ts" setup>
import ScImg from '@/components/comm/ScImg.vue';
import { ImageSticker } from '@/types';
import { currentEditorType } from '@/constants/project';
import { computed, ref, onMounted, onUnmounted, watch } from 'vue';
import {
  currentSetting,
  globalImageStickerItemSize,
} from '@/components/ImageTextEditor/hook/useWorkInfo';
import { eventBus, EVENT_NAMES } from '@/utils/eventBus';
// 文档在这：https://mauricius.github.io/vue-draggable-resizable/#/
import VueDraggableResizable from 'vue-draggable-resizable';
import { pxToSize, sizeToPx } from './utils';

const props = withDefaults(
  defineProps<{
    /** 当前切图信息 */
    stickerInfo: ImageSticker;
    /**是否限制拖动 */
    isLimit?: boolean;
    /** 可选图片压缩宽度 */
    maxWidth?: number;
  }>(),
  {
    isLimit: false,
  },
);

const vdrKey = ref(0);

const x = computed(() => {
  const _x = sizeToPx(props.stickerInfo.x);
  return currentEditorType == 'image'
    ? _x + (currentSetting.value?.space?.h ?? 0)
    : _x;
});
const y = computed(() => {
  const _y = sizeToPx(props.stickerInfo.y);
  return currentEditorType == 'image'
    ? _y + (currentSetting.value?.space?.v ?? 0)
    : _y;
});
const width = computed(() => {
  return sizeToPx(props.stickerInfo.width || 100);
});

const height = computed(() => {
  return sizeToPx(props.stickerInfo.height || 100);
});

onMounted(() => {
  eventBus.on(EVENT_NAMES.CURRENT_SPACE_CHANGE, handler);
});

onUnmounted(() => {
  eventBus.off(EVENT_NAMES.CURRENT_SPACE_CHANGE, handler);
});

// 图文编辑器特殊处理：
// 限制了拖动范围的情况下(:parent="true")，vue-draggable-resizable会有时机问题（属于组件bug，必须每次重新重新计算width后，都通过vdrKey自增使得组件自动重建才能解决，原因如下：
// 因为在vue-draggable-resizable内部，x、y是直接响应式传入的prop值，而width是组件内维护的(不是直接响应式传入的prop值)
// 时机问题在于：prop中x（或者y）的改变，会先触发vue-draggable-resizable对于x（或者y）的监听，然后触发计算可拖拽范围（这时候它维护的width还是旧的值，所以计算出来的拖拽范围是错误的）
// 等到prop中width真正改变的时候，vue-draggable-resizable是先用旧的width值计算拖拽范围（这样子通过旧的width计算的范围也是错误的），再更新width
// 注意的是，花字也用了vue-draggable-resizable组件，也需要限制拖动范围，但由于花字不需要传入w、h，所以没有这个时机问题
const handler = () => {
  vdrKey.value += 1;
};

/** 拖拽事件 */
const handleDrag = (...$event: [number, number]) => {
  const offsetX =
    currentEditorType == 'image' ? currentSetting.value?.space?.h ?? 0 : 0;
  props.stickerInfo.x = pxToSize($event[0] - offsetX);

  const offsetY =
    currentEditorType == 'image' ? currentSetting.value?.space?.v ?? 0 : 0;
  props.stickerInfo.y = pxToSize($event[1] - offsetY);
};

/** 缩放事件 */
const lockRatio = ref(false);
const handleResizeStart = (handle: string) => {
  if (
    handle === 'tl' ||
    handle === 'tr' ||
    handle === 'bl' ||
    handle === 'br'
  ) {
    lockRatio.value = true;
  } else {
    lockRatio.value = false;
  }
};
const handleResize = (...$event: [number, number, number, number]) => {
  props.stickerInfo.x = pxToSize($event[0]);
  props.stickerInfo.y = pxToSize($event[1]);
  props.stickerInfo.width = pxToSize($event[2]);
  props.stickerInfo.height = pxToSize($event[3]);
};

watch(
  () => [width.value, height.value],
  newSize => {
    globalImageStickerItemSize.value = {
      width: newSize[0],
      height: newSize[1],
    };
  },
);
</script>

<style lang="scss" scoped>
.vdr {
  @apply b-assist cursor-move;
}
::v-deep {
  .handle {
    @apply b-assist z-1;
    width: 10px;
    height: 10px;
    border-radius: 5px;
    background: #fff;
    border: 1px solid #999;
    &.handle-tl {
      @apply top-[-5px] left-[-5px];
    }
    &.handle-tr {
      @apply top-[-5px] right-[-5px];
    }
    &.handle-bl {
      @apply bottom-[-5px] left-[-5px];
    }
    &.handle-br {
      @apply bottom-[-5px] right-[-5px];
    }
    &.handle-tm {
      @apply top-[-3px] h-[6px] rounded-[3px];
    }
    &.handle-ml {
      @apply left-[-3px] w-[6px] rounded-[3px];
    }
    &.handle-mr {
      @apply right-[-3px] w-[6px] rounded-[3px];
    }
    &.handle-bm {
      @apply bottom-[-3px] h-[6px] rounded-[3px];
    }
  }
}
</style>
