# BgMusicSelector 组件 refreshOnOpen 配置使用说明

## 功能概述

在 `BgMusicSelector.vue` 组件中新增了 `refreshOnOpen` 配置选项，用于控制弹窗打开时的数据获取行为。

## 配置说明

### refreshOnOpen 属性

- **类型**: `Boolean`
- **默认值**: `false`
- **描述**: 控制每次打开弹窗时是否重新获取音乐资源数据

## 使用方式

### 1. 默认行为（refreshOnOpen: false 或不设置）

```vue
<template>
  <BgMusicSelector
    v-model="showMusicSelector"
    :resIds="selectedMusicIds"
    :maxSelectCount="5"
    :canUseAiRecommend="true"
    :useAiRecommendValue.sync="useAiRecommend"
    @changeMusicInfo="handleMusicChange"
  />
</template>
```

**行为说明**：

- 组件首次挂载时获取音乐数据并缓存
- 后续打开弹窗时复用缓存数据，不重新请求 API
- 适用于音乐资源相对稳定的场景

### 2. 每次刷新数据（refreshOnOpen: true）

```vue
<template>
  <BgMusicSelector
    v-model="showMusicSelector"
    :resIds="selectedMusicIds"
    :maxSelectCount="5"
    :canUseAiRecommend="true"
    :useAiRecommendValue.sync="useAiRecommend"
    :refreshOnOpen="true"
    @changeMusicInfo="handleMusicChange"
  />
</template>
```

**行为说明**：

- 组件挂载时不会立即加载数据，等到弹窗真正打开时才加载
- 每次打开弹窗都重新调用 API 获取最新的音乐资源数据
- 确保用户看到的是最新的音乐列表
- 适用于音乐资源经常更新的场景
- 避免了不必要的初始请求，提升性能

## 实现原理

### 核心逻辑

1. **弹窗状态监听**: 通过 `watch` 监听 `props.value` 变化
2. **条件判断**: 当弹窗打开时，如果 `props.refreshOnOpen === true` 则重新获取数据
3. **组件挂载**: 根据 `refreshOnOpen` 配置决定是否在挂载时初始化数据

### 生命周期处理

```javascript
// 监听弹窗显示状态
watch(
  () => props.value,
  async newVal => {
    visible.value = newVal;

    // 当弹窗打开时，如果开启了 refreshOnOpen 则重新获取数据
    if (newVal && props.refreshOnOpen) {
      await initData();
    }
  },
);

// 组件挂载时的处理
onMounted(() => {
  // 如果开启了 refreshOnOpen，则等到弹窗打开时才加载数据
  if (props.refreshOnOpen) {
    return;
  }

  // 未开启 refreshOnOpen 时，保持原来的行为：组件挂载时就初始化数据
  initData();
});
```

## 向后兼容性

- 新增的 `refreshOnOpen` 属性为可选属性，默认值为 `false`
- 不影响现有组件的使用方式和行为
- 现有代码无需修改即可正常工作

## 使用建议

### 推荐使用 refreshOnOpen: true 的场景

1. **音乐库经常更新**: 音乐资源频繁增删改的系统
2. **多用户协作**: 多个用户可能同时添加音乐资源
3. **实时性要求高**: 需要确保用户看到最新音乐列表的场景

### 推荐使用默认行为的场景

1. **音乐库相对稳定**: 音乐资源变化不频繁
2. **性能优先**: 希望减少不必要的 API 请求
3. **网络环境较差**: 避免重复请求影响用户体验

## 注意事项

1. 启用 `refreshOnOpen` 会增加 API 请求频率，请根据实际需求权衡
2. 数据刷新过程中会有 loading 状态，用户体验可能略有影响
3. 建议在音乐资源确实需要实时更新的场景下才启用此功能
