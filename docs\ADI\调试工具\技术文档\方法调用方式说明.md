# 作品操作方法的三种调用方式说明

## ⚠️ 重要警告

在 EditProjectView 模块中，`handleSaveUpdate`、`handleEditUpdate`、`handleGenerateUpdate` 这三个方法有**三种不同的调用方式**。

**删除这些方法前必须检查所有调用方式，否则会导致功能异常！**

## 三种调用方式详解

### 1. 通过 Composable 返回值直接调用

**位置**：
- `src/views/EditProjectView/pages/SecondStepVideo/index.vue`
- `src/views/EditProjectView/pages/SecondStepImage/index.vue`

**调用方式**：
```typescript
// 从 useInfiniteWorkList 的返回值中直接获取
const {
  handleSaveUpdate,
  handleEditUpdate,
  handleGenerateUpdate,
} = useInfiniteWorkList(projectId);

// 直接调用
await handleEditUpdate(workId);
await handleGenerateUpdate(workId);
```

**使用场景**：
- 视频编辑器关闭回调处理
- 图文编辑器关闭回调处理

### 2. 通过组件引用调用

**位置**：
- `src/views/EditProjectView/composables/useSecondStep.ts`

**调用方式**：
```typescript
// currentCompRef 是第二步组件的引用（SecondStepVideo/SecondStepImage）
// 调用的是组件暴露的方法
await currentCompRef.handleSaveUpdate(workIds, isBatchSave);
```

**使用场景**：
- 保存到我的作品功能
- 批量保存作品后的状态更新

**重要说明**：
- `currentCompRef` 指向第二步组件实例
- 这些组件通过 `return` 语句暴露了这些方法
- 与 inject 方式获取的是同一个实现，但调用路径不同

### 3. 通过 Provide/Inject 调用

**位置**：
- `src/components/WorkListBar/WorkListBarItem.vue`

**调用方式**：
```typescript
// 通过 inject 获取统一的作品操作接口
const workListActions = inject('workListActions');
const handleSaveUpdate = workListActions?.handleSaveUpdate;

// 调用方法
await handleSaveUpdate([workId], false);
```

**使用场景**：
- 单个作品保存
- 作品列表项的操作

## 架构图示

```
useInfiniteWorkList
├── 返回值直接调用 ──→ SecondStepVideo/SecondStepImage
├── 组件引用调用 ──→ useSecondStep ──→ currentCompRef.method()
└── provide/inject ──→ WorkListBarItem ──→ inject('workListActions')
```

## 删除检查清单

在考虑删除这些方法时，必须检查：

- [ ] `SecondStepVideo/index.vue` 中的直接调用
- [ ] `SecondStepImage/index.vue` 中的直接调用和 props 传递
- [ ] `useSecondStep.ts` 中的组件引用调用
- [ ] `WorkListBarItem.vue` 中的 inject 调用
- [ ] 所有相关的类型定义和接口

## 重构建议

如果要简化这些方法，建议的重构步骤：

1. **统一调用方式**：将所有调用都改为通过 inject 方式
2. **更新组件暴露**：移除组件 return 中的这些方法
3. **更新组件引用调用**：将 `currentCompRef.method()` 改为 inject 方式
4. **保持向后兼容**：在过渡期间保留多种调用方式

## 总结

这三个方法是关键的业务逻辑方法，被多个组件以不同方式调用。删除或修改时必须：

1. **全面检查**：确认所有调用点都已更新
2. **测试验证**：验证所有相关功能正常工作
3. **文档更新**：更新相关文档和注释

**记住：不能仅仅因为某个方法在 `workListActions` 中存在就认为只有 inject 方式在使用！**
