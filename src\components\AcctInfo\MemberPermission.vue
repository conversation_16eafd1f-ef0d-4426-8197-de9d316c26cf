<template>
  <div class="member-permission">
    <div class="member-permission__actions">
      <fa-button
        type="primary"
        class="member-permission__add-button"
        @click="editMember"
        >添加成员</fa-button
      >
    </div>
    <fa-table
      :pagination="showPagination"
      :columns="columns"
      :data-source="dataList"
      row-key="id"
      skin-type="base"
      @change="handleTableChange"
    >
      <template #authBit="record">
        <div
          @click="handleSetAuth(record)"
          :class="
            isSwitchDisabled(record) ? '!cursor-not-allowed' : 'cursor-pointer'
          "
          class="member-permission__auth-wrapper"
        >
          <fa-switch
            defaultChecked
            class="member-permission__auth-switch"
            :checked="record.authBit !== USER_ROLE.NONE"
            :disabled="isSwitchDisabled(record)"
          />
        </div>
      </template>
      <template #operate>
        <fa-button class="pl-[0]" type="link" size="small" @click="editMember"
          >编辑</fa-button
        >
      </template>
    </fa-table>
  </div>
</template>

<script lang="ts" setup>
import { reactive, computed, ref } from 'vue';
import { getMemberList, setMemberAuth } from '@/api/AcctInfoView/index.ts';
import { USER_ROLE } from '@/constants/permission';
import { message } from '@fk/faicomponent';
import { getExternalDynamicUrl } from '@/constants/system';
import { VERSION_NUM_KEY, VERSION_NUM_LIMIT_MAP } from '@/constants/version';
import store from '@/store';
import { showVersionMsg } from '@/utils/version';

const version = computed(() => store.state.user.version);
const isBoss = computed(() => store.state.user.isBoss);

/**
 * 分页配置
 */
const pageConfig = reactive({
  showSizeChanger: true,
  showQuickJumper: true,
  hideOnSinglePage: true,
  current: 1,
  pageSize: 10,
  total: 0,
});

/**
 * 是否显示分页，超过10条数据显示分页
 */
const showPagination = computed(() => {
  return pageConfig.total > 10 ? pageConfig : false;
});

/**
 * 表格列
 */
const columns = [
  {
    title: '成员账号',
    dataIndex: 'sacct',
    width: '25%',
  },
  {
    title: '姓名',
    dataIndex: 'name',
    width: '25%',
  },
  {
    title: '权限',
    scopedSlots: { customRender: 'authBit' },
    width: '25%',
  },
  {
    title: '操作',
    scopedSlots: { customRender: 'operate' },
    width: '25%',
  },
];

/**
 * 成员列表
 */
const dataList = ref<
  {
    /** sid */
    sid: number;
    /** 成员名称 */
    sacct: string;
    /** 成员权限(0-无,1-超级管理员,2-管理员) */
    authBit: number;
    /** 是否允许设置权限 */
    allowSetAuth?: boolean;
  }[]
>([]);

/**
 * @description 定义成员记录接口
 */
interface MemberRecord {
  /** sid */
  sid: number;
  /** 成员名称 */
  sacct: string;
  /** 成员权限(0-无,1-超级管理员,2-管理员) */
  authBit: number;
  /** 是否允许设置权限 */
  allowSetAuth?: boolean;
}

/**
 * @description 判断开关是否禁用
 * 1. boss账号不能关闭权限
 * 2.根据allowSetAuth判断是否能开启权限(受版本坐席限制)
 * 3. 非boss账号无权限控制开关
 * @param {MemberRecord} record 成员数据
 * @returns {boolean} 开关是否禁用
 */
const isSwitchDisabled = (record: MemberRecord): boolean => {
  return (
    isDiasbaledSuperAdmin(record) ||
    isDisabledVersionLimit(record) ||
    isDisabledNoPermission()
  );
};

/**
 * @description 判断是否为超级管理员且不能关闭权限
 * @param {MemberRecord} record 成员数据
 * @returns {boolean} 是否为超级管理员
 */
const isDiasbaledSuperAdmin = (record: MemberRecord): boolean => {
  return record.authBit === USER_ROLE.SUPER_ADMIN;
};

/**
 * @description 判断非boss账号是否超过版本限制不能开启权限
 * @param {MemberRecord} record 成员数据
 * @returns {boolean} 是否超过版本限制
 */
const isDisabledVersionLimit = (record: MemberRecord): boolean => {
  return record.authBit === USER_ROLE.NONE && !record.allowSetAuth;
};

/**
 * @description 用户没有权限控制（除boss账号外，其他用户无权限控制）
 */
const isDisabledNoPermission = (): boolean => {
  return !isBoss.value;
};

/**
 * @description 检查版本限制并显示提示信息
 */
const checkVersionLimit = () => {
  let baseMsg =
    VERSION_NUM_LIMIT_MAP[VERSION_NUM_KEY.MEMBER_PERMISSION][version.value].msg;
  showVersionMsg(baseMsg, version.value);
};

/**
 * @description 处理表格分页、排序等变化
 * @param {{ current: number; pageSize: number }} page 分页信息
 */
const handleTableChange = (page: { current: number; pageSize: number }) => {
  pageConfig.current = page.current;
  pageConfig.pageSize = page.pageSize;
  getMemberListData();
};

/**
 * @description 获取成员列表数据
 */
const getMemberListData = async () => {
  const [err, res] = await getMemberList({
    pageNow: pageConfig.current,
    limit: pageConfig.pageSize,
    desc: true,
    sortKey: 'createTime',
  });
  if (err) {
    message.error(err.message || '获取失败');
    return;
  }
  dataList.value = res?.data;
  pageConfig.total = res?.total;
};

/**
 * @description 设置成员权限
 * @param {MemberRecord} record 成员数据
 */
const handleSetAuth = async (record: MemberRecord) => {
  if (isDiasbaledSuperAdmin(record) || isDisabledNoPermission()) {
    return;
  }
  if (isDisabledVersionLimit(record)) {
    checkVersionLimit();
    return;
  }
  const [err] = await setMemberAuth(
    record.sid,
    record.authBit !== USER_ROLE.NONE,
  );
  if (err) {
    message.error(err.message || '设置失败');
    return;
  }
  message.success('设置成功');
  getMemberListData(); // 刷新列表
};

/**
 * @description 编辑成员，跳转到成员管理页面
 */
const editMember = () => {
  window.open(getExternalDynamicUrl().MEMBER_MANAGE, '_blank');
};

getMemberListData();
</script>

<style scoped>
.member-permission {
  /* 尺寸相关 */
  @apply p-[24px_0] size-full;
}

.member-permission__actions {
  /* 布局相关 */
  @apply flex justify-end;
}

.member-permission__add-button {
  /* 尺寸相关 */
  @apply w-88px mb-24px text-14px;
}

.member-permission__auth-wrapper {
  /* 布局相关 */
  @apply flex items-center;
}

.member-permission__auth-switch {
  /* 交互相关 */
  @apply pointer-events-none;
}

.member-permission__auth-wrapper.--cursor-not-allowed {
  /* 交互相关 */
  @apply cursor-not-allowed;
}
</style>
