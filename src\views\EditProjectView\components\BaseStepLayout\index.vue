<template>
  <div class="base-step">
    <div class="base-step__layout">
      <!-- 左侧面板 -->
      <div class="base-step__panel base-step__panel--left">
        <slot name="left-panel"></slot>
      </div>

      <!-- 中间面板 -->
      <div class="base-step__panel base-step__panel--center">
        <slot name="center-panel"></slot>
      </div>

      <!-- 右侧面板 -->
      <div class="base-step__panel base-step__panel--right">
        <slot name="right-panel"></slot>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent } from 'vue';

export default defineComponent({
  name: 'BaseStepLayout',
});
</script>

<style lang="scss" scoped>
.base-step {
  /* 尺寸相关 */
  @apply w-full h-full;
  /* 外观相关 */
  @apply bg-[#f5f6f8];
}

.base-step__layout {
  /* 布局相关 */
  @apply flex;
  /* 尺寸相关 */
  @apply w-full h-full;
  /* 外观相关 */
  @apply overflow-hidden;
}

.base-step__panel {
  /* 尺寸相关 */
  @apply h-full;
}

.base-step__panel--left {
  /* 尺寸相关 */
  @apply mr-16px overflow-y-auto;
  width: 392px;
}

.base-step__panel--center {
  /* 布局相关 */
  @apply flex flex-col items-center justify-center;
  /* 尺寸相关 */
  @apply p-32px pr-0 overflow-y-auto;
  width: 662px;
  /* 外观相关 */
  @apply bg-white rounded-[16px_0_0_16px] shadow-[0_0_5px_#00000008];
}

.base-step__panel--right {
  /* 布局相关 */
  @apply flex-1;
  /* 尺寸相关 */
  @apply overflow-y-auto py-0px;
  /* 外观相关 */
  @apply bg-white;
  .content-panel {
    /* 尺寸相关 */
    @apply py-32px;
  }
}

/* 响应式布局样式 */
@media screen and (max-width: 1550px) {
  .base-step__panel--left {
    width: 352px;
  }

  .base-step__panel--center {
    width: 432px;
  }
}
</style>
