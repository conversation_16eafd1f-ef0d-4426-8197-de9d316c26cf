import { computed, type Ref } from 'vue';
import {
  getWorkStatusInfo,
  isRecompletedWork,
  isAnyFailedWork,
  isGeneratingWork,
  isRegeneratingWork,
  getWorkStatusDisplayName,
  isAnyGeneratingWork,
} from '@/constants/workStatus';
import type { VideoWorkItem } from '@/types/Work';

/**
 * 视频播放器状态管理Hook
 * @description 统一管理播放器的各种状态计算和显示逻辑
 */
export function useVideoPlayerState(
  progress: Ref<number>,
  isLoading: Ref<boolean>,
  isError: Ref<boolean>,
  workData: Ref<VideoWorkItem | undefined>,
) {
  /**
   * 是否显示加载状态
   */
  const showLoadingState = computed(() => {
    if (!workData.value) return false;

    const statusInfo = getWorkStatusInfo(workData.value);
    return isAnyGeneratingWork(statusInfo);
  });

  /**
   * 是否显示重新生成完成状态
   */
  const showRegeneratedState = computed(() => {
    if (!workData.value) return false;

    const statusInfo = getWorkStatusInfo(workData.value);
    return isRecompletedWork(statusInfo);
  });

  /**
   * 是否显示失败状态
   */
  const showFailedState = computed(() => {
    if (!workData.value) return false;

    const statusInfo = getWorkStatusInfo(workData.value);
    return isAnyFailedWork(statusInfo);
  });

  /**
   * 是否显示错误状态
   */
  const showErrorState = computed(() => {
    return isError.value;
  });

  /**
   * 是否可以显示视频播放器
   */
  const showVideoPlayer = computed(() => {
    return (
      !showLoadingState.value &&
      !showRegeneratedState.value &&
      !showFailedState.value &&
      !showErrorState.value &&
      !isLoading.value
    );
  });

  /**
   * 容器CSS类名
   */
  const containerClasses = computed(() => {
    if (!workData.value) {
      return {
        'video-player__container--error': isError.value,
        'video-player__container--loading': isLoading.value,
      };
    }

    const statusInfo = getWorkStatusInfo(workData.value);

    return {
      'video-player__container--generating': isGeneratingWork(statusInfo),
      'video-player__container--regenerating': isRegeneratingWork(statusInfo),
      'video-player__container--error': isError.value,
      'video-player__container--loading': isLoading.value,
    };
  });

  /**
   * 加载文本
   */
  const loadingText = computed(() => {
    if (!workData.value) {
      if (isLoading.value) {
        return '视频加载中...';
      }
      return '准备就绪';
    }

    const statusInfo = getWorkStatusInfo(workData.value);
    const statusName = getWorkStatusDisplayName(statusInfo);

    if (statusName === '生成中') {
      return ['视频生成中', '请稍后预览'];
    } else if (statusName === '重新生成中') {
      return ['视频重新生成中', '请稍后预览'];
    } else if (isLoading.value) {
      return '视频加载中...';
    }
    return '准备就绪';
  });

  /**
   * 当前进度值
   */
  const currentProgress = computed(() => {
    return Math.max(0, Math.min(100, progress.value));
  });

  /**
   * 状态描述文本
   */
  const statusText = computed(() => {
    if (!workData.value) return '未知状态';

    const statusInfo = getWorkStatusInfo(workData.value);
    return getWorkStatusDisplayName(statusInfo);
  });

  /**
   * 是否处于生成状态
   */
  const isGenerating = computed(() => {
    if (!workData.value) return false;

    const statusInfo = getWorkStatusInfo(workData.value);
    return isAnyGeneratingWork(statusInfo);
  });

  /**
   * 是否可以进行操作
   */
  const canOperate = computed(() => {
    return !isGenerating.value && !isLoading.value && !isError.value;
  });

  return {
    // 显示状态
    showLoadingState,
    showRegeneratedState,
    showFailedState,
    showErrorState,
    showVideoPlayer,

    // 样式和类名
    containerClasses,

    // 文本和进度
    loadingText,
    currentProgress,
    statusText,

    // 状态判断
    isGenerating,
    canOperate,
  };
}
