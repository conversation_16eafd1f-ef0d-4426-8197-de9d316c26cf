# 智能轮询系统文档索引

## 📋 文档概述

智能轮询系统是作品生成页面的核心功能，提供了完整的状态监控和进度更新解决方案。经过文档整理，现在保留核心必要文档，提高可维护性。

**当前版本**: v6.0.0 (模块化架构) **文档整理**: 2025-01-15

## 📁 文档结构

```text
docs/ADI/智能轮询系统/
├── README.md                                    # 系统概览指南
├── 功能特性/
│   └── 智能轮询-视频项目50%进度检查功能.md      # 特殊功能说明
├── 更新日志/
│   └── 智能轮询-完整更新日志.md                 # 完整版本历史
└── 技术文档/
    └── 智能轮询-完整技术指南.md                 # 详细技术文档
```

## 📖 核心文档

### 🏠 系统概览

- **[智能轮询系统](./README.md)** - 系统概览、快速使用和文档导航

### 🔧 技术文档

- **[完整技术指南](./技术文档/智能轮询-完整技术指南.md)** - 架构设计、模块详解、扩展指南

### 📝 版本历史

- **[完整更新日志](./更新日志/智能轮询-完整更新日志.md)** - v4.0.0 到 v6.0.0 的完整版本历史

### ⚡ 功能特性

- **[视频 50%进度检查](./功能特性/智能轮询-视频项目50%进度检查功能.md)** - 视频项目特有功能

## 🔧 重要文件位置

### 源码实现

- **核心实现**: `src/views/EditProjectView/composables/useWorkPolling/` (模块化架构)
- **集成使用**: `src/views/EditProjectView/composables/useInfiniteWorkList.ts`
- **单元测试**: `src/views/EditProjectView/composables/__tests__/useWorkPolling*.test.ts`

### 测试环境

- **单元测试**: `src/views/EditProjectView/composables/__tests__/useWorkPolling*.test.ts`

## 📋 文档管理规范

### 文档分类

- **系统概览**: 快速了解和使用指南
- **技术文档**: 深度技术实现和架构设计
- **更新日志**: 版本变更记录和历史
- **功能特性**: 具体功能的详细说明

### 命名规范

- 统一使用 `智能轮询-[具体内容].md` 格式
- 避免创建过多细分文档
- 优先更新现有文档而非新建

## 🎯 使用指南

| 需求场景     | 推荐文档        | 说明               |
| ------------ | --------------- | ------------------ |
| 快速了解系统 | README.md       | 系统概览和基本使用 |
| 深入技术实现 | 完整技术指南.md | 架构设计和扩展方法 |
| 查看版本历史 | 完整更新日志.md | 了解系统演进过程   |
| 特殊功能说明 | 功能特性目录    | 具体功能的详细说明 |

## ⚠️ 重要提醒

**文档已整理**: 2025-01-15 完成文档整理，删除重复内容，保留核心文档。

**架构稳定**: 当前 v6.0.0 架构稳定，不建议大幅修改。

**维护原则**: 优先更新现有文档，避免文档碎片化。

---

**维护团队**: 前端开发团队 **最后更新**: 2025-01-15 **文档状态**: 已整理完成
