<template>
  <div class="empty-state">
    <div class="empty-state__icon">
      <svg
        viewBox="0 0 64 41"
        width="64"
        height="41"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <g transform="translate(0 1)" fill="none" fillRule="evenodd">
          <ellipse fill="#f5f5f5" cx="32" cy="33" rx="32" ry="7"></ellipse>
          <g fillRule="nonzero" stroke="#d9d9d9">
            <path
              d="M55 12.76L44.854 1.258C44.367.474 43.656 0 42.907 0H21.093c-.749 0-1.46.474-1.947 1.257L9 12.761V22h46v-9.24z"
            ></path>
            <path
              d="M41.613 15.931c0-1.605.994-2.93 2.227-2.931H55v18.137C55 33.26 53.68 35 52.05 35h-40.1C10.32 35 9 33.259 9 31.137V13h11.16c1.233 0 2.227 1.323 2.227 2.928v.022c0 1.605 1.005 2.901 2.237 2.901h14.752c1.232 0 2.237-1.308 2.237-2.913v-.007z"
              fill="#fafafa"
            ></path>
          </g>
        </g>
      </svg>
    </div>
    <div class="empty-state__text">暂无生成的{{ typeText }}</div>
    <div class="empty-state__subtext">{{ typeText }}生成完成后会显示在这里</div>
  </div>
</template>

<script lang="ts">
import { defineComponent, computed, PropType } from 'vue';
import { WorkType, WorkTypeValue } from '@/components/WorkListBar/types';

export default defineComponent({
  name: 'EmptyState',
  props: {
    workListBarType: {
      type: Number as PropType<WorkTypeValue>,
      default: WorkType.VIDEO,
      validator: (value: WorkTypeValue) =>
        [WorkType.VIDEO, WorkType.IMAGE].includes(value),
    },
  },
  setup(props) {
    // 媒体类型文本
    const typeText = computed(() =>
      props.workListBarType === WorkType.VIDEO ? '视频' : '图片',
    );

    return {
      typeText,
    };
  },
});
</script>

<style lang="scss" scoped>
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 0;
  flex: 1;
  background: #fff;

  &__icon {
    margin-bottom: 16px;
    color: #bfbfbf;
  }

  &__text {
    color: #666;
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 8px;
  }

  &__subtext {
    color: #999;
    font-size: 14px;
  }
}
</style>
