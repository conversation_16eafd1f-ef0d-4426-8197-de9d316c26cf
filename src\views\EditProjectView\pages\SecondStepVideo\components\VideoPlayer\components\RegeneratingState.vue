<template>
  <div class="regenerating-state">
    <div class="regenerating-state__content">
      <div class="regenerating-state__spinner">
        <svg viewBox="0 0 50 50" class="regenerating-state__spinner-svg">
          <circle
            cx="25"
            cy="25"
            r="20"
            fill="none"
            stroke="currentColor"
            stroke-width="5"
            stroke-linecap="round"
            class="regenerating-state__spinner-circle"
          />
        </svg>
      </div>
      <div class="regenerating-state__message">{{ message }}</div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent } from 'vue';

export default defineComponent({
  name: 'RegeneratingState',
  props: {
    /** 显示的消息文本 */
    message: {
      type: String,
      default: '视频重新生成中，请稍候...',
    },
  },
});
</script>

<style lang="scss">
.regenerating-state {
  @apply absolute top-0 left-0 w-full h-full flex justify-center items-center bg-black/70 text-white zi-regenerated-state;

  &__content {
    @apply flex flex-col items-center;
  }

  &__spinner {
    @apply w-12 h-12 mb-4;
  }

  &__spinner-svg {
    @apply animate-spin;
  }

  &__spinner-circle {
    @apply animate-[dash_1.5s_ease-in-out_infinite];
    stroke-dasharray: 90, 150;
    stroke-dashoffset: 0;
  }

  &__message {
    @apply text-sm text-white text-center;
  }
}

@keyframes dash {
  0% {
    stroke-dasharray: 1, 150;
    stroke-dashoffset: 0;
  }
  50% {
    stroke-dasharray: 90, 150;
    stroke-dashoffset: -35;
  }
  100% {
    stroke-dasharray: 90, 150;
    stroke-dashoffset: -124;
  }
}
</style>
