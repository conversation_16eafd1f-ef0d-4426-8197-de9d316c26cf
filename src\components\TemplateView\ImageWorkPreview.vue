<template>
  <div class="image-work-preview relative h-full">
    <div class="h-full overflow-y-scroll">
      <!-- 轮播图 -->
      <div
        class="carousel-container"
        @mousedown="handleMouseDown"
        @mousemove="handleMouseMove"
        @mouseup="handleMouseUp"
        @mouseleave="handleMouseLeave"
        :style="{ maxHeight: carouselMaxHeight }"
      >
        <fa-carousel
          ref="carousel"
          class="w-full h-full bg-white"
          :class="{ 'single-item-carousel': isSingleItem }"
          :style="{ maxHeight: elCarouselMaxHeight }"
          arrow="never"
          trigger="click"
          :autoplay="!disableAutoplay"
          @change="handleCarouselChange"
        >
          <!-- 加载状态 -->
          <div
            v-if="showLoading"
            v-for="(_resItem, index) in Math.max(imgList.length, 1)"
            :key="index"
            class="h-full"
          >
            <LoadingPreview
              :imgSrc="coverImg"
              :percent="loadingPercent"
              :loadingText="loadingText"
              class="w-full h-full"
            />
          </div>
          <!-- 失败状态 -->
          <div v-if="showFailed" key="failed-state" class="h-full">
            <FailedState
              :errMsg="failedErrorMessage"
              :workType="PROJECT_TYPE_IMAGE"
              class="w-full h-full"
            />
          </div>
          <!-- 正常图片 -->
          <div
            v-if="!showLoading && !showFailed"
            v-for="(resItem, index) in imgList"
            :key="`preview-img-${index}`"
            class="h-full"
          >
            <ScImg
              :src="resItem.resId"
              :belong="belong || 'user'"
              fit="contain"
              class="m-auto h-full w-full bg-[#fff]"
              :maxWidth="400"
            />
          </div>
        </fa-carousel>
      </div>
      <!-- 文案 -->
      <div class="bg-white min-h-[120px] p-[12px] pb-[52px]">
        <div
          class="text-title font-semibold text-[14px] sc-text-wrap-smart"
          :class="titleClass"
          v-text="title"
        ></div>
        <div
          class="text-text text-[12px] sc-text-wrap-smart pb-[10px]"
          :class="contentClass"
          v-text="content"
        ></div>
      </div>
    </div>
    <img
      src="@/assets/TemplatePreview/bottomSumulator.webp"
      class="absolute zi-footer bottom-0 left-0 right-0"
    />
  </div>
</template>

<script lang="ts" setup>
import ScImg from '../comm/ScImg.vue';
import LoadingPreview from '@/components/WorkView/WorkModal/LoadingPreview.vue';
import FailedState from '@/views/EditProjectView/pages/SecondStepVideo/components/VideoPlayer/components/FailedState.vue';
import { computed, ref, onMounted, onUnmounted, withDefaults } from 'vue';
import { eventBus, EVENT_NAMES } from '@/utils/eventBus';
import { useCarouselSwipe } from '@/hook/useCarouselSwipe';
import type { CarouselInstance } from '@/types/element-ui';
import { PROJECT_TYPE_IMAGE } from '@/views/EditProjectView/constants';

const props = withDefaults(
  defineProps<{
    /** 标题 */
    title: string;
    /** 内容 */
    content: string;
    /** 图片列表 */
    imgList: {
      resId: string;
      resType: number;
    }[];
    /** 归属，默认不传是归属用户 */
    belong?: 'oss' | 'user' | 'system';
    /** 轮播图最大高度，支持 CSS 值或 'full' */
    carouselMaxHeight?: string;
    /** 是否启用轮播图控制功能 */
    enableCarouselControl?: boolean;
    /** 是否禁用自动轮播 */
    disableAutoplay?: boolean;
    /** 是否显示加载状态 */
    showLoading?: boolean;
    /** 加载进度百分比 */
    loadingPercent?: number;
    /** 加载提示文案（支持字符串或字符串数组） */
    loadingText?: string | string[];
    /** 封面图片地址（用于加载状态背景） */
    coverImg?: string;
    /** 是否显示失败状态 */
    showFailed?: boolean;
    /** 失败错误信息 */
    failedErrorMessage?: string;
    /** 标题额外的 CSS 类名 */
    titleClass?: string;
    /** 内容额外的 CSS 类名 */
    contentClass?: string;
  }>(),
  {
    enableCarouselControl: false,
    disableAutoplay: false,
    showLoading: false,
    loadingPercent: 0,
    showFailed: false,
    failedErrorMessage: '生成失败',
  },
);

// 计算轮播图最大高度
const carouselMaxHeight = computed(() => {
  if (props.carouselMaxHeight === 'full') return '100%';
  return props.carouselMaxHeight || '360px';
});

// 计算 el-carousel 组件的最大高度（加载状态和失败状态时单独处理）
const elCarouselMaxHeight = computed(() => {
  // 加载状态时返回 100%
  if (props.showLoading) return '100%';
  // 失败状态时返回 100%
  if (props.showFailed) return '100%';
  return carouselMaxHeight.value;
});

// 计算是否只有一个图片项（用于隐藏指示器）
const isSingleItem = computed(() => {
  // 失败状态时只有一个失败项，应该隐藏指示器
  if (props.showFailed) {
    return true;
  }
  // 加载状态时，根据图片数量判断
  if (props.showLoading) {
    return Math.max(props.imgList.length, 1) === 1;
  }
  return props.imgList.length <= 1;
});

// 轮播图引用
const carousel = ref<CarouselInstance | null>(null);

/**
 * 设置轮播图当前显示项
 * @param index 图片索引
 */
const setActiveItem = (index: number) => {
  if (carousel.value && typeof carousel.value.goTo === 'function') {
    carousel.value.goTo(index);
  }
};

// 监听图片预览切换事件
const handleImagePreviewChange = (...args: unknown[]) => {
  const index = args[0] as number;
  setActiveItem(index);
};

/**
 * 处理轮播图切换事件
 * @param activeIndex 当前激活的轮播图索引
 * @param _oldActiveIndex 之前激活的轮播图索引（未使用）
 */
const handleCarouselChange = (activeIndex: number, _oldActiveIndex: number) => {
  // 只有在启用轮播图控制功能时才发送事件
  if (props.enableCarouselControl) {
    // 通过事件总线通知ImageSidebar更新选中状态
    eventBus.emit(EVENT_NAMES.CAROUSEL_CHANGE, activeIndex);
  }
};

// 计算是否禁用滑动功能
const isSwipeDisabled = computed(() => {
  return isSingleItem.value || props.showLoading || props.showFailed;
});

// 使用轮播图滑动 hooks
const { handleMouseDown, handleMouseMove, handleMouseUp, handleMouseLeave } =
  useCarouselSwipe(carousel, {
    disabled: isSwipeDisabled,
    threshold: 50,
    onSwipe: direction => {
      // 滑动完成回调，可以在这里添加额外的逻辑
      console.log(`轮播图滑动: ${direction}`);
    },
  });

// 组件挂载时监听事件（仅在启用轮播图控制功能时）
onMounted(() => {
  if (props.enableCarouselControl) {
    eventBus.on(EVENT_NAMES.IMAGE_PREVIEW_CHANGE, handleImagePreviewChange);
  }
});

// 组件卸载时移除事件监听（仅在启用轮播图控制功能时）
onUnmounted(() => {
  if (props.enableCarouselControl) {
    eventBus.off(EVENT_NAMES.IMAGE_PREVIEW_CHANGE, handleImagePreviewChange);
  }
});
</script>

<style lang="scss" scope>
.image-work-preview {
  @apply bg-white;
  div {
    /* 隐藏滚动条 */
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE 10+ */

    &::-webkit-scrollbar {
      display: none; /* Chrome, Safari, Edge */
    }
  }
}
.fa-carousel {
  .slick-list {
    height: 100%;
    .slick-track {
      height: 100%;
      .slick-slide > div {
        height: 100%;
      }
    }
  }
  .slick-dots {
    width: 100% !important;
    height: 2px !important;
    padding: 0 8px !important;
    display: flex !important;
    bottom: 4px !important;
    column-gap: 2px;
    align-items: center;
    li {
      padding-left: 0;
      padding-right: 0;
      padding-bottom: 8px;
      margin: 0 !important;
      flex-grow: 1;
      button {
        width: 100% !important;
        height: 2px !important;
        border-radius: 1px;
        background: #d9d9d9 !important;
        opacity: 0.3;
      }
      &.slick-active {
        button {
          opacity: 0.9;
        }
      }
    }
  }
}
.slick-slider {
  @apply h-full;
}

// 当只有一个项目时隐藏指示器
.single-item-carousel {
  .el-carousel__indicators {
    display: none !important;
  }
}
/* 轮播图容器样式 */
.carousel-container {
  @apply w-full h-full;
  /* 鼠标滑动功能样式 */
  cursor: default;
  user-select: none;

  &:active {
    cursor: default;
  }

  /* 防止图片被拖拽 */
  img {
    pointer-events: none;
    user-select: none;
    -webkit-user-drag: none;
    -khtml-user-drag: none;
    -moz-user-drag: none;
    -o-user-drag: none;
  }
}

.el-carousel__container {
  @apply h-full;
}
</style>
