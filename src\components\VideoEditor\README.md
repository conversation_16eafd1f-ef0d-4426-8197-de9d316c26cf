# VideoEditor 视频编辑器模块

视频编辑器（VideoEditor）模块，负责视频编辑、贴图、脚本、音乐等多媒体编辑核心业务流程。

## 目录结构

```
VideoEditor/
├── VideoEditor.vue           # 视频编辑器主组件
├── index.ts                  # 入口导出
├── constants.ts              # 常量定义
├── hook/                     # 业务逻辑与状态管理 hooks
├── StickerEditor/            # 贴图编辑子模块
├── ScriptEditor/             # 脚本编辑子模块
├── MusicEditor/              # 音乐编辑子模块
└── README.md                 # 目录说明文档
```

## 核心功能

- 视频编辑主流程（分镜、合成、导出等）
- 贴图编辑（添加、调整、删除贴图）
- 脚本编辑（字幕、台词、脚本管理）
- 音乐编辑（配乐、音效、剪辑）
- 多媒体资源管理与状态同步
- 编辑器状态与交互管理

## 技术实现要点

- 采用 Vue3 组合式 API，状态与逻辑分离
- 组件高度解耦，便于复用与扩展
- 支持多种编辑子模块（贴图、脚本、音乐等）
- 统一多媒体资源与状态管理
- 类型安全与详细注释
- 业务 hooks 集中管理（hook/）

## 用法示例

```vue
<template>
  <VideoEditor :work="workData" @save="handleSave" />
</template>

<script setup lang="ts">
import VideoEditor from '@/components/VideoEditor/VideoEditor.vue';
const workData = {
  /* ... */
};
function handleSave(result) {
  // 处理保存回调
}
</script>
```

## 组件说明

| 组件/目录名     | 说明             |
| --------------- | ---------------- |
| VideoEditor.vue | 视频编辑器主视图 |
| index.ts        | 入口导出         |
| constants.ts    | 常量定义         |
| hook/           | 业务 hooks 逻辑  |
| StickerEditor/  | 贴图编辑子模块   |
| ScriptEditor/   | 脚本编辑子模块   |
| MusicEditor/    | 音乐编辑子模块   |

## 扩展与维护

- 支持自定义编辑器功能扩展
- 支持多种业务子模块复用
- 便于接入新业务（如滤镜、特效等）
- 详细注释与类型定义，便于团队协作

## 更新日志

- 2025-07-01 v1.0.0 初始版本
- 2025-08-01 v1.1.0 优化编辑体验与性能

---

如需扩展或维护，请参考各组件/逻辑文件内注释与类型定义。
