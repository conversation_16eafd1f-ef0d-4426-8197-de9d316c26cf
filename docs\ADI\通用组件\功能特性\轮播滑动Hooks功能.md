# useCarouselSwipe Hooks 使用说明

## 概述

`useCarouselSwipe` 是一个用于轮播图鼠标滑动功能的 Vue 3 Composition API hooks，提供了完整的鼠标滑动切换轮播图的功能。

**文件位置**: `src/hook/useCarouselSwipe.ts`

## 功能特性

- ✅ **鼠标滑动切换**：向左滑动下一张，向右滑动上一张
- ✅ **智能禁用**：支持动态禁用滑动功能
- ✅ **可配置阈值**：自定义滑动触发距离
- ✅ **回调支持**：滑动完成后的回调函数
- ✅ **防止图片拖拽**：自动处理图片拖拽问题
- ✅ **全局事件处理**：即使鼠标移出区域也能正确处理
- ✅ **内存泄漏防护**：自动清理事件监听器

## 安装与导入

```typescript
import { useCarouselSwipe } from '@/hook/useCarouselSwipe';
```

## 基本用法

```vue
<template>
  <div
    class="carousel-container"
    @mousedown="handleMouseDown"
    @mousemove="handleMouseMove"
    @mouseup="handleMouseUp"
    @mouseleave="handleMouseLeave"
  >
    <el-carousel ref="carousel">
      <!-- 轮播图内容 -->
    </el-carousel>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue';
import { useCarouselSwipe } from '@/hook/useCarouselSwipe';

const carousel = ref();

// 计算是否禁用滑动
const isDisabled = computed(() => {
  // 根据业务逻辑判断是否禁用
  return false;
});

// 使用 hooks
const { handleMouseDown, handleMouseMove, handleMouseUp, handleMouseLeave } =
  useCarouselSwipe(carousel, {
    disabled: isDisabled,
    threshold: 50,
    onSwipe: direction => {
      console.log(`滑动方向: ${direction}`);
    },
  });
</script>

<style scoped>
.carousel-container {
  cursor: grab;
  user-select: none;

  &:active {
    cursor: grabbing;
  }

  /* 防止图片被拖拽 */
  img {
    pointer-events: none;
    user-select: none;
    -webkit-user-drag: none;
    -khtml-user-drag: none;
    -moz-user-drag: none;
    -o-user-drag: none;
  }
}
</style>
```

## API 参数

### useCarouselSwipe(carouselRef, options)

#### 参数

- **carouselRef**: `Ref<any>` - 轮播图组件的引用
- **options**: `Object` - 配置选项
  - **disabled**: `Ref<boolean>` - 是否禁用滑动功能（可选）
  - **threshold**: `number` - 滑动阈值，默认 50px（可选）
  - **onSwipe**: `(direction: 'prev' | 'next') => void` - 滑动完成回调（可选）

#### 返回值

- **isDragging**: `Ref<boolean>` - 是否正在拖拽状态
- **handleMouseDown**: `(event: MouseEvent) => void` - 鼠标按下事件处理函数
- **handleMouseMove**: `(event: MouseEvent) => void` - 鼠标移动事件处理函数
- **handleMouseUp**: `(event: MouseEvent) => void` - 鼠标抬起事件处理函数
- **handleMouseLeave**: `() => void` - 鼠标离开事件处理函数
- **resetDragState**: `() => void` - 重置拖拽状态函数

## 实际应用示例

在 `ImageWorkPreview.vue` 组件中的使用：

```typescript
// 计算是否禁用滑动功能
const isSwipeDisabled = computed(() => {
  return isSingleItem.value || props.showLoading || props.showFailed;
});

// 使用轮播图滑动 hooks
const { handleMouseDown, handleMouseMove, handleMouseUp, handleMouseLeave } =
  useCarouselSwipe(carousel, {
    disabled: isSwipeDisabled,
    threshold: 50,
    onSwipe: direction => {
      console.log(`轮播图滑动: ${direction}`);
    },
  });
```

## 注意事项

1. **CSS 样式**：需要为轮播图容器添加相应的 CSS 样式来防止图片拖拽
2. **事件绑定**：必须将鼠标事件绑定到轮播图的容器元素上
3. **内存管理**：hooks 会自动处理事件监听器的清理，无需手动清理
4. **兼容性**：适用于 Vue 3 + Composition API

## 优势

- **代码复用**：可以在多个轮播图组件中复用
- **逻辑分离**：将滑动逻辑从组件中分离出来
- **类型安全**：完整的 TypeScript 类型支持
- **易于测试**：独立的 hooks 更容易进行单元测试
- **可维护性**：集中管理滑动逻辑，便于维护和更新
