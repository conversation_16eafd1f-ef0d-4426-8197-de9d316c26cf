# OpenAI 推荐词功能重构技术文档

## 重构背景

### 产品需求变更

原有的 OpenAI 推荐词功能存在以下限制：

1. **API 调用次数限制**：每个字段最多调用 10 次 API
2. **单次获取数量少**：每次只获取少量推荐词
3. **用户体验不佳**：超过限制后只能循环显示历史数据

### 新需求

1. **批量获取**：后端接口一次性返回 30 个推荐词
2. **去重处理**：前端对返回的推荐词进行去重
3. **分批显示**：每次只显示 3 个推荐词，通过"换一换"循环显示
4. **自动重新获取**：当前批次显示完毕后，重新调用接口获取新推荐词
5. **缓存失效机制**：依赖字段变化时清空缓存，重新获取推荐词

## 重构方案

### 1. 数据结构重构

#### 移除的旧数据结构

```typescript
// 移除 API 调用次数限制相关
const apiCallCount = ref<number>(0);
const MAX_API_CALLS = 10;

// 移除历史数据缓存机制
const historicalSuggestions = ref<Array<Array<{ value: string } | string>>>([]);
const currentHistoryIndex = ref<number>(0);
```

#### 新增的数据结构

```typescript
// 推荐词缓存池（存储30个去重后的推荐词）
const cachedSuggestions = ref<Array<{ value: string } | string>>([]);

// 当前显示索引（控制显示哪3个推荐词）
const currentDisplayIndex = ref<number>(0);

// 每次显示的推荐词数量
const DISPLAY_COUNT = 3;
```

### 2. 核心功能重构

#### 去重处理函数

```typescript
const deduplicateSuggestions = (
  suggestions: Array<{ value: string } | string>,
): Array<{ value: string } | string> => {
  const seen = new Set<string>();
  return suggestions.filter(suggestion => {
    const value = getSuggestionValue(suggestion);
    if (seen.has(value)) return false;
    seen.add(value);
    return true;
  });
};
```

#### 显示更新函数

```typescript
const updateDisplayedSuggestions = (): void => {
  if (cachedSuggestions.value.length === 0) {
    suggestions.value = [];
    return;
  }

  const startIndex = currentDisplayIndex.value;
  const endIndex = Math.min(
    startIndex + DISPLAY_COUNT,
    cachedSuggestions.value.length,
  );
  suggestions.value = cachedSuggestions.value.slice(startIndex, endIndex);
};
```

#### 智能刷新检测

```typescript
const shouldRefetchSuggestions = (): boolean => {
  // 缓存池为空，需要获取
  if (cachedSuggestions.value.length === 0) return true;

  // 显示索引为0且当前没有显示推荐词，说明依赖字段可能发生了变化
  if (currentDisplayIndex.value === 0 && suggestions.value.length === 0) {
    return true;
  }

  return false;
};
```

### 3. API 调用逻辑重构

#### 重构前

```typescript
// 检查API调用次数限制
if (apiCallCount.value >= MAX_API_CALLS) {
  // 从历史数据中循环显示
  const historyData = historicalSuggestions.value[currentHistoryIndex.value];
  suggestions.value = historyData || [];
  return;
}

// 增加API调用计数
apiCallCount.value += 1;

// API调用后存储到历史缓存
historicalSuggestions.value.push([...responseData.answerList]);
```

#### 重构后

```typescript
// 直接调用API，无次数限制
const [err, res] = await getOpenAiTip({...});

// 处理响应，去重并缓存
if (responseData?.answerList && Array.isArray(responseData.answerList)) {
  const deduplicatedSuggestions = deduplicateSuggestions(responseData.answerList);
  cachedSuggestions.value = deduplicatedSuggestions;
  currentDisplayIndex.value = 0;
  updateDisplayedSuggestions();
}
```

### 4. "换一换"逻辑重构

#### 重构前

```typescript
const handleRefresh = (): void => {
  if (canRefresh.value && !loading.value) {
    fetchSuggestions(); // 每次都调用API
  }
};
```

#### 重构后

```typescript
const handleRefresh = (): void => {
  if (!canRefresh.value || loading.value) return;

  // 智能检测是否需要重新获取
  if (shouldRefetchSuggestions()) {
    fetchSuggestions();
    return;
  }

  // 缓存中还有未显示的推荐词
  if (cachedSuggestions.value.length > 0) {
    const nextIndex = currentDisplayIndex.value + DISPLAY_COUNT;

    if (nextIndex < cachedSuggestions.value.length) {
      // 显示下一批
      currentDisplayIndex.value = nextIndex;
      updateDisplayedSuggestions();
    } else {
      // 当前批次已显示完毕，重新获取
      fetchSuggestions();
    }
  } else {
    fetchSuggestions();
  }
};
```

## 关键问题解决

### 问题 1：显示逻辑冲突

#### 问题描述

依赖字段变化时清空 `suggestions.value` 导致 `shouldShowSuggestions` 变为 `false`，整个推荐区域消失，"换一换"按钮不可见。

#### 原始逻辑

```typescript
// 依赖字段变化时
if (hasChanged) {
  cachedSuggestions.value = []; // 清空缓存
  suggestions.value = []; // 清空显示 ← 导致区域消失
  currentDisplayIndex.value = 0;
}
```

#### 解决方案

采用**"保留显示，延迟刷新"**策略：

```typescript
// 依赖字段变化时
if (hasChanged) {
  // 只重置显示索引，不清空缓存和当前显示
  currentDisplayIndex.value = 0;

  // 保留当前显示的推荐词，避免推荐区域突然消失
  // 下次点击"换一换"时会重新调用API获取新的推荐词

  updateDependencyCache();
}
```

#### 效果

1. **用户体验**：依赖字段变化时推荐区域保持可见
2. **功能正确**：下次点击"换一换"时获取基于新依赖字段的推荐词
3. **逻辑自洽**：显示状态和缓存状态解耦，避免冲突

### 问题 2：依赖条件满足时未自动获取

#### 问题描述

当表单项依赖其他字段时，如果依赖条件从"不满足"变为"满足"，组件不会自动获取推荐词，用户必须手动获得焦点才能触发。

#### 问题场景

```
表单项3依赖表单项1、2：
1. 页面初始加载 → 表单项1、2为空 → dependenciesFulfilled = false
2. 用户填写表单项1 → 依赖字段变化 → dependenciesFulfilled 仍为 false
3. 用户填写表单项2 → 依赖字段变化 → dependenciesFulfilled 变为 true
4. ❌ 但组件不会自动获取推荐词，用户必须手动点击获得焦点
```

#### 原始逻辑

```typescript
// 依赖字段变化监听器
watch(
  () => props.contextData,
  () => {
    // 只处理字段变化，不检查依赖条件状态变化
    if (hasChanged) {
      updateDependencyCache();
      // ❌ 没有检查依赖是否从"不满足"变为"满足"
    }
  },
);
```

#### 解决方案

在依赖字段变化监听器中添加依赖条件状态检测：

```typescript
watch(
  () => props.contextData,
  () => {
    // 记录变化前的依赖满足状态
    const wasDependenciesFulfilled = dependenciesFulfilled.value;

    // ... 处理字段变化 ...

    // 检查依赖条件是否从"不满足"变为"满足"
    const isDependenciesFulfilledNow = dependenciesFulfilled.value;
    const shouldAutoFetch =
      !wasDependenciesFulfilled &&
      isDependenciesFulfilledNow &&
      !hasEverLoaded.value;

    if (shouldAutoFetch) {
      debouncedFetchSuggestions(); // 自动获取推荐词
    }
  },
);
```

#### 修复效果

1. **自动触发**：依赖条件满足时自动获取推荐词
2. **用户体验**：无需手动获得焦点，减少操作步骤
3. **防抖保护**：保持原有的防抖机制，避免频繁 API 调用
4. **状态准确**：精确检测依赖条件的状态变化

## 性能优化

### 1. 减少 API 调用

- **重构前**：每次"换一换"都调用 API
- **重构后**：一次获取 30 个，本地循环显示，减少网络请求

### 2. 智能缓存管理

- **按需刷新**：只有在必要时才重新获取推荐词
- **状态分离**：显示状态与缓存状态独立管理
- **延迟失效**：依赖字段变化时延迟到用户操作时才刷新

### 3. 去重优化

- **前端去重**：避免显示重复内容
- **Set 数据结构**：高效的重复检测
- **一次性处理**：API 返回后立即去重，避免重复计算

## 兼容性保证

### 1. API 接口兼容

- 保持原有的 `getOpenAiTip` 接口调用方式
- 兼容现有的 `AiTipParams` 和 `AiTipResult` 类型定义
- 支持原有的依赖字段配置格式

### 2. 组件接口兼容

- 保持原有的 props 接口不变
- 保持原有的事件接口不变
- 保持原有的样式类名不变

### 3. 使用方式兼容

- 组件的使用方式完全不变
- 外部调用代码无需修改
- 配置参数保持向后兼容

## 测试建议

### 1. 功能测试

- [ ] 初次加载推荐词
- [ ] "换一换"循环显示
- [ ] 依赖字段变化后的刷新
- [ ] API 调用失败的处理
- [ ] 去重功能的正确性

### 2. 性能测试

- [ ] API 调用次数统计
- [ ] 内存使用情况
- [ ] 响应时间测试
- [ ] 大量推荐词的处理

### 3. 边界测试

- [ ] 空推荐词列表
- [ ] 单个推荐词
- [ ] 超长推荐词文本
- [ ] 网络异常情况

## 维护指南

### 1. 关键配置

```typescript
const DISPLAY_COUNT = 3; // 每次显示的推荐词数量
```

### 2. 调试日志

组件中保留了关键调试日志，便于问题排查：

```typescript
console.log(
  `🚀 ADI-LOG ~ setup ~ props.dependencyFields:`,
  props.dependencyFields,
);
console.log(`🚀 ADI-LOG ~ setup ~ hasChanged:`, hasChanged);
```

### 3. 扩展建议

- 可配置的显示数量
- 可配置的缓存策略
- 更丰富的动画效果
- 推荐词质量评分

## 总结

本次重构成功实现了产品需求，主要成果：

1. **功能完整**：支持批量获取、去重、分批显示、循环切换
2. **性能优化**：减少 API 调用，智能缓存管理
3. **用户体验**：无缝切换，保持界面稳定
4. **代码质量**：逻辑清晰，易于维护
5. **向后兼容**：保持原有接口不变

重构后的组件更加健壮、高效，为后续功能扩展奠定了良好基础。
