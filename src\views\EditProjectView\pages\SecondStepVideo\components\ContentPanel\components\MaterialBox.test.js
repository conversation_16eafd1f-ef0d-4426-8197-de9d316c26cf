import { mount } from '@vue/test-utils';
import MaterialBox from './MaterialBox.vue';
import { message } from '@fk/faicomponent';
import { describe, it, expect, vi, beforeEach } from 'vitest';

// Mock clipboard-copy
vi.mock('clipboard-copy', () => ({
  default: vi.fn(),
}));

// Mock message
vi.mock('@fk/faicomponent', () => ({
  message: {
    warning: vi.fn(),
    success: vi.fn(),
    error: vi.fn(),
  },
}));

describe('MaterialBox', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('基础功能', () => {
    it('应该正常渲染复制按钮', () => {
      const wrapper = mount(MaterialBox, {
        propsData: {
          type: 'text',
          layout: 'header-content',
          content: '测试内容',
        },
      });

      const copyButton = wrapper.find(
        '.material-box--layout-header-content__header-actions-copy',
      );
      expect(copyButton.exists()).toBe(true);
      expect(copyButton.attributes('disabled')).toBeFalsy();
    });

    it('应该正常复制content内容', async () => {
      const copy = await import('clipboard-copy');
      const wrapper = mount(MaterialBox, {
        propsData: {
          type: 'text',
          layout: 'header-content',
          content: '测试内容',
        },
      });

      await wrapper.vm.handleCopyButtonClick();
      expect(copy.default).toHaveBeenCalledWith('测试内容');
      expect(message.success).toHaveBeenCalledWith('复制成功');
    });

    it('内容为空时应该显示默认警告', async () => {
      const wrapper = mount(MaterialBox, {
        propsData: {
          type: 'text',
          layout: 'header-content',
          content: '',
        },
      });

      await wrapper.vm.handleCopyButtonClick();
      expect(message.warning).toHaveBeenCalledWith('没有可复制的内容');
    });

    it('可以自定义空内容提示文案', async () => {
      const wrapper = mount(MaterialBox, {
        propsData: {
          type: 'text',
          layout: 'header-content',
          content: '',
          emptyMessage: '暂无内容可复制',
        },
      });

      await wrapper.vm.handleCopyButtonClick();
      expect(message.warning).toHaveBeenCalledWith('暂无内容可复制');
    });

    it('优先复制copyText而不是content', async () => {
      const copy = await import('clipboard-copy');
      const wrapper = mount(MaterialBox, {
        propsData: {
          type: 'text',
          layout: 'header-content',
          content: '显示内容',
          copyText: '复制内容',
        },
      });

      await wrapper.vm.handleCopyButtonClick();
      expect(copy.default).toHaveBeenCalledWith('复制内容');
      expect(message.success).toHaveBeenCalledWith('复制成功');
    });

    it('通过disabled prop禁用时应该禁用复制按钮', () => {
      const wrapper = mount(MaterialBox, {
        propsData: {
          type: 'text',
          layout: 'header-content',
          disabled: true,
          content: '测试内容',
        },
      });

      const copyButton = wrapper.find(
        '.material-box--layout-header-content__header-actions-copy',
      );
      expect(copyButton.attributes('disabled')).toBeTruthy();
    });

    it('复制失败时应该显示错误提示', async () => {
      const copy = await import('clipboard-copy');
      copy.default.mockRejectedValue(new Error('复制失败'));

      const wrapper = mount(MaterialBox, {
        propsData: {
          type: 'text',
          layout: 'header-content',
          content: '测试内容',
        },
      });

      await wrapper.vm.handleCopyButtonClick();
      expect(message.error).toHaveBeenCalledWith('复制失败');
    });
  });

  describe('事件处理', () => {
    it('应该正确触发copyButtonClick成功事件', async () => {
      const copy = await import('clipboard-copy');
      copy.default.mockResolvedValue(); // 确保复制成功

      const wrapper = mount(MaterialBox, {
        propsData: {
          type: 'text',
          layout: 'header-content',
          content: '测试内容',
        },
      });

      await wrapper.vm.handleCopyButtonClick();
      expect(wrapper.emitted('copyButtonClick')).toBeTruthy();
      expect(wrapper.emitted('copyButtonClick')[0]).toEqual([true]);
    });

    it('复制失败时应该触发失败事件', async () => {
      const copy = await import('clipboard-copy');
      copy.default.mockRejectedValue(new Error('复制失败'));

      const wrapper = mount(MaterialBox, {
        propsData: {
          type: 'text',
          layout: 'header-content',
          content: '测试内容',
        },
      });

      await wrapper.vm.handleCopyButtonClick();
      expect(wrapper.emitted('copyButtonClick')).toBeTruthy();
      expect(wrapper.emitted('copyButtonClick')[0]).toEqual([false]);
    });

    it('空内容时应该触发失败事件', async () => {
      const wrapper = mount(MaterialBox, {
        propsData: {
          type: 'text',
          layout: 'header-content',
          content: '',
        },
      });

      await wrapper.vm.handleCopyButtonClick();
      expect(wrapper.emitted('copyButtonClick')).toBeTruthy();
      expect(wrapper.emitted('copyButtonClick')[0]).toEqual([false]);
    });
  });

  describe('组件配置', () => {
    it('应该正确计算操作按钮文本 - 复制', () => {
      const wrapper = mount(MaterialBox, {
        propsData: {
          type: 'text',
          layout: 'header-content',
        },
      });

      expect(wrapper.vm.computedActionButtonText).toBe('复制');
    });

    it('应该正确计算操作按钮文本 - 编辑脚本', () => {
      const wrapper = mount(MaterialBox, {
        propsData: {
          type: 'text',
          layout: 'content-actions',
        },
      });

      expect(wrapper.vm.computedActionButtonText).toBe('编辑脚本');
    });

    it('应该正确计算操作按钮文本 - 更换音乐', () => {
      const wrapper = mount(MaterialBox, {
        propsData: {
          type: 'music',
          layout: 'content-actions',
        },
      });

      expect(wrapper.vm.computedActionButtonText).toBe('更换音乐');
    });

    it('应该正确计算操作按钮文本 - 更换配音', () => {
      const wrapper = mount(MaterialBox, {
        propsData: {
          type: 'voice',
          layout: 'content-actions',
        },
      });

      expect(wrapper.vm.computedActionButtonText).toBe('更换配音');
    });

    it('应该支持自定义操作按钮文本', () => {
      const wrapper = mount(MaterialBox, {
        propsData: {
          type: 'text',
          layout: 'header-content',
          actionButtonText: '自定义复制',
        },
      });

      expect(wrapper.vm.computedActionButtonText).toBe('自定义复制');
    });

    it('应该正确设置组件类名', () => {
      const wrapper = mount(MaterialBox, {
        propsData: {
          type: 'music',
          layout: 'content-actions',
        },
      });

      expect(wrapper.vm.materialBoxClasses).toEqual([
        'material-box--type-music',
        'material-box--layout-content-actions',
      ]);
    });
  });

  describe('Props验证', () => {
    it('应该有正确的默认props值', () => {
      const wrapper = mount(MaterialBox);

      expect(wrapper.vm.type).toBe('text');
      expect(wrapper.vm.layout).toBe('content-actions');
      expect(wrapper.vm.actionButtonText).toBe('操作按钮');
      expect(wrapper.vm.disabled).toBe(false);
      expect(wrapper.vm.copyText).toBe('');
      expect(wrapper.vm.emptyMessage).toBe('没有可复制的内容');
      expect(wrapper.vm.title).toBe('');
      expect(wrapper.vm.content).toBe('');
    });
  });
});
