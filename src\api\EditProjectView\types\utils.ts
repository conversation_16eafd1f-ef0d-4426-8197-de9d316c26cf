/**
 * @fileoverview API 类型工具函数和类型守卫
 * @description 该文件包含类型转换函数、类型判断函数和扩展类型定义
 */

import type {
  ApiVideoScript,
  ApiImageScript,
  ApiVideoSetting,
  ApiImageTextSetting,
  ApiVideoData,
  ApiImageTextData,
} from './response';

// ============= 类型守卫函数 =============

/**
 * 判断是否为视频脚本
 */
export function isApiVideoScript(script: unknown): script is ApiVideoScript {
  return Array.isArray((script as ApiVideoScript)?.segments);
}

/**
 * 判断是否为图文脚本
 */
export function isApiImageScript(script: unknown): script is ApiImageScript {
  return typeof (script as ApiImageScript)?.content === 'string';
}

/**
 * 判断是否为视频设置
 */
export function isApiVideoSetting(
  setting: unknown,
): setting is ApiVideoSetting {
  return (
    typeof setting === 'object' &&
    setting !== null &&
    'bgm' in setting &&
    'voice' in setting
  );
}

/**
 * 判断是否为图文设置
 */
export function isApiImageTextSetting(
  setting: unknown,
): setting is ApiImageTextSetting {
  return (
    typeof setting === 'object' && setting !== null && 'graphicList' in setting
  );
}

/**
 * 判断是否为视频数据
 */
export function isApiVideoData(data: unknown): data is ApiVideoData {
  return (
    typeof data === 'object' &&
    data !== null &&
    'baseVideo' in data &&
    'fullVideo' in data
  );
}

/**
 * 判断是否为图文数据
 */
export function isApiImageTextData(data: unknown): data is ApiImageTextData {
  return typeof data === 'object' && data !== null && 'graphic' in data;
}
