import { PROJECT_TYPE, PROJECT_STATUS } from '@/constants/project';
import { ScriptSegment } from '@/types';
/**
 * @description 基础项目类
 */
export interface ProjectData {
  /** 项目id */
  id: number;
  /** aid */
  aid: number;
  /** 原型id */
  protold: number;
  /** 模板id */
  templateId?: number;
  /** 模板名称 */
  templateName?: string;
  /** 项目作品类型（视频：0，图文：1） */
  type: PROJECT_TYPE;
  /** 项目名称 */
  name: string;
  /** 生成的作品总数 */
  totalNum: number;
  /** 已生成的作品数量 */
  sucNum: number;
  /** 已保存的作品数量 */
  saveNum: number;
  /** 生成失败的作品数量 */
  failNum: number;
  /** 是否存在生成失败的作品 */
  hadFailWork?: boolean;
  /** 状态（0: 草稿, 1: 生成中, 2: 保存, 3: 已完成） */
  status: PROJECT_STATUS;
  /** 项目封面，首个素材首帧图 (可选的封面图结构字段) */
  coverImg?: CoverData;
  /** 模板封面图（模板首帧图） (可选的封面图结构字段) */
  templateCoverImg?: CoverData;
  /** 用户设置 (配音, 背景音乐等, 视频使用), 图文结构为 null */
  setting?: SettingData | null;
  /** MD5 (所有上传的用户资源id)，用于判断是否需要重新下载用户资源 */
  resMd5?: string;
  /** uuid uid，扣点业务使用 */
  txId?: string;
  /** 删除标记 (0: 未删除，1: 已删除) */
  del?: number;
  /** 创建时间 (datetime) */
  createTime: string;
  /** 更新时间 (datetime) */
  updateTime?: string;
  /** 文案内容 */
  script: {
    /** 标题，仅当类型为图文时存在 */
    title?: string;
    /** 文案内容，仅当类型为图文时存在 */
    content?: string;
    /** 脚本，仅当类型为视频时存在 */
    segments?: ScriptSegment[];
  };
  /** 脚本列表 */
  scriptList?: string[];
  /** 行业分类 */
  industry: number;
  /** 场景分类 */
  scene: number;
}

/**
 * @description 封面图结构
 */
export interface CoverData {
  /** 资源Id */
  resId: string;
  /** 类型 */
  resType: string | number;
}

/**
 * @description 背景音乐设置
 */
export interface SettingBgm {
  /** 是否开启 */
  open: boolean;
  /** 是否开启智能推荐 */
  auto: boolean;
  /** 背景音乐的资源id列表 */
  resids: string[];
}

/**
 * @description 配音设置
 */
export interface SettingVoice {
  /** 是否开启 */
  auto: boolean;
  /** 配音员id，注意不是资源id */
  voiceType: string;
}

/*
 * @description 用户设置 (配音, 背景音乐等, 视频使用), 图文结构为 null
 */
export interface SettingData {
  /** 背景音乐设置 */
  bgm: SettingBgm;
  /** 配音设置 */
  voice: SettingVoice;
}
