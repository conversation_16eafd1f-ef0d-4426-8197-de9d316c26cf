/**
 * @fileoverview 数据验证工具函数
 * @description 提供项目状态检查和数据验证功能
 */

import { PROJECT_STATUS } from '@/constants/project';
import { shouldRestrictProjectAccess } from '@/views/EditProjectView/utils/projectStatus';
import { ERROR_MESSAGES, STATUS_NAMES } from '../config';
import type { GetEditInfoResponse } from '../types/response';

/**
 * 获取状态名称
 * @description 根据状态码获取对应的中文名称
 * @param status 项目状态码
 * @returns 状态中文名称
 */
function getStatusName(status: number): string {
  switch (status) {
    case PROJECT_STATUS.GENERATING:
      return STATUS_NAMES.GENERATING;
    case PROJECT_STATUS.TO_BE_SAVED:
      return STATUS_NAMES.TO_BE_SAVED;
    default:
      return STATUS_NAMES.UNKNOWN;
  }
}

/**
 * 验证项目访问权限
 * @description 检查项目状态是否允许访问编辑页面
 * @param projectData 项目数据
 * @param projectId 项目ID
 * @param checkProjectStatus 是否启用状态检查
 * @returns 验证结果，如果验证失败返回错误对象，成功返回null
 */
export function validateProjectAccess(
  projectData: GetEditInfoResponse | undefined,
  projectId: number | undefined,
  checkProjectStatus: boolean = false,
): Error | null {
  // 如果不需要检查状态，直接返回成功
  if (!checkProjectStatus) {
    return null;
  }

  // 如果没有项目ID或项目数据，跳过检查
  if (!projectId || !projectData || projectData.status === undefined) {
    return null;
  }

  // 检查项目状态能否访问上传原料页（生成中状态不能访问）
  const shouldDenyAccess = shouldRestrictProjectAccess(projectData.status);

  if (shouldDenyAccess) {
    const statusName = getStatusName(projectData.status);
    const errorMessage = `${ERROR_MESSAGES.PROJECT_STATUS_CHECK_FAILED}：当前项目状态为 ${projectData.status}(${statusName})，只有非生成中状态才允许访问第一步，需要重定向到正确步骤`;
    
    console.warn(errorMessage, {
      项目ID: projectId,
      当前状态: projectData.status,
      状态名称: statusName,
      判断逻辑: '使用 shouldRestrictProjectAccess 统一判断',
      是否拒绝访问: shouldDenyAccess,
    });
    
    return new Error(errorMessage);
  }

  return null;
}
