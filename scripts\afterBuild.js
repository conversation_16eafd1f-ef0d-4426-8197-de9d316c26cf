import fs from 'fs';
import path from 'path';

// 引入文件操作模块
const sourceDir = path.resolve(path.dirname(''), './dist'); // build 输出目录
const targetHtmlDir = path.resolve(path.dirname(''), '../scportal/web'); // index.html 目标目录
const targetAssetsDir = path.resolve(path.dirname(''), '../scportal/res'); // assets 目标目录

// 复制文件函数
function copyFile(source, target) {
  fs.copyFileSync(source, target);
  console.log(`文件已复制: ${source} -> ${target}`);
}

// 递归复制文件夹内容的函数
function copyDirRecursive(srcDir, destDir) {
  if (!fs.existsSync(destDir)) {
    fs.mkdirSync(destDir, { recursive: true });
  }
  const items = fs.readdirSync(srcDir);
  items.forEach(item => {
    const srcPath = path.join(srcDir, item);
    const destPath = path.join(destDir, item);
    const stat = fs.statSync(srcPath);
    if (stat.isDirectory()) {
      // 如果是文件夹则递归复制
      copyDirRecursive(srcPath, destPath);
    } else {
      // 如果是文件则直接复制
      copyFile(srcPath, destPath);
    }
  });
}

// 递归清空目录下所有文件和子目录
function clearDirRecursive(dirPath) {
  if (!fs.existsSync(dirPath)) return;
  const items = fs.readdirSync(dirPath);
  items.forEach(item => {
    const itemPath = path.join(dirPath, item);
    const stat = fs.statSync(itemPath);
    if (stat.isFile()) {
      fs.unlinkSync(itemPath);
    } else if (stat.isDirectory()) {
      clearDirRecursive(itemPath);
      fs.rmdirSync(itemPath);
    }
  });
  console.log(`已清空目录: ${dirPath}`);
}

// 执行复制操作
const sourceHtml = path.join(sourceDir, 'index.html');
const sourceAssets = path.join(sourceDir, 'assets');

try {
  // 读取 index.html 内容
  let htmlContent = fs.readFileSync(sourceHtml, 'utf-8');
  // 在文件最前面插入两行 JSP 代码
  htmlContent =
    '<%@ page import="fai.web.ScFileStg" %>\n<%String _resRoot = ScFileStg.getScPortalResRoot();%>\n' +
    htmlContent;
  // 将修改后的内容写回 index.html
  fs.writeFileSync(sourceHtml, htmlContent, 'utf-8');
  // 复制 index.html
  copyFile(sourceHtml, path.join(targetHtmlDir, 'index.jsp'));

  // 复制资源文件前，删除目标目录下的旧文件
  const subDirs = ['js', 'css', 'image'];
  subDirs.forEach(dir => {
    const fullDir = path.join(targetAssetsDir, dir);
    clearDirRecursive(fullDir);
  });

  // 复制access下的文件
  const assetFiles = fs.readdirSync(sourceAssets);
  assetFiles.forEach(file => {
    const ext = path.extname(file).toLowerCase();
    let targetSubDir = '';

    // 判断文件类型并设置目标子目录
    if (ext === '.js') {
      targetSubDir = 'js';
    } else if (ext === '.css' || ext === '.ttf' || ext === '.woff') {
      targetSubDir = 'css';
    } else if (
      ['.png', '.jpg', '.jpeg', '.gif', '.svg', '.webp'].includes(ext)
    ) {
      targetSubDir = 'image';
    } else {
      // 其他类型文件不处理
      return;
    }

    // 构建目标目录路径
    const targetDir = path.join(targetAssetsDir, targetSubDir);
    if (!fs.existsSync(targetDir)) {
      fs.mkdirSync(targetDir, { recursive: true });
    }

    // 复制文件
    copyFile(path.join(sourceAssets, file), path.join(targetDir, file));
  });

  // 复制 public/fonts 目录下所有的 ttf 文件到 targetAssetsDir 下的 css/fonts 目录
  const fontDir = path.join(sourceDir, 'fonts');
  if (fs.existsSync(fontDir)) {
    const ttfFiles = fs.readdirSync(fontDir).filter(file => {
      return path.extname(file).toLowerCase() === '.ttf';
    });
    ttfFiles.forEach(file => {
      const sourceFile = path.join(fontDir, file);
      const targetFontDir = path.join(targetAssetsDir, 'css', 'fonts');
      if (!fs.existsSync(targetFontDir)) {
        fs.mkdirSync(targetFontDir, { recursive: true });
      }
      const targetFile = path.join(targetFontDir, file);
      copyFile(sourceFile, targetFile);
    });
  }

  // 复制 public/fontIcons 目录下所有文件到 targetAssetsDir 下的 css/fontIcons 目录
  const fontIconsDir = path.join(sourceDir, 'fontIcons');
  const targetFontIconsDir = path.join(targetAssetsDir, 'css', 'fontIcons');
  if (fs.existsSync(fontIconsDir)) {
    copyDirRecursive(fontIconsDir, targetFontIconsDir);
  }

  console.log('所有文件已成功复制！');
} catch (error) {
  console.error('文件复制过程中发生错误:', error);
}
