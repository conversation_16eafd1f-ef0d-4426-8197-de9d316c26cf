import Vue from 'vue';
import {
  MIME_ICON_TYPE,
  MIMETYPE_PRESET_PREVIEW_LIST,
  STATUS_DEF,
} from '@/components/MaterialManager/constants/index.ts';
import { MaterialManageInfo } from '@/components/MaterialManager/types/index.ts';
import {
  INFO_KEYS,
  SORT_MODE_KEY,
  IMG_PREVIEW_KEY_LIST,
  MIMETYPE_KEY,
} from '@/constants/material';
import { day } from '@/utils/dayjs';
import { isObject, isEqual, isString, isNumber } from 'lodash-es';
import {
  getMaterialFullUrl,
  checkVideoCover,
} from '@/components/MaterialBasicUpload/utils/index.ts';
import { updateFolder, updateResource } from '@/api/Material/index';
import { message as FaMessage } from '@fk/faicomponent';
import { addFolder, moveResource, moveFolder } from '@/api/Material/index';
import store from '@/store';
import { FILE_TYPES } from '@/constants/fileType';
import { isFolder } from '@/utils/resource';

export function getSuffix(info: MaterialManageInfo): string {
  if (isFolder(info)) {
    return '';
  }
  const suffix =
    info[INFO_KEYS.NAME].split('.').pop()?.toLowerCase() || 'default';
  return suffix;
}

export function iconType(info: MaterialManageInfo): string {
  const suffix = getSuffix(info);
  return MIME_ICON_TYPE[suffix as keyof typeof MIME_ICON_TYPE];
}

export function checkOwnerFilePresetPreview(info: MaterialManageInfo): boolean {
  const suffix = getSuffix(info);
  return (
    // 文件夹格式或者正在上传的图片/视频
    MIMETYPE_PRESET_PREVIEW_LIST.includes(suffix as MIMETYPE_KEY) ||
    (!IMG_PREVIEW_KEY_LIST.includes(info.fileType as FILE_TYPES) &&
      !(
        info[INFO_KEYS.RES_ID] &&
        checkVideoCover(info as { fileType: FILE_TYPES; resId: string })
      ))
  );
}

export function inputBlur(
  index: number,
  originText: string,
  {
    value,
    target,
  }: {
    value: string;
    target: HTMLElement | HTMLInputElement;
  },
): void {
  value = value.trim();

  if (value) {
    store.commit('changeName', { index, value });
  } else if (target.nodeName === 'INPUT') {
    (target as HTMLInputElement).value = originText;
  } else {
    target.innerText = originText;
  }
}

// 定义文件夹项接口
export interface FolderItem {
  id: number;
  name: string;
  parentId?: number;
  children?: FolderItem[];
  [key: string]: unknown;
}

// 定义转换后的文件夹项接口
interface TransformedFolderItem {
  [INFO_KEYS.ID]: number;
  [INFO_KEYS.PARENT_ID]: number | undefined;
  [INFO_KEYS.NAME]: string;
  [INFO_KEYS.TYPE]: string;
  [INFO_KEYS.CHILDREN]: TransformedFolderItem[];
  [INFO_KEYS.SELECT]: boolean;
}

/**
 * @description 检查一个值是否为数组且含有元素
 * @param {*} value - 要检查的值
 * @returns {boolean} - 如果是数组且含有元素，则返回true；否则返回false
 */
export function isArrayWithElements<T>(value: unknown): value is T[] {
  return Array.isArray(value) && value.length > 0;
}

/**
 * @description 将扁平列表转换为树形结构
 * @param {T[]} data - 扁平数据列表
 * @param {Object} options - 配置选项
 * @param {string} options.ID - ID字段名
 * @param {string} options.PARENT_ID - 父ID字段名
 * @returns {T[]} - 树形结构数据
 */
export function buildTree<T extends { [key: string]: unknown }>(
  data: T[],
  options: { ID: string; PARENT_ID: string },
): T[] {
  const { ID, PARENT_ID } = options;
  const map: Record<string, T & { children: T[] }> = {};
  const result: T[] = [];

  // 创建映射表
  data.forEach(item => {
    const key = String(item[ID]);
    map[key] = { ...item, children: [] } as T & { children: T[] };
  });

  // 构建树形结构
  data.forEach(item => {
    const key = String(item[ID]);
    const parentId = String(item[PARENT_ID]);
    if (parentId && map[parentId]) {
      map[parentId].children.push(map[key]);
    } else {
      result.push(map[key]);
    }
  });

  return result;
}

export function helper(list: FolderItem[] = []): TransformedFolderItem[] {
  return list.map(item => {
    let children: TransformedFolderItem[] = [];
    if (item.children && isArrayWithElements(item.children)) {
      children = helper(item.children);
    }
    return {
      [INFO_KEYS.ID]: item.id,
      [INFO_KEYS.NAME]: item.name,
      [INFO_KEYS.PARENT_ID]: item.parentId,
      [INFO_KEYS.TYPE]: 'folder', //这个场景下必定是folder类型
      [INFO_KEYS.CHILDREN]: children,
      [INFO_KEYS.SELECT]: false,
    };
  });
}

export function dirListToTree(dirList: FolderItem[]): TransformedFolderItem[] {
  const tree = buildTree(dirList, {
    ID: 'id',
    PARENT_ID: 'parentId',
  });

  return helper(tree);
}

/**
 * @description read data's key
 * @param {object} obj
 * @param {string | array} path //eg: a.b.c
 * @returns {any}
 */
export function getter(obj: Record<string, unknown>, path: string | string[]) {
  if (obj === undefined) {
    return obj;
  }
  let props = [];
  if (Array.isArray(path)) {
    props = path;
  } else {
    props = path.split('.');
  }
  let result = obj;
  for (const prop of props) {
    const isArrayIndex = /\[\d+\]/.test(prop);
    if (isArrayIndex) {
      const match = prop.match(/\d+/);
      const index = match ? parseInt(match[0]) : 0;
      result = result[index] as Record<string, unknown>;
    } else {
      result = result[prop] as Record<string, unknown>;
    }
    if (result === undefined) {
      break;
    }
  }
  return result;
}

export function getNodesByBfs(
  tree: MaterialManageInfo[],
  CHILDREN = 'children',
) {
  // console.log("tree", tree);
  let queue = [...tree];
  let nodeList: MaterialManageInfo[] = [];
  let tmpQueue: MaterialManageInfo[] = [];
  while (queue.length) {
    while (queue.length) {
      let node = queue.shift();
      if (node) {
        nodeList.push(node);
        let children = (getter(node, CHILDREN) ||
          []) as unknown as MaterialManageInfo[];
        //加上父亲的引用
        children.forEach((item: MaterialManageInfo) => {
          item.parent = node;
        });
        if (children.length > 0) {
          tmpQueue.push(...children);
        }
      }
    }
    queue = tmpQueue;
    tmpQueue = [];
  }

  return nodeList;
}

/**
 * @description translate array to map struct
 * @param {array} list
 * @param {string | function} fieldOrHandler
 * @returns {object}
 */
export function array2Map(
  list: unknown[] = [],
  fieldOrHandler: string | ((item: unknown) => unknown) = '',
) {
  let obj: Record<string, unknown[]> = {};
  if (!Array.isArray(list)) {
    list = [];
  }
  list.forEach((item: unknown) => {
    let value: string | number;
    if (typeof fieldOrHandler === 'function') {
      value = fieldOrHandler(item) as string | number;
    } else {
      value = (item as Record<string, unknown>)[fieldOrHandler] as
        | string
        | number;
    }
    const key = String(value);
    if (!obj[key]) {
      obj[key] = [];
    }
    obj[key].push(item);
  });

  return obj;
}

/**
 * @description translate object to map struct eg: [{a:1},{a:2}] => {1:{a:1}, 2:{a:2}}
 * @param {Array} list
 * @param {string|function} uniqueFieldOrHandler
 * @returns {{}}
 */
export const uniqueArray2Map = (
  list: unknown[] = [],
  uniqueFieldOrHandler: string | ((item: unknown) => unknown) = '',
) => {
  let arrayMap = array2Map(list, uniqueFieldOrHandler);
  let finalMap: Record<string, unknown> = {};
  Object.keys(arrayMap).forEach(key => {
    finalMap[key] = arrayMap[key][0];
  });
  return finalMap;
};

/**
 * 获取节点的所有父节点
 * @param node 当前节点
 * @returns 父节点数组
 */
function getParentsOfNode(
  node: MaterialManageInfo,
  PARENT = 'parent',
): MaterialManageInfo[] {
  const parents: MaterialManageInfo[] = [];
  while (node[PARENT]) {
    parents.unshift(node[PARENT] as MaterialManageInfo);
    node = node[PARENT] as MaterialManageInfo;
  }
  return parents;
}

/**
 *
 * @param {object} dirNodes
 * @param {string} dirId
 * @returns {Array<{
 *     id: string,
 *     name: string
 * }>}
 */
export function getDirPath(
  dirNodes: MaterialManageInfo[],
  dirId: string | number,
) {
  let node = dirNodes.find(
    (item: MaterialManageInfo) => item[INFO_KEYS.ID] === dirId,
  );
  if (node) {
    let parents = getParentsOfNode(node);
    console.log('node', node);
    console.log('parents', parents);
    let nodePath = [...parents, node];
    return nodePath.map(node => {
      return {
        [INFO_KEYS.ID]: node[INFO_KEYS.ID],
        [INFO_KEYS.NAME]: node[INFO_KEYS.NAME],
      };
    });
  }
  return [];
}

export function isSearch(status: number) {
  return [STATUS_DEF.SEARCHED, STATUS_DEF.SEARCHED_404].includes(status);
}

export function sortFmList(
  fmList: MaterialManageInfo[],
  sortKey: string,
  sortMode: string,
): MaterialManageInfo[] {
  let dirList = fmList.filter(isFolder);
  let fileList = fmList.filter(item => !isFolder(item));
  // 选择排序模式
  const isAscending = sortMode === SORT_MODE_KEY.ASCE;
  fileList.sort((itemA, itemB) => {
    let a = itemA[sortKey] as number | string;
    let b = itemB[sortKey] as number | string;
    if (isNumber(a) && isNumber(b)) {
      return isAscending ? a - b : b - a;
    } else if (isString(a) && isString(b)) {
      return isAscending ? a.localeCompare(b) : b.localeCompare(a);
    }
    return isAscending
      ? (a as number) - (b as number)
      : (b as number) - (a as number);
  });
  return [...dirList, ...fileList];
}

// 跟素材弹窗内部保持一致的计算方式
export function calcSize(s: number, isToolBar: boolean) {
  if (!s) return isToolBar ? '0.0MB' : '-';
  if (s / (1024 * 1024 * 1024 * 1024) > 1) {
    return `${(s / (1024 * 1024 * 1024 * 1024)).toFixed(1)}T`;
  } else if (s / (1024 * 1024 * 1024) > 1) {
    return `${(s / (1024 * 1024 * 1024)).toFixed(1)}G`;
  } else if (s / (1024 * 1024) > 1) {
    return `${(s / (1024 * 1024)).toFixed(1)}MB`;
  } else if (s / 1024 > 1) {
    return `${(s / 1024).toFixed(1)}KB`;
  }
  return `${s}B`;
}

export function formatTime(time: number): string {
  return time ? day(time).format('YYYY-MM-DD HH:mm:ss') : '-';
}

export function getImgUrl(info: MaterialManageInfo): string {
  return getMaterialFullUrl(
    info[INFO_KEYS.RES_ID] as string,
    FILE_TYPES.WEBP || info[INFO_KEYS.FILE_TYPE],
    'user',
    110,
  );
}

export function getVideoSnapUrl(info: MaterialManageInfo): string {
  return getMaterialFullUrl(
    info[INFO_KEYS.EXTRA]?.cover as unknown as string,
    FILE_TYPES.WEBP || info[INFO_KEYS.FILE_TYPE],
    'user',
    110,
  );
}

/**
 * @description check if fmInfo name can be updated
 * @param {FmInfoDef} info
 * @param {string} name
 * @returns {boolean}
 */
function checkFmInfoNameUpdate(
  info: MaterialManageInfo,
  name: string,
): boolean {
  if (isObject(info)) {
    const oldName = info[INFO_KEYS.NAME];
    if (!isEqual(oldName, name)) {
      if (isString(name)) {
        return true;
      }
    }
  }
  return false;
}

/**
 * @description change file name
 * @param {FmInfoDef} info
 * @param {string} name
 * @returns {Promise<boolean>}
 */
export async function changeFileName(
  info: MaterialManageInfo,
  name: string,
): Promise<boolean> {
  name = name.trim();
  if (checkFmInfoNameUpdate(info, name)) {
    const [err] = await updateResource({
      name,
      resId: info[INFO_KEYS.RES_ID] as string,
    });
    if (err) {
      FaMessage.error(err.message || '修改失败');
    }
    return !err;
  }
  return false;
}

/**
 * @description change dir name
 * @param {FmInfoDef} info
 * @param {string} name
 * @returns {Promise<boolean>}
 */
export async function changeDirName(
  info: MaterialManageInfo,
  name: string,
): Promise<boolean> {
  name = name.trim();
  if (checkFmInfoNameUpdate(info, name)) {
    //这里的接口id需要number，但是业务传入string了
    const [err] = await updateFolder({
      name,
      id: Number(info[INFO_KEYS.ID]),
    });
    if (err) {
      FaMessage.error(err.message || '修改失败');
    }
    return !err;
  }
  return false;
}

/**
 * @description update curr fmList
 * @return {Promise<void>}
 */
export async function loadCurrFmList() {
  await store.dispatch('updateFolderContent');
}

export function updateView() {
  store.dispatch('updateFolderContent');
  store.dispatch('updateFolderList');
}

/**
 * @param {FmInfoDef} info
 * @param {string} newName
 * @return {Promise<void>}
 */
export async function changeFmName(info: MaterialManageInfo, newName: string) {
  let ok = false;
  if (isFolder(info)) {
    ok = await changeDirName(info, newName);
  } else {
    ok = await changeFileName(info, newName);
  }

  if (ok) {
    //按照windows的规范，如果处于搜索状态，那么修改名称之后，需要重新拉取数据，避免不匹配的元素出现在当前界面下。
    await loadCurrFmList();

    //restore select status
    const nowFmInfoList = store.getters['nowFmInfoList'];
    const id = info[INFO_KEYS.ID];
    const select = !!info[INFO_KEYS.SELECT];
    let nowInfo = nowFmInfoList.find(
      (item: MaterialManageInfo) => item[INFO_KEYS.ID] === id,
    );
    if (isObject(nowInfo)) {
      Vue.set(nowInfo, INFO_KEYS.SELECT, select);
    }
    store.dispatch('updateFolderList');
  }
}

export async function folderAdd(parentId?: number, form?: string) {
  const reg = /新建文件夹\((\d+)\)/;
  let maxNum = 0;
  store.getters.dirNodes.forEach((folder: MaterialManageInfo) => {
    const matching = String(folder[INFO_KEYS.NAME]).match(reg);
    if (matching && matching[1]) {
      maxNum = Math.max(maxNum, parseInt(matching[1], 10));
    }
  });
  const preset = {
    parentId: parentId || 0,
    name: `新建文件夹(${maxNum + 1})`,
  };
  const [err, res] = await addFolder(preset);
  if (err) {
    FaMessage.error(err.message || '创建文件夹失败');
    return;
  }
  FaMessage.success('添加成功');
  parentId === store.state.meta.id && (await loadCurrFmList());
  if (res?.data) {
    (res.data as { id: number; form?: string }).form = form || 'all';
  }
  store.commit('setFolderAddInfo', res?.data);
  store.dispatch('updateFolderList');
}

export async function moveFilesToDir(
  targetDirId: number,
  resIds: string[],
): Promise<{ success: boolean }> {
  const preset = {
    parentId: targetDirId || 0,
    resIds,
  };
  const [err] = await moveResource(preset);
  if (err) {
    FaMessage.error(err.message || '文件移动失败');
    return { success: false };
  }
  FaMessage.success('移动成功');
  updateView();
  return { success: true };
}

export async function moveFolderToDir(
  parentId: number,
  ids: number[],
): Promise<{ success: boolean }> {
  const preset = {
    parentId: parentId || 0,
    ids,
  };
  const [err] = await moveFolder(preset);
  if (err) {
    FaMessage.error(err.message || '文件夹移动失败');
    return { success: false };
  }
  FaMessage.success('移动成功');
  updateView();
  return { success: true };
}
