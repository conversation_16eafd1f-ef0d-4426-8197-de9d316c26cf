# ImagePreview 高度范围调整

## 修改概述

根据需求，调整了 ImagePreview 组件中动态轮播图高度计算的范围限制。

## 修改内容

### 高度范围调整

**修改前：**
- 最小高度：200px
- 最大高度：600px

**修改后：**
- 最小高度：378px
- 最大高度：504px

### 修改原因

1. **最小高度调整为378px**：
   - 与轮播图容器的固定宽度（378px）保持一致
   - 确保最小情况下轮播图呈现正方形比例（1:1）
   - 避免过小的高度影响用户体验

2. **最大高度调整为504px**：
   - 限制轮播图的最大高度，避免过高影响页面布局
   - 504px ≈ 378px × 1.33，对应4:3的宽高比
   - 提供合理的显示范围，适应大多数图片比例

## 影响的文件

### 1. 核心组件文件
- `src/views/EditProjectView/pages/SecondStepImage/components/ImagePreview/index.vue`

### 2. 文档文件
- `docs/ADI/ImagePreview动态轮播图高度计算功能.md`
- `docs/ADI/ImagePreview动态高度计算示例.md`

## 代码变更

### 组件代码
```typescript
// 修改前
const minHeight = 200;
const maxHeight = 600;

// 修改后
const minHeight = 378;
const maxHeight = 504;
```

## 实际效果

### 不同图片比例的显示效果

1. **正方形图片（1:1）**：
   - 计算高度：378px
   - 实际高度：378px（在范围内）

2. **横向图片（16:9）**：
   - 计算高度：212.625px
   - 实际高度：378px（被最小值限制）

3. **纵向图片（9:16）**：
   - 计算高度：672px
   - 实际高度：504px（被最大值限制）

4. **极宽图片（20:1）**：
   - 计算高度：18.9px
   - 实际高度：378px（被最小值限制）

### 优势

1. **更合理的显示范围**：
   - 避免轮播图过小或过大
   - 保持良好的视觉比例

2. **一致的用户体验**：
   - 最小高度与容器宽度一致，确保基本的显示质量
   - 最大高度限制避免页面布局被破坏

3. **适应性更强**：
   - 能够适应更多种类的图片比例
   - 在极端情况下提供合理的回退方案

## 测试建议

建议测试以下场景：

1. **标准比例图片**：
   - 1:1（正方形）
   - 4:3（传统照片比例）
   - 16:9（宽屏比例）

2. **极端比例图片**：
   - 超宽图片（如横幅图）
   - 超高图片（如长截图）

3. **边界情况**：
   - 空图片列表
   - 图片加载失败
   - 图片尺寸为0

## 兼容性

- ✅ 向后兼容：不影响现有功能
- ✅ 渐进增强：在支持的环境中提供更好体验
- ✅ 错误恢复：异常情况下优雅回退到默认值

## 总结

此次修改优化了轮播图的显示效果，通过调整高度范围限制，确保在各种图片比例下都能提供良好的用户体验，同时保持页面布局的稳定性。
