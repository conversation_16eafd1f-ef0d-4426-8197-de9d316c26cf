/**
 * 高级日志工具
 * 支持分组、表格、开关控制等功能
 */

// 日志级别枚举
export enum LogLevel {
  DEBUG = 0,
  INFO = 1,
  WARN = 2,
  ERROR = 3,
  OFF = 4,
}

// 日志配置接口
interface LoggerConfig {
  level: LogLevel;
  enableWorkOperations: boolean;
  enableApiRequests: boolean;
  enableCacheOperations: boolean;
  enablePerformance: boolean;
  enableGrouping: boolean;
  enableMemoryMonitoring: boolean;
}

// 默认配置
const defaultConfig: LoggerConfig = {
  level: import.meta.env.DEV ? LogLevel.DEBUG : LogLevel.WARN,
  enableWorkOperations: true,
  enableApiRequests: true,
  enableCacheOperations: true,
  enablePerformance: true,
  enableGrouping: true,
  enableMemoryMonitoring: true,
};

// 当前配置
let currentConfig: LoggerConfig = { ...defaultConfig };

// 日志样式配置
const styles = {
  success: 'color: #4CAF50; font-weight: bold; font-size: 14px;',
  error: 'color: #F44336; font-weight: bold; font-size: 14px;',
  warning: 'color: #FF9800; font-weight: bold; font-size: 14px;',
  info: 'color: #2196F3; font-weight: bold; font-size: 14px;',
  debug: 'color: #607D8B; font-weight: bold;',
  operation: 'color: #9C27B0; font-weight: bold;',
  performance: 'color: #00BCD4; font-weight: bold;',
  cache: 'color: #FF5722; font-weight: bold;',
  api: 'color: #3F51B5; font-weight: bold;',
  memory: 'color: #E91E63; font-weight: bold; font-size: 14px;',
};

/**
 * 日志工具类
 */
export class Logger {
  private static instance: Logger;
  private groupStack: string[] = [];

  private constructor() {}

  static getInstance(): Logger {
    if (!Logger.instance) {
      Logger.instance = new Logger();
    }
    return Logger.instance;
  }

  /**
   * 更新日志配置
   */
  static configure(config: Partial<LoggerConfig>): void {
    currentConfig = { ...currentConfig, ...config };
    console.log('%c📋 日志配置已更新', styles.info, currentConfig);
  }

  /**
   * 获取当前配置
   */
  static getConfig(): LoggerConfig {
    return { ...currentConfig };
  }

  /**
   * 检查是否应该输出日志
   */
  private shouldLog(level: LogLevel, category?: keyof LoggerConfig): boolean {
    if (currentConfig.level > level) return false;
    if (category && !currentConfig[category]) return false;
    return true;
  }

  /**
   * 开始分组日志
   */
  startGroup(
    title: string,
    level: LogLevel = LogLevel.INFO,
    category?: keyof LoggerConfig,
  ): void {
    if (!this.shouldLog(level, category) || !currentConfig.enableGrouping)
      return;

    const emoji = this.getEmojiForLevel(level);
    console.groupCollapsed(`${emoji} ${title}`);
    this.groupStack.push(title);
  }

  /**
   * 结束分组日志
   */
  endGroup(): void {
    if (this.groupStack.length > 0 && currentConfig.enableGrouping) {
      console.groupEnd();
      this.groupStack.pop();
    }
  }

  /**
   * 输出表格数据
   */
  table(
    data: Record<string, unknown> | unknown[],
    title?: string,
    level: LogLevel = LogLevel.INFO,
  ): void {
    if (!this.shouldLog(level)) return;

    if (title) {
      console.log(`%c📊 ${title}`, styles.info);
    }
    console.table(data);
  }

  /**
   * 输出操作开始日志
   */
  operationStart(context: string, params: Record<string, unknown>): void {
    if (!this.shouldLog(LogLevel.INFO, 'enableWorkOperations')) return;

    // 过滤掉轮询更新作品的日志
    if (context === '轮询更新作品') return;

    this.startGroup(
      `🚀 ${context} 开始`,
      LogLevel.INFO,
      'enableWorkOperations',
    );
    this.table(params, '操作参数');
  }

  /**
   * 输出操作完成日志
   */
  operationEnd(
    context: string,
    result: Record<string, unknown>,
    duration?: number,
  ): void {
    if (!this.shouldLog(LogLevel.INFO, 'enableWorkOperations')) return;

    // 过滤掉轮询更新作品的日志
    if (context === '轮询更新作品') return;

    if (duration !== undefined && currentConfig.enablePerformance) {
      result.耗时 = `${duration.toFixed(2)}ms`;
    }

    console.log(`%c✅ ${context} 完成`, styles.success);
    this.table(result, '操作结果');
    this.endGroup();
  }

  /**
   * 输出操作失败日志
   */
  operationError(
    context: string,
    error: Error | unknown,
    duration?: number,
  ): void {
    if (!this.shouldLog(LogLevel.ERROR, 'enableWorkOperations')) return;

    // 过滤掉轮询更新作品的日志
    if (context === '轮询更新作品') return;

    const errorInfo: Record<string, unknown> = {
      错误信息: error instanceof Error ? error.message : String(error),
      错误类型: error instanceof Error ? error.constructor?.name : typeof error,
    };

    if (duration !== undefined && currentConfig.enablePerformance) {
      errorInfo.耗时 = `${duration.toFixed(2)}ms`;
    }

    if (error instanceof Error && error.stack) {
      errorInfo.错误堆栈 = error.stack;
    }

    console.log(`%c❌ ${context} 失败`, styles.error);
    this.table(errorInfo, '错误详情');
    this.endGroup();
  }

  /**
   * 输出API请求日志
   */
  apiRequest(url: string, params: Record<string, unknown>): void {
    if (!this.shouldLog(LogLevel.DEBUG, 'enableApiRequests')) return;

    console.log(`%c📡 API请求`, styles.api, { URL: url, 参数: params });
  }

  /**
   * 输出API响应日志
   */
  apiResponse(url: string, data: unknown, duration?: number): void {
    if (!this.shouldLog(LogLevel.DEBUG, 'enableApiRequests')) return;

    const responseInfo: Record<string, unknown> = {
      URL: url,
      数据量: Array.isArray(data) ? `${data.length} 条` : '1 条',
    };

    if (duration !== undefined && currentConfig.enablePerformance) {
      responseInfo.耗时 = `${duration.toFixed(2)}ms`;
    }

    console.log(`%c📡 API响应`, styles.api);
    this.table(responseInfo, 'API响应信息');
  }

  /**
   * 输出缓存操作日志
   */
  cacheOperation(operation: string, details: Record<string, unknown>): void {
    if (!this.shouldLog(LogLevel.DEBUG, 'enableCacheOperations')) return;

    console.log(`%c🗑️ ${operation}`, styles.cache);
    this.table(details, '缓存操作详情');
  }

  /**
   * 输出性能统计
   */
  performance(name: string, metrics: Record<string, unknown>): void {
    if (!this.shouldLog(LogLevel.INFO, 'enablePerformance')) return;

    console.log(`%c⚡ 性能统计 - ${name}`, styles.performance);
    this.table(metrics, '性能指标');
  }

  /**
   * 输出警告日志
   */
  warn(message: string, data?: Record<string, unknown>): void {
    if (!this.shouldLog(LogLevel.WARN)) return;

    console.warn(`%c⚠️ ${message}`, styles.warning);
    if (data) {
      this.table(data, '警告详情');
    }
  }

  /**
   * 输出调试日志
   */
  debug(message: string, data?: unknown): void {
    if (!this.shouldLog(LogLevel.DEBUG)) return;

    console.log(`%c🔍 ${message}`, styles.debug);
    if (data) {
      console.log(data);
    }
  }

  /**
   * 输出信息日志
   */
  info(message: string, data?: unknown): void {
    if (!this.shouldLog(LogLevel.INFO)) return;

    console.log(`%cℹ️ ${message}`, styles.info);
    if (data) {
      console.log(data);
    }
  }

  /**
   * 输出内存监控日志
   */
  memory(message: string, data?: Record<string, unknown>): void {
    if (!this.shouldLog(LogLevel.DEBUG, 'enableMemoryMonitoring')) return;

    console.log(`%c🧠 [MEMORY] ${message}`, styles.memory);
    if (data) {
      this.table(data, '内存监控详情');
    }
  }

  /**
   * 输出内存监控警告
   */
  memoryWarn(message: string, warnings: string[]): void {
    if (!this.shouldLog(LogLevel.WARN, 'enableMemoryMonitoring')) return;

    console.warn(`%c⚠️ [MEMORY] ${message}`, styles.warning);
    if (warnings.length > 0) {
      this.table({ 警告列表: warnings }, '内存泄漏警告详情');
    }
  }

  /**
   * 开始内存监控分组日志
   */
  memoryGroup(title: string): void {
    if (
      !this.shouldLog(LogLevel.DEBUG, 'enableMemoryMonitoring') ||
      !currentConfig.enableGrouping
    )
      return;

    console.groupCollapsed(`%c🧠 [MEMORY] ${title}`, styles.memory);
    this.groupStack.push(title);
  }

  /**
   * 结束内存监控分组日志
   */
  memoryGroupEnd(): void {
    if (
      this.groupStack.length > 0 &&
      currentConfig.enableGrouping &&
      this.shouldLog(LogLevel.DEBUG, 'enableMemoryMonitoring')
    ) {
      console.groupEnd();
      this.groupStack.pop();
    }
  }

  /**
   * 输出内存监控错误
   */
  memoryError(message: string, error: Error | unknown): void {
    if (!this.shouldLog(LogLevel.ERROR, 'enableMemoryMonitoring')) return;

    const errorInfo: Record<string, unknown> = {
      错误信息: error instanceof Error ? error.message : String(error),
      错误类型: error instanceof Error ? error.constructor?.name : typeof error,
    };

    if (error instanceof Error && error.stack) {
      errorInfo.错误堆栈 = error.stack;
    }

    console.error(`%c❌ [MEMORY] ${message}`, styles.error);
    this.table(errorInfo, '内存监控错误详情');
  }

  /**
   * 根据日志级别获取对应的emoji
   */
  private getEmojiForLevel(level: LogLevel): string {
    switch (level) {
      case LogLevel.DEBUG:
        return '🔍';
      case LogLevel.INFO:
        return 'ℹ️';
      case LogLevel.WARN:
        return '⚠️';
      case LogLevel.ERROR:
        return '❌';
      default:
        return '📝';
    }
  }
}

// 导出单例实例
export const logger = Logger.getInstance();

// 导出便捷方法
export const configureLogger = Logger.configure;
export const getLoggerConfig = Logger.getConfig;
