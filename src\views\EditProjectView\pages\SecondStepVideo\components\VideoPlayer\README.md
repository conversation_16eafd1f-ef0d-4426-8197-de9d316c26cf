# VideoPlayer 组件

## 概述

VideoPlayer 是一个基于 VideoJS 的高度可配置的视频播放器组件，支持封面图、错误处理、重试机制等功能。

## 特性

- ✅ **模块化架构**: 使用 Composition API 和 hooks 实现清晰的关注点分离
- ✅ **内存监控**: 完善的内存泄漏检测和播放器实例管理
- ✅ **日志系统**: 统一的 `[MEMORY]` 日志前缀，便于过滤和排查
- ✅ **错误处理**: 统一的错误处理和重试机制
- ✅ **状态管理**: 集中的状态管理和计算属性
- ✅ **配置系统**: 灵活的配置管理，支持动态配置
- ✅ **类型安全**: 完整的 TypeScript 类型定义
- ✅ **封面图支持**: 支持动态封面图显示
- ✅ **响应式设计**: 适配不同屏幕尺寸
- ✅ **性能优化**: 防抖机制、竞态条件处理、资源及时清理

## 使用方式

### 基础用法

```vue
<template>
  <VideoPlayer
    :videoUrl="videoUrl"
    :videoStatus="videoStatus"
    :progress="progress"
    :coverImg="coverImg"
    :coverImgType="coverImgType"
    @video-player:action="handlePlayerAction"
  />
</template>

<script setup>
import VideoPlayer from './components/VideoPlayer/index.vue';

const videoUrl = ref('https://example.com/video.mp4');
const videoStatus = ref(1); // 使用 WORK_STATUS 常量
const progress = ref(0);
const coverImg = ref(12345);
const coverImgType = ref(1);

const handlePlayerAction = event => {
  console.log('播放器操作:', event.actionType);
};
</script>
```

### 高级配置

```vue
<template>
  <VideoPlayer
    :videoUrl="videoUrl"
    :videoStatus="videoStatus"
    :progress="progress"
    :coverImg="coverImg"
    :coverImgType="coverImgType"
    :config="playerConfig"
    @video-player:action="handlePlayerAction"
  />
</template>

<script setup>
const playerConfig = {
  autoplay: false,
  muted: true,
  loop: true,
  controls: false,
  preload: 'metadata',
};
</script>
```

## Props

| 属性         | 类型              | 默认值 | 说明             |
| ------------ | ----------------- | ------ | ---------------- |
| videoUrl     | string            | -      | 视频 URL（必需） |
| videoStatus  | number            | -      | 视频状态（必需） |
| progress     | number            | 0      | 进度百分比       |
| coverImg     | number            | 0      | 封面图资源 ID    |
| coverImgType | number            | 0      | 封面图类型       |
| config       | VideoPlayerConfig | {}     | 自定义播放器配置 |

## Events

| 事件名              | 参数                   | 说明           |
| ------------------- | ---------------------- | -------------- |
| video-player:action | { actionType: string } | 播放器操作事件 |

### 事件类型

- `play`: 开始播放
- `pause`: 暂停播放
- `ended`: 播放结束
- `error`: 播放错误
- `fullscreen`: 全屏切换

## 架构设计

### Hooks 组织

```
hooks/
├── usePlayer.ts              # 主要播放器管理
├── usePlayerConfig.ts        # 配置管理
├── usePlayerEvents.ts        # 事件处理
├── useErrorHandling.ts       # 错误处理
├── useVideoPlayerState.ts    # 状态管理
├── useFullscreen.ts          # 全屏功能
└── usePlaybackState.ts       # 播放状态
```

### 组件结构

```
VideoPlayer/
├── index.vue                 # 主组件
├── types.ts                  # 类型定义
├── README.md                 # 文档
├── hooks/                    # Composition API hooks
├── components/               # 子组件
├── config/                   # 配置文件
└── constants/                # 常量定义
```

## 扩展指南

### 添加新的播放器配置

1. 在 `types.ts` 中扩展 `VideoPlayerConfig` 接口
2. 在 `usePlayerConfig.ts` 中添加配置处理逻辑
3. 在 `config/player.config.ts` 中添加默认值

### 添加新的错误类型

1. 在 `useErrorHandling.ts` 中的 `errorMessages` 对象中添加新的错误映射
2. 根据需要扩展错误处理逻辑

### 添加新的状态

1. 在 `constants/` 中定义新的状态常量
2. 在 `useVideoPlayerState.ts` 中添加相应的计算属性
3. 在主组件中使用新的状态

## 最佳实践

1. **状态管理**: 使用专门的状态管理 hook，避免在主组件中直接计算状态
2. **错误处理**: 统一使用错误处理 hook，提供一致的用户体验
3. **配置管理**: 通过配置 hook 管理播放器配置，支持动态配置
4. **类型安全**: 充分利用 TypeScript 类型系统，确保类型安全
5. **组件解耦**: 通过 props 和 events 进行组件通信，避免紧耦合

## 性能优化

- 使用 `computed` 进行状态计算，避免不必要的重新计算
- 合理使用 `watch` 监听器，避免过度监听
- 在组件销毁时正确清理播放器实例和事件监听器
- 使用 `key` 属性确保在 URL 变化时正确重建播放器

## 🔍 内存检测与调试

### 统一日志前缀

所有内存相关日志使用统一的 `[MEMORY]` 前缀，便于过滤排查：

```javascript
[MEMORY] 播放器实例创建 (作品ID: 12345)
[MEMORY] 播放器实例销毁 (作品ID: 12345)
[MEMORY] VideoPlayer 内存泄漏警告: ["播放器实例数量过多: 6"]
```

### 日志过滤方法

在浏览器开发者工具的控制台过滤框中输入 `[MEMORY]` 即可只显示内存相关日志。

### 内存监控命令

```javascript
// 查看当前内存统计
memoryMonitor.printDetailedStats();

// 强制垃圾回收（开发环境）
memoryMonitor.forceGarbageCollection();

// 重置监控数据
memoryMonitor.reset();
```
