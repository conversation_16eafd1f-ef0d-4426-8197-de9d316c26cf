import { Module } from 'vuex';
import { RootState } from '@/store';
import Vue from 'vue';
import store from '@/store';
import {
  SORT_BY_KEY,
  STATUS_DEF,
  FM_HISTORY_LIST_KEY,
} from '@/components/MaterialManager/constants/index.ts';
import {
  MaterialManageInfo,
  FileType,
} from '@/components/MaterialManager/types/index.ts';
import {
  INFO_KEYS,
  SORT_MODE_KEY,
  IMG_UPLOAD_LIST,
  VIDEO_UPLOAD_LIST,
  ROOT_FOLDER_ID,
} from '@/constants/material';
import {
  dirListToTree,
  getNodesByBfs,
  uniqueArray2Map,
  getDirPath,
  isSearch,
  sortFmList,
  FolderItem,
  updateView,
  moveFilesToDir,
  moveFolderToDir,
} from '@/components/MaterialManager/utils/index.ts';
import {
  getFolderList,
  getFolderContent,
  deleteFolder,
  deleteResource,
} from '@/api/Material/index.ts';
import { message as FaMessage } from '@fk/faicomponent';
import { commonUploadFiles } from '@/utils/upload/commonUpload';
import { showConfirmModal } from '@/components/comm/ScModalConfirm/index';
import { isFolder } from '@/utils/resource';
import { uploadFileData } from '@/components/MaterialBasicUpload/utils/uploadTempStorage';
import { createTempMaterialFileData } from '@/components/MaterialManager/utils/uploadTempStorage';
import {
  updateFileProgress,
  updateFileUploadComplete,
  removeFileFromList,
} from '@/components/MaterialBasicUpload/utils/uploadTempStorage';

// 使用 localStorage
const initialSearchValue = JSON.parse(
  localStorage.getItem(FM_HISTORY_LIST_KEY) || '[]',
);

export interface MetaState {
  selectType: number;
  searchValue: string;
  historyList: string[];
  status: number;
  metaLoaded: boolean;
  /** 是否展示 moveModal */
  showMoveModal: boolean;
  moveOne: boolean | number;
  showUpload: boolean;

  /** 默认加载的结果列表 */
  fmInfoList: Array<MaterialManageInfo>;
  /** 搜索的结果列表 */
  searchedFmInfoList: Array<MaterialManageInfo>;

  treeData: Array<MaterialManageInfo>;
  /** sidebar分类 */
  category: string;
  /** 窗口文件夹id */
  id: number;
  /** 以什么排序 */
  sortBy: string;
  /** 排序模式 */
  sortMode: string;

  /** 回收站列表 */
  deleteList: Array<MaterialManageInfo>;

  folderAddInfo: object;

  page: number; // 当前页码
  pageSize: number; // 每页显示数量
  filesTotal: number; // 文件总数
  moveOneData: MaterialManageInfo | null; // 移动单个资源ID
  fileUploading: boolean; // 文件上传中
}

export const state: MetaState = {
  selectType: FileType.ALL,
  searchValue: '',
  historyList: initialSearchValue,
  status: STATUS_DEF.INIT,
  metaLoaded: false, // 素材库整体是否加载完成
  /** 是否展示 moveModal */
  showMoveModal: false,
  moveOne: false,
  showUpload: false,

  fmInfoList: [],
  searchedFmInfoList: [],
  treeData: [],
  /** sidebar分类 */
  category: 'file',
  /** 窗口文件夹id */
  id: ROOT_FOLDER_ID, // 0 - 全部

  /** 默认以时间排序 */
  sortBy: SORT_BY_KEY.CREATE_TIME,
  /** 默认排序模式为降序 */
  sortMode: SORT_MODE_KEY.DESC,

  /** 回收站列表 */
  deleteList: [] as MaterialManageInfo[],

  folderAddInfo: {},

  /** 分页参数 */
  page: 1, // 当前页码
  pageSize: 50, // 每页显示数量
  filesTotal: 0, // 文件总数
  moveOneData: null, // 移动单个资源/文件夹信息
  fileUploading: false, // 文件上传中
};

interface Getters {
  filesInfo: {
    item: MaterialManageInfo;
    path: MaterialManageInfo[];
  };
  dirTreeData: MaterialManageInfo[];
  dirNodes: MaterialManageInfo[];
  dirNodesMapById: Record<string, MaterialManageInfo>;
  currDir: MaterialManageInfo | undefined;
  currDirPath: Array<{
    [INFO_KEYS.ID]: string | number;
    [INFO_KEYS.NAME]: string;
  }>;
  currSubDir: MaterialManageInfo[];
  currSubDirNodes: MaterialManageInfo[];
  /** 对nowFmInfoList进行排序的结果列表，比如基于文件名，上传时间等进行排序 */
  sortedFmInfoList: Array<MaterialManageInfo>;
  /** 搜索关键词的结果列表 */
  searchedFiles: Array<MaterialManageInfo>;
  /** 当前使用的结果列表，如果是搜索状态则拿到searchedFmInfoList，反之是fmInfoList */
  nowFmInfoList: Array<MaterialManageInfo>;
  selects: Array<boolean>;
  nowSelectedFmInfoList: Array<MaterialManageInfo>;
  nowSelectedResIdList: Array<string>;
}

const getters = {
  selects(_state: MetaState, getters: Getters) {
    return getters.sortedFmInfoList.map(item => item[INFO_KEYS.SELECT]);
  },
  nowSelectedFmInfoList(_state: MetaState, getters: Getters) {
    return getters.nowFmInfoList.filter(item => item[INFO_KEYS.SELECT]);
  },
  nowSelectedResIdList(_state: MetaState, getters: Getters) {
    return getters.nowSelectedFmInfoList
      .filter(item => !isFolder(item))
      .map(item => item[INFO_KEYS.RES_ID]);
  },
  nowSelectedFolderIdList(_state: MetaState, getters: Getters) {
    return getters.nowSelectedFmInfoList
      .filter(item => isFolder(item))
      .map(item => item[INFO_KEYS.ID]);
  },
  isSearchStatus(state: MetaState) {
    return isSearch(state.status);
  },
  dirTreeData(state: MetaState) {
    const folderItems = state.treeData.map(item => ({
      id: Number(item[INFO_KEYS.ID]),
      name: item[INFO_KEYS.NAME],
      parentId: item.parentId !== null ? Number(item.parentId) : undefined,
      children: item[INFO_KEYS.CHILDREN] as FolderItem[] | undefined,
    }));
    const treeData = dirListToTree(folderItems);
    return treeData;
  },
  dirNodes: (_state: MetaState, getters: Getters) => {
    const convertToFileInfo = (
      item: MaterialManageInfo,
    ): MaterialManageInfo => ({
      ...item,
      [INFO_KEYS.TYPE]: item[INFO_KEYS.TYPE]
        ? Number(item[INFO_KEYS.TYPE])
        : undefined,
      [INFO_KEYS.CHILDREN]: item[INFO_KEYS.CHILDREN]?.map(convertToFileInfo),
    });
    const fileInfoArray = getters.dirTreeData.map(convertToFileInfo);
    return getNodesByBfs(fileInfoArray, INFO_KEYS.CHILDREN);
  },
  dirNodesMapById(_state: MetaState, getters: Getters) {
    return uniqueArray2Map(getters.dirNodes, INFO_KEYS.ID);
  },
  currDirPath(_state: MetaState, getters: Getters) {
    const convertToFileInfo = (
      item: MaterialManageInfo,
    ): MaterialManageInfo => ({
      ...item,
      [INFO_KEYS.TYPE]: item[INFO_KEYS.TYPE]
        ? Number(item[INFO_KEYS.TYPE])
        : undefined,
      [INFO_KEYS.CHILDREN]: item[INFO_KEYS.CHILDREN]?.map(convertToFileInfo),
    });
    const fileInfoArray = getters.dirNodes.map(convertToFileInfo);
    return getDirPath(fileInfoArray, state.id);
  },
  currDir(_state: MetaState, getters: Getters) {
    return getters.dirNodesMapById[state.id];
  },
  // 当前目录下的子目录，和文件fmInfo是同一个概念
  currSubDir(_state: MetaState, getters: Getters) {
    if (getters.currDir) {
      return getters.currDir[INFO_KEYS.CHILDREN] || [];
    } else {
      return [];
    }
  },
  currSubDirNodes(_state: MetaState, getters: Getters) {
    //获取当前目录其下子所有子目录，不包括
    if (!getters.currDir) return [];
    const convertToFileInfo = (
      item: MaterialManageInfo,
    ): MaterialManageInfo => ({
      ...item,
      [INFO_KEYS.TYPE]: item[INFO_KEYS.TYPE]
        ? Number(item[INFO_KEYS.TYPE])
        : undefined,
      [INFO_KEYS.CHILDREN]: item[INFO_KEYS.CHILDREN]?.map(convertToFileInfo),
    });
    const fileInfoArray = (getters.currDir[INFO_KEYS.CHILDREN] || []).map(
      convertToFileInfo,
    );
    return getNodesByBfs(fileInfoArray, INFO_KEYS.CHILDREN);
  },

  nowFmInfoList(state: MetaState) {
    return isSearch(state.status) ? state.searchedFmInfoList : state.fmInfoList;
  },
  sortedFmInfoList(state: MetaState) {
    const nowFmInfoList = isSearch(state.status)
      ? state.searchedFmInfoList
      : state.fmInfoList;
    return sortFmList(nowFmInfoList, state.sortBy, state.sortMode);
  },
};

const mutations = {
  setSelectType(state: MetaState, selectType: number) {
    state.selectType = selectType;
  },
  setSearchValue(state: MetaState, searchValue: string) {
    state.searchValue = searchValue;
  },
  setHistoryList(state: MetaState, historyList: string[]) {
    state.historyList = [...new Set(historyList)];
    localStorage.setItem(
      FM_HISTORY_LIST_KEY,
      JSON.stringify(state.historyList),
    );
  },
  pushHistoryList(state: MetaState, val: string) {
    state.searchValue = val;
    if (val === '') return;
    let historyList = state.historyList;
    historyList.unshift(val);
    historyList.length > 100 && historyList.slice(0, 100); // 最多保留100条搜索记录避免内存占用过多
    state.historyList = [...new Set(historyList)];
    localStorage.setItem(
      FM_HISTORY_LIST_KEY,
      JSON.stringify(state.historyList),
    );
  },
  setStatus(state: MetaState, status: number) {
    state.status = status;
  },
  setFolderAddInfo(state: MetaState, folderAddInfo: FolderItem) {
    state.folderAddInfo = folderAddInfo;
  },
  setSearchedFmInfoList(
    state: MetaState,
    searchedFmInfoList: MaterialManageInfo[],
  ) {
    state.searchedFmInfoList = searchedFmInfoList;
  },
  setMetaLoaded(state: MetaState, metaLoaded: boolean) {
    state.metaLoaded = metaLoaded;
  },
  setTreeData(state: MetaState, treeData: MaterialManageInfo[]) {
    state.treeData = treeData;
  },
  setPage(state: MetaState, page: number) {
    state.page = page;
  },
  setPageSize(state: MetaState, pageSize: number) {
    state.pageSize = pageSize;
  },
  setFilesTotal(state: MetaState, filesTotal: number) {
    state.filesTotal = filesTotal;
  },
  setFmInfoList(state: MetaState, fmInfoList: MaterialManageInfo[]) {
    state.fmInfoList = fmInfoList;
  },
  setMoveOne(state: MetaState, moveOne: boolean) {
    state.moveOne = moveOne;
  },
  setMoveOneData(state: MetaState, moveOneData: MaterialManageInfo | null) {
    state.moveOneData = moveOneData;
  },

  setMoveModal(state: MetaState, payload: boolean) {
    state.showMoveModal = payload;
  },
  async handleUpload(state: MetaState) {
    commonUploadFiles({
      acceptList: [...IMG_UPLOAD_LIST, ...VIDEO_UPLOAD_LIST],
      currentFolder: state.id,
      onUploadStart: (files: File[]) => {
        const fileListTmp: MaterialManageInfo[] = [];
        files.forEach(file => {
          // 遍历文件列表 转换数据 并添加到列表中
          const tmpFile = createTempMaterialFileData(file, state.id);
          fileListTmp.unshift(tmpFile);
        });
        store.commit('setFmInfoList', fileListTmp.concat(state.fmInfoList));
        state.fileUploading = true;
      },
      onProgress: (file, percent) => {
        updateFileProgress(state.fmInfoList, file, percent);
      },
      onSuccess: (uploadFileData: uploadFileData, file: File) => {
        updateFileUploadComplete(state.fmInfoList, file, uploadFileData);
      },
      onAllComplete: () => {
        store.dispatch('materialUpload/updateSpaceUsage');
        store.commit('setSelectType', FileType.ALL);
        updateView();
        state.fileUploading = false;
      },
      onError: (file: File) => {
        removeFileFromList(state.fmInfoList, file);
        state.fileUploading = false;
      },
    });
  },

  setCategory(state: MetaState, payload: string) {
    state.category = payload;
    store.commit('changeAllFilesSelect', false);
  },

  setId(state: MetaState, payload: number) {
    state.id = payload;
    store.commit('changeAllFilesSelect', false);
  },

  setSort(
    state: MetaState,
    { sortBy, sortMode }: { sortBy: string; sortMode: string },
  ) {
    state.sortBy = sortBy;
    state.sortMode = sortMode;
    store.commit('changeAllFilesSelect', false);
  },

  addImg() {},

  restore() {},

  changeDir(state: MetaState, dirId: string | number) {
    state.id = Number(dirId);
    state.page = 1;
  },
  changeAllFilesSelect(state: MetaState, checked: boolean) {
    const nowFmInfoList = isSearch(state.status)
      ? state.searchedFmInfoList
      : state.fmInfoList;
    const sortedFmInfoList = sortFmList(
      nowFmInfoList,
      state.sortBy,
      state.sortMode,
    );
    sortedFmInfoList.forEach(item => {
      Vue.set(item, INFO_KEYS.SELECT, !!checked);
    });
  },
  changeSelectById(state: MetaState, info: MaterialManageInfo) {
    const nowFmInfoList = isSearch(state.status)
      ? state.searchedFmInfoList
      : state.fmInfoList;
    const sortedFmInfoList = sortFmList(
      nowFmInfoList,
      state.sortBy,
      state.sortMode,
    );
    sortedFmInfoList.forEach(item => {
      item[INFO_KEYS.ID] === info[INFO_KEYS.ID] &&
        Vue.set(item, INFO_KEYS.SELECT, !info[INFO_KEYS.SELECT]);
    });
  },
  changeSelectByIndex(state: MetaState, index: number) {
    const nowFmInfoList = isSearch(state.status)
      ? state.searchedFmInfoList
      : state.fmInfoList;
    const sortedFmInfoList = sortFmList(
      nowFmInfoList,
      state.sortBy,
      state.sortMode,
    );
    Vue.set(
      sortedFmInfoList[index],
      INFO_KEYS.SELECT,
      !sortedFmInfoList[index][INFO_KEYS.SELECT],
    );
  },
};

const actions = {
  /** 更新文件夹列表 */
  async updateFolderList({
    commit,
  }: {
    commit: (mutation: string, payload: MaterialManageInfo[]) => void;
  }) {
    const [err, res] = await getFolderList();
    if (err) {
      FaMessage.error(err.message || '文件夹加载失败');
      return;
    }
    commit('setTreeData', res?.data as unknown as MaterialManageInfo[]);
  },
  /** 更新文件夹内容列表 */
  async updateFolderContent({
    state,
    commit,
  }: {
    state: MetaState;
    commit: (mutation: string, payload: unknown) => void;
  }) {
    commit('setMetaLoaded', false);
    const [err, res] = await getFolderContent({
      parentId: state.id,
      limit: state.pageSize,
      page: state.page,
      type: state.selectType,
      typeFilter: !!state.selectType,
    });
    if (err) {
      commit('setMetaLoaded', true);
      FaMessage.error(err.message || '文件加载失败');
      return;
    }
    commit('setSearchValue', '');
    commit('setStatus', STATUS_DEF.INIT);
    commit('setFilesTotal', res?.total || 0);
    commit('setFmInfoList', res?.data || []);
    setTimeout(() => {
      commit('setMetaLoaded', true);
    }, 300);
  },
  /** 更新文件夹搜索内容列表 */
  async loadDirSearchedFmList({
    state,
    commit,
  }: {
    state: MetaState;
    commit: (mutation: string, payload: unknown) => void;
  }) {
    commit('setMetaLoaded', false);
    const [err, res] = await getFolderContent({
      parentId: state.id,
      limit: state.pageSize,
      page: state.page,
      type: state.selectType,
      name: state.searchValue,
      typeFilter: !!state.selectType,
    });
    if (err) {
      commit('setMetaLoaded', true);
      FaMessage.error(err.message || '文件加载失败');
      return;
    }
    commit('setFilesTotal', res?.total || 0);
    commit('setSearchedFmInfoList', res?.data || []);
    setTimeout(() => {
      commit('setMetaLoaded', true);
    }, 300);
  },
  /** 切换目录 */
  async changeDir({ state }: { state: MetaState }, dirId: string | number) {
    state.id = Number(dirId);
    state.page = 1;
    state.status = STATUS_DEF.INIT;
    state.selectType = FileType.ALL;
  },
  /** 单个删除文件 */
  async deleteFile(
    _: { state: MetaState; getters: Getters },
    info: MaterialManageInfo,
  ) {
    if (isFolder(info)) {
      const id = Number(info[INFO_KEYS.ID]);
      showConfirmModal({
        content: '删除文件夹，将同时删除该文件夹下的所有文件，确定删除？',
        okType: 'danger',
        onOk: async () => {
          const [err] = await deleteFolder({ id });
          if (err) {
            FaMessage.error(err.message || '删除失败');
            return;
          }
          FaMessage.success('删除成功');
          updateView();
          store.dispatch('materialUpload/updateSpaceUsage');
        },
      });
    } else {
      const resId = info[INFO_KEYS.RES_ID];
      const name = info[INFO_KEYS.NAME];
      showConfirmModal({
        content: `是否删除文件${name}？`,
        okType: 'danger',
        onOk: async () => {
          const [err] = await deleteResource({
            resIds: [resId as string],
          });
          if (err) {
            FaMessage.error(err.message || '删除失败');
            return;
          }
          FaMessage.success('删除成功');
          updateView();
          store.dispatch('materialUpload/updateSpaceUsage');
        },
      });
    }
  },
  /** 批量删除文件 */
  async multipleDelete() {
    if (store.getters.nowSelectedFmInfoList.length === 0) {
      return FaMessage.warning('请选择文件');
    }
    showConfirmModal({
      content: '是否删除选中文件？',
      okType: 'danger',
      onOk: async () => {
        const nowSelectedResIdList = store.getters.nowSelectedResIdList;
        const nowSelectedFolderIdList = store.getters.nowSelectedFolderIdList;
        let isErr = false;
        for (const id of nowSelectedFolderIdList) {
          const [err] = await deleteFolder({ id });
          isErr = Boolean(err);
        }
        if (nowSelectedResIdList.length > 0) {
          const [err] = await deleteResource({
            resIds: nowSelectedResIdList,
          });
          isErr = Boolean(err);
        }
        if (isErr) {
          FaMessage.error('部分文件删除失败');
        } else {
          FaMessage.success('批量删除成功');
        }
        updateView();
        store.dispatch('materialUpload/updateSpaceUsage');
      },
    });
  },
  async moveIn({ state }: { state: MetaState }, targetDirId: number) {
    // 移动单个资源
    if (state.moveOne) {
      let res = null;
      const moveOneData = state.moveOneData as MaterialManageInfo;
      if (isFolder(moveOneData)) {
        // 移动单个文件夹
        res = await moveFolderToDir(targetDirId, [moveOneData?.id]);
      } else {
        // 移动单个文件
        const resId = moveOneData?.[INFO_KEYS.RES_ID] as string;
        res = await moveFilesToDir(targetDirId, [resId]);
      }
      store.commit('setMoveOne', false);
      store.commit('setMoveOneData', null);
      res?.success && store.commit('setMoveModal', false);
    } else {
      // 存在同时移动文件和文件夹的情况
      const nowSelectedFolderIdList = store.getters.nowSelectedFolderIdList;
      const nowSelectedResIdList = store.getters.nowSelectedResIdList;
      // 移动多个资源
      if (nowSelectedResIdList.length > 0) {
        await moveFilesToDir(targetDirId, nowSelectedResIdList);
      }
      // 移动多个文件夹
      if (nowSelectedFolderIdList.length > 0) {
        moveFolderToDir(targetDirId, nowSelectedFolderIdList);
      }
    }
  },
};

const meta: Module<MetaState, RootState> = {
  state,
  getters,
  mutations,
  actions,
};
export default meta;
