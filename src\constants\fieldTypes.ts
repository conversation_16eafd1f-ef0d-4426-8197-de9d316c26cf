/**
 * 字段类型常量
 * 定义表单字段类型与组件类型的映射关系
 */

// 字段类型枚举
export enum FIELD_TYPES {
  TEXT = 0, // 文本类型
  TAGS = 1, // 标签类型
  SELECT = 2, // 选择类型
}

// 组件类型枚举
export enum COMPONENT_TYPES {
  INPUT = 'input', // 输入框
  SELECT_TAGS = 'selectTags', // 标签选择
  SELECT = 'select', // 下拉选择
}

// 字段类型类型定义
export type FieldType = FIELD_TYPES | number;

// 组件类型类型定义
export type ComponentType = COMPONENT_TYPES | string;

/**
 * 将字段类型映射为组件类型
 * @description 将接口返回的字段类型映射为前端组件类型
 * @param {FieldType} fieldType 字段类型：0-文本，1-标签, 2-选择
 * @returns {ComponentType} 组件类型
 */
export function mapFieldTypeToComponentType(
  fieldType: FieldType,
): ComponentType {
  switch (fieldType) {
    case FIELD_TYPES.TEXT:
      return COMPONENT_TYPES.INPUT;
    case FIELD_TYPES.TAGS:
      return COMPONENT_TYPES.SELECT_TAGS;
    case FIELD_TYPES.SELECT:
      return COMPONENT_TYPES.SELECT;
    default:
      return COMPONENT_TYPES.INPUT;
  }
}

/**
 * 将组件类型映射为字段类型
 * @description 将前端组件类型映射为接口所需的字段类型
 * @param {ComponentType} componentType 组件类型
 * @returns {FieldType} 字段类型：0-文本，1-标签, 2-选择
 */
export function mapComponentTypeToFieldType(
  componentType: ComponentType,
): FieldType {
  switch (componentType) {
    case COMPONENT_TYPES.INPUT:
      return FIELD_TYPES.TEXT;
    case COMPONENT_TYPES.SELECT_TAGS:
      return FIELD_TYPES.TAGS;
    case COMPONENT_TYPES.SELECT:
      return FIELD_TYPES.SELECT;
    default:
      return FIELD_TYPES.TEXT;
  }
}
