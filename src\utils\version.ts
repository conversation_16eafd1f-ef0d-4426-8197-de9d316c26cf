import { message } from '@fk/faicomponent';
import { getExternalDynamicUrl } from '@/constants/system';
import { VERSION, IS_MAX_VERSION } from '@/constants/version';
import { CreateElement } from 'vue';

// Vite 会在构建时创建一个从原始路径到最终打包后URL的映射
// 例如: { '/src/assets/images/logo.png': '/assets/logo.a1b2c3d4.svg' }
const assetModules = import.meta.glob('@/assets/common/version/**/*.svg', {
  eager: true,
  as: 'url',
});

/**
 * 获取版本图标URL
 * @param path 路径 例如：/src/assets/common/version/small/free.svg
 * @returns 版本图标URL
 */
export const getVersionIconUrl = (path: string): string => {
  // 从映射中查找对应的URL
  const url = assetModules[path];

  if (!url) {
    // 如果找不到资源，在开发模式下给出清晰的警告
    if (import.meta.env.DEV) {
      console.warn(
        `[getAssetUrl] 资源未找到: ${path}。请确保路径正确，且该资源存在于 /src/assets/ 目录下。`,
      );
      console.warn('[getAssetUrl] 可用的资源路径:', Object.keys(assetModules));
    }
    return ''; // 或者返回一个默认的占位图URL
  }

  return url;
};

/**
 * 版本功能提示（判断是否需要显示【去升级】按钮）
 * @param msg 提示文案
 * @param version 当前版本
 */
export const showVersionMsg = (msg: string, version: VERSION) => {
  // 创建消息内容的 VNode
  const content = (h: CreateElement) => {
    if (!IS_MAX_VERSION(version)) {
      // 如果不是最高版本 ，要显示去升级按钮
      return h('span', [
        msg,
        ' ', // 添加空格
        h(
          'a',
          {
            domProps: {
              href: getExternalDynamicUrl().VERSION_BUY,
              target: '_blank',
            },
            class: 'text-primary',
          },
          '去升级',
        ),
      ]);
    }
    return h('span', msg);
  };

  message.warning({
    content: (h: CreateElement) => {
      return content(h);
    },
  });
};
