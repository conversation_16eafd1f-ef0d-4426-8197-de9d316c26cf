/**
 * 上传队列工具
 * - 提供可配置的并发上传能力（默认3）
 * - 维护上传状态：pending | uploading | success | error
 * - 暴露 runUploadQueue 以调度实际上传过程
 */

import { uploadFileData } from '@/components/MaterialBasicUpload/utils/uploadTempStorage';

/**
 * 上传错误对象
 */
export interface ErrorObject {
  code: number;
  message: string;
  isRequestError: boolean;
}

/**
 * 单个文件上传结果
 */
export interface UploadResult {
  file: uploadFileData;
  success: boolean;
  error?: ErrorObject;
}

/**
 * 全局上传队列状态
 */
export const uploadQueue = {
  // key 为文件唯一键，值为上传状态
  uploadStatus: {} as Record<
    string,
    'pending' | 'uploading' | 'success' | 'error'
  >,
  // 等待上传的文件列表
  queuePending: [] as File[],
  // 正在上传的文件列表
  queueUploading: [] as File[],
  // 最大并发数（默认3）
  allowQueueSize: 3,
};

/**
 * 设置并发上传数
 * - 小于1时自动设为1
 * - 过大可能影响网络开销，建议不超过10
 */
export function setUploadConcurrency(concurrency: number): void {
  const safeValue = Number.isFinite(concurrency)
    ? Math.max(1, Math.floor(concurrency))
    : 3;
  uploadQueue.allowQueueSize = safeValue;
}

/**
 * 生成文件唯一键（基于名称、大小、最后修改时间）
 */
export function getFileUniqueKey(file: File): string {
  return `${file.name}_${file.size}_${file.lastModified}`;
}

/**
 * 实际上传执行器接口
 */
type Uploader = (
  file: File,
  currentFolder: number,
  onProgress?: (file: File, percent: number) => void,
  onSuccess?: (result: uploadFileData, file: File) => void,
  onError?: (file: File, error: ErrorObject | unknown) => void,
  signal?: AbortSignal,
) => Promise<UploadResult>;

/**
 * 运行上传队列（调度器）
 * @param files 待上传文件列表
 * @param currentFolder 当前文件夹ID
 * @param uploader 实际上传执行器（例如七牛/自研SDK）
 * @param onProgress 进度回调
 * @param onSuccess 单个文件成功回调
 * @param onError 单个文件失败回调
 * @param onAllComplete 所有文件完成回调
 * @param signal 取消信号（可选）
 */
export async function runUploadQueue(
  files: File[],
  currentFolder: number,
  uploader: Uploader,
  onProgress: ((file: File, percent: number) => void) | undefined,
  onSuccess: ((result: uploadFileData, file: File) => void) | undefined,
  onError: ((file: File, error: ErrorObject | unknown) => void) | undefined,
  onAllComplete: ((results: Array<UploadResult>) => void) | undefined,
  signal: AbortSignal | undefined,
): Promise<void> {
  const uploadResults: Array<UploadResult> = [];

  // 空列表快速返回
  if (!files || files.length === 0) {
    onAllComplete?.(uploadResults);
    return;
  }

  uploadQueue.queuePending = [...files];
  uploadQueue.queueUploading = [];
  uploadQueue.uploadStatus = {};
  uploadQueue.queuePending.forEach(f => {
    uploadQueue.uploadStatus[getFileUniqueKey(f)] = 'pending';
  });

  return new Promise<void>(resolve => {
    // 移除上传中列表中的指定文件
    const removeFromUploading = (file: File) => {
      uploadQueue.queueUploading = uploadQueue.queueUploading.filter(
        f => f !== file,
      );
    };

    const tryStartNext = () => {
      while (
        uploadQueue.queueUploading.length < uploadQueue.allowQueueSize &&
        uploadQueue.queuePending.length > 0
      ) {
        const nextFile = uploadQueue.queuePending.shift() as File;
        const key = getFileUniqueKey(nextFile);
        uploadQueue.uploadStatus[key] = 'uploading';
        uploadQueue.queueUploading.push(nextFile);

        const wrappedOnProgress = (file: File, percent: number) => {
          onProgress?.(file, percent);
        };

        const wrappedOnSuccess = (result: uploadFileData, file: File) => {
          uploadQueue.uploadStatus[getFileUniqueKey(file)] = 'success';
          removeFromUploading(file);
          uploadResults.push({ file: result, success: true });
          onSuccess?.(result, file);
          tryStartNext();
          checkAllComplete();
        };

        const wrappedOnError = (file: File, error: ErrorObject | unknown) => {
          uploadQueue.uploadStatus[getFileUniqueKey(file)] = 'error';
          removeFromUploading(file);
          uploadResults.push({
            file: {} as uploadFileData,
            success: false,
            error: error as ErrorObject,
          });
          onError?.(file, error);
          tryStartNext();
          checkAllComplete();
        };

        void uploader(
          nextFile,
          currentFolder,
          wrappedOnProgress,
          wrappedOnSuccess,
          wrappedOnError,
          signal,
        );
      }
    };

    const checkAllComplete = () => {
      if (
        uploadQueue.queuePending.length === 0 &&
        uploadQueue.queueUploading.length === 0
      ) {
        if (uploadResults.length) {
          onAllComplete?.(uploadResults);
        }
        resolve();
      }
    };

    tryStartNext();
  });
}
