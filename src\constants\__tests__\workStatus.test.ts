/// <reference types="vitest/globals" />
import {
  WORK_STATUS,
  getWorkStatusInfo,
  isRegeneratingWork,
  isRecompletedWork,
  isRetryFailedWork,
  isAnyFailedWork,
  isAnyCompletedWork,
  isPollingWork,
  isGeneratingWork,
  isNormalCompletedWork,
  isNormalFailedWork,
  getWorkStatusDisplayName,
} from '../workStatus';

describe('统一状态处理系统', () => {
  describe('getWorkStatusInfo', () => {
    it('应该正确创建状态信息对象', () => {
      const workItem = { status: WORK_STATUS.GENERATING, editAgain: true };
      const statusInfo = getWorkStatusInfo(workItem);

      expect(statusInfo).toEqual({
        status: WORK_STATUS.GENERATING,
        editAgain: true,
      });
    });

    it('应该正确处理缺少 editAgain 字段的情况', () => {
      const workItem = { status: WORK_STATUS.GENERATING };
      const statusInfo = getWorkStatusInfo(workItem);

      expect(statusInfo).toEqual({
        status: WORK_STATUS.GENERATING,
        editAgain: undefined,
      });
    });
  });

  describe('基础状态判断函数', () => {
    it('isRegeneratingWork 应该正确判断重新生成中状态', () => {
      const statusInfo1 = getWorkStatusInfo({
        status: WORK_STATUS.GENERATING,
        editAgain: true,
      });
      expect(isRegeneratingWork(statusInfo1)).toBe(true);

      const statusInfo2 = getWorkStatusInfo({
        status: WORK_STATUS.GENERATING,
        editAgain: false,
      });
      expect(isRegeneratingWork(statusInfo2)).toBe(false);

      const statusInfo3 = getWorkStatusInfo({
        status: WORK_STATUS.COMPLETED,
        editAgain: true,
      });
      expect(isRegeneratingWork(statusInfo3)).toBe(false);
    });

    it('isRecompletedWork 应该正确判断重新生成完成状态', () => {
      const statusInfo1 = getWorkStatusInfo({
        status: WORK_STATUS.COMPLETED,
        editAgain: true,
      });
      expect(isRecompletedWork(statusInfo1)).toBe(true);

      const statusInfo2 = getWorkStatusInfo({
        status: WORK_STATUS.COMPLETED,
        editAgain: false,
      });
      expect(isRecompletedWork(statusInfo2)).toBe(false);
    });

    it('isRetryFailedWork 应该正确判断重新生成失败状态', () => {
      const statusInfo1 = getWorkStatusInfo({
        status: WORK_STATUS.FAILED,
        editAgain: true,
      });
      expect(isRetryFailedWork(statusInfo1)).toBe(true);

      const statusInfo2 = getWorkStatusInfo({
        status: WORK_STATUS.FAILED,
        editAgain: false,
      });
      expect(isRetryFailedWork(statusInfo2)).toBe(false);
    });

    it('isAnyFailedWork 应该正确判断任何失败状态', () => {
      const statusInfo1 = getWorkStatusInfo({
        status: WORK_STATUS.FAILED,
        editAgain: true,
      });
      expect(isAnyFailedWork(statusInfo1)).toBe(true);

      const statusInfo2 = getWorkStatusInfo({
        status: WORK_STATUS.FAILED,
        editAgain: false,
      });
      expect(isAnyFailedWork(statusInfo2)).toBe(true);

      const statusInfo3 = getWorkStatusInfo({ status: WORK_STATUS.COMPLETED });
      expect(isAnyFailedWork(statusInfo3)).toBe(false);
    });

    it('isPollingWork 应该正确判断需要轮询的状态', () => {
      const statusInfo1 = getWorkStatusInfo({
        status: WORK_STATUS.GENERATING,
        editAgain: true,
      });
      expect(isPollingWork(statusInfo1)).toBe(true);

      const statusInfo2 = getWorkStatusInfo({
        status: WORK_STATUS.GENERATING,
        editAgain: false,
      });
      expect(isPollingWork(statusInfo2)).toBe(true);

      const statusInfo3 = getWorkStatusInfo({ status: WORK_STATUS.COMPLETED });
      expect(isPollingWork(statusInfo3)).toBe(false);
    });

    it('isAnyCompletedWork 应该正确判断已完成状态', () => {
      const statusInfo1 = getWorkStatusInfo({
        status: WORK_STATUS.COMPLETED,
        editAgain: true,
      });
      expect(isAnyCompletedWork(statusInfo1)).toBe(true);

      const statusInfo2 = getWorkStatusInfo({
        status: WORK_STATUS.COMPLETED,
        editAgain: false,
      });
      expect(isAnyCompletedWork(statusInfo2)).toBe(true);

      const statusInfo3 = getWorkStatusInfo({ status: WORK_STATUS.GENERATING });
      expect(isAnyCompletedWork(statusInfo3)).toBe(false);
    });
  });

  describe('状态名称获取', () => {
    it('getWorkStatusDisplayName 应该正确返回普通状态名称', () => {
      const statusInfo1 = getWorkStatusInfo({
        status: WORK_STATUS.GENERATING,
        editAgain: false,
      });
      expect(getWorkStatusDisplayName(statusInfo1)).toBe('生成中');

      const statusInfo2 = getWorkStatusInfo({
        status: WORK_STATUS.COMPLETED,
        editAgain: false,
      });
      expect(getWorkStatusDisplayName(statusInfo2)).toBe('已完成');

      const statusInfo3 = getWorkStatusInfo({
        status: WORK_STATUS.FAILED,
        editAgain: false,
      });
      expect(getWorkStatusDisplayName(statusInfo3)).toBe('生成失败');

      const statusInfo4 = getWorkStatusInfo({
        status: WORK_STATUS.HIDDEN,
      });
      expect(getWorkStatusDisplayName(statusInfo4)).toBe('隐藏');

      const statusInfo5 = getWorkStatusInfo({
        status: WORK_STATUS.EDIT,
      });
      expect(getWorkStatusDisplayName(statusInfo5)).toBe('编辑中');
    });

    it('getWorkStatusDisplayName 应该正确返回重新生成状态名称', () => {
      const statusInfo1 = getWorkStatusInfo({
        status: WORK_STATUS.GENERATING,
        editAgain: true,
      });
      expect(getWorkStatusDisplayName(statusInfo1)).toBe('重新生成中');

      const statusInfo2 = getWorkStatusInfo({
        status: WORK_STATUS.COMPLETED,
        editAgain: true,
      });
      expect(getWorkStatusDisplayName(statusInfo2)).toBe('重新生成完成');

      const statusInfo3 = getWorkStatusInfo({
        status: WORK_STATUS.FAILED,
        editAgain: true,
      });
      expect(getWorkStatusDisplayName(statusInfo3)).toBe('重新生成失败');
    });

    it('getWorkStatusDisplayName 应该正确处理未知状态', () => {
      const statusInfo = getWorkStatusInfo({ status: 999 });
      expect(getWorkStatusDisplayName(statusInfo)).toBe('未知状态');
    });
  });

  describe('细分状态判断函数', () => {
    it('isGeneratingWork 应该正确判断普通生成中状态', () => {
      const statusInfo1 = getWorkStatusInfo({
        status: WORK_STATUS.GENERATING,
        editAgain: false,
      });
      expect(isGeneratingWork(statusInfo1)).toBe(true);

      const statusInfo2 = getWorkStatusInfo({
        status: WORK_STATUS.GENERATING,
        editAgain: true,
      });
      expect(isGeneratingWork(statusInfo2)).toBe(false);

      const statusInfo3 = getWorkStatusInfo({
        status: WORK_STATUS.COMPLETED,
        editAgain: false,
      });
      expect(isGeneratingWork(statusInfo3)).toBe(false);
    });

    it('isNormalCompletedWork 应该正确判断普通完成状态', () => {
      const statusInfo1 = getWorkStatusInfo({
        status: WORK_STATUS.COMPLETED,
        editAgain: false,
      });
      expect(isNormalCompletedWork(statusInfo1)).toBe(true);

      const statusInfo2 = getWorkStatusInfo({
        status: WORK_STATUS.COMPLETED,
        editAgain: true,
      });
      expect(isNormalCompletedWork(statusInfo2)).toBe(false);

      const statusInfo3 = getWorkStatusInfo({
        status: WORK_STATUS.GENERATING,
        editAgain: false,
      });
      expect(isNormalCompletedWork(statusInfo3)).toBe(false);
    });

    it('isNormalFailedWork 应该正确判断普通失败状态', () => {
      const statusInfo1 = getWorkStatusInfo({
        status: WORK_STATUS.FAILED,
        editAgain: false,
      });
      expect(isNormalFailedWork(statusInfo1)).toBe(true);

      const statusInfo2 = getWorkStatusInfo({
        status: WORK_STATUS.FAILED,
        editAgain: true,
      });
      expect(isNormalFailedWork(statusInfo2)).toBe(false);

      const statusInfo3 = getWorkStatusInfo({
        status: WORK_STATUS.COMPLETED,
        editAgain: false,
      });
      expect(isNormalFailedWork(statusInfo3)).toBe(false);
    });
  });

  describe('边界情况测试', () => {
    it('应该正确处理 editAgain 为 undefined 的情况', () => {
      const statusInfo = getWorkStatusInfo({ status: WORK_STATUS.GENERATING });
      expect(isRegeneratingWork(statusInfo)).toBe(false);
      expect(isGeneratingWork(statusInfo)).toBe(true);
    });

    it('应该正确处理 editAgain 为 null 的情况', () => {
      const statusInfo = getWorkStatusInfo({
        status: WORK_STATUS.COMPLETED,
        editAgain: null as any,
      });
      expect(isRecompletedWork(statusInfo)).toBe(false);
      expect(isNormalCompletedWork(statusInfo)).toBe(true);
    });

    it('应该正确处理未知状态', () => {
      const statusInfo = getWorkStatusInfo({ status: 999 });
      expect(getWorkStatusDisplayName(statusInfo)).toBe('未知状态');
    });
  });

  describe('完整场景测试', () => {
    it('应该正确处理普通生成到完成的场景', () => {
      const workItem: { status: number; editAgain?: boolean } = {
        status: WORK_STATUS.GENERATING,
        editAgain: false,
      };

      // 测试普通生成流程
      let statusInfo = getWorkStatusInfo(workItem);
      expect(isGeneratingWork(statusInfo)).toBe(true);
      expect(isPollingWork(statusInfo)).toBe(true);
      expect(getWorkStatusDisplayName(statusInfo)).toBe('生成中');

      // 生成完成
      workItem.status = WORK_STATUS.COMPLETED;
      statusInfo = getWorkStatusInfo(workItem);
      expect(isNormalCompletedWork(statusInfo)).toBe(true);
      expect(isAnyCompletedWork(statusInfo)).toBe(true);
      expect(getWorkStatusDisplayName(statusInfo)).toBe('已完成');
    });

    it('应该正确处理重新生成的完整场景', () => {
      const workItem: { status: number; editAgain?: boolean } = {
        status: WORK_STATUS.GENERATING,
        editAgain: true,
      };

      // 重新生成中
      let statusInfo = getWorkStatusInfo(workItem);
      expect(isRegeneratingWork(statusInfo)).toBe(true);
      expect(isGeneratingWork(statusInfo)).toBe(false);
      expect(isPollingWork(statusInfo)).toBe(true);
      expect(getWorkStatusDisplayName(statusInfo)).toBe('重新生成中');

      // 重新生成完成
      workItem.status = WORK_STATUS.COMPLETED;
      statusInfo = getWorkStatusInfo(workItem);
      expect(isRecompletedWork(statusInfo)).toBe(true);
      expect(isNormalCompletedWork(statusInfo)).toBe(false);
      expect(isAnyCompletedWork(statusInfo)).toBe(true);
      expect(getWorkStatusDisplayName(statusInfo)).toBe('重新生成完成');

      // 重新生成失败
      workItem.status = WORK_STATUS.FAILED;
      statusInfo = getWorkStatusInfo(workItem);
      expect(isRetryFailedWork(statusInfo)).toBe(true);
      expect(isNormalFailedWork(statusInfo)).toBe(false);
      expect(isAnyFailedWork(statusInfo)).toBe(true);
      expect(getWorkStatusDisplayName(statusInfo)).toBe('重新生成失败');
    });
  });
});
