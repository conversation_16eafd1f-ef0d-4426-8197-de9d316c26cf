# 通用骨架屏系统

## 概述

通用骨架屏系统为除首页和项目页面外的其他页面提供统一的加载骨架屏效果，提升用户体验。

## 功能特性

### 支持的页面

通用骨架屏适用于以下页面：

- `/meta` - 素材库页面
- `/acct-info` - 企业信息页面
- `/project` - 我的项目页面
- `/work` - 我的作品页面

### 骨架屏类型

系统支持以下几种骨架屏类型：

1. **首页骨架屏** (`data-skeleton=""` 或无属性)

   - 路由：`/`
   - 包含完整的 header、sidebar 和复杂的 content 区域

2. **通用骨架屏** (`data-skeleton="common"`)

   - 路由：上述支持页面列表
   - 简化的 content 区域，适用于大多数普通页面

3. **隐藏骨架屏** (`data-skeleton="hide"`)
   - 其他未定义的路由
   - 隐藏所有骨架屏

## 技术实现

### 1. CSS 样式定义

在 `index.html` 中定义了通用骨架屏的样式：

```css
/* 通用骨架屏样式 */
#common-skeleton-app {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  min-width: 1366px;
  height: 100vh;
  z-index: 300;
  background-color: #fff;
  display: none;
  flex-direction: column;
}

/* 显示通用骨架屏 */
html[data-skeleton='common'] #common-skeleton-app {
  display: flex;
}
```

### 2. HTML 结构

通用骨架屏采用与首页骨架屏相同的基础结构：

```html
<div id="common-skeleton-app">
  <div class="ske-header">
    <!-- 页头骨架 -->
  </div>
  <div class="ske-main">
    <div class="ske-sidebar">
      <!-- 侧边栏骨架 -->
    </div>
    <div class="ske-content">
      <div class="ske-loading-bar"></div>
      <!-- 简化的内容区域 -->
    </div>
  </div>
</div>
```

### 3. 路由判断逻辑

#### index.html 中的逻辑

```javascript
var COMMON_SKELETON_ROUTES = ['/meta', '/acct-info', '/project', '/work'];

function updateSkeletonState() {
  // ... 路由解析逻辑
  if (COMMON_SKELETON_ROUTES.indexOf(path) !== -1) {
    document.documentElement.setAttribute('data-skeleton', 'common');
  }
  // ...
}
```

#### skeletonManager.ts 中的逻辑

```typescript
const COMMON_SKELETON_ROUTES = new Set([
  '/meta', '/acct-info', '/project', '/work'
]);

showSkeletonByRoute(route: string) {
  // ... 路由解析逻辑
  if (COMMON_SKELETON_ROUTES.has(path)) {
    this.setSkeleton(SKELETON_STATES.COMMON);
  }
  // ...
}
```

## 使用方法

### 自动触发

骨架屏会根据路由自动显示和隐藏，无需手动干预。

### 手动控制

如需手动控制骨架屏状态：

```javascript
// 显示通用骨架屏
document.documentElement.setAttribute('data-skeleton', 'common');

// 隐藏骨架屏
document.documentElement.setAttribute('data-skeleton', 'hide');

// 显示首页骨架屏
document.documentElement.removeAttribute('data-skeleton');
```

### 通过骨架屏管理器

```typescript
import { skeletonManager } from '@/utils/skeletonManager';

// 根据路由显示骨架屏
skeletonManager.showSkeletonByRoute('/meta');

// 手动隐藏骨架屏
skeletonManager.hideSkeleton();
```

## 测试验证

### 手动测试

1. 访问支持通用骨架屏的页面（如 `/#/meta`）
2. 观察页面加载时是否显示通用骨架屏
3. 检查骨架屏的样式和动画效果
4. 验证路由切换时骨架屏的正确显示/隐藏

### 调试工具

可以使用以下调试函数（在浏览器控制台中）：

```javascript
// 检查当前骨架屏状态
checkSkeletonStatus();

// 测试指定路由
testRoute('/meta');

// 手动设置骨架屏状态
setSkeletonState('common');
```

## 注意事项

1. **z-index 层级**：骨架屏使用 `z-index: 300`，确保在页面内容之上
2. **响应式设计**：最小宽度设置为 1366px，适配桌面端应用
3. **动画效果**：复用现有的 `.ske-loading-bar` 动画效果
4. **性能考虑**：骨架屏在 DOM 中始终存在，通过 CSS 控制显示/隐藏

## 维护指南

### 添加新页面支持

如需为新页面添加通用骨架屏支持：

1. 在 `COMMON_SKELETON_ROUTES` 数组中添加新路由
2. 同时更新 `index.html` 和 `skeletonManager.ts` 中的路由配置
3. 测试新路由的骨架屏显示效果

### 自定义样式

如需自定义通用骨架屏样式：

1. 修改 `index.html` 中的 `#common-skeleton-app` 相关样式
2. 保持与现有骨架屏的一致性
3. 确保响应式设计和动画效果正常

## 更新日志

### v1.1.0 (2025-01-21)

- 🔧 调整支持页面范围，精简为 4 个核心页面
- 📝 更新路由配置：`/meta`, `/acct-info`, `/project`, `/work`
- 🗑️ 移除对 `/prototype`, `/adm-set`, `/no-permission` 的支持

### v1.0.0 (2025-01-21)

- ✨ 新增通用骨架屏功能
- 🎨 采用与首页骨架屏相同的基础结构
- 🔧 简化 content 区域为单一 div
- 📝 支持 4 个主要页面的骨架屏显示
- 🚀 集成到现有骨架屏管理系统
