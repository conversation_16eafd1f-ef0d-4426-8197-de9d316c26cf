/**
 * @fileoverview WorkListBar 临时更新 totalItems 功能测试
 * @description 测试作品删除成功后临时更新 totalItems 的核心逻辑
 */

import { describe, it, expect, beforeEach, afterEach } from 'vitest';
import { ref, type Ref } from 'vue';
import {
  eventBus,
  EVENT_NAMES,
  type TempUpdateTotalItemsData,
} from '@/utils/eventBus';

describe('WorkListBar 临时更新 totalItems 功能', () => {
  // 模拟 totalItems 响应式变量
  let totalItems: Ref<number>;

  // 模拟事件处理函数
  let handleTempUpdateTotalItems: (...args: unknown[]) => void;

  beforeEach(() => {
    // 重置状态
    totalItems = ref(10); // 初始值为 10

    // 模拟 WorkListBar 组件中的事件处理函数
    handleTempUpdateTotalItems = (...args: unknown[]) => {
      const data = args[0] as TempUpdateTotalItemsData;

      if (!data || typeof data !== 'object') return;

      if (data.operation === 'decrease') {
        totalItems.value = Math.max(0, totalItems.value - data.amount);
      } else if (data.operation === 'increase') {
        totalItems.value = totalItems.value + data.amount;
      }
    };
  });

  afterEach(() => {
    // 清理所有事件监听器
    eventBus.off(EVENT_NAMES.TEMP_UPDATE_TOTAL_ITEMS);
  });

  describe('减少操作测试', () => {
    it('应该正确减少 totalItems 的值', () => {
      // 初始值
      expect(totalItems.value).toBe(10);

      // 模拟删除一个作品
      const updateData: TempUpdateTotalItemsData = {
        operation: 'decrease',
        amount: 1,
      };

      handleTempUpdateTotalItems(updateData);

      // 验证结果
      expect(totalItems.value).toBe(9);
    });

    it('应该正确减少多个作品的数量', () => {
      // 初始值
      expect(totalItems.value).toBe(10);

      // 模拟删除 3 个作品
      const updateData: TempUpdateTotalItemsData = {
        operation: 'decrease',
        amount: 3,
      };

      handleTempUpdateTotalItems(updateData);

      // 验证结果
      expect(totalItems.value).toBe(7);
    });

    it('应该防止 totalItems 变为负数', () => {
      // 设置较小的初始值
      totalItems.value = 2;

      // 尝试减少超过当前值的数量
      const updateData: TempUpdateTotalItemsData = {
        operation: 'decrease',
        amount: 5,
      };

      handleTempUpdateTotalItems(updateData);

      // 验证结果不会小于 0
      expect(totalItems.value).toBe(0);
    });
  });

  describe('增加操作测试', () => {
    it('应该正确增加 totalItems 的值', () => {
      // 初始值
      expect(totalItems.value).toBe(10);

      // 模拟增加一个作品
      const updateData: TempUpdateTotalItemsData = {
        operation: 'increase',
        amount: 1,
      };

      handleTempUpdateTotalItems(updateData);

      // 验证结果
      expect(totalItems.value).toBe(11);
    });

    it('应该正确增加多个作品的数量', () => {
      // 初始值
      expect(totalItems.value).toBe(10);

      // 模拟增加 5 个作品
      const updateData: TempUpdateTotalItemsData = {
        operation: 'increase',
        amount: 5,
      };

      handleTempUpdateTotalItems(updateData);

      // 验证结果
      expect(totalItems.value).toBe(15);
    });
  });

  describe('边界情况测试', () => {
    it('应该忽略无效的数据', () => {
      const initialValue = totalItems.value;

      // 传入 null
      handleTempUpdateTotalItems(null);
      expect(totalItems.value).toBe(initialValue);

      // 传入 undefined
      handleTempUpdateTotalItems(undefined);
      expect(totalItems.value).toBe(initialValue);

      // 传入非对象类型
      handleTempUpdateTotalItems('invalid');
      expect(totalItems.value).toBe(initialValue);
    });

    it('应该忽略无效的操作类型', () => {
      const initialValue = totalItems.value;

      // 传入无效的操作类型
      const invalidData = {
        operation: 'invalid' as any,
        amount: 1,
      };

      handleTempUpdateTotalItems(invalidData);
      expect(totalItems.value).toBe(initialValue);
    });

    it('应该处理 amount 为 0 的情况', () => {
      const initialValue = totalItems.value;

      // 减少 0 个
      handleTempUpdateTotalItems({
        operation: 'decrease',
        amount: 0,
      });
      expect(totalItems.value).toBe(initialValue);

      // 增加 0 个
      handleTempUpdateTotalItems({
        operation: 'increase',
        amount: 0,
      });
      expect(totalItems.value).toBe(initialValue);
    });
  });

  describe('EventBus 集成测试', () => {
    it('应该通过 EventBus 正确处理事件', () => {
      // 注册事件监听器
      eventBus.on(
        EVENT_NAMES.TEMP_UPDATE_TOTAL_ITEMS,
        handleTempUpdateTotalItems,
      );

      // 初始值
      expect(totalItems.value).toBe(10);

      // 通过 EventBus 发送事件
      eventBus.emit(EVENT_NAMES.TEMP_UPDATE_TOTAL_ITEMS, {
        operation: 'decrease',
        amount: 2,
      });

      // 验证结果
      expect(totalItems.value).toBe(8);
    });

    it('应该支持多次事件触发', () => {
      // 注册事件监听器
      eventBus.on(
        EVENT_NAMES.TEMP_UPDATE_TOTAL_ITEMS,
        handleTempUpdateTotalItems,
      );

      // 初始值
      expect(totalItems.value).toBe(10);

      // 第一次减少
      eventBus.emit(EVENT_NAMES.TEMP_UPDATE_TOTAL_ITEMS, {
        operation: 'decrease',
        amount: 1,
      });
      expect(totalItems.value).toBe(9);

      // 第二次减少
      eventBus.emit(EVENT_NAMES.TEMP_UPDATE_TOTAL_ITEMS, {
        operation: 'decrease',
        amount: 2,
      });
      expect(totalItems.value).toBe(7);

      // 增加
      eventBus.emit(EVENT_NAMES.TEMP_UPDATE_TOTAL_ITEMS, {
        operation: 'increase',
        amount: 1,
      });
      expect(totalItems.value).toBe(8);
    });
  });

  describe('实际使用场景模拟', () => {
    it('应该模拟普通删除作品的场景', () => {
      // 注册事件监听器
      eventBus.on(
        EVENT_NAMES.TEMP_UPDATE_TOTAL_ITEMS,
        handleTempUpdateTotalItems,
      );

      // 模拟有 5 个作品
      totalItems.value = 5;

      // 模拟 WorkListBarItem 发送删除成功事件
      eventBus.emit(EVENT_NAMES.TEMP_UPDATE_TOTAL_ITEMS, {
        operation: 'decrease',
        amount: 1,
      });

      // 验证 UI 立即更新
      expect(totalItems.value).toBe(4);
    });
  });
});
