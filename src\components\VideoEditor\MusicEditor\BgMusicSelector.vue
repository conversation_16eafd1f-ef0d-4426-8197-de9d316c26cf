<template>
  <div class="absolute z-1000" v-show="wrapperVisible">
    <el-dialog
      :visible.sync="visible"
      width="800px"
      :show-close="false"
      :close-on-click-modal="false"
      :append-to-body="false"
      :modal-append-to-body="false"
    >
      <div class="bg-music-selector" v-click-outside="handleClickOutside">
        <!-- 头部 -->
        <div
          class="h-[55px] px-[28px] b-divider b-b-1 flex shrink-0 justify-between items-center"
        >
          <div class="text-title font-bold">更换音乐</div>
          <Icon
            @click="handleCancel"
            type="guanbi-tancong"
            class="w-[12px] h-[12px] text-assist hover:text-title cursor-pointer"
          />
        </div>
        <!-- 内容 -->
        <div class="flex-grow-1 flex flex-col overflow-hidden">
          <!-- 搜索和分类筛选 -->
          <div class="px-[40px] pt-[24px] pb-[16px]">
            <!-- 搜索框 -->
            <div class="flex justify-between items-center mb-[24px]">
              <fa-input
                class="w-[200px] search-input"
                v-model="searchQuery"
                placeholder="请输入歌曲名或歌手"
                @keyup.enter="handleSearch"
                :maxLength="100"
              >
                <Icon
                  type="xiaosuo"
                  class="w-[20px] h-[20px] text-[#ccc] relative left-[7px] cursor-pointer"
                  slot="suffix"
                  @click="handleSearch"
                />
              </fa-input>
              <div v-if="canUseAiRecommend" class="flex items-center">
                <fa-switch
                  size="small"
                  v-model="useAiRecommend"
                  @change="handleAiRecommendChange"
                /><span
                  @click="() => (useAiRecommend = !useAiRecommend)"
                  class="ml-[8px] text-[14px] text-subText cursor-pointer"
                  >智能推荐</span
                >
              </div>
            </div>
            <!-- 分类筛选 -->
            <div class="flex flex-wrap gap-x-[24px] gap-y-[16px]">
              <span
                v-for="category in categories"
                :key="category.id"
                class="category-item"
                :class="{ active: selectedCategory === category.id }"
                @click="handleChangeCategory(category.id)"
              >
                {{ category.name }}
              </span>
            </div>
          </div>
          <!-- 列表 -->
          <div
            class="flex-1 overflow-auto scrollbar-small"
            @scroll="handleScroll"
            ref="scrollContainer"
            v-show="musicList.length > 0"
          >
            <div
              class="pt-[8px] pb-[24px] grid grid-cols-2 gap-[16px] px-[40px]"
            >
              <div
                class="music-item"
                v-for="music in musicList"
                :key="music.resId"
                :class="{
                  'playing': playingMusic?.resId === music.resId,
                  'selected': isSelected(music),
                  'multi-select': maxSelectCount > 1,
                }"
                @click="
                  isSelected(music)
                    ? maxSelectCount > 1
                      ? handleMultiSelectChangeWithAiCheck(music, false)
                      : null
                    : maxSelectCount > 1
                    ? handleMultiSelectChangeWithAiCheck(music, true)
                    : handleSelectMusicWithAiCheck(music)
                "
              >
                <!-- 封面图 -->
                <div
                  class="relative w-[48px] h-[48px]"
                  @click.stop="handlePlayMusic(music)"
                >
                  <ScImg
                    class="music-item-cover"
                    :src="music.cover.resId"
                    :type="music.cover.resType"
                    belong="oss"
                    :max-width="48"
                  />
                  <div class="play-ctrl-box">
                    <Icon
                      v-show="playingMusic?.resId !== music.resId"
                      class="play-ctrl-icon"
                      type="bofang"
                    />
                    <Icon
                      v-show="playingMusic?.resId === music.resId"
                      class="play-ctrl-icon"
                      type="zanting"
                    />
                  </div>
                </div>
                <!-- 中间信息 -->
                <div
                  class="flex-1 mx-[16px] overflow-hidden flex flex-col justify-center"
                >
                  <div
                    class="overflow-hidden whitespace-nowrap text-ellipsis line-height-[19px] mb-[4px] text-text"
                  >
                    {{ music.name }}
                  </div>
                  <div class="line-height-[19px] text-[#BFBFBF]">
                    {{ music.duration }}
                  </div>
                </div>
                <!-- 右侧按钮 -->
                <div class="flex items-center action-buttons">
                  <fa-button
                    class="select-btn"
                    v-show="!isSelected(music)"
                    type="primary"
                    @click.stop="
                      maxSelectCount > 1
                        ? handleMultiSelectChangeWithAiCheck(music, true)
                        : handleSelectMusicWithAiCheck(music)
                    "
                    >选择使用</fa-button
                  >
                  <div
                    v-show="isSelected(music)"
                    class="rounded-[6px] flex justify-center items-center text-primary cursor-pointer"
                    @click.stop="
                      maxSelectCount > 1
                        ? handleMultiSelectChange(music, false)
                        : null
                    "
                  >
                    <Icon
                      type="dangqianshiyong"
                      class="w-[18px] h-[18px] mr-[4px]"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
          <!-- 列表空状态 -->
          <div
            class="text-center flex-1 flex flex-col justify-center items-center"
            v-show="musicList.length === 0 && !loading"
          >
            <img class="w-[159px]" src="@/assets/common/empty.webp" />
            <span class="text-assist mt-[24px]">暂无数据</span>
          </div>
        </div>
        <!-- 底部按钮 -->
        <div class="py-[12px] flex shrink-0 justify-center b-divider b-t-1">
          <fa-button
            type="primary"
            class="mr-[16px] px-[28px]"
            @click="handleConfirm"
            >确认</fa-button
          >
          <fa-button class="px-[28px]" @click="handleCancel">取消</fa-button>
        </div>
        <!-- 音乐播放器 -->
        <audio
          :src="currPlayUrl"
          class="hidden"
          ref="audioPlayer"
          preload="metadata"
          crossorigin="anonymous"
          @error="handleAudioError"
          @loadstart="handleAudioLoadStart"
          @canplay="handleAudioCanPlay"
          @ended="handleAudioEnded"
        ></audio>
      </div>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import {
  getMusicCategory,
  getMusicList,
  getMusicInfo,
} from '@/api/VideoEditor/music';
import ScImg from '@/components/comm/ScImg.vue';
import { Music } from '@/types';
import { message } from '@fk/faicomponent';
import { ref, watch, onMounted, computed } from 'vue';
/*************弹窗控制 star***************/
const props = defineProps<{
  /** 是否显示 */
  value: boolean;
  /** 资源ID列表，用于初始化已选音乐 */
  resIds?: string[];
  /** 最大可选数量，默认为1 */
  maxSelectCount?: number;
  /** 是否可以使用AI推荐 */
  canUseAiRecommend?: boolean;
  /** 是否使用AI推荐 */
  useAiRecommendValue?: boolean;
  /** 每次打开弹窗时是否重新获取数据，默认为false */
  refreshOnOpen?: boolean;
}>();
const visible = ref(false);
const wrapperVisible = ref(false);
const emit = defineEmits<{
  (e: 'input', value: boolean): void;
  (
    e: 'changeMusicInfo',
    musicList: Music[],
    useAiRecommend: boolean,
    maxSelectCount?: number,
  ): void;
  (e: 'update:useAiRecommendValue', value: boolean): void;
}>();
watch(visible, newVal => {
  emit('input', newVal);
  wrapperVisible.value = newVal;
});
watch(
  () => props.value,
  async newVal => {
    visible.value = newVal;

    // 当弹窗打开时
    if (newVal) {
      // 保存打开时的初始状态
      await initSelectedMusic();
      initialState.value = {
        selectedMusics: [...selectedMusicList.value],
        useAiRecommend: useAiRecommend.value,
      };

      // 如果开启了 refreshOnOpen 则重新获取数据
      if (props.refreshOnOpen) {
        await initData();
      }
    }
  },
);

/**
 * 处理点击外部区域关闭弹窗
 */
const handleClickOutside = (event: MouseEvent) => {
  // 检查是否点击在遮罩层上
  const dialogWrapper = (event.target as Element)?.closest(
    '.el-dialog__wrapper',
  );
  const dialogContent = (event.target as Element)?.closest('.el-dialog');

  // 如果点击在遮罩层上但不在对话框内容上，则关闭弹窗
  if (dialogWrapper && !dialogContent) {
    handleCancel();
  }
};

/*************弹窗控制 end***************/

/*************搜索和分类筛选 star***************/
/** 搜索词 */
const searchQuery = ref('');
/** 分类列表 */
const categories = ref<
  {
    id: number;
    name: string;
  }[]
>([]);
/** 选中的分类 */
const selectedCategory = ref(0);
/** 音乐列表 */
const musicList = ref<Music[]>([]);
/** 音乐列表分页 */
const page = ref(1);
/** 列表total */
const total = ref(0);
/** loading 标识 */
const loading = ref(false);

// 最大可选数量，默认为1
const maxSelectCount = props.maxSelectCount || 1;
// 是否可以使用AI推荐
const canUseAiRecommend = props.canUseAiRecommend !== false;
// 是否使用AI推荐 - 使用计算属性实现双向绑定
const useAiRecommend = computed({
  get: () => props.useAiRecommendValue ?? false,
  set: value => {
    emit('update:useAiRecommendValue', value);
  },
});

// 处理智能推荐变更
const handleAiRecommendChange = (value: boolean) => {
  if (value) {
    // 清空已选音乐
    selectedMusicList.value = [];
    // 暂停播放
    handlePlayMusic();
  } else {
    // 取消智能推荐时，默认选择第一个音乐
    if (musicList.value.length > 0) {
      if (maxSelectCount === 1) {
        // 单选模式
        selectedMusicList.value = [musicList.value[0]];
      } else {
        // 多选模式，选择第一个
        selectedMusicList.value = [musicList.value[0]];
      }
    }
  }
};

// 初始化分类和首个分类的音乐列表
const initData = async () => {
  const [catErr, catRes] = await getMusicCategory();
  if (catErr) {
    message.error(catErr.message);
    throw catErr;
  }
  categories.value = catRes.data;
  await handleChangeCategory(categories.value[0].id);

  // 初始化已选音乐
  initSelectedMusic();
};

// 初始化组件
onMounted(() => {
  if (props.refreshOnOpen) {
    return;
  }

  initData();
});

const scrollContainer = ref<HTMLElement | null>(null);
/** 搜索音乐 */
const handleSearch = async () => {
  if (loading.value) return;
  page.value = 1;
  loading.value = true;
  const [err, res] = await getMusicList(
    selectedCategory.value,
    searchQuery.value,
    page.value,
  );
  loading.value = false;
  if (err) {
    message.error(err.message);
    throw err;
  }
  handlePlayMusic();
  scrollContainer.value?.scrollTo(0, 0);
  musicList.value = res.data;
  total.value = res.total;
};
/** 点击分类筛选 */
const handleChangeCategory = async (id: number) => {
  if (loading.value) return;
  page.value = 1;
  loading.value = true;
  selectedCategory.value = id;
  const [err, res] = await getMusicList(id, searchQuery.value, page.value);
  loading.value = false;
  if (err) {
    message.error('获取音乐失败', err.message);
    throw err;
  }
  handlePlayMusic();
  scrollContainer.value?.scrollTo(0, 0);
  musicList.value = res.data;
  total.value = res.total;
};
/** 加载下一页 */
const handleLoadMore = () => {
  if (loading.value) return;
  if (musicList.value.length >= total.value) return;
  page.value += 1;
  loading.value = true;
  getMusicList(selectedCategory.value, searchQuery.value, page.value).then(
    ([err, res]) => {
      loading.value = false;
      if (err) {
        return console.error(err);
      }
      musicList.value.push(...res.data);
      total.value = res.total;
    },
  );
};
const handleScroll = (event: Event) => {
  const target = event.target as HTMLElement;
  // 判断是否滚动到底部，如果到底部则触发下一页加载
  const isBottom =
    target.scrollHeight - target.scrollTop <= target.clientHeight + 20;
  if (isBottom) {
    handleLoadMore();
  }
};
/*************搜索和分类筛选 end***************/

/*************选择音乐和预览音乐 star***************/
/** 当前选择的音乐列表 */
const selectedMusicList = ref<Music[]>([]);

/** 弹窗打开时的初始状态，用于取消时恢复 */
const initialState = ref<{
  selectedMusics: Music[];
  useAiRecommend: boolean;
}>({
  selectedMusics: [],
  useAiRecommend: false,
});

// 初始化已选音乐
const initSelectedMusic = async () => {
  if (!props.resIds || props.resIds.length === 0) return;

  // 获取已选音乐信息
  const promises = props.resIds.map(resId => getMusicInfo(resId));
  const results = await Promise.all(promises);

  // 过滤掉错误结果，只保留成功获取的音乐
  const musics = results.reduce((acc, [err, res]) => {
    if (!err && res?.data) {
      acc.push(res.data);
    }
    return acc;
  }, [] as Music[]);

  selectedMusicList.value = musics;
};

/** 判断音乐是否已选择 */
const isSelected = (music: Music) => {
  return selectedMusicList.value.some(item => item.resId === music.resId);
};

/** 选择单个音乐（单选模式） */
const handleSelectMusic = (music: Music) => {
  selectedMusicList.value = [music];
};

/** 多选模式下选择/取消选择音乐 */
const handleMultiSelectChange = (music: Music, checked: boolean) => {
  if (checked) {
    // 检查是否已达到最大选择数量
    if (selectedMusicList.value.length >= maxSelectCount) {
      message.warning(`最多只能选择${maxSelectCount}个音乐`);
      return;
    }
    // 添加新选中的音乐
    selectedMusicList.value.push(music);
  } else {
    // 在多选模式下且未开启智能推荐时，至少需要保留一个音乐
    if (
      maxSelectCount > 1 &&
      !useAiRecommend.value &&
      selectedMusicList.value.length <= 1
    ) {
      message.warning('至少需要选择一个音乐');
      return;
    }
    // 移除取消选择的音乐
    selectedMusicList.value = selectedMusicList.value.filter(
      item => item.resId !== music.resId,
    );
  }
};

/** 选择单个音乐并处理智能推荐状态 */
const handleSelectMusicWithAiCheck = (music: Music) => {
  // 如果当前开启了智能推荐，则取消智能推荐
  if (useAiRecommend.value) {
    useAiRecommend.value = false;
  }
  // 选择音乐
  handleSelectMusic(music);
};

/** 多选模式下选择/取消选择音乐并处理智能推荐状态 */
const handleMultiSelectChangeWithAiCheck = (music: Music, checked: boolean) => {
  // 如果当前开启了智能推荐且是选择操作，则取消智能推荐
  if (useAiRecommend.value && checked) {
    useAiRecommend.value = false;
  }
  // 执行选择/取消选择操作
  handleMultiSelectChange(music, checked);
};

/** 当前播放音乐 */
const playingMusic = ref<Music>();
/** 当前播放URL */
const currPlayUrl = ref('');
/** 音乐播放器 */
const audioPlayer = ref<HTMLAudioElement | null>(null);
/** 播放和暂停音乐 */
const handlePlayMusic = async (music?: Music) => {
  if (!music) {
    playingMusic.value = undefined;
    audioPlayer.value?.pause();
    return;
  }

  if (!audioPlayer.value) {
    console.error('音频播放器元素未找到');
    return;
  }

  // 检查链接是否有效
  if (!music.link || music.link.trim() === '') {
    console.error('音乐链接为空');
    message.error('音乐链接无效');
    return;
  }

  try {
    if (playingMusic.value?.resId === music.resId) {
      // 暂停当前播放
      playingMusic.value = undefined;
      audioPlayer.value.pause();
      audioPlayer.value.currentTime = 0;
    } else {
      // 播放新音频
      // 先暂停当前播放
      if (playingMusic.value) {
        audioPlayer.value.pause();
      }

      playingMusic.value = music;
      currPlayUrl.value = music.link;

      // 等待音频加载完成后播放
      await new Promise<void>((resolve, reject) => {
        const audio = audioPlayer.value!;

        const onLoadedData = () => {
          audio.removeEventListener('loadeddata', onLoadedData);
          audio.removeEventListener('error', onError);
          resolve();
        };

        const onError = () => {
          audio.removeEventListener('loadeddata', onLoadedData);
          audio.removeEventListener('error', onError);
          reject(new Error('音频加载失败'));
        };

        audio.addEventListener('loadeddata', onLoadedData);
        audio.addEventListener('error', onError);

        // 触发加载
        audio.load();
      });

      await audioPlayer.value.play();
    }
  } catch (error) {
    console.error('音频播放失败:', error);
    message.error('音频播放失败，请检查网络连接');
    playingMusic.value = undefined;
  }
};

// 音频事件处理函数
const handleAudioError = (event: Event) => {
  if (currPlayUrl.value === '') {
    return;
  }
  console.error('音频加载错误:', event);
  playingMusic.value = undefined;
  message.error('音频加载失败');
};

const handleAudioLoadStart = () => {
  // 音频开始加载时的处理
};

const handleAudioCanPlay = () => {
  // 音频可以播放时的处理
};

// 音频播放结束事件处理函数
const handleAudioEnded = () => {
  // 播放结束后重置播放状态
  playingMusic.value = undefined; // 置为未播放状态
};
/*************选择音乐和预览音乐 end***************/

/*************确认和取消 star***************/
/** 确认 */
const handleConfirm = () => {
  // 在多选模式下且未开启智能推荐时，必须至少选择一个音乐
  if (
    maxSelectCount > 1 &&
    !useAiRecommend.value &&
    selectedMusicList.value.length === 0
  ) {
    message.warning('请至少选择一个音乐');
    return;
  }

  audioPlayer.value?.pause();
  playingMusic.value = undefined;
  currPlayUrl.value = '';
  emit(
    'changeMusicInfo',
    selectedMusicList.value,
    useAiRecommend.value,
    maxSelectCount,
  );
  emit('input', false);
};
/** 取消 */
const handleCancel = () => {
  currPlayUrl.value = '';
  playingMusic.value = undefined;
  audioPlayer.value?.pause();

  // 只有在多选模式下才需要恢复初始状态和发送取消事件
  if (maxSelectCount > 1) {
    // 恢复到弹窗打开时的初始状态
    const initialSelectedMusics = initialState.value.selectedMusics;
    const initialUseAiRecommend = initialState.value.useAiRecommend;

    // 发送取消事件，传递初始状态而不是当前选择状态
    emit(
      'changeMusicInfo',
      initialSelectedMusics,
      initialUseAiRecommend,
      maxSelectCount,
    );
  }

  emit('input', false);
};
/*************确认和取消 end***************/
</script>

<style lang="scss" scoped>
.bg-music-selector {
  display: flex;
  flex-direction: column;
  height: calc(100vh - 80px);
  overflow: hidden;
  .music-item {
    @apply flex p-[16px] rounded-[8px] bg-white b-1 b-divider select-none;

    /* 默认状态：未选中时显示指针 */
    cursor: pointer;

    /* 已选中状态的指针样式 */
    &.selected {
      /* 单选模式：已选中时不显示指针 */
      cursor: default;
    }

    /* 多选模式：已选中时仍显示指针（因为可以取消选择） */
    &.selected.multi-select {
      cursor: pointer;
    }
    .music-item-cover {
      @apply w-[48px] h-[48px] rounded-full mr-[16px];
    }
    .play-ctrl-box {
      display: none;
      background-color: rgba(0, 0, 0, 0.5);
      overflow: hidden;
      position: absolute;
      top: 0;
      left: 0;
      width: 48px;
      height: 48px;
      border-radius: 50%;
      justify-content: center;
      align-items: center;
      cursor: pointer;
      .play-ctrl-icon {
        @apply w-[16px] h-[16px] text-white;
      }
    }
    .action-buttons {
      /* 布局相关 */
      @apply flex items-center;

      .select-btn {
        /* 显示状态 */
        display: none;
      }
    }

    &:hover,
    &.playing {
      /* 外观相关 */
      @apply b-primary;

      .play-ctrl-box {
        /* 显示状态 */
        @apply flex;
      }

      .action-buttons .select-btn {
        /* 显示状态 */
        display: block;
      }
    }
  }
}
.category-item {
  @apply text-[14px] text-subText cursor-pointer whitespace-nowrap;
  &.active {
    @apply font-bold text-title;
  }
  &:hover {
    @apply text-title;
  }
}
::v-deep {
  .el-dialog {
    @apply rounded-[16px] my-[40px]!;
    .el-dialog__header {
      @apply p-0;
    }
    .el-dialog__body {
      @apply p-0;
    }
  }
  .search-input {
    .fa-input {
      @apply rounded-[6px];
    }
  }
}
</style>
