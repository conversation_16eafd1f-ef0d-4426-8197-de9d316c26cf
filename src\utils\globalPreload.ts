// 全局前置信息，例如用户信息、企业信息、资源域名、直分销等；
// 业务页面可以通过store.state.system.isPreload来判断全局前置信息是否加载完成

import router from '@/router';
import store from '@/store';
import { message } from '@fk/faicomponent';

export const starGlobalPreload = async (path: string) => {
  if (store.state.system.isPreload) return;
  // addSkeletonTask();
  try {
    await Promise.all([
      store.dispatch('updateUserInfo'),
      store.dispatch('updatePoints'),
      store.dispatch('getIndustryAndScene'),
    ]);
    if (path === '/adm-set') {
      if (!store.state.user.isStaff) {
        // 非内部用户重定向到首页
        router.push('/');
      }
    }
    // 动态设置页面标题
    const isOem = store.state.system.isOem;
    if (isOem) {
      document.title = '分销速创';
    } else {
      document.title = '凡科速创';
      // 设置页面的 favicon
      const link = document.createElement('link');
      link.rel = 'icon';
      link.href = 'https://i.fkw.com/favicon.ico';
      document.head.appendChild(link);
    }
    console.log('全局前置信息加载完成');
    store.commit('setPreloadDone', true);
    // finishSkeletonTask();
  } catch (error) {
    message.error('系统异常，请刷新重试', 0);
    throw new Error('全局前置信息加载失败' + error);
  }
};
