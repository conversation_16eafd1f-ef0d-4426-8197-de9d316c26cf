<template>
  <div class="button-group">
    <!-- 数量选择步进器 -->
    <el-popover
      placement="top"
      trigger="click"
      :visible-arrow="true"
      popper-class="stepper-popover-overlay"
      v-model="popoverVisible"
    >
      <div class="stepper-popover">
        <div class="stepper-popover__content">
          <fa-button
            class="stepper-popover__button stepper-popover__button--minus"
            :disabled="count <= minCount"
            @click="decreaseCount"
          >
            <fa-icon type="minus" />
          </fa-button>
          <fa-input-number
            hideHandlerButton
            id="inputNumber"
            class="stepper-popover__input"
            :min="minCount"
            :max="maxCount"
            v-model="count"
            @change="handleCountChange"
          />
          <fa-button
            class="stepper-popover__button stepper-popover__button--plus"
            :disabled="count >= maxCount"
            @click="increaseCount"
          >
            <fa-icon type="fa-add" />
          </fa-button>
        </div>
      </div>
      <div
        slot="reference"
        class="button-group__selector"
        :class="{ 'button-group__selector--disabled': loading }"
        @click="handleSelectorClick"
      >
        <span class="button-group__count">{{ count }} 个</span>
        <Icon
          type="jiantou_xia"
          class="button-group__arrow"
          :class="{ 'button-group__arrow--rotated': popoverVisible }"
        />
      </div>
    </el-popover>

    <div class="divider"></div>

    <!-- 生成预览按钮 -->
    <fa-button
      type="primary"
      size="small"
      class="generate-button"
      :loading="loading"
      v-bind="$attrs"
      v-on="$listeners"
    >
      <span class="generate-button__text">生成并预览</span>
      <span class="star">
        <img
          class="star__icon"
          src="@/assets/common/score.svg"
          style="filter: brightness(0) invert(1)"
        />
        <span
          class="star__text"
          :class="{ 'star__text--hidden': !showIntegral }"
          >{{ integral }}</span
        >
      </span>
    </fa-button>
  </div>
</template>

<script>
export default {
  name: 'GenerateButton',
  components: {},
  props: {
    /**
     * 生成数量
     * @type {Number}
     * @default 1
     */
    num: {
      type: Number,
      default: 1,
    },
    /**
     * 总消耗积分（已根据数量计算）
     * @type {Number}
     * @default 100
     */
    integral: {
      type: Number,
      default: 100,
    },
    /**
     * 是否显示积分
     * @type {Boolean}
     * @default true
     */
    showIntegral: {
      type: Boolean,
      default: true,
    },
    /**
     * 是否处于加载状态
     * @type {Boolean}
     * @default false
     */
    loading: {
      type: Boolean,
      default: false,
    },
    /**
     * 最小生成数量
     * @type {Number}
     * @default 1
     */
    minCount: {
      type: Number,
      default: 1,
    },
    /**
     * 最大生成数量
     * @type {Number}
     * @default 10
     */
    maxCount: {
      type: Number,
      default: 10,
    },
  },
  data() {
    return {
      // 当前选择的数量
      count: this.num,
      // popover显示状态
      popoverVisible: false,
    };
  },

  watch: {
    /**
     * 监听数量变化并同步到父组件
     */
    count: {
      handler(newVal) {
        this.$emit('update:num', newVal);
      },
    },
  },
  methods: {
    /**
     * 增加数量
     */
    increaseCount() {
      if (this.count < this.maxCount) {
        this.count++;
      }
    },
    /**
     * 减少数量
     */
    decreaseCount() {
      if (this.count > this.minCount) {
        this.count--;
      }
    },
    /**
     * 处理输入框数量变化
     */
    handleCountChange(value) {
      // 确保值在有效范围内
      if (value < this.minCount) {
        this.count = this.minCount;
      } else if (value > this.maxCount) {
        this.count = this.maxCount;
      } else {
        this.count = value;
      }
    },
    /**
     * 处理选择器点击
     */
    handleSelectorClick() {
      // 加载状态下不允许打开popover
      if (this.loading) {
        return;
      }
      // el-popover 会自动处理显示状态
    },
  },
};
</script>

<style lang="scss" scoped>
.button-group {
  @apply flex items-center
         border-rd-8px
         bg-gradient-linear bg-gradient-[92.19deg,#105fff_0%,#7923f9_100%];

  .button-group__selector {
    /* 布局相关 */
    @apply flex items-center justify-between cursor-pointer;
    /* 尺寸相关 */
    @apply h-40px pl-25px pr-17px py-8px;
    /* 文字相关 */
    @apply text-white;

    &--disabled {
      @apply cursor-not-allowed opacity-60;
    }

    .button-group__count {
      @apply text-white font-normal min-w-40px text-right;
    }

    .button-group__arrow {
      /* 布局相关 */
      @apply ml-13px;
      /* 尺寸相关 */
      @apply w-10px h-10px;
      /* 文字相关 */
      @apply text-white text-12px;
      /* 动画相关 */
      @apply transition-transform duration-200;

      &--rotated {
        @apply rotate-180;
      }
    }
  }

  .divider {
    @apply w-1px h-24px
           mt-0 mb-0 ml-0
           bg-[#fff] opacity-60;
  }

  .generate-button {
    @apply flex items-center
           h-40px
           min-w-184px
           pl-36px! py-8px pr-25px
           bg-transparent border-none
           text-white font-bold;

    .star {
      @apply flex items-center
             ml-16px;

      &__icon {
        @apply w-16px h-16px
               mr-3px
               text-white;
      }

      &__text {
        @apply text-15px lh-none
               font-400
               text-left text-[#fff]
               transition-opacity duration-200;

        &--hidden {
          @apply opacity-0;
        }
      }
    }
    :global(.fa-btn.fa-btn-loading::before) {
      @apply hidden;
    }
  }
}

/* 步进器弹窗样式 */
.stepper-popover {
  /* 布局相关 */
  @apply flex flex-col;
  /* 尺寸相关 */
  @apply p-16px;
  /* 外观相关 */
  @apply bg-white border-rd-8px p-4px;

  .stepper-popover__header {
    /* 布局相关 */
    @apply mb-12px;
    /* 文字相关 */
    @apply text-14px font-medium text-gray-800;
  }

  .stepper-popover__content {
    /* 布局相关 */
    @apply flex items-center justify-center;

    .stepper-popover__button {
      /* 布局相关 */
      @apply flex items-center justify-center;
      /* 尺寸相关 */
      @apply w-32px h-32px p-0;
      /* 外观相关 */
      @apply bg-[#F3F3F5] border-none border-gray-200 border-rd-4px;
      /* 文字相关 */
      @apply text-[#666];

      &:hover:not(:disabled) {
        @apply bg-[#E8E8E8] border-none text-[#666];
      }

      &:disabled {
        @apply text-[#BFBFBF] cursor-not-allowed;
      }

      &--minus,
      &--plus {
        /* 图标尺寸 */
        ::v-deep(.fa-icon) {
          @apply text-14px;
        }
      }
    }

    .stepper-popover__input {
      /* 尺寸相关 */
      @apply w-66px;
      /* 外观相关 */
      @apply @apply border-none! shadow-none!;

      &:hover,
      &:focus,
      &:active {
        @apply border-none! shadow-none!;
      }

      ::v-deep(.fa-input-number-input) {
        /* 布局相关 */
        @apply text-center;

        /* 文字相关 */
        @apply text-15px font-400 text-15px text-center text-[#333];

        /* 外观相关 */
        @apply @apply border-none! shadow-none!;

        &:hover,
        &:focus,
        &:active {
          @apply border-none! shadow-none!;
        }
      }
    }
  }
}
</style>

<style lang="scss">
/* el-popover 弹窗样式 */
.stepper-popover-overlay {
  /* 弹窗内容样式 */
  &.el-popover {
    padding: 0;
    border: none;
    border-radius: 8px;
    min-width: initial;
    background: #fff;
    box-shadow: 0 3px 20px #0000001a;
    transform: translateY(12px) !important;
  }
}
</style>
