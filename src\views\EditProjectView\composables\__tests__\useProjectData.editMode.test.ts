/**
 * @fileoverview useProjectData 编辑模式状态管理单元测试
 * 测试修复项目编辑模式状态管理bug的功能
 */

import { describe, it, expect, beforeEach, afterEach, vi, Mock } from 'vitest';
import { ref } from 'vue';
import { useProjectData } from '../useProjectData';
import { getEditProjectViewData } from '@/api/EditProjectView';
import router from '@/router';

// Mock API 模块
vi.mock('@/api/EditProjectView', () => ({
  getEditProjectViewData: vi.fn(),
}));

// Mock router
vi.mock('@/router', () => ({
  default: {
    currentRoute: {
      path: '/video-project',
      query: { templateId: '456', step: '0' },
    },
    replace: vi.fn(),
  },
}));

// Mock message
vi.mock('@fk/faicomponent', () => ({
  message: {
    error: vi.fn(),
  },
}));

const mockGetEditProjectViewData = getEditProjectViewData as Mock;
const mockRouter = router as any;

describe('useProjectData - 编辑模式状态管理', () => {
  beforeEach(() => {
    // 清除所有模拟函数的调用记录
    vi.clearAllMocks();

    // 重置路由状态
    mockRouter.currentRoute = {
      path: '/video-project',
      query: { templateId: '456', step: '0' },
    };
    mockRouter.replace = vi.fn();

    // 重置 cookie 状态
    document.cookie = '';
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('新建模式转编辑模式状态管理', () => {
    it('初始新建模式：isEditMode应该为false', () => {
      // 设置路由为新建模式（没有projectId）
      mockRouter.currentRoute.query = { templateId: '456', step: '0' };

      const { isEditMode } = useProjectData({
        initialStep: ref(0),
        templateId: 456,
      });

      expect(isEditMode.value).toBe(false);
    });

    it('初始编辑模式：isEditMode应该为true', () => {
      // 设置路由为编辑模式（有projectId）
      mockRouter.currentRoute.query = {
        projectId: '123',
        templateId: '456',
        step: '0',
      };

      const { isEditMode } = useProjectData({
        initialStep: ref(0),
        templateId: 456,
      });

      expect(isEditMode.value).toBe(true);
    });

    it('新建模式下获取projectId后，isEditMode应该更新为true', async () => {
      // 设置路由为新建模式（没有projectId）
      mockRouter.currentRoute.query = { templateId: '456', step: '0' };

      // 模拟API返回新创建的项目数据
      mockGetEditProjectViewData.mockResolvedValue([
        null,
        {
          projectId: 123,
          templateId: 456,
          type: 0,
          inputForm: [],
          resForm: [],
        },
      ]);

      const {
        isEditMode,
        fetchFormData,
        updateRouteWithProjectId: _updateRouteWithProjectId,
      } = useProjectData({
        initialStep: ref(0),
        templateId: 456,
      });

      // 初始状态应该是新建模式
      expect(isEditMode.value).toBe(false);

      // 调用fetchFormData，模拟新建项目流程
      await fetchFormData();

      // 验证updateRouteWithProjectId被调用，并且isEditMode被更新为true
      expect(mockRouter.replace).toHaveBeenCalledWith({
        path: '/video-project',
        query: {
          templateId: '456',
          step: '0',
          projectId: '123',
        },
      });

      // 验证isEditMode已经更新为true
      expect(isEditMode.value).toBe(true);
    });

    it('currentProjectId应该正确获取项目ID', async () => {
      // 设置路由为新建模式（没有projectId）
      mockRouter.currentRoute.query = { templateId: '456', step: '0' };

      // 模拟API返回新创建的项目数据
      mockGetEditProjectViewData.mockResolvedValue([
        null,
        {
          projectId: 123,
          templateId: 456,
          type: 0,
          inputForm: [],
          resForm: [],
        },
      ]);

      const { currentProjectId, fetchFormData } = useProjectData({
        initialStep: ref(0),
        templateId: 456,
      });

      // 初始状态应该没有项目ID
      expect(currentProjectId.value).toBeUndefined();

      // 调用fetchFormData获取数据
      await fetchFormData();

      // 此时应该有项目ID了（从路由更新后获取）
      expect(currentProjectId.value).toBe(123);
    });

    it('编辑模式下currentProjectId应该正确获取', () => {
      // 设置路由为编辑模式（有projectId）
      mockRouter.currentRoute.query = {
        projectId: '456',
        templateId: '789',
        step: '0',
      };

      const { currentProjectId } = useProjectData({
        initialStep: ref(0),
        templateId: 789,
      });

      // 编辑模式下应该能获取到路由中的项目ID
      expect(currentProjectId.value).toBe(456);
    });

    it('编辑模式下不应该调用updateRouteWithProjectId', async () => {
      // 设置路由为编辑模式（有projectId）
      mockRouter.currentRoute.query = {
        projectId: '123',
        templateId: '456',
        step: '0',
      };

      // 模拟API返回项目数据
      mockGetEditProjectViewData.mockResolvedValue([
        null,
        {
          projectId: 123,
          templateId: 456,
          type: 0,
          inputForm: [],
          resForm: [],
        },
      ]);

      const { isEditMode, fetchFormData } = useProjectData({
        initialStep: ref(0),
        templateId: 456,
      });

      // 初始状态应该是编辑模式
      expect(isEditMode.value).toBe(true);

      // 调用fetchFormData
      await fetchFormData();

      // 验证不会调用路由更新
      expect(mockRouter.replace).not.toHaveBeenCalled();

      // 验证isEditMode保持为true
      expect(isEditMode.value).toBe(true);
    });

    it('手动调用updateRouteWithProjectId应该正确更新状态', () => {
      // 设置路由为新建模式（没有projectId）
      mockRouter.currentRoute.query = { templateId: '456', step: '0' };

      const { isEditMode, updateRouteWithProjectId } = useProjectData({
        initialStep: ref(0),
        templateId: 456,
      });

      // 初始状态应该是新建模式
      expect(isEditMode.value).toBe(false);

      // 手动调用updateRouteWithProjectId
      updateRouteWithProjectId(123);

      // 验证路由被更新
      expect(mockRouter.replace).toHaveBeenCalledWith({
        path: '/video-project',
        query: {
          templateId: '456',
          step: '0',
          projectId: '123',
        },
      });

      // 验证isEditMode被更新为true
      expect(isEditMode.value).toBe(true);
    });

    it('编辑模式下调用updateRouteWithProjectId应该不执行任何操作', () => {
      // 设置路由为编辑模式（有projectId）
      mockRouter.currentRoute.query = {
        projectId: '123',
        templateId: '456',
        step: '0',
      };

      const { isEditMode, updateRouteWithProjectId } = useProjectData({
        initialStep: ref(0),
        templateId: 456,
      });

      // 初始状态应该是编辑模式
      expect(isEditMode.value).toBe(true);

      // 调用updateRouteWithProjectId
      updateRouteWithProjectId(456);

      // 验证路由没有被更新
      expect(mockRouter.replace).not.toHaveBeenCalled();

      // 验证isEditMode保持为true
      expect(isEditMode.value).toBe(true);
    });
  });
});
