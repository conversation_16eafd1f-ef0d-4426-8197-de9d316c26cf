/**
 * 点击外部区域指令
 * 只有真正的点击（mousedown + click 在同一位置）才会触发回调
 */
export interface ClickOutsideBinding {
  value: (event: MouseEvent) => void;
}

interface ElementWithCleanup extends HTMLElement {
  _clickOutsideCleanup?: () => void;
}

const clickOutside = {
  bind(el: ElementWithCleanup, binding: ClickOutsideBinding) {
    let mouseDownTarget: EventTarget | null = null;

    const handleMouseDown = (event: MouseEvent) => {
      mouseDownTarget = event.target;
    };

    const handleClick = (event: MouseEvent) => {
      // 只有在同一个目标上 mousedown 和 click 才算真正的点击
      if (
        mouseDownTarget === event.target &&
        !el.contains(event.target as Node)
      ) {
        binding.value?.(event);
      }
      mouseDownTarget = null;
    };

    document.addEventListener('mousedown', handleMouseDown);
    document.addEventListener('click', handleClick);

    // 保存清理函数到元素上
    el._clickOutsideCleanup = () => {
      document.removeEventListener('mousedown', handleMouseDown);
      document.removeEventListener('click', handleClick);
    };
  },

  unbind(el: ElementWithCleanup) {
    // 清理事件监听器
    el._clickOutsideCleanup?.();
  },
};

export default clickOutside;
