# 保存至我的作品按钮禁用功能实现

## 功能概述

在预览效果页面中，当用户选中处于特定状态的作品时，【保存至我的作品】按钮会被设置为置灰（禁用）状态，以防止用户保存不合适的作品。

## 禁用条件

按钮在以下情况下会被禁用：

1. **生成中状态的作品** - `WORK_STATUS.GENERATING`
2. **重新生成中状态的作品** - `WORK_STATUS.REGENERATING`
3. **生成失败状态的作品** - `WORK_STATUS.FAILED`
4. **重新生成失败状态的作品** - `WORK_STATUS.FAILED_AGAIN`
5. **已保存状态的作品** - 通过 `saveTime` 字段判断
6. **没有选中任何作品且不是全选状态**

## 实现细节

### 1. 父组件修改 (`src/views/EditProjectView/index.vue`)

父组件已经正确传递了所需的 Props：

```vue
<ProjectFooter
  :selected-work-ids="selectedWorkIds"
  :is-all-selected="isAllSelected"
  :work-list="currentWorkList"
  <!-- 其他props... -->
/>
```

其中 `currentWorkList` 计算属性已存在：

```typescript
/**
 * 获取当前作品列表
 */
const currentWorkList = computed(() => {
  const component = activeComponentRef.value;
  return component?.workList || [];
});
```

### 2. ProjectFooter 组件修改 (`src/views/EditProjectView/components/ProjectFooter/index.vue`)

#### 更新接口定义

```typescript
/**
 * 作品状态检查所需的最小接口
 */
interface WorkStatusCheckItem {
  id: number;
  status: number;
  isRegenerate?: boolean;
  saveTime?: string; // 新增：保存时间字段，用于判断是否已保存
}
```

#### 新增 Props 定义

```typescript
/**
 * 选中的作品ID列表
 */
selectedWorkIds: {
  type: Array,
  default: () => [],
},
/**
 * 是否全选
 */
isAllSelected: {
  type: Boolean,
  default: false,
},
/**
 * 作品列表
 */
workList: {
  type: Array,
  default: () => [],
},
```

#### 新增状态判断逻辑

```typescript
/**
 * 检查选中作品是否包含不可保存的状态
 * 包括：生成中、重新生成中、生成失败、重新生成失败、已保存
 */
const shouldDisableSaveButton = computed(() => {
  // 如果没有选中任何作品且不是全选状态，则禁用按钮
  if (!props.selectedWorkIds?.length && !props.isAllSelected) {
    return true;
  }

  /**
   * 检查单个作品是否不可保存
   * @param work 作品项
   * @returns 是否不可保存
   */
  const isWorkUnsavable = (work: WorkStatusCheckItem): boolean => {
    const statusInfo = getWorkStatusInfo(work);
    const isSaved = Boolean(work.saveTime);

    return (
      isGeneratingWork(statusInfo) ||
      isRegeneratingWork(statusInfo) ||
      isAnyFailedWork(statusInfo) ||
      isSaved
    );
  };

  // 如果是全选状态，检查所有作品中是否有不可保存的状态
  if (props.isAllSelected) {
    return props.workList.some(isWorkUnsavable);
  }

  // 非全选状态，检查选中的作品中是否有不可保存的状态
  const selectedWorks = props.workList.filter((work: WorkStatusCheckItem) =>
    props.selectedWorkIds.includes(work.id),
  );

  return selectedWorks.some(isWorkUnsavable);
});
```

#### 模板修改

```vue
<fa-button
  v-if="currentStep === SECOND_STEP_INDEX"
  class="project-footer__save-button"
  :class="{ 'project-footer__save-button--disabled': shouldDisableSaveButton }"
  type="default"
  :disabled="shouldDisableSaveButton"
  @click="onSaveToWorks"
>
  <span>保存至我的作品</span>
</fa-button>
```

#### 置灰样式

```scss
.project-footer__save-button--disabled {
  /* 外观相关 - 置灰样式 */
  background: linear-gradient(92.19deg, #105fff 0%, #7923f9 100%);
  opacity: 0.4;

  /* 交互相关 */
  @apply cursor-not-allowed;
}
```

## 功能验证

### 测试场景

1. **选中生成中状态的作品** - 按钮应该被禁用
2. **选中重新生成中状态的作品** - 按钮应该被禁用
3. **选中生成失败状态的作品** - 按钮应该被禁用
4. **选中已保存状态的作品** - 按钮应该被禁用
5. **选中正常完成且未保存的作品** - 按钮应该启用
6. **全选状态下包含不可保存作品** - 按钮应该被禁用
7. **全选状态下所有作品都可保存** - 按钮应该启用
8. **没有选中任何作品且不是全选状态** - 按钮应该被禁用

### 实现状态

✅ **已完成** - 所有核心功能已实现并集成到现有代码中

- ✅ 接口定义更新（添加 `saveTime` 字段）
- ✅ 状态判断逻辑实现（包含已保存状态检查）
- ✅ 按钮禁用样式应用
- ✅ 文档更新完成

## 总结

该功能已成功实现，能够根据选中作品的状态自动禁用【保存至我的作品】按钮，防止用户保存不合适的作品，提升用户体验和系统稳定性。

## 使用的状态判断函数

实现中使用了项目中现有的统一状态判断函数：

- `getWorkStatusInfo()` - 获取统一的状态信息
- `isGeneratingWork()` - 判断是否为生成中状态
- `isRegeneratingWork()` - 判断是否为重新生成中状态
- `isAnyFailedWork()` - 判断是否为任何失败状态

## 功能特点

1. **实时更新** - 当选中的作品状态发生变化时，按钮的可用性会实时更新
2. **视觉反馈** - 禁用状态下按钮显示为置灰，并且鼠标悬停时显示不可点击的光标
3. **状态一致性** - 使用项目中统一的状态管理系统，确保状态判断的一致性
4. **全选支持** - 支持全选模式下的状态检查
5. **向后兼容** - 不影响现有功能，只是增加了额外的状态检查

## 测试建议

建议在以下场景下测试功能：

1. 选中生成中的作品，验证按钮是否禁用
2. 选中重新生成中的作品，验证按钮是否禁用
3. 选中生成失败的作品，验证按钮是否禁用
4. 选中重新生成失败的作品，验证按钮是否禁用
5. 选中混合状态的作品（包含可保存和不可保存的），验证按钮是否禁用
6. 全选状态下包含不可保存作品，验证按钮是否禁用
7. 只选中可保存状态的作品，验证按钮是否启用
8. 作品状态变化时，验证按钮状态是否实时更新
