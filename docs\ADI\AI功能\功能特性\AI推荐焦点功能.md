# AI 推荐词焦点显示和刷新次数限制功能

## 功能概述

本次修改为 DynamicForm 组件中的 AI 推荐词功能添加了四个重要特性：

1. **焦点显示控制**：AI 推荐词可以预先获取但只在表单项获得焦点时显示
2. **失焦保持显示**：表单项失去焦点时，AI 推荐词区域保持显示状态，不会隐藏
3. **已选推荐词过滤**：用户选择某个推荐词后，该推荐词会从列表中移除，避免重复选择
4. **刷新次数限制**：每个表单项最多允许调用 AI 推荐 API 10 次，超过限制后循环显示历史推荐词

## 修改的文件

### 1. OpenAiTipButton 组件 (`src/components/DynamicForm/FormItems/OpenAiTipButton/index.vue`)

#### 新增 Props

- `isFocused: Boolean` - 控制 AI 推荐是否应该显示，只有在表单项获得焦点时才显示推荐

#### 新增响应式数据

- `apiCallCount: ref<number>(0)` - API 调用次数计数器
- `historicalSuggestions: ref<Array<Array<{ value: string } | string>>>([])` - 历史推荐词缓存
- `currentHistoryIndex: ref<number>(0)` - 当前历史索引
- `selectedSuggestions: ref<Set<string>>(new Set())` - 已选择的推荐词集合
- `hasEverBeenFocused: ref<boolean>(false)` - 当前组件是否曾经获得过焦点
- `MAX_API_CALLS = 10` - 最大 API 调用次数限制

#### 修改的逻辑

1. **显示时机控制**：

   - 修改 `shouldShowSuggestions` 计算属性，使用 `hasEverBeenFocused` 条件替代 `props.isFocused`
   - 组件挂载时可以预先获取数据，获得焦点时显示，失去焦点后保持显示
   - 确保多个表单项时，每个 AI 推荐词独立显示，不会相互干扰

2. **已选推荐词过滤**：

   - 新增 `updateSelectedSuggestions` 函数，根据表单控件类型跟踪已选择的推荐词
   - 修改 `filteredSuggestions` 计算属性，过滤掉已选择的推荐词
   - 支持不同表单控件类型：input/textarea（文本包含检查）、selectTags（数组值检查）、select（直接值比较）

3. **刷新次数限制**：

   - 在 `fetchSuggestions` 函数中添加调用次数检查
   - 超过限制时从历史缓存中循环显示数据
   - 成功获取新数据时自动添加到历史缓存

4. **焦点监听**：

   - 添加 `watch` 监听器监听 `isFocused` 属性变化
   - 获得焦点且尚未加载数据时触发获取推荐

5. **字段值监听**：
   - 添加 `watch` 监听器监听当前字段值变化
   - 字段值变化时更新已选择推荐词集合

### 2. DynamicFormItem 组件 (`src/components/DynamicForm/FormItems/DynamicFormItem.vue`)

#### 新增状态管理

- `isFocused: ref<boolean>(false)` - 表单项焦点状态

#### 新增方法

- `handleFocus()` - 处理表单控件获得焦点
- `handleBlur()` - 处理表单控件失去焦点

#### 修改的模板

为以下表单控件类型添加了焦点事件监听和 `is-focused` 属性传递：

- `input` 类型：`fa-input` 组件
- `select` 类型：`fa-select` 组件
- `selectTags` 类型：`fa-select` 组件（标签模式）

每个 `OpenAiTipButton` 组件都添加了 `:is-focused="isFocused"` 属性绑定。

## 功能特性

### 1. 焦点显示控制

- AI 推荐词可以在组件挂载时预先获取数据
- 只有当用户点击或聚焦到对应的表单项时，才显示已获取的 AI 推荐词
- **失去焦点时推荐词保持显示状态，不会隐藏**
- **多个表单项时，每个表单项的 AI 推荐词独立显示，不会相互干扰**

### 2. 已选推荐词过滤

- 用户选择某个 AI 推荐词后，该推荐词会从推荐列表中移除
- 避免用户重复选择相同的推荐词
- 支持不同表单控件类型：
  - **input/textarea**：检查当前文本是否包含推荐词
  - **selectTags**：检查数组值中是否包含推荐词
  - **select**：直接比较当前选中值
- 实时更新过滤状态，响应表单值变化

### 3. 刷新次数限制

- 每个表单项独立计算 API 调用次数
- 最多允许调用 10 次 AI 推荐 API
- 超过限制后：
  - 不再调用 API 接口
  - 从历史推荐词缓存中循环显示
  - 保持"换一换"按钮可用性

### 4. 历史数据缓存

- 每次成功获取 AI 推荐后自动缓存结果
- 支持循环显示历史推荐词
- 缓存数据在组件生命周期内持久保存

## 使用方式

功能已自动集成到现有的 DynamicForm 组件中，无需额外配置。只要表单项配置了 `openAiTip: true`，就会自动享有这些新特性。

## 兼容性

- 保持与现有 API 的完全兼容
- 不影响现有的依赖字段检查逻辑
- 保留原有的防抖机制
- 向后兼容所有现有的表单配置

## 技术细节

- 使用 Vue 3 Composition API 实现
- 响应式数据管理确保状态同步
- 事件驱动的焦点状态管理
- 内存高效的历史数据缓存机制

## 验证方式

### 手动验证步骤

1. **启动开发服务器**：

   ```bash
   npm run dev
   ```

2. **访问项目编辑页面**：

   - 进入任何包含 AI 推荐功能的表单页面（如 FirstStep）
   - 查看有 `openAiTip: true` 配置的表单项

3. **验证焦点显示功能**：

   - 初始状态：AI 推荐词区域不显示
   - 点击表单输入框：AI 推荐词区域显示
   - 点击其他地方失去焦点：**AI 推荐词区域保持显示，不会隐藏**

4. **验证已选推荐词过滤功能**：

   - 在有 AI 推荐的表单项中选择一个推荐词
   - 观察该推荐词是否从推荐列表中消失
   - 修改表单值，观察过滤状态是否正确更新

5. **验证刷新次数限制**：
   - 在有 AI 推荐的表单项中获得焦点
   - 连续点击"换一换"按钮超过 10 次
   - 观察是否停止调用 API 并开始循环显示历史推荐词

### 代码验证

项目已通过构建验证：

```bash
npm run build  # ✅ 构建成功
```

## 实现状态

- ✅ OpenAiTipButton 组件焦点控制功能
- ✅ DynamicFormItem 组件焦点事件监听
- ✅ 失焦保持显示功能（移除焦点隐藏逻辑）
- ✅ 已选推荐词过滤功能
- ✅ 多种表单控件类型的推荐词过滤支持
- ✅ API 调用次数限制机制
- ✅ 历史推荐词缓存和循环显示
- ✅ 所有表单控件类型支持（input、select、selectTags）
- ✅ 向后兼容性保证
- ✅ 构建验证通过
