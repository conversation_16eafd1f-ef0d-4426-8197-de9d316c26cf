# 视频时长校验功能实现说明

## 功能概述

为视频类型的文件表单项添加时长校验功能，确保用户上传的视频素材总时长符合业务要求。

## 校验规则

### 时长限制
- **最小时长**：15秒
- **最大时长**：600秒（10分钟）

### 校验时机
- **生成模式**：点击【生成并预览】按钮时触发完整校验
- **保存模式**：点击【保存】按钮时跳过时长校验

### 错误提示
- 时长不足：`"视频素材总时长需超过15s，建议上传多段视频素材"`
- 时长超限：`"视频素材总时长不能超过10min，建议减少视频素材或上传更短的视频素材"`

## 技术实现

### 1. 核心实现位置

文件：`src/views/EditProjectView/pages/FirstStep/composables/useForm.ts`

在 `createFileFormItem` 函数中为视频类型文件添加时长校验规则。

### 2. 实现逻辑

```typescript
// 为视频类型文件添加时长校验规则
if (mediaType === MEDIA_TYPES.VIDEO) {
  formItem.rules.push({
    trigger: VALIDATION_TRIGGER.MAGIC,
    triggerAuto: true,
    validator: (
      _rule: FormRule,
      value: unknown,
      callback: (error?: Error) => void,
    ) => {
      // 保存模式跳过时长校验
      if (currentValidationMode === VALIDATION_MODE.SAVE) {
        console.log('保存模式：跳过视频时长校验');
        return callback();
      }

      // 检查文件列表是否为空
      if (!value || (Array.isArray(value) && value.length === 0)) {
        // 如果没有文件，跳过时长校验（由必填校验处理）
        return callback();
      }

      // 计算视频文件总时长
      const files = Array.isArray(value) ? value : [];
      let totalDuration = 0;

      for (const file of files) {
        if (file && typeof file === 'object' && 'duration' in file) {
          const duration = Number(file.duration) || 0;
          totalDuration += duration;
        }
      }

      // 时长校验：最小15秒，最大600秒（10分钟）
      const MIN_DURATION = 15; // 15秒
      const MAX_DURATION = 600; // 10分钟

      if (totalDuration < MIN_DURATION) {
        callback(new Error('视频素材总时长需超过15s，建议上传多段视频素材'));
      } else if (totalDuration > MAX_DURATION) {
        callback(new Error('视频素材总时长不能超过10min，建议减少视频素材或上传更短的视频素材'));
      } else {
        callback();
      }
    },
  });
}
```

### 3. 关键特性

#### 差异化校验支持
- 利用现有的 `currentValidationMode` 机制
- 生成模式：执行完整的时长校验
- 保存模式：跳过时长校验，仅进行其他必要校验

#### 时长计算逻辑
- 遍历文件列表中的所有视频文件
- 累加每个文件的 `duration` 字段
- 处理无效或缺失的时长数据（默认为0）

#### 错误处理
- 空文件列表：跳过校验（由必填校验处理）
- 无效时长数据：视为0秒处理
- 混合有效/无效数据：仅计算有效时长

## 测试覆盖

### 测试文件
`src/views/EditProjectView/pages/FirstStep/__tests__/video-duration-validation.test.ts`

### 测试场景

#### 生成模式校验
- ✅ 单个视频时长在有效范围内
- ✅ 多个视频总时长在有效范围内
- ✅ 总时长等于边界值（15秒、600秒）
- ✅ 总时长小于最小值（错误提示）
- ✅ 总时长超过最大值（错误提示）
- ✅ 空文件列表（跳过校验）
- ✅ 文件缺失duration字段
- ✅ duration为0或无效值

#### 保存模式校验
- ✅ 跳过时长校验（时长不足）
- ✅ 跳过时长校验（时长超限）

#### 边界情况
- ✅ 混合有效和无效duration的文件

## 数据结构依赖

### FileInfo 接口
```typescript
interface FileInfo {
  name: string;
  url: string;
  status: FileStatus;
  duration?: number; // 视频时长，单位为秒
  mediaType?: MediaType;
  // ... 其他字段
}
```

### 时长数据来源
- 视频文件上传后，后端返回的 `extra.duration` 字段
- 通过 `convertMaterialFileToFileInfo` 函数转换到 `FileInfo.duration`

## 与现有架构的集成

### 1. 校验触发器
使用 `VALIDATION_TRIGGER.MAGIC` 确保与现有校验体系兼容

### 2. 差异化校验
完全兼容现有的差异化校验机制，无需修改其他代码

### 3. 错误显示
校验错误会自动显示在对应表单项下方，与其他校验错误保持一致

## 使用示例

### 用户操作流程
1. 用户在视频项目的第一步中上传视频文件
2. 点击【生成并预览】按钮
3. 系统自动计算所有视频文件的总时长
4. 如果时长不符合要求，显示相应错误提示
5. 用户根据提示调整视频素材
6. 重新校验直到通过

### 开发者使用
无需额外配置，功能会自动应用到所有视频类型的文件表单项。

## 注意事项

1. **仅对视频类型生效**：校验规则只会添加到 `mediaType === MEDIA_TYPES.VIDEO` 的表单项
2. **依赖时长数据**：需要确保视频文件上传后包含有效的 `duration` 字段
3. **与必填校验配合**：时长校验不处理空文件列表，由必填校验负责
4. **性能考虑**：时长计算是轻量级操作，不会影响表单性能

## 后续扩展

如需调整时长限制或错误提示，可以：
1. 将时长常量提取到配置文件
2. 支持不同项目类型的不同时长限制
3. 添加更详细的时长统计信息显示
