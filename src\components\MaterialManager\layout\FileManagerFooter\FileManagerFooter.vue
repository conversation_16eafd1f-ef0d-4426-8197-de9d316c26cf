<template>
  <FaFileManagerFooter
    :value="page"
    :total="filesTotal"
    :page-size.sync="pageSize"
    :page-size-options="['50', '100', '150', '200']"
    @change="handleChange"
    @page-change="handlePageChange"
    @size-change="handleSizeChange"
  >
    <template #text>全选本页</template>
    <template #default>
      <fa-config-provider :auto-insert-space-in-button="false">
        <fa-button
          class="notCancelSelect ml12"
          size="small"
          :disabled="!selectedLength"
          @click="handleClick(category === 'file' ? 'move' : 'restore')"
        >
          {{ category === 'file' ? '移动' : '还原' }}
        </fa-button>
      </fa-config-provider>
      <fa-config-provider :auto-insert-space-in-button="false">
        <fa-button
          class="notCancelSelect ml12"
          :ghost="category !== 'file'"
          :type="category === 'file' ? 'default' : 'danger'"
          :disabled="!selectedLength"
          size="small"
          @click="handleClick('multipleDelete')"
        >
          {{ category === 'file' ? '删除' : '彻底删除' }}
        </fa-button>
      </fa-config-provider>
    </template>
  </FaFileManagerFooter>
</template>

<script>
import {
  Button as FaButton,
  ConfigProvider as FaConfigProvider,
} from '@fk/faicomponent';
import { FileManager as FaFileManager } from '@fk/fa-component-cus';
import store from '@/store';

export default {
  name: 'FileManagerFooter',
  components: {
    FaButton,
    FaConfigProvider,
    FaFileManagerFooter: FaFileManager.FileManagerFooter,
  },

  data() {
    return {};
  },

  computed: {
    page() {
      return store.state.meta.page;
    },
    pageSize() {
      return store.state.meta.pageSize;
    },
    filesTotal() {
      return store.state.meta.filesTotal;
    },
    category() {
      return store.state.meta.category;
    },
    selects() {
      return store.getters.selects;
    },
    selectedLength() {
      const selected = this.selects.filter(item => item === true) || [];
      return selected.length;
    },
  },

  methods: {
    handleChange(checked) {
      store.commit('changeAllFilesSelect', checked);
    },
    updateFolderContent() {
      if (store.getters.isSearchStatus) {
        store.dispatch('updateFolderContent');
      } else {
        store.dispatch('updateFolderContent');
      }
    },

    handlePageChange(page) {
      store.commit('setPage', page);
      this.updateFolderContent();
    },

    handleSizeChange(pageSize) {
      store.commit('setPageSize', pageSize);
      store.commit('setPage', 1); // 切换每页条数时，重置为第一页
      this.updateFolderContent();
    },

    handleClick(type) {
      if (type === 'move') {
        store.commit('setMoveModal', true);
      } else {
        type === 'multipleDelete' ? store.dispatch(type) : store.commit(type);
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.ml12 {
  margin-left: 12px;
}
</style>
