import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { nextTick } from 'vue';
import { useUnsavedChanges } from '../composables/useUnsavedChanges';

// Mock lodash-es
vi.mock('lodash-es', () => ({
  debounce: vi.fn(fn => {
    const debouncedFn = (...args: any[]) => fn(...args);
    debouncedFn.cancel = vi.fn();
    return debouncedFn;
  }),
}));

describe('useUnsavedChanges', () => {
  let mockAddEventListener: ReturnType<typeof vi.fn>;
  let mockRemoveEventListener: ReturnType<typeof vi.fn>;

  beforeEach(() => {
    // Mock window.addEventListener and removeEventListener
    mockAddEventListener = vi.fn();
    mockRemoveEventListener = vi.fn();

    Object.defineProperty(window, 'addEventListener', {
      value: mockAddEventListener,
      writable: true,
    });

    Object.defineProperty(window, 'removeEventListener', {
      value: mockRemoveEventListener,
      writable: true,
    });

    // Mock document.addEventListener and removeEventListener
    vi.spyOn(document, 'addEventListener').mockImplementation(() => {});
    vi.spyOn(document, 'removeEventListener').mockImplementation(() => {});
  });

  afterEach(() => {
    vi.clearAllMocks();
    vi.restoreAllMocks();
  });

  it('应该正确初始化未保存状态', () => {
    const { hasUnsavedChanges, isInitialized } = useUnsavedChanges();

    expect(hasUnsavedChanges.value).toBe(false);
    expect(isInitialized.value).toBe(false);
  });

  it('应该正确设置未保存状态', () => {
    const { hasUnsavedChanges, setUnsavedChanges } = useUnsavedChanges();

    setUnsavedChanges(true);
    expect(hasUnsavedChanges.value).toBe(true);

    setUnsavedChanges(false);
    expect(hasUnsavedChanges.value).toBe(false);
  });

  it('应该正确重置未保存状态', () => {
    const { hasUnsavedChanges, setUnsavedChanges, resetUnsavedChanges } =
      useUnsavedChanges();

    // 先设置为true
    setUnsavedChanges(true);
    expect(hasUnsavedChanges.value).toBe(true);

    // 重置
    resetUnsavedChanges();
    expect(hasUnsavedChanges.value).toBe(false);
  });

  it('应该在重置后进入静默期', async () => {
    const { hasUnsavedChanges, setUnsavedChanges, resetUnsavedChanges } =
      useUnsavedChanges();

    // 先设置为true
    setUnsavedChanges(true);
    expect(hasUnsavedChanges.value).toBe(true);

    // 重置，进入静默期
    resetUnsavedChanges();
    expect(hasUnsavedChanges.value).toBe(false);

    // 等待静默期结束
    await new Promise(resolve => setTimeout(resolve, 1100));

    // 验证静默期结束后可以正常设置状态
    setUnsavedChanges(true);
    expect(hasUnsavedChanges.value).toBe(true);
  });

  it('应该在初始化时注册事件监听器', async () => {
    const { initializeUnsavedChanges, isInitialized } = useUnsavedChanges();

    await nextTick();
    initializeUnsavedChanges();

    expect(isInitialized.value).toBe(true);
    expect(mockAddEventListener).toHaveBeenCalledWith(
      'beforeunload',
      expect.any(Function),
    );
    expect(document.addEventListener).toHaveBeenCalledWith(
      'input',
      expect.any(Function),
      true,
    );
    expect(document.addEventListener).toHaveBeenCalledWith(
      'change',
      expect.any(Function),
      true,
    );
    expect(document.addEventListener).toHaveBeenCalledWith(
      'click',
      expect.any(Function),
      true,
    );
  });

  it('应该在清理时移除事件监听器', async () => {
    const { initializeUnsavedChanges, cleanupUnsavedChanges, isInitialized } =
      useUnsavedChanges();

    // 先初始化
    await nextTick();
    initializeUnsavedChanges();
    expect(isInitialized.value).toBe(true);

    // 清理
    cleanupUnsavedChanges();
    expect(isInitialized.value).toBe(false);
    expect(mockRemoveEventListener).toHaveBeenCalledWith(
      'beforeunload',
      expect.any(Function),
    );
    expect(document.removeEventListener).toHaveBeenCalledWith(
      'input',
      expect.any(Function),
      true,
    );
    expect(document.removeEventListener).toHaveBeenCalledWith(
      'change',
      expect.any(Function),
      true,
    );
    expect(document.removeEventListener).toHaveBeenCalledWith(
      'click',
      expect.any(Function),
      true,
    );
  });

  it('应该防止重复初始化', async () => {
    const { initializeUnsavedChanges, isInitialized } = useUnsavedChanges();

    await nextTick();
    initializeUnsavedChanges();
    expect(isInitialized.value).toBe(true);

    // 清除之前的调用记录
    mockAddEventListener.mockClear();

    // 再次初始化
    initializeUnsavedChanges();

    // 不应该再次注册事件监听器
    expect(mockAddEventListener).not.toHaveBeenCalled();
  });

  it('beforeunload 处理函数应该在有未保存更改时返回确认消息', () => {
    const { setUnsavedChanges } = useUnsavedChanges();

    // 模拟 beforeunload 事件
    const mockEvent = {
      preventDefault: vi.fn(),
      returnValue: '',
    } as any;

    setUnsavedChanges(true);

    // 获取注册的 beforeunload 处理函数
    const beforeUnloadHandler = mockAddEventListener.mock.calls.find(
      call => call[0] === 'beforeunload',
    )?.[1];

    if (beforeUnloadHandler) {
      const result = beforeUnloadHandler(mockEvent);
      expect(mockEvent.preventDefault).toHaveBeenCalled();
      expect(mockEvent.returnValue).toBe('系统可能不会保存您所做的更改');
      expect(result).toBe('系统可能不会保存您所做的更改');
    }
  });

  it('beforeunload 处理函数应该在没有未保存更改时不返回确认消息', () => {
    const { hasUnsavedChanges } = useUnsavedChanges();

    // 模拟 beforeunload 事件
    const mockEvent = {
      preventDefault: vi.fn(),
      returnValue: '',
    } as any;

    // 确保没有未保存的更改
    expect(hasUnsavedChanges.value).toBe(false);

    // 获取注册的 beforeunload 处理函数
    const beforeUnloadHandler = mockAddEventListener.mock.calls.find(
      call => call[0] === 'beforeunload',
    )?.[1];

    if (beforeUnloadHandler) {
      const result = beforeUnloadHandler(mockEvent);
      expect(mockEvent.preventDefault).not.toHaveBeenCalled();
      expect(result).toBeUndefined();
    }
  });

  it('应该正确处理 KeepAlive 场景的重新激活', () => {
    const { reactivateUnsavedChanges, isInitialized } = useUnsavedChanges();

    // 初始状态应该是未初始化
    expect(isInitialized.value).toBe(false);

    // 调用重新激活
    reactivateUnsavedChanges();

    // 应该已经初始化
    expect(isInitialized.value).toBe(true);
    expect(mockAddEventListener).toHaveBeenCalledWith(
      'beforeunload',
      expect.any(Function),
    );
  });
});
