import { getFontList } from '@/api/VideoEditor/sticker';
import { Font } from '@/types';
import { ref } from 'vue';

const fontList = ref<Font[]>([]);
const fontMap = ref<Map<string, Font>>(new Map());
let loaded = false;

export const useFontInfo = () => {
  if (!loaded) {
    loaded = true;
    getFontList().then(([err, res]) => {
      if (err) {
        console.error('Failed to fetch font list:', err);
        loaded = false;
        return;
      }
      fontList.value = res.data;
      fontMap.value = new Map(res.data.map(font => [font.fontFamily, font]));
    });
  }
  return {
    fontList,
    fontMap,
  };
};
