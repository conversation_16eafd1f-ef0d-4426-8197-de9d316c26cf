import { ref, computed, Ref } from 'vue';

interface UsePlaybackStateOptions {
  /** 是否可以显示视频 */
  canShowVideo: Ref<boolean>;
  /** 播放器是否已初始化 */
  isInitialized: Ref<boolean>;
  /** 是否处于错误状态 */
  isError: Ref<boolean>;
}

/**
 * 播放状态管理 hook
 * @description 管理视频播放的各种状态，如播放/暂停、时间、进度等
 */
export function usePlaybackState({
  canShowVideo,
  isInitialized,
  isError,
}: UsePlaybackStateOptions) {
  // 播放状态
  const isPlaying = ref(false);
  const currentTime = ref(0);
  const duration = ref(0);
  const bufferedPercent = ref(0);
  const playedPercent = ref(0);

  /**
   * 是否禁用控制
   * @description 在以下情况下禁用控制：
   * 1. 无法显示视频（包括生成中和重新生成中的状态）
   * 2. 播放器未初始化
   * 3. 播放器处于错误状态
   */
  const isControlDisabled = computed(() => {
    return !canShowVideo.value || !isInitialized.value || isError.value;
  });

  /**
   * 重置所有播放状态
   * @description 将所有状态重置为初始值
   */
  const resetState = () => {
    isPlaying.value = false;
    currentTime.value = 0;
    duration.value = 0;
    bufferedPercent.value = 0;
    playedPercent.value = 0;
  };

  return {
    // 状态
    isPlaying,
    currentTime,
    duration,
    bufferedPercent,
    playedPercent,
    isControlDisabled,
    // 方法
    resetState,
  };
}

// 导出类型
export interface PlaybackState {
  isPlaying: Ref<boolean>;
  currentTime: Ref<number>;
  duration: Ref<number>;
  bufferedPercent: Ref<number>;
  playedPercent: Ref<number>;
  isControlDisabled: Ref<boolean>;
  resetState: () => void;
}
