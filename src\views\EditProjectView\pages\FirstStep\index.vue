<template>
  <div class="first-step">
    <!-- 加载状态 -->
    <fa-spin v-if="loading" class="first-step__loading" size="large" />

    <template v-else>
      <!-- 基础信息 -->
      <div class="first-step__section first-step__section--left">
        <div class="w-[500px]">
          <div class="first-step__header">
            <div class="first-step__header-left">
              <Icon type="bianji" class="w-[20px] h-[20px]" />
              <span class="first-step__title">{{
                firstStepHeaderLeftTitleCal
              }}</span>
            </div>
            <div class="first-step__header-right" v-if="hasExampleScript">
              <!-- 示例脚本 -->
              <fa-popover
                placement="bottom"
                trigger="hover"
                overlayClassName="first-step__example-popover"
              >
                <template slot="content">
                  <div class="first-step__example-content">
                    <FaScrollArea
                      class="first-step__example-scroll"
                      max-height="400px"
                      :thumb-style="thumbStyle"
                      :bar-style="barStyle"
                    >
                      <div
                        class="first-step__example-box"
                        v-html="exampleScriptContent"
                      ></div>
                    </FaScrollArea>
                  </div>
                </template>
                <div class="flex items-center">
                  <Icon type="zhili" class="w-[20px] h-[20px]" />
                  <span class="first-step__subtitle">示例脚本</span>
                </div>
              </fa-popover>
            </div>
          </div>
          <DynamicForm
            class="first-step__form"
            ref="dynamicForm"
            :form-items="formItems"
            :initial-values="initialValues"
            :form-config="formConfig"
          >
          </DynamicForm>
        </div>

        <!-- 背景音乐 -->
        <div
          class="first-step__section-item w-[500px]"
          v-if="type === PROJECT_TYPE_VIDEO"
        >
          <div class="first-step__section-content">
            <div
              class="first-step__card bgm-card"
              @click="handleShowBgMusicSelector"
            >
              <div class="bgm-card__left" @click.stop>
                <fa-switch
                  v-model="bgMusicSettings.open"
                  size="small"
                  @change="handleBgMusicSwitchChange"
                />
              </div>
              <div class="bgm-card__content">
                <span v-if="!bgMusicSettings.open" class="bgm-card__name"
                  ><span class="bgm-card__title">背景音乐：</span
                  >{{
                    bgMusicSettings.useAiRecommend
                      ? '智能推荐'
                      : bgMusicInfo.map(item => item.name).join('、') ||
                        '未选择'
                  }}</span
                >
                <span
                  v-else-if="bgMusicSettings.useAiRecommend"
                  class="bgm-card__name"
                  ><span class="bgm-card__title">背景音乐：</span>智能推荐</span
                >
                <template v-else>
                  <template v-if="bgMusicInfo.length > 0">
                    <div class="bgm-card__name">
                      <span class="bgm-card__title">背景音乐：</span
                      >{{
                        bgMusicInfo.map(item => item.name).join('、') ||
                        '未选择'
                      }}
                    </div>
                  </template>
                  <template v-else>
                    <div class="bgm-card__empty">
                      <span class="bgm-card__title">背景音乐：</span>未选择
                    </div>
                  </template>
                </template>
              </div>
              <div class="bgm-card__right">
                <Icon
                  type="jiantou_you"
                  class="w-[10px] h-[10px] text-[#999]"
                />
              </div>
            </div>
          </div>
        </div>

        <!-- 配音 -->
        <div
          class="first-step__section-item w-[500px]"
          v-if="type === PROJECT_TYPE_VIDEO"
        >
          <div class="first-step__section-content">
            <div
              class="first-step__card voice-card"
              @click="showDubbingSelector"
            >
              <div class="voice-card__content">
                <span
                  v-if="voiceSettings.useAiRecommend"
                  class="voice-card__name"
                  ><span class="voice-card__title">配音：</span>智能推荐</span
                >
                <span
                  v-else-if="voiceInfo && voiceInfo.name"
                  class="voice-card__name"
                  ><span class="voice-card__title">配音：</span
                  >{{ voiceInfo.name }}</span
                >
                <span v-else class="voice-card__name">
                  <span class="voice-card__title">配音：</span>未选择
                </span>
              </div>
              <div class="voice-card__right">
                <Icon
                  type="jiantou_you"
                  class="w-[10px] h-[10px] text-[#999]"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
      <fa-divider class="first-step__divider" type="vertical" />
      <!-- 文件上传 -->
      <div class="first-step__section first-step__section--right">
        <div class="first-step__header">
          <div class="first-step__header-left">
            <Icon type="shipinsucai" class="w-[20px] h-[20px]" />
            <span class="first-step__title">{{
              firstStepHeaderRightTitleCal
            }}</span>
          </div>
        </div>
        <DynamicForm
          class="first-step__form"
          ref="dynamicFormFiles"
          :form-items="fileFormItems"
          :initial-values="fileInitialValues"
          :form-config="formConfig"
          @upload-click="handleUploadClickEvent"
          @file-delete="handleFileDeleteEvent"
          @replace="handleReplaceEvent"
        >
        </DynamicForm>
      </div>
    </template>

    <!-- 音乐资源弹窗 -->
    <BgMusicSelector
      v-if="type === PROJECT_TYPE_VIDEO"
      v-model="isShowBgMusicSelector"
      :resIds="bgMusicSettings.resIds"
      :max-select-count="10"
      :can-use-ai-recommend="true"
      :use-ai-recommend-value.sync="bgMusicSettings.useAiRecommend"
      :refresh-on-open="true"
      @changeMusicInfo="handleChangeMusicInfo"
    />

    <!-- 配音弹窗 -->
    <DubbingSelector
      v-if="type === PROJECT_TYPE_VIDEO"
      v-model="isShowDubbingSelector"
      :voiceId="voiceSettings.voiceId"
      :can-use-ai-recommend="true"
      :use-ai-recommend-value.sync="voiceSettings.useAiRecommend"
      @changeDubbingInfo="handleChangeDubbingInfo"
    />
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, computed, watch } from 'vue';
import DynamicForm from '@/components/DynamicForm/DynamicForm.vue';
import {
  PROJECT_TYPE_VIDEO,
  PROJECT_TYPE_IMAGE,
} from '@/views/EditProjectView/constants/index';
import type {
  ProjectType,
  EditProjectViewData,
  ValidationResult,
} from '@/views/EditProjectView/types/index';
import {
  useForm,
  useFileUpload,
  useScriptContent,
  useBgMusic,
  useDubbing,
} from './composables';
import type {
  DynamicFileFormInstance,
  FormEventParams,
  DynamicFormMethods,
} from '@/views/EditProjectView/types/index';
import BgMusicSelector from '@/components/VideoEditor/MusicEditor/BgMusicSelector.vue';
import DubbingSelector from '@/components/VideoEditor/MusicEditor/DubbingSelector.vue';
import { getDefaultScrollAreaStyles } from '@/constants/scrollArea';
import { useDataProtection } from '@/composables/useDataProtection';

/**
 * @description FirstStep 组件 - 项目编辑第一步
 */
export default defineComponent({
  name: 'FirstStep',
  components: { DynamicForm, BgMusicSelector, DubbingSelector },

  props: {
    // 类型（视频：0，图文：1）
    type: {
      type: Number as () => ProjectType,
      default: PROJECT_TYPE_VIDEO,
    },
    // 表单数据
    formData: {
      type: Object as () => EditProjectViewData | null,
      default: () => null,
    },
  },

  setup(props) {
    // 表单引用，使用严格的类型约束
    const dynamicForm = ref<DynamicFormMethods | null>(null);
    const dynamicFormFiles = ref<DynamicFileFormInstance | null>(null);

    // 使用composables
    const {
      formConfig,
      initialValues,
      formItems,
      fileInitialValues,
      fileFormItems,
      loading,
      resetAllForms,
      validateAllForms,
      validateAllFormsForSave,
      processFormConfig,
    } = useForm(props.type);

    const { handleUploadClick, handleFileDelete, handleReplace } =
      useFileUpload(fileFormItems);

    const { exampleScriptContent, hasExampleScript, processScriptContent } =
      useScriptContent();

    // 使用背景音乐和配音composables
    const {
      bgMusicSettings,
      bgMusicInfo,
      isShowBgMusicSelector,
      showBgMusicSelector,
      handleChangeMusicInfo,
      initBgMusicInfo,
      handleBgMusicSwitchChange,
      setOnDataChange: setBgMusicDataChange,
    } = useBgMusic(props.formData);

    const {
      voiceSettings,
      voiceInfo,
      isShowDubbingSelector,
      showDubbingSelector,
      handleChangeDubbingInfo,
      initVoiceInfo,
    } = useDubbing(props.formData);

    // 使用统一的数据保护功能
    const dataProtection = useDataProtection({
      stepIndex: 0, // 第一步的索引
      enableBrowserProtection: true, // 启用浏览器刷新/关闭保护
      enableExitProtection: true, // 启用退出确认保护
    });

    // 从数据保护实例中解构需要的方法和状态
    const {
      hasUnsavedChanges,
      resetUnsavedChanges,
      setUnsavedChanges,
      reactivate: reactivateUnsavedChanges,
    } = dataProtection;

    // 获取滚动区域样式配置
    const { barStyle, thumbStyle } = getDefaultScrollAreaStyles({
      thumbStyle: { right: '0px', backgroundColor: '#bbb' },
    });

    const handleShowBgMusicSelector = () => {
      bgMusicSettings.value.open = true;
      showBgMusicSelector();
    };

    // 事件处理函数 - 由于需要传递ref，这里做一次包装
    const handleUploadClickEvent = (params: FormEventParams['upload']) => {
      if (dynamicFormFiles.value) {
        handleUploadClick(params, dynamicFormFiles.value);
      }
    };

    const handleFileDeleteEvent = (params: FormEventParams['fileDelete']) => {
      if (dynamicFormFiles.value) {
        handleFileDelete(params, dynamicFormFiles.value);
      }
    };

    const handleReplaceEvent = (params: FormEventParams['replace']) => {
      if (dynamicFormFiles.value) {
        handleReplace(params, dynamicFormFiles.value);
      }
    };

    // 获取所有表单数据，包括背景音乐和配音设置
    const getAllFormDataWithSettings = () => {
      if (!dynamicForm.value || !dynamicFormFiles.value) {
        throw new Error('表单引用不存在');
      }

      const basicFormData = dynamicForm.value.getFormData();
      const filesFormData = dynamicFormFiles.value.getFormData();

      return {
        basicFormData,
        filesFormData,
        bgMusic: bgMusicSettings.value,
        voice: voiceSettings.value,
      };
    };

    // 验证所有表单的方法（生成模式：完整校验）
    const validateAllFormsWithRef = async (): Promise<ValidationResult> => {
      if (!dynamicForm.value || !dynamicFormFiles.value) {
        return {
          valid: false,
          data: {},
        };
      }

      return validateAllForms(dynamicForm.value, dynamicFormFiles.value);
    };

    // 验证所有表单的方法（保存模式：仅最大长度校验）
    const validateAllFormsForSaveWithRef =
      async (): Promise<ValidationResult> => {
        if (!dynamicForm.value || !dynamicFormFiles.value) {
          return {
            valid: false,
            data: {},
          };
        }

        // 先清除校验错误信息
        dynamicForm.value.clearValidate();
        dynamicFormFiles.value.clearValidate();

        return validateAllFormsForSave(
          dynamicForm.value,
          dynamicFormFiles.value,
        );
      };

    // 重置所有表单的方法
    const resetAllFormsWithRef = () => {
      if (dynamicForm.value && dynamicFormFiles.value) {
        resetAllForms(dynamicForm.value, dynamicFormFiles.value);
      }
    };

    // 计算属性
    const firstStepHeaderLeftTitleCal = computed(() => {
      return props.type === PROJECT_TYPE_VIDEO ? '帮我写脚本' : '基本信息';
    });

    const firstStepHeaderRightTitleCal = computed(() => {
      return props.type === PROJECT_TYPE_VIDEO
        ? '上传视频素材'
        : props.type === PROJECT_TYPE_IMAGE
        ? '上传图片素材'
        : '';
    });

    // 设置背景音乐数据变更回调
    setBgMusicDataChange(setUnsavedChanges);

    // 监听formData变化，处理表单配置和数据
    watch(
      () => props.formData,
      async newVal => {
        if (newVal) {
          loading.value = true;
          // 处理动态表单配置和数据
          processFormConfig(newVal);
          // 处理示例脚本内容
          processScriptContent(newVal.script, props.type);
          // 初始化背景音乐和配音设置
          await initBgMusicInfo();
          await initVoiceInfo();
          loading.value = false;
        }
      },
      { immediate: true }, // 组件创建时立即执行一次
    );

    return {
      // 常量
      PROJECT_TYPE_VIDEO,
      PROJECT_TYPE_IMAGE,

      // refs
      dynamicForm,
      dynamicFormFiles,

      // 状态
      loading,
      formConfig,
      formItems,
      fileFormItems,
      initialValues,
      fileInitialValues,
      exampleScriptContent,
      hasExampleScript,

      // 背景音乐相关
      bgMusicSettings,
      bgMusicInfo,
      isShowBgMusicSelector,
      showBgMusicSelector,
      handleShowBgMusicSelector,
      handleChangeMusicInfo,
      handleBgMusicSwitchChange,

      // 配音相关
      voiceSettings,
      voiceInfo,
      isShowDubbingSelector,
      showDubbingSelector,
      handleChangeDubbingInfo,

      // 计算属性
      firstStepHeaderLeftTitleCal,
      firstStepHeaderRightTitleCal,

      // 事件处理
      handleUploadClickEvent,
      handleFileDeleteEvent,
      handleReplaceEvent,

      // 对外方法，使用更严格的类型约束
      validateAllForms: validateAllFormsWithRef,
      validateAllFormsForSave: validateAllFormsForSaveWithRef,
      getAllFormData: getAllFormDataWithSettings,
      resetAllForms: resetAllFormsWithRef,

      // 数据保护相关
      hasUnsavedChanges,
      resetUnsavedChanges,
      setUnsavedChanges,
      reactivateUnsavedChanges,

      // 滚动区域样式
      barStyle,
      thumbStyle,
    };
  },
});
</script>

<style lang="scss" scoped>
.first-step {
  /* 布局相关 */
  @apply flex justify-between self-start;
  /* 尺寸相关 */
  @apply p-32px w-full;
}

.first-step__section {
  /* 布局相关 */
  @apply flex-1;
  /* 尺寸相关 */
  @apply w-[calc(50%-44px)];
}

.first-step__section--left {
  /* 布局相关 */
  @apply flex flex-col items-end;
}

.first-step__section--right {
  /* 布局相关 */
  @apply flex flex-col;

  & :deep(.fa-form-item-label) {
    /* 文字相关 */
    @apply font-medium pb-4px;
  }
}

.first-step__section--left .first-step__header,
.first-step__section--right .first-step__header {
  /* 布局相关 */
  @apply flex justify-between items-center;
  /* 尺寸相关 */
  @apply pb-20px;
}

.first-step__header-left {
  /* 布局相关 */
  @apply flex items-center;
  /* 文字相关 */
  @apply text-[#999];
}

.first-step__header-right {
  /* 布局相关 */
  @apply flex items-center;
  /* 文字相关 */
  @apply text-[#999];
  /* 交互相关 */
  @apply cursor-pointer;
  &:hover {
    /* 文字相关 */
    @apply text-[#666] transition-colors duration-300;
  }
}

.first-step__title {
  /* 布局相关 */
  @apply ml-4px;
  /* 文字相关 */
  @apply font-bold text-16px text-left text-[#111];
}

.first-step__subtitle {
  /* 布局相关 */
  @apply ml-4px;
  /* 文字相关 */
  @apply font-normal text-14px text-right;
}

.first-step__form {
  /* 深度选择器样式 */
  & :deep(.fa-form-item-extra) {
    /* 布局相关 */
    @apply mt-4px;
    /* 文字相关 */
    @apply text-[#666] text-12px;
  }

  & :deep(.fa-form-item) {
    /* 布局相关 */
    /* 需要减去公司输入框的padding-bottom: 8px */
    @apply mb-[16px];
  }

  & :deep(.fa-form-item-group-title) {
    /* 布局相关 */
    @apply mb-16px pb-8px;
    /* 文字相关 */
    @apply text-16px font-medium text-[#333];
    /* 外观相关 */
    @apply border-b border-solid border-[#f0f0f0];
  }

  & :deep(.fa-form-item-group-description) {
    /* 布局相关 */
    @apply mb-16px;
    /* 文字相关 */
    @apply text-[#666] text-13px;
  }
}

.first-step__divider {
  /* 布局相关 */
  @apply mx-88px h-auto self-stretch;
}

.first-step__loading {
  /* 布局相关 */
  @apply absolute top-1/2 left-1/2;
  /* 变换相关 */
  @apply transform -translate-x-1/2 -translate-y-1/2;
}

/* 背景音乐和配音设置样式 */
.first-step__section-item {
  /* 布局相关 */
  @apply mb-16px;
}

.first-step__card {
  /* 布局相关 */
  @apply flex items-center;
  /* 尺寸相关 */
  @apply p-12px;
  /* 外观相关 */
  @apply rounded-6px bg-[#F3F3F5] transition-colors duration-300;
  /* 交互相关 */
  @apply cursor-pointer;

  &:hover {
    /* 外观相关 */
    @apply bg-[#E8E8E8];
  }
}

/* 音频控件通用样式 */
.bgm-card,
.voice-card {
  /* 布局相关 */
  @apply flex items-center justify-between;
}

.bgm-card__left,
.voice-card__left {
  /* 布局相关 */
  @apply flex-shrink-0 mr-12px flex items-center;
}

.bgm-card__content,
.voice-card__content {
  /* 布局相关 */
  @apply flex-1;
  /* 内容限制 */
  @apply overflow-hidden;
}

.bgm-card__right,
.voice-card__right {
  /* 布局相关 */
  @apply flex-shrink-0 ml-8px;
}

.bgm-card__title,
.voice-card__title {
  /* 布局相关 */
  @apply ml-2px mr-6px;
  /* 文字相关 */
  @apply text-14px text-[#999] truncate;
}

.bgm-card__name,
.voice-card__name {
  /* 布局相关 */
  @apply block;
  /* 文字相关 */
  @apply text-14px font-medium text-[#333] truncate;
}

.bgm-card__empty {
  /* 布局相关 */
  @apply block;
  /* 文字相关 */
  @apply text-14px text-[#999] truncate;
}
</style>

<style lang="scss">
/* 示例脚本弹窗样式 */
.first-step__example-popover {
  & .fa-popover-inner-content {
    /* 尺寸相关 */
    @apply p-0;
  }

  & .first-step__example-content {
    /* 尺寸相关 */
    @apply w-[480px] max-h-[432px] p-16px;
    /* 外观相关 */
    @apply rounded-8px bg-white shadow-[0_3px_20px_#0000001a];
  }

  & .first-step__example-scroll {
    /* 尺寸相关 */
    @apply max-h-[400px];
  }

  & .first-step__example-box {
    /* 尺寸相关 */
    @apply p-12px;
    /* 外观相关 */
    @apply rounded-8px bg-[#f3f3f5];
    /* 文字相关 */
    @apply text-14px leading-[28px] text-[#333];
  }

  & .script-example-section {
    /* 布局相关 */
    @apply mb-16px;

    &:last-child {
      /* 布局相关 */
      @apply mb-0;
    }
  }

  & .script-example-section-title {
    /* 布局相关 */
    @apply mb-8px;
    /* 文字相关 */
    @apply text-14px font-medium text-[#333];
  }

  & .script-example-section-content {
    /* 文字相关 */
    @apply text-14px leading-[1.6] text-[#333];
  }

  /* 标签样式 */
  & .script-example-tag {
    /* 尺寸相关 */
    @apply p-[2px_4px] mx-1px;
    /* 文字相关 */
    @apply text-[#3261fd];
  }

  /* 标签标签样式 */
  & .script-example-tag-label {
    /* 布局相关 */
    @apply inline-block mx-2px mt-[-3px] align-middle;
    /* 尺寸相关 */
    @apply p-[0_4px];
    /* 外观相关 */
    @apply rounded-4px bg-[#fafafa] border border-solid border-[#d9d9d9];
    /* 文字相关 */
    @apply leading-[20px] text-12px text-[#666];
  }
}
</style>
