/**
 * @fileoverview EditProjectView 模块配置文件
 * @description 集中管理常量、默认值和配置项
 */

/**
 * 错误消息常量
 */
export const ERROR_MESSAGES = {
  /** 模板数据获取失败 */
  TEMPLATE_FETCH_FAILED: '获取模板数据失败',
  /** 模板数据为空 */
  TEMPLATE_DATA_EMPTY: '模板数据为空，请检查模板ID是否正确',
  /** 项目数据获取失败 */
  PROJECT_FETCH_FAILED: '获取项目编辑数据失败，将使用默认配置',
  /** 项目状态检查失败 */
  PROJECT_STATUS_CHECK_FAILED: '项目状态检查失败',
} as const;

/**
 * 状态名称映射
 */
export const STATUS_NAMES = {
  GENERATING: '生成中',
  TO_BE_SAVED: '待保存',
  UNKNOWN: '未知状态',
} as const;

/**
 * 过滤配置
 */
export const FILTER_CONFIG = {
  /** 需要从 inputFormList 中过滤掉的 variable 值 */
  EXCLUDED_INPUT_VARIABLES: ['output_quantity'] as string[],
};

/**
 * 默认配置
 */
export const DEFAULT_CONFIG = {
  /** 默认背景音乐设置 */
  BG_MUSIC: {
    open: false,
    useAiRecommend: false,
    resIds: [],
    name: '',
    vol: 75,
  },
  /** 默认配音设置 */
  VOICE: {
    open: false,
    useAiRecommend: true,
    voiceId: '',
    voiceName: '',
    speed: 1.0,
    extraType: undefined,
  },
} as const;
