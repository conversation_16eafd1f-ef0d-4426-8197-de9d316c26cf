/**
 * @fileoverview 事件处理器注册工具函数
 * @description 提供事件处理器的注册和管理功能
 */

import { WorkItem } from '@/types';
import { WorkEventManager } from '../core';
import { WORK_EVENT_TYPE } from '../constants';
import type { UseWorkPollingOptions } from '../types';

/**
 * 注册事件处理器
 * @param eventManager 事件管理器实例
 * @param eventHandlers 事件处理器配置
 */
export function registerEventHandlers(
  eventManager: WorkEventManager,
  eventHandlers: UseWorkPollingOptions<WorkItem>['eventHandlers'],
): void {
  // 注册核心事件处理器
  eventManager.on(WORK_EVENT_TYPE.DATA_UPDATED, eventHandlers.onDataUpdated);
  eventManager.on(
    WORK_EVENT_TYPE.PROGRESS_CHANGED,
    eventHandlers.onProgressChanged,
  );

  // 注册状态变化事件处理器（用于处理完成和失败逻辑）
  eventManager.on(
    WORK_EVENT_TYPE.STATUS_CHANGED,
    eventHandlers.onStatusChanged,
  );
}
