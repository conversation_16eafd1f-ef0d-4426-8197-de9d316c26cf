/**
 * @fileoverview 智能轮询状态分析器
 * @description 负责分析作品状态和进度变化，生成相应的事件
 */

import { WorkItem } from '@/types';
import {
  getWorkStatusInfo,
  isWorkStatusEqual,
  WorkStatusInfo,
} from '@/constants/workStatus';
import { WorkEventManager } from './EventManager';
import { WorkEvent, StateMap, ProgressMap } from '../types';
import { WORK_EVENT_TYPE } from '../constants';

/**
 * 状态分析器类
 * 负责分析作品状态和进度变化，生成相应的事件
 */
export class WorkStateAnalyzer {
  private eventManager: WorkEventManager;

  constructor(eventManager: WorkEventManager) {
    this.eventManager = eventManager;
  }

  /**
   * 分析作品数据变化并触发相应事件
   * @param updatedWorks 更新后的作品数据
   * @param previousStates 之前的状态映射（可选）
   * @param previousProgress 之前的进度映射（可选）
   * @param context 触发上下文
   */
  async analyzeAndEmitEvents<T extends WorkItem>(
    updatedWorks: T[],
    previousStates?: StateMap,
    previousProgress?: ProgressMap,
    context: string = '状态分析',
  ): Promise<void> {
    const timestamp = Date.now();
    const events: WorkEvent[] = [];

    // 分析每个作品的变化
    for (const work of updatedWorks) {
      const workEvents = this.analyzeWorkChanges(
        work,
        timestamp,
        context,
        previousStates,
        previousProgress,
      );
      events.push(...workEvents);
    }

    // 添加数据更新事件
    if (updatedWorks.length > 0) {
      events.push(
        this.createDataUpdatedEvent(updatedWorks, timestamp, context),
      );
    }

    // 批量触发事件
    await this.emitEvents(events);
  }

  /**
   * 分析单个作品的变化
   * @private
   */
  private analyzeWorkChanges(
    work: WorkItem,
    timestamp: number,
    context: string,
    previousStates?: StateMap,
    previousProgress?: ProgressMap,
  ): WorkEvent[] {
    const events: WorkEvent[] = [];
    const workId = work.id;
    const currentStatusInfo = getWorkStatusInfo(work);
    const currentProgress = work.progress || 0;

    // 分析状态变化
    const statusEvents = this.analyzeStatusChanges(
      workId,
      currentStatusInfo,
      timestamp,
      context,
      previousStates,
    );
    events.push(...statusEvents);

    // 分析进度变化
    const progressEvents = this.analyzeProgressChanges(
      workId,
      currentProgress,
      timestamp,
      context,
      previousProgress,
    );
    events.push(...progressEvents);

    return events;
  }

  /**
   * 分析状态变化
   * @private
   */
  private analyzeStatusChanges(
    workId: number,
    currentStatusInfo: WorkStatusInfo,
    timestamp: number,
    context: string,
    previousStates?: StateMap,
  ): WorkEvent[] {
    const events: WorkEvent[] = [];

    if (!previousStates?.has(workId)) {
      return events;
    }

    const previousStatusInfo = previousStates.get(workId)!;
    if (isWorkStatusEqual(previousStatusInfo, currentStatusInfo)) {
      return events;
    }

    // 状态变化事件（统一的状态变化处理）
    events.push({
      type: WORK_EVENT_TYPE.STATUS_CHANGED,
      workId,
      timestamp,
      context,
      previousStatusInfo,
      currentStatusInfo,
    });

    return events;
  }

  /**
   * 分析进度变化
   * @private
   */
  private analyzeProgressChanges(
    workId: number,
    currentProgress: number,
    timestamp: number,
    context: string,
    previousProgress?: ProgressMap,
  ): WorkEvent[] {
    const events: WorkEvent[] = [];

    if (!previousProgress?.has(workId)) {
      return events;
    }

    const previousProgressValue = previousProgress.get(workId)!;
    if (previousProgressValue === currentProgress) {
      return events;
    }

    events.push({
      type: WORK_EVENT_TYPE.PROGRESS_CHANGED,
      workId,
      timestamp,
      context,
      previousProgress: previousProgressValue,
      currentProgress,
    });

    return events;
  }

  /**
   * 创建数据更新事件
   * @private
   */
  private createDataUpdatedEvent(
    updatedWorks: WorkItem[],
    timestamp: number,
    context: string,
  ): WorkEvent {
    return {
      type: WORK_EVENT_TYPE.DATA_UPDATED,
      workId: 0, // 数据更新事件不特定于某个作品
      timestamp,
      context,
      updatedWorks,
      updateCount: updatedWorks.length,
    };
  }

  /**
   * 批量触发事件
   * @private
   */
  private async emitEvents(events: WorkEvent[]): Promise<void> {
    for (const event of events) {
      await this.eventManager.emit(event);
    }
  }
}
