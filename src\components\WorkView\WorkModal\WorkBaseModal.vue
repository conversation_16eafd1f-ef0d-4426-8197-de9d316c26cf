<template>
  <fa-modal
    v-model="isShowProxy"
    :width="width"
    :footer="null"
    :closable="true"
    :maskClosable="true"
    centered
    wrapClassName="work-base-modal"
    v-on="$listeners"
  >
    <Icon slot="closeIcon" type="guanbi-tancong" class="size-[12px]" />
    <slot></slot>
  </fa-modal>
</template>

<script setup lang="ts">
import { defineProps, defineEmits, computed } from 'vue';

const props = defineProps({
  isShow: {
    type: Boolean,
    default: false,
  },
  width: {
    type: Number,
    default: 900,
  },
  height: {
    type: Number,
    default: 684,
  },
});
const emit = defineEmits(['update:isShow']);

const isShowProxy = computed({
  get: () => props.isShow,
  set: (val: boolean) => emit('update:isShow', val),
});
</script>

<style lang="scss">
.work-base-modal {
  .fa-modal-content {
    @apply bg-background;
    .fa-modal-body {
      @apply p-0;
    }
    .fa-modal-close {
      @apply top-[24px] right-[24px];
      .fa-modal-close-x {
        @apply size-[12px] text-assist hover:text-title;
      }
    }
  }
  .preview-title {
    @apply mb-[8px] text-[15px] text-title font-bold;
  }
}
</style>
