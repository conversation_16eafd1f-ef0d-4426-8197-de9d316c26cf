<?xml version="1.0" encoding="UTF-8"?>
<!-- Generator: Adobe Illustrator 26.0.0, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" id="图层_1" x="0px" y="0px" viewBox="0 0 66 24" xml:space="preserve">
<linearGradient id="SVGID_1_" gradientUnits="userSpaceOnUse" x1="-45.7748" y1="12" x2="13.9146" y2="12" gradientTransform="matrix(1 0 -0.3003 1 56.2787 0)">
	<stop offset="0" style="stop-color:#039DFF"></stop>
	<stop offset="1" style="stop-color:#15ABFF"></stop>
</linearGradient>
<path fill="url(#SVGID_1_)" d="M59.9,24H10.8c-2.1,0-3.5-1.8-3.3-4L9.4,4c0.3-2.2,2.1-4,4.2-4h49.1c2.1,0,3.5,1.8,3.3,4l-1.9,16  C63.9,22.2,62,24,59.9,24z"></path>
<linearGradient id="SVGID_00000067924577244051033720000006900365617244418451_" gradientUnits="userSpaceOnUse" x1="-45.6357" y1="12" x2="-8.5772" y2="12" gradientTransform="matrix(1 0 -0.3003 1 56.2787 0)">
	<stop offset="0" style="stop-color:#13A7FF"></stop>
	<stop offset="0.732" style="stop-color:#13A7FF;stop-opacity:0"></stop>
</linearGradient>
<path opacity="0.5" fill="url(#SVGID_00000067924577244051033720000006900365617244418451_)" d="M45,24H11c-2.2,0-3.8-1.8-3.5-4  l2-16c0.3-2.2,2.3-4,4.5-4h34L45,24z"></path>
<linearGradient id="SVGID_00000100378272653637154650000009420388816942077316_" gradientUnits="userSpaceOnUse" x1="-9.5501" y1="5" x2="10.2283" y2="5" gradientTransform="matrix(1 0 -0.3003 1 56.2787 0)">
	<stop offset="0.1089" style="stop-color:#12AEFF;stop-opacity:0"></stop>
	<stop offset="0.9815" style="stop-color:#12AEFF"></stop>
</linearGradient>
<path opacity="0.4" fill="url(#SVGID_00000100378272653637154650000009420388816942077316_)" d="M65.3,10H42.8L44,0h18.7  c2.1,0,3.5,1.8,3.3,4L65.3,10z"></path>
<linearGradient id="SVGID_00000143609348509762184760000008354749216717171381_" gradientUnits="userSpaceOnUse" x1="-12.9142" y1="14" x2="36.0197" y2="14" gradientTransform="matrix(1 0 -0.124 1 22.9151 0)">
	<stop offset="0" style="stop-color:#15AAFF"></stop>
	<stop offset="0.9238" style="stop-color:#15AAFF;stop-opacity:0"></stop>
</linearGradient>
<polygon opacity="0.5" fill="url(#SVGID_00000143609348509762184760000008354749216717171381_)" points="60.6,19 7.6,19 9.1,9 62,9   "></polygon>
<linearGradient id="SVGID_00000081619561862415583950000009780433729621694882_" gradientUnits="userSpaceOnUse" x1="-43.4908" y1="20" x2="4.1815" y2="20" gradientTransform="matrix(1 0 -0.3003 1 56.2787 0)">
	<stop offset="1.300000e-07" style="stop-color:#13A7FF"></stop>
	<stop offset="1" style="stop-color:#13A7FF;stop-opacity:0"></stop>
</linearGradient>
<path opacity="0.7" fill="url(#SVGID_00000081619561862415583950000009780433729621694882_)" d="M59.9,24H10.8c-2.1,0-3.5-1.8-3.3-4  L8,16h56.6l-0.5,4C63.9,22.2,62,24,59.9,24z"></path>
<g>
	<path fill="#FFFFFF" d="M29.7,13.1c0.6,0.7,1.5,1.3,2.4,1.6c-0.4,0.3-1,0.9-1.3,1.3c-0.7-0.4-1.4-0.9-1.9-1.5l-0.2,1h-2.5L26,16.5   h4.6l-0.2,1.4H19.6l0.2-1.4h4.4l0.2-0.9H22l0.2-0.9c-0.8,0.6-1.6,1.1-2.5,1.4c-0.2-0.4-0.5-1-0.8-1.3c1.1-0.4,2.2-1,3.1-1.8h-2.7   l0.2-1.4h2.7l0.7-4.2H21l0.2-1.3h1.9l0.2-1.1H25l-0.2,1.1h4.4l0.2-1.1h1.7l-0.2,1.1h1.9l-0.2,1.3h-1.9l-0.7,4.2h2.6l-0.2,1.4H29.7z    M28.6,14.3c-0.3-0.4-0.6-0.8-0.8-1.2h-4c-0.4,0.4-0.8,0.8-1.2,1.2h2l0.2-0.9h1.7l-0.2,0.9H28.6z M23.8,11.7h4.4l0.1-0.6h-4.4   L23.8,11.7z M24.1,9.9h4.4l0.1-0.6h-4.4L24.1,9.9z M24.6,7.6l-0.1,0.6h4.4l0.1-0.6H24.6z"></path>
	<path fill="#FFFFFF" d="M34.7,5.8h5l-0.3,1.5h-1.9c-0.4,1-0.8,1.9-1.3,2.8h2.4l-1.1,6.4H35l-0.2,1h-1.4l0.8-4.4   c-0.2,0.3-0.5,0.5-0.7,0.8c0-0.4-0.1-1.5-0.2-2c1.1-1.2,1.9-2.9,2.6-4.7h-1.5L34.7,5.8z M37,11.6h-1.1l-0.6,3.5h1.1L37,11.6z    M46.1,12L45,18.2h-1.6l0.1-0.7h-5.7l1-5.4h1.7l-0.7,3.8h1.2l0.8-4.5h-2.5L40,6.5h1.5l-0.6,3.4H42l0.8-4.7h1.7l-0.8,4.7h1l0.6-3.4   h1.6l-0.9,4.8h-2.5l-0.8,4.5h1.2l0.7-3.8H46.1z"></path>
	<path fill="#FFFFFF" d="M52.5,11.9l-1.1,6.3h-1.5l0.9-4.9h-1.3c-0.4,1.8-1.1,3.7-2.1,5c-0.2-0.3-0.7-0.8-1-1c1.1-1.7,1.7-4.4,2-6.3   l1-5.4h1.5l-0.6,3.3h1l0.6-3.7h1.5l-0.6,3.7h1l-0.3,1.5h-3.4l-0.1,0.6c-0.1,0.3-0.1,0.6-0.2,0.9H52.5z M60.7,9.2   c-0.7,2.5-1.6,4.4-2.8,5.9c0.5,0.8,1,1.4,1.8,1.8c-0.4,0.3-1,0.9-1.4,1.3c-0.6-0.5-1.2-1.1-1.6-1.8c-0.7,0.7-1.6,1.3-2.5,1.8   c-0.1-0.4-0.4-0.9-0.6-1.2c-0.3,0.5-0.5,0.9-0.9,1.3c-0.2-0.3-0.8-0.7-1.2-0.9c1.3-1.8,2-4.8,2.3-6.8L54.7,6c2.1-0.1,4.4-0.4,6-0.8   l0.7,1.5C59.8,7,57.8,7.2,56,7.3L55.7,9h3.7L59.7,9L60.7,9.2z M56,15c-0.4-1.1-0.6-2.4-0.7-3.8c-0.3,1.8-0.8,3.9-1.7,5.6   C54.5,16.4,55.3,15.8,56,15z M56.6,10.5c0.1,1.1,0.3,2.1,0.6,3.1c0.6-0.9,1.1-1.9,1.5-3.1H56.6z"></path>
</g>
<g>
	
		<linearGradient id="SVGID_00000173874234836424379420000001852160837153696684_" gradientUnits="userSpaceOnUse" x1="3.5849" y1="0.4681" x2="8.4226" y2="23.5681">
		<stop offset="0" style="stop-color:#25AEFF"></stop>
		<stop offset="1" style="stop-color:#20AAFF"></stop>
	</linearGradient>
	<polygon fill="url(#SVGID_00000173874234836424379420000001852160837153696684_)" points="12,24 5,24 0,0 7,0  "></polygon>
	
		<linearGradient id="SVGID_00000067932216358665000220000017676884703507852189_" gradientUnits="userSpaceOnUse" x1="110.7176" y1="0.6983" x2="115.3076" y2="23.4263" gradientTransform="matrix(-1 0 0 1 124 0)">
		<stop offset="0" style="stop-color:#24AEFF"></stop>
		<stop offset="1" style="stop-color:#30E0FF"></stop>
	</linearGradient>
	<polygon fill="url(#SVGID_00000067932216358665000220000017676884703507852189_)" points="5,24 12,24 17,0 10,0  "></polygon>
	<defs>
		<filter id="Adobe_OpacityMaskFilter" filterUnits="userSpaceOnUse" x="5" y="0" width="12" height="24">
			<feColorMatrix type="matrix" values="1 0 0 0 0  0 1 0 0 0  0 0 1 0 0  0 0 0 1 0"></feColorMatrix>
		</filter>
	</defs>
	
		<mask maskUnits="userSpaceOnUse" x="5" y="0" width="12" height="24" id="SVGID_00000141426613293191721080000008969082502429983638_">
		<g filter="url(#Adobe_OpacityMaskFilter)">
			<g opacity="0.8">
				<image overflow="visible" width="81" height="104" transform="matrix(1 0 0 1 -26 -40)">
				</image>
			</g>
		</g>
	</mask>
	<polygon opacity="0.9" mask="url(#SVGID_00000141426613293191721080000008969082502429983638_)" fill="#FFFFFF" points="5,24    12,24 17,0 10,0  "></polygon>
</g>
</svg>
