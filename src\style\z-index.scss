/**
 * z-index 层级管理系统
 * 
 * 层级划分规则：
 * - 普通元素/小交互组件: 0 ~ 100
 * - float/吸顶/吸底: 101 ~ 200
 * - 交互元素/大交互组件: 201 ~ 300
 * - 弹窗: 1001 ~ 2000
 * - loading/分享蒙层: 2001 ~ 3000
 * - 其它/AD: 3001 ~ 9999
 */

// z-index 配置映射
$z-indexes: (
  // **Block**: 普通元素/小交互组件 (0-100)
  // 侧边栏基础层级
  "sidebar": 20,

  // **Block**: float/吸顶/吸底 (101-200)
  // 头部导航
  "header": 101,

  // 底部导航
  "footer": 101,

  // **Block**: 交互元素/大交互组件 (201-300)
  // 下拉菜单
  "dropdown": 201,

  // 工具提示
  "tooltip": 210,

  // 弹出框
  "popover": 220,

  // **Block**: 弹窗 (1001-2000)
  // 普通模态框
  "modal": 1001,

  // **Block**: loading/分享蒙层 (2001-3000)
  // 加载遮罩
  "loading": 2001,

  // **Block**: 其它/AD (3001-9999)
  // 通知提示
  "notification": 3001,

  // 广告浮层
  "ad-float": 3100
);

/**
 * 获取z-index值
 * @param {String} $key - z-index配置键名
 * @return {Number} z-index值
 * @throws 当键名不存在时抛出警告
 */
@function z($key) {
  @if map-has-key($z-indexes, $key) {
    @return map-get($z-indexes, $key);
  }

  @warn "z-index键名 '#{$key}' 未定义，请检查拼写或在$z-indexes中添加定义";
  @return 0;
}
