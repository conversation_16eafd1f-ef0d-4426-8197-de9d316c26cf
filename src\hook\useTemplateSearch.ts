import { getTemplate } from '@/api/TemplateView';
import { PROJECT_TYPE } from '@/constants/project';
import { Template } from '@/types';
import { message } from '@fk/faicomponent';
import { computed, ref } from 'vue';

/** 搜索词 */
export const searchValue = ref('');

/** 是否仅查看内部上架模板 */
export const filterInteralTemplate = ref(false);

/** 默认行业 */
export const DEFAULT_INDUSTRY = -1;
/** 当前选择行业 */
export const currentIndustry = ref(DEFAULT_INDUSTRY);

/** 默认场景 */
export const DEFAULT_SCENE = -1;
/** 当前选择场景 */
export const currentScene = ref(DEFAULT_SCENE);

/** 当前选择类型 */
export const currentType = ref(PROJECT_TYPE.VIDEO);
/** 当前页码 */
export const templatePage = ref(1);

/** 模板列表 */
export const templateList = ref<Template[]>([]);

/** 是否loading */
export const loadingTemplate = ref(false);

/** 模板总数 */
export const totalTemplates = ref(0);

/** 是否为搜索 */
export const isSearch = ref(false);

/** 本次搜索词 */
export const currSearchWord = ref('');

/** 是否到页尾 */
export const isEnd = computed(
  () => templateList.value.length >= totalTemplates.value,
);

/** 是否下一页加载中 */
export const isLoadingNextPage = ref(false);

const updateTemplate = async () => {
  // 统一处理搜索词，去除首尾空格
  const trimmedSearchValue = searchValue.value.trim();

  // 根据处理后的搜索词判断是否为搜索状态
  isSearch.value = !!trimmedSearchValue;
  currSearchWord.value = trimmedSearchValue;

  const [err, res] = await getTemplate({
    industry: currentIndustry.value,
    scene: currentScene.value,
    type: currentType.value,
    search: trimmedSearchValue,
    page: templatePage.value,
    internalTemplate: filterInteralTemplate.value,
  });
  loadingTemplate.value = false;
  if (err) {
    message.error(err.message || '获取模板失败');
    throw err;
  }
  totalTemplates.value = res.total || 0;
  return res.data || [];
};

/** 搜索功能 */
export const searchTemplate = async () => {
  loadingTemplate.value = true;
  isLoadingNextPage.value = false;
  // 重置筛选和页码
  currentIndustry.value = DEFAULT_INDUSTRY;
  currentScene.value = DEFAULT_SCENE;
  currentType.value = PROJECT_TYPE.VIDEO;
  templatePage.value = 1;

  // 请求数据
  templateList.value.length = 0;
  templateList.value = (await updateTemplate()) || [];
};

/** 筛选功能 */
export const filterTemplate = async () => {
  loadingTemplate.value = true;
  isLoadingNextPage.value = false;
  // 重置页码
  templatePage.value = 1;

  // 请求数据
  templateList.value.length = 0;
  templateList.value = (await updateTemplate()) || [];
};

/** 分页功能 */
export const nextPage = async () => {
  if (isEnd.value || isLoadingNextPage.value) return;
  isLoadingNextPage.value = true;
  templatePage.value += 1;
  templateList.value = [
    ...templateList.value,
    ...((await updateTemplate()) || []),
  ];
  isLoadingNextPage.value = false;
};
