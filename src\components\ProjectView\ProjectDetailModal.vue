<template>
  <fa-modal :visible="isShow" :width="520" centered @cancel="handleClose">
    <template slot="title">
      <div class="detail-header">
        <div class="detail-title">项目详情</div>
        <div class="project-id">项目ID：{{ projectData.id }}</div>
      </div>
    </template>
    <div class="project-detail">
      <div class="detail-content">
        <div class="detail-item" v-for="item in detailItems" :key="item.key">
          <div class="detail-label">{{ item.label }}</div>
          <div class="detail-value flex items-center" :class="item.class">
            <template v-if="item.key === 'status'">
              <div class="status-tag" :class="item.statusColor"></div>
              {{ item.value }}
              <div class="ml-[8px] text-assist">
                （{{ projectStatusOtherInfo }}）
              </div>
            </template>
            <template v-else-if="item.key === 'templateName'">
              <div class="flex items-center gap-[18px]">
                <span class="flex-1">{{ item.value }}</span>
                <fa-popover placement="top" :width="300">
                  <div
                    class="w-[56px] text-primary hover:text-[#1b4ef9] cursor-pointer"
                  >
                    查看详情
                  </div>
                  <template #content>
                    <div class="popover-content">
                      <template v-for="(item, index) in templateInfo">
                        <div
                          v-if="item.isShow"
                          :key="index"
                          class="templateInfo"
                          :class="item.type"
                        >
                          <div class="label">{{ item.label }}</div>
                          <div class="value">
                            <div
                              v-if="item.type === 'structure'"
                              class="w-[294px]"
                            >
                              <div
                                v-for="(structureItem, sIndex) in item.value"
                                :key="sIndex"
                                class="structureItem"
                              >
                                <div class="tag">{{ structureItem }}</div>
                                <span
                                  v-if="sIndex !== item.value.length - 1"
                                  class="plus-icon"
                                  >+</span
                                >
                              </div>
                            </div>
                            <template v-else-if="item.type === 'tag'">
                              <div class="tag">{{ item.value }}</div>
                            </template>
                            <template v-else>
                              {{ item.value }}
                            </template>
                          </div>
                        </div>
                      </template>
                    </div>
                  </template>
                </fa-popover>
              </div>
            </template>
            <template v-else-if="item.key === 'savedCount'">
              <div v-if="item.value == 0">0</div>
              <!-- 点击数字，打开新标签页，页面跳转至我的作品-当前项目筛选结果页 -->
              <div
                v-else
                class="color-primary cursor-pointer"
                @click="jumptoMyWorks"
              >
                {{ item.value }}
              </div>
            </template>
            <template v-else>
              {{ item.value }}
            </template>
          </div>
        </div>
      </div>
    </div>
    <template slot="footer">
      <fa-button type="primary" @click="handleClose"> 关闭弹窗 </fa-button>
    </template>
  </fa-modal>
</template>

<script setup lang="ts">
import { computed, defineProps, defineEmits, type PropType } from 'vue';
import store from '@/store';
import router from '@/router';
import {
  PROJECT_STATUS,
  PROJECT_STATUS_NAME,
  PROJECT_STATUS_CLASS_NAME,
  PROJECT_TYPE,
  PROJECT_TYPE_NAME,
} from '@/constants/project';
import { ProjectData } from '@/types/Project';
import { day } from '@/utils/dayjs';
import { getProjectStatusInfo } from './utils';

/**
 * ProjectDetailModal 组件 Props
 * @property visible 是否显示弹窗
 * @property projectData 项目详情数据
 */
const props = defineProps({
  /** 是否显示弹窗 */
  visible: {
    type: Boolean,
    default: false,
  },
  /** 项目详情数据 */
  projectData: {
    type: Object as PropType<ProjectData>,
    default: () => ({}),
  },
} as const);

const emit = defineEmits<{
  (e: 'update:visible', value: boolean): void;
}>();

// Computed
const isShow = computed(() => {
  return props.visible;
});

// 作品生成进度
const projectStatusOtherInfo = computed(() => {
  const { status, sucNum, failNum, totalNum, saveNum } = props.projectData;
  const info = getProjectStatusInfo({
    status,
    sucNum,
    failNum,
    totalNum,
    saveNum,
  });
  return `${info.label}：${info.value}`;
});

const detailItems = computed(() => {
  const status: PROJECT_STATUS = props.projectData.status;
  return [
    { key: 'name', label: '项目名称', value: props.projectData.name },
    {
      key: 'status',
      label: '项目状态',
      value: PROJECT_STATUS_NAME[status],
      class: 'status',
      statusColor: PROJECT_STATUS_CLASS_NAME[status],
    },
    {
      key: 'type',
      label: '作品类型',
      value: PROJECT_TYPE_NAME[props.projectData.type as PROJECT_TYPE],
    },
    {
      key: 'savedCount',
      label: '已保存作品数量',
      value: props.projectData.saveNum || 0,
      class: 'savedCount',
    },
    {
      key: 'createTime',
      label: '创建时间',
      value: day(props.projectData.createTime).format('YYYY/MM/DD HH:mm:ss'),
    },
    {
      key: 'updateTime',
      label: '最近编辑时间',
      value: props.projectData.updateTime
        ? day(props.projectData.updateTime).format('YYYY/MM/DD HH:mm:ss')
        : '-',
    },
    {
      key: 'templateName',
      label: '所属模板',
      value: props.projectData.templateName,
      class: 'templateName',
    },
  ];
});

// 所属模板信息
const templateInfo = computed(() => {
  const { industry, scene, scriptList } = props.projectData;
  return [
    {
      label: '适用场景',
      value: store.state.system.industryMap?.[industry] || '',
      type: 'tag',
      isShow: true,
    },
    {
      label: '适用行业',
      value: store.state.system.sceneMap?.[scene] || '',
      type: 'text',
      isShow: true,
    },
    {
      label: '脚本结构',
      value: scriptList || [],
      type: 'structure',
      isShow: props.projectData.type === PROJECT_TYPE.VIDEO,
    },
  ];
});

// Methods
const handleClose = () => {
  emit('update:visible', false);
};
/** 点击数字，打开新标签页，页面跳转至我的作品-当前项目筛选结果页 */
const jumptoMyWorks = () => {
  const resolvedPath = router.resolve({
    path: `/work`,
    query: {
      projectId: String(props.projectData.id),
    },
  });
  window.open(resolvedPath.href, '_blank');
};
</script>

<style lang="scss" scoped>
.detail-header {
  @apply flex items-center;
  .detail-title {
    @apply color-title text-[15px] font-bold mr-[16px];
  }

  .project-id {
    @apply text-assist text-[13px]/3.25 font-normal;
  }
}

.detail-item {
  @apply flex items-start mb-[24px] text-[14px];
  &:last-child {
    @apply mb-0;
  }
}

.detail-label {
  @apply w-[122px] color-assist;
}

.detail-value {
  @apply flex-1 color-text;

  &.status {
    .status-tag {
      @apply w-[6px] h-[6px] rounded-[6px] mr-[8px];
    }
    .status-draft {
      @apply bg-[#bfbfbf];
    }

    .status-processing {
      @apply bg-[#ffad5c];
    }

    .status-completed {
      @apply bg-[#75c15b];
    }
  }
}

.templateInfo {
  @apply flex items-center mb-[20px];

  &:last-child {
    @apply mb-0 items-start;
  }

  .label {
    @apply mr-[24px] text-assist;
  }

  .value {
    @apply text-title;
  }

  .tag {
    @apply p-[3px_8px] text-text bg-background rounded-[4px] text-[13px];
  }

  .structureItem {
    @apply inline-flex items-center mb-[8px];

    .plus-icon {
      @apply mx-[8px] text-title;
    }
  }
}
</style>
