<template>
  <div class="error-state">
    <div class="error-state__icon">
      <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path
          d="M12 4C7.584 4 4 7.584 4 12C4 16.416 7.584 20 12 20C16.416 20 20 16.416 20 12C20 7.584 16.416 4 12 4ZM13 16H11V14H13V16ZM13 12H11V8H13V12Z"
          fill="currentColor"
        />
      </svg>
    </div>
    <div class="error-state__message">{{ message }}</div>
    <button
      v-if="showRetry"
      class="error-state__retry-btn"
      @click="$emit('retry')"
    >
      重试
    </button>
  </div>
</template>

<script lang="ts">
import { defineComponent } from 'vue';

export default defineComponent({
  name: 'ErrorState',
  props: {
    /** 错误信息 */
    message: {
      type: String,
      default: '视频加载失败，请稍后重试',
    },
    /** 是否显示重试按钮 */
    showRetry: {
      type: Boolean,
      default: true,
    },
  },
  emits: ['retry'],
});
</script>

<style lang="scss">
.error-state {
  @apply absolute top-0 left-0 w-full h-full flex flex-col justify-center items-center bg-black/70 text-white zi-error-state;

  &__icon {
    @apply w-12 h-12 mb-4 text-red-500;

    svg {
      @apply w-full h-full;
    }
  }

  &__message {
    @apply text-sm text-center mb-4;
  }

  &__retry-btn {
    @apply bg-blue-500 hover:bg-blue-600 active:bg-blue-700 text-white text-sm rounded px-6 py-2 transition-colors duration-300 cursor-pointer border-none;
  }
}
</style>
