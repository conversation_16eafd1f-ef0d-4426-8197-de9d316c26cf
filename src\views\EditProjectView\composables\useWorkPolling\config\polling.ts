/**
 * @fileoverview 智能轮询配置
 * @description 定义轮询相关的配置参数
 */

/**
 * 轮询间隔配置（毫秒）
 */
export const POLLING_INTERVALS = {
  /** 视频项目：进度小于20%时的轮询间隔 */
  VIDEO_LOW_PROGRESS: 5000,
  /** 视频项目：进度在20%-90%之间的轮询间隔 */
  VIDEO_MEDIUM_PROGRESS: 10000,
  /** 视频项目：进度大于90%时的轮询间隔 */
  VIDEO_HIGH_PROGRESS: 3000,
  /** 图文项目：进度小于50%时的轮询间隔 */
  IMAGE_LOW_PROGRESS: 4000,
  /** 图文项目：进度大于等于50%时的轮询间隔 */
  IMAGE_HIGH_PROGRESS: 2000,
} as const;

/**
 * 轮询超时配置（毫秒）
 */
export const POLLING_TIMEOUT = {
  /** 轮询超时时间（10分钟） */
  DURATION: 10 * 60 * 1000,
} as const;
