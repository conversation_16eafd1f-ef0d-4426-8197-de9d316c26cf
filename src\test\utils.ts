/**
 * 测试工具函数
 * 提供常用的测试辅助方法
 */

import { mount, shallowMount, Wrapper, createLocalVue } from '@vue/test-utils';
import { vi, expect } from 'vitest';
import Vue from 'vue';

/**
 * 创建 Vue 组件的测试包装器
 */
// eslint-disable-next-line @typescript-eslint/no-explicit-any
export function createWrapper(
  component: any,
  options: Record<string, unknown> = {},
): Wrapper<Vue> {
  const localVue = createLocalVue();

  const defaultOptions = {
    localVue,
    mocks: {
      $router: {
        push: vi.fn(),
        replace: vi.fn(),
        go: vi.fn(),
        back: vi.fn(),
        forward: vi.fn(),
      },
      $route: {
        params: {},
        query: {},
        path: '/',
        name: 'test',
      },
      $store: {
        state: {},
        getters: {},
        dispatch: vi.fn(),
        commit: vi.fn(),
      },
      $message: {
        success: vi.fn(),
        error: vi.fn(),
        warning: vi.fn(),
        info: vi.fn(),
      },
    },
    stubs: [
      'router-link',
      'router-view',
      'el-button',
      'el-input',
      'el-form',
      'el-form-item',
      'el-dialog',
      'el-table',
      'el-table-column',
      'el-pagination',
      'el-loading',
      'el-select',
      'el-option',
      'el-checkbox',
      'el-radio',
      'el-radio-group',
      'el-date-picker',
      'el-upload',
      'el-progress',
      'el-tooltip',
      'el-popover',
      'el-dropdown',
      'el-dropdown-menu',
      'el-dropdown-item',
      'el-tabs',
      'el-tab-pane',
      'el-card',
      'el-collapse',
      'el-collapse-item',
      'el-tree',
      'el-cascader',
      'el-switch',
      'el-slider',
      'el-time-picker',
      'el-time-select',
      'el-rate',
      'el-color-picker',
      'el-transfer',
      'el-image',
      'el-avatar',
      'el-empty',
      'el-result',
      'el-skeleton',
      'el-skeleton-item',
    ],
    ...options,
  };

  return mount(component, defaultOptions);
}

/**
 * 创建浅层渲染的 Vue 组件测试包装器
 */
// eslint-disable-next-line @typescript-eslint/no-explicit-any
export function createShallowWrapper(
  component: any,
  options: Record<string, unknown> = {},
): Wrapper<Vue> {
  const localVue = createLocalVue();

  const defaultOptions = {
    localVue,
    mocks: {
      $router: {
        push: vi.fn(),
        replace: vi.fn(),
        go: vi.fn(),
        back: vi.fn(),
        forward: vi.fn(),
      },
      $route: {
        params: {},
        query: {},
        path: '/',
        name: 'test',
      },
      $store: {
        state: {},
        getters: {},
        dispatch: vi.fn(),
        commit: vi.fn(),
      },
      $message: {
        success: vi.fn(),
        error: vi.fn(),
        warning: vi.fn(),
        info: vi.fn(),
      },
    },
    ...options,
  };

  return shallowMount(component, defaultOptions);
}

/**
 * 模拟 API 响应
 */
export function mockApiResponse<T>(data: T, error?: Error) {
  if (error) {
    return Promise.resolve([error, null]);
  }
  return Promise.resolve([null, { data, code: 200, message: 'success' }]);
}

/**
 * 等待 Vue 的下一个 tick
 */
export async function nextTick() {
  return new Promise(resolve => {
    setTimeout(resolve, 0);
  });
}

/**
 * 等待指定时间
 */
export function sleep(ms: number) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

/**
 * 模拟用户输入
 */
export async function setInputValue(
  wrapper: Wrapper<Vue>,
  selector: string,
  value: string,
) {
  const input = wrapper.find(selector);
  await input.setValue(value);
  await input.trigger('input');
  await input.trigger('change');
}

/**
 * 模拟用户点击
 */
export async function clickElement(wrapper: Wrapper<Vue>, selector: string) {
  const element = wrapper.find(selector);
  await element.trigger('click');
}

/**
 * 检查元素是否存在
 */
export function expectElementExists(wrapper: Wrapper<Vue>, selector: string) {
  const element = wrapper.find(selector);
  expect(element.exists()).toBe(true);
}

/**
 * 检查元素是否不存在
 */
export function expectElementNotExists(
  wrapper: Wrapper<Vue>,
  selector: string,
) {
  const element = wrapper.find(selector);
  expect(element.exists()).toBe(false);
}

/**
 * 检查元素文本内容
 */
export function expectElementText(
  wrapper: Wrapper<Vue>,
  selector: string,
  text: string,
) {
  const element = wrapper.find(selector);
  expect(element.text()).toBe(text);
}

/**
 * 检查元素是否包含指定文本
 */
export function expectElementContainsText(
  wrapper: Wrapper<Vue>,
  selector: string,
  text: string,
) {
  const element = wrapper.find(selector);
  expect(element.text()).toContain(text);
}

/**
 * 检查元素是否可见
 */
export function expectElementVisible(wrapper: Wrapper<Vue>, selector: string) {
  const element = wrapper.find(selector);
  expect(element.isVisible()).toBe(true);
}

/**
 * 检查元素是否隐藏
 */
export function expectElementHidden(wrapper: Wrapper<Vue>, selector: string) {
  const element = wrapper.find(selector);
  expect(element.isVisible()).toBe(false);
}
