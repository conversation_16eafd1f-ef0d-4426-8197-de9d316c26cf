/**
 * @description useForm composable 测试文件
 * 测试必填验证规则的重构是否正确工作
 */

import { describe, it, expect, beforeEach } from 'vitest';

// 模拟 useForm composable 的部分功能
import { VALIDATION_TRIGGER } from '@/views/EditProjectView/types/index';
import type { FormRule } from '@/views/EditProjectView/types/index';

/**
 * 模拟校验模式
 */
const VALIDATION_MODE = {
  GENERATE: 'generate', // 生成模式：完整校验
  SAVE: 'save', // 保存模式：仅最大长度校验
} as const;

let currentValidationMode: string = VALIDATION_MODE.GENERATE;

/**
 * 设置校验模式
 */
const setValidationMode = (mode: string): void => {
  currentValidationMode = mode;
};

/**
 * 创建必填验证规则（测试版本）
 */
const createRequiredRule = (
  _label: string,
  _componentType: string,
): FormRule => {
  return {
    trigger: VALIDATION_TRIGGER.MAGIC,
    triggerAuto: true,
    validator: (
      _rule: FormRule,
      value: unknown,
      callback: (error?: Error) => void,
    ) => {
      // 保存模式跳过必填校验
      if (currentValidationMode === VALIDATION_MODE.SAVE) {
        console.log('保存模式：跳过必填校验');
        return callback();
      }

      // 统一的空值检查逻辑
      const isEmpty = Array.isArray(value)
        ? value.length === 0
        : !value || String(value).trim() === '';

      if (isEmpty) {
        callback(new Error('输入不能为空'));
      } else {
        callback();
      }
    },
  };
};

/**
 * 创建文件上传必填验证规则（测试版本）
 */
const createFileRequiredRule = (mediaType: string = 'video'): FormRule => {
  return {
    trigger: VALIDATION_TRIGGER.MAGIC,
    triggerAuto: true,
    validator: (
      _rule: FormRule,
      value: unknown,
      callback: (error?: Error) => void,
    ) => {
      // 保存模式跳过文件必填校验
      if (currentValidationMode === VALIDATION_MODE.SAVE) {
        console.log('保存模式：跳过文件必填校验');
        return callback();
      }

      // 检查文件列表是否为空
      const isEmpty = !value || (Array.isArray(value) && value.length === 0);

      if (isEmpty) {
        // 根据媒体类型显示不同的错误信息
        const errorMessage =
          mediaType === 'video'
            ? '视频素材总时长需超过15s，建议上传多段视频素材'
            : '请上传相关素材';
        callback(new Error(errorMessage));
      } else {
        callback();
      }
    },
  };
};

describe('useForm - 差异化表单校验测试', () => {
  beforeEach(() => {
    // 重置校验模式
    setValidationMode(VALIDATION_MODE.GENERATE);
  });

  describe('createRequiredRule', () => {
    it('应该返回包含validator函数的规则对象', () => {
      const rule = createRequiredRule('测试字段', 'input');

      expect(rule).toHaveProperty('trigger', VALIDATION_TRIGGER.MAGIC);
      expect(rule).toHaveProperty('triggerAuto', true);
      expect(rule).toHaveProperty('validator');
      expect(typeof rule.validator).toBe('function');
    });

    it('生成模式下应该正确验证空字符串', () => {
      return new Promise<void>(resolve => {
        setValidationMode(VALIDATION_MODE.GENERATE);
        const rule = createRequiredRule('测试字段', 'input');

        rule.validator!(rule, '', error => {
          expect(error).toBeInstanceOf(Error);
          expect(error?.message).toBe('输入不能为空');
          resolve();
        });
      });
    });

    it('保存模式下应该跳过必填校验', () => {
      return new Promise<void>(resolve => {
        setValidationMode(VALIDATION_MODE.SAVE);
        const rule = createRequiredRule('测试字段', 'input');

        rule.validator!(rule, '', error => {
          expect(error).toBeUndefined();
          resolve();
        });
      });
    });

    it('应该正确验证有效字符串', () => {
      return new Promise<void>(resolve => {
        const rule = createRequiredRule('测试字段', 'input');

        rule.validator!(rule, '有效内容', error => {
          expect(error).toBeUndefined();
          resolve();
        });
      });
    });

    it('应该正确验证selectTags组件的空数组', () => {
      return new Promise<void>(resolve => {
        const rule = createRequiredRule('标签字段', 'selectTags');

        rule.validator!(rule, [], error => {
          expect(error).toBeInstanceOf(Error);
          expect(error?.message).toBe('输入不能为空');
          resolve();
        });
      });
    });

    it('应该正确验证selectTags组件的有效数组', () => {
      return new Promise<void>(resolve => {
        const rule = createRequiredRule('标签字段', 'selectTags');

        rule.validator!(rule, ['标签1', '标签2'], error => {
          expect(error).toBeUndefined();
          resolve();
        });
      });
    });
  });

  describe('createFileRequiredRule', () => {
    it('应该返回包含validator函数的规则对象', () => {
      const rule = createFileRequiredRule();

      expect(rule).toHaveProperty('trigger', VALIDATION_TRIGGER.MAGIC);
      expect(rule).toHaveProperty('triggerAuto', true);
      expect(rule).toHaveProperty('validator');
      expect(typeof rule.validator).toBe('function');
    });

    it('生成模式下应该正确验证空视频文件列表', () => {
      return new Promise<void>(resolve => {
        setValidationMode(VALIDATION_MODE.GENERATE);
        const rule = createFileRequiredRule('video');

        rule.validator!(rule, [], error => {
          expect(error).toBeInstanceOf(Error);
          expect(error?.message).toBe(
            '视频素材总时长需超过15s，建议上传多段视频素材',
          );
          resolve();
        });
      });
    });

    it('生成模式下应该正确验证空图文文件列表', () => {
      return new Promise<void>(resolve => {
        setValidationMode(VALIDATION_MODE.GENERATE);
        const rule = createFileRequiredRule('image');

        rule.validator!(rule, [], error => {
          expect(error).toBeInstanceOf(Error);
          expect(error?.message).toBe('请上传相关素材');
          resolve();
        });
      });
    });

    it('保存模式下应该跳过文件必填校验', () => {
      return new Promise<void>(resolve => {
        setValidationMode(VALIDATION_MODE.SAVE);
        const rule = createFileRequiredRule();

        rule.validator!(rule, [], error => {
          expect(error).toBeUndefined();
          resolve();
        });
      });
    });

    it('应该正确验证有效文件列表', () => {
      return new Promise<void>(resolve => {
        const rule = createFileRequiredRule();

        rule.validator!(rule, [{ id: 1, name: 'test.jpg' }], error => {
          expect(error).toBeUndefined();
          resolve();
        });
      });
    });
  });
});
