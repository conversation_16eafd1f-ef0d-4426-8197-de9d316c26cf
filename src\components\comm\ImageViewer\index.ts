import Vue from 'vue';
import ImageViewer from './ImageViewer.vue';

/**
 * 打开图片预览弹窗组件
 * @param props 配置参数
 * @param props.imgList 最大选择数量
 * @returns void
 */
export const showImageViewer = (props: { imgList: string[] }) => {
  Vue.prototype.$modal.open('ImageViewer', {
    component: ImageViewer,
    props: {
      visible: true,
      ...props,
    },
    onClose: () => {
      console.log('ImageViewer已关闭');
    },
  });
};

/**
 * 关闭图片预览弹窗组件
 */
export const closeImageViewer = () => {
  Vue.prototype.$modal.close('ImageViewer');
};
