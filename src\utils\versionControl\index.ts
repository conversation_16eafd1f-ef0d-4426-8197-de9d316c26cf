import store from '@/store';
import { showVersionMsg } from '@/utils/version';
import { VERSION } from '@/constants/version';
import {
  VERSION_FEATURE_CONFIG,
  getDefaultFeatureMessage,
  isValidFeatureKey,
  type VersionFeatureConfig,
} from './config';

/**
 * 通用版本校验工具类
 * @description 提供统一的版本校验接口，支持多种使用模式
 */
export class VersionController {
  /**
   * 检查用户当前版本是否满足功能要求
   * @param requiredVersion 功能要求的最低版本
   * @param currentVersion 用户当前版本，默认从store获取
   * @returns 是否满足版本要求
   */
  static checkVersionPermission(
    requiredVersion: VERSION,
    currentVersion?: VERSION,
  ): boolean {
    const userVersion = currentVersion ?? store.state.user.version;
    return userVersion >= requiredVersion;
  }

  /**
   * AI改嘴型功能的版本检查
   * @param isEnabled 当前是否启用
   * @param onDowngrade 降级时的回调
   * @returns 是否应该启用该功能
   */
  static checkMouthShapeFeature(
    isEnabled: boolean,
    onDowngrade?: () => void,
  ): boolean {
    const hasPermission = this.checkVersionPermission(VERSION.BASIC);

    // 如果当前启用但没有权限，执行降级
    if (isEnabled && !hasPermission && onDowngrade) {
      onDowngrade();
      console.log(`当前启用但没有权限，执行降级: AI改嘴型功能的版本检查`);
      return false;
    }

    return hasPermission ? isEnabled : false;
  }
  /**
   * 检查功能权限
   * @param featureKey 功能标识
   * @returns 是否有权限
   */
  static hasPermission(featureKey: string): boolean {
    if (!isValidFeatureKey(featureKey)) {
      console.warn(`[VersionController] 无效的功能标识: ${featureKey}`);
      return true; // 未配置的功能默认允许访问
    }

    const config = VERSION_FEATURE_CONFIG[featureKey];
    return this.checkVersionPermission(config.requiredVersion);
  }

  /**
   * 获取功能配置
   * @param featureKey 功能标识
   * @returns 功能配置
   */
  static getFeatureConfig(featureKey: string): VersionFeatureConfig | null {
    if (!isValidFeatureKey(featureKey)) {
      console.warn(`[VersionController] 无效的功能标识: ${featureKey}`);
      return null;
    }
    return VERSION_FEATURE_CONFIG[featureKey];
  }

  /**
   * 显示版本限制提示
   * @param featureKey 功能标识
   */
  static showUpgradeMessage(featureKey: string): void {
    const config = this.getFeatureConfig(featureKey);
    if (!config) return;

    const message = getDefaultFeatureMessage(config);
    const currentVersion = store.state.user.version;
    showVersionMsg(message, currentVersion);
  }

  /**
   * 接口级自动降级处理
   * @param data 原始数据
   * @param featureKey 功能标识
   * @returns 处理后的数据
   */
  static applyDowngrade<T>(data: T, featureKey: string): T {
    const config = this.getFeatureConfig(featureKey);
    if (!config || !config.supportDowngrade) {
      return data;
    }

    // 如果用户有权限，直接返回原数据
    if (this.hasPermission(featureKey)) {
      return data;
    }

    // 执行降级处理
    if (config.downgradeHandler) {
      try {
        return config.downgradeHandler(data as Record<string, unknown>) as T;
      } catch (error) {
        console.error(
          `[VersionController] 降级处理失败 (${featureKey}):`,
          error,
        );
        return data;
      }
    }

    return data;
  }

  /**
   * 批量应用降级处理
   * @param dataList 数据列表
   * @param featureKey 功能标识
   * @returns 处理后的数据列表
   */
  static applyDowngradeToList<T>(dataList: T[], featureKey: string): T[] {
    return dataList.map(item => this.applyDowngrade(item, featureKey));
  }

  /**
   * 检查并执行操作（消息提示式校验）
   * @param featureKey 功能标识
   * @param operation 要执行的操作
   * @returns 是否执行了操作
   */
  static checkAndExecute(featureKey: string, operation: () => void): boolean {
    if (!this.hasPermission(featureKey)) {
      this.showUpgradeMessage(featureKey);
      return false;
    }

    operation();
    return true;
  }

  /**
   * 获取功能的版本要求信息
   * @param featureKey 功能标识
   * @returns 版本要求信息
   */
  static getVersionRequirement(featureKey: string): {
    requiredVersion: number;
    featureName: string;
    hasPermission: boolean;
  } | null {
    const config = this.getFeatureConfig(featureKey);
    if (!config) return null;

    return {
      requiredVersion: config.requiredVersion,
      featureName: config.featureName,
      hasPermission: this.hasPermission(featureKey),
    };
  }
}

// 导出配置和类型，方便外部使用
export { VERSION_FEATURE_CONFIG, type VersionFeatureConfig } from './config';
export default VersionController;

// 导出向后兼容的函数
export const checkVersionPermission = VersionController.checkVersionPermission;
export const checkMouthShapeFeature = VersionController.checkMouthShapeFeature;
