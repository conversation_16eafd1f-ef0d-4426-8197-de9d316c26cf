# API 请求封装

基于 Axios 的 API 请求封装，提供更简洁、类型安全和统一的 HTTP 请求处理方式。

## 特点

1. **await-to 范式**: 使用 `[err, res]` 返回形式，避免 try-catch 嵌套，让异步代码更清晰。
2. **错误优先处理**: 培养优先处理错误的编码习惯，增强代码健壮性。
3. **统一错误结构**: 无论是网络请求错误还是后端业务逻辑错误，都会转换为统一的 `StandardizedError` 结构，简化错误处理。
4. **类型安全**: 结合 TypeScript 泛型，确保在成功和业务错误时都能获得正确的 `data` 类型。

## 使用方法

### 导入所需方法

```typescript
import {
  GET,
  POST,
  POST_FORM,
  POST_JSON,
  POST_MULTIPART,
  StandardizedError,
} from '@/api/request';
```

### 基础使用示例

```typescript
// @/api/[module]/index.ts
interface UserData {
  id: number;
  name: string;
  role: string;
}
export const getUserInfo = (userId: number) => {
  return GET<UserData>(`/user/info/${userId}`);
};

// @/views/[module]/index.vue
async function displayUserInfo(userId: number) {
  const [err, res] = await getUserInfo(userId);

  if (err) {
    // 优先处理错误逻辑
    console.error('请求失败:', err.message); // 通用错误信息
    console.error('错误类型:', err.type); // 'business' 或 'network'
    console.error('错误码:', err.code); // 后端业务码或 HTTP 状态码

    if (err.type === 'business') {
      // 业务错误，可以访问 err.data (类型安全)
      alert(
        `操作失败: ${err.message} (错误码: ${err.code}, 数据: ${JSON.stringify(
          err.data,
        )})`,
      );
    } else {
      // 网络错误，通常由全局错误处理器处理，或给出通用提示
      alert('网络请求发生错误，请稍后再试。');
    }
    return;
  }

  // 处理业务逻辑 (res 类型为 ApiResponse<UserData>)
  const userData = res.data; // userData 类型为 UserData
  console.log('用户信息:', userData.name, userData.role);
}
```

## 重要说明：错误处理机制

**核心理念**：本封装已在内部实现了完整的错误处理逻辑，将所有错误（网络层、HTTP 层、业务层）统一转换为 `StandardizedError` 对象。业务代码中**不需要**额外添加 `try-catch` 语句来捕获请求相关的错误。

### `StandardizedError` 结构

```typescript
export interface StandardizedError<DataType = unknown> {
  message: string; // 统一的、可直接展示给用户的错误消息
  type: 'business' | 'network'; // 错误类型
  code?: number; // 业务错误码 (rt) 或 HTTP 状态码
  original?: unknown; // 原始错误对象，供调试或特殊处理使用
  data?: DataType; // 业务错误时，此字段包含 API 返回的 data (类型与请求泛型一致)
  rt?: number; // 业务错误特有：业务返回码 (与 code 在业务错误时一致)
  success?: false; // 业务错误特有：始终为 false
  total?: number; // 业务错误特有：分页等场景下的总数
}
```

### ✅ 业务代码中的错误处理关注点

在大多数业务场景中，你主要需要关注和处理的是 `type: 'business'` 的错误。这类错误通常表示后端接口明确告知的操作失败，例如参数校验失败、权限不足、资源不存在等。

```typescript
import { StandardizedError } from '@/api/request';

interface Product {
  id: string;
  name: string;
}

async function fetchProduct(productId: string) {
  const [err, res] = await GET<Product>(`/api/products/${productId}`);

  if (err) {
    // 统一的错误提示
    showGlobalNotification({ type: 'error', message: err.message });

    if (err.type === 'business') {
      // **业务逻辑重点处理区域**
      // 例如，根据 err.code (业务错误码) 执行特定操作
      if (err.code === 40401) {
        // 假设 40401 表示产品未找到
        redirectToNotFoundPage();
      } else if (err.code === 40301) {
        // 假设 40301 表示无权限
        showPermissionDeniedModal();
      }
      // 还可以访问 err.data (类型为 Product | undefined)
      console.warn('业务错误详情:', err.data);
    }
    // 对于 err.type === 'network' 的错误，通常由全局统一处理 (如网络断开提示)
    // 业务组件层面一般不需要针对网络错误做非常具体的逻辑。
    return;
  }

  // 处理成功情况 (res 类型为 ApiResponse<Product>)
  const product = res.data; // product 类型为 Product
  updateProductUI(product);
}
```

### 错误处理流程总结

1.  **请求发起**: 调用封装的 `GET`, `POST` 等方法。
2.  **内部处理**:
    - **网络/HTTP 错误**: Axios 抛出的错误（超时、网络中断、4xx/5xx HTTP 状态码等）会被捕获，并转换为 `StandardizedError`，其中 `type` 为 `'network'`，`message` 为通用网络错误提示或具体 HTTP 错误信息，`code` 为 HTTP 状态码。
    - **业务逻辑错误**: API 响应 `success: false` 时，响应体被转换为 `StandardizedError`，其中 `type` 为 `'business'`，`message` 来自 `ApiResponse.msg`，`code` 和 `rt` 来自 `ApiResponse.rt`，`data` 来自 `ApiResponse.data` (类型安全)。
    - **服务器响应格式错误**: 如果服务器返回 2xx 但响应体不是预期的 `ApiResponse` 结构，也会被处理为 `type: 'network'` 的 `StandardizedError`。
3.  **返回结果**: 业务代码收到 `[err, res]`。
    - 如果 `err` 不为 `null`，它就是一个 `StandardizedError` 对象。
    - 如果 `err` 为 `null`，则 `res` 是成功的 `ApiResponse<T>` 对象。

**核心优势**: 业务代码只需判断 `err` 对象是否存在，然后通过 `err.type` 来区分处理业务逻辑错误和通用网络错误，大大简化了错误处理的复杂度，并提高了类型安全性。

### 不同内容类型的请求

```typescript
// 表单数据提交 (application/x-www-form-urlencoded)
function loginUser(username: string, password: string) {
  return POST_FORM('/api/login', { username, password });
}

// JSON 数据提交 (application/json)
function createArticle(article: ArticleData) {
  return POST_JSON('/api/articles', article);
}

// 文件上传 (multipart/form-data)
function uploadAvatar(userId: string, file: File) {
  const formData = new FormData();
  formData.append('userId', userId);
  formData.append('avatar', file);

  return POST_MULTIPART('/api/upload/avatar', formData);
}
```

## 使用对比

### 传统 axios 使用方式 (冗余的 try-catch 和错误判断)

```typescript
import axios, { AxiosError } from 'axios';

interface MyData {
  /* ... */
}
interface ApiResponse<T> {
  data: T;
  msg: string;
  rt: number;
  success: boolean;
}

async function getMyDataLegacy(id: string) {
  try {
    const response = await axios.get<ApiResponse<MyData>>(`/api/mydata/${id}`);
    if (response.data && response.data.success === false) {
      // 业务错误
      console.error('业务错误:', response.data.msg, '代码:', response.data.rt);
      // 可能还需要根据 rt 做不同处理
    } else if (response.data) {
      // 成功
      const myData = response.data.data;
      console.log(myData);
    } else {
      // 响应格式不对
      console.error('服务器响应格式错误');
    }
  } catch (error) {
    // 网络或HTTP错误
    const axiosError = error as AxiosError;
    if (axiosError.isAxiosError) {
      console.error(
        '网络请求错误:',
        axiosError.message,
        'HTTP状态:',
        axiosError.response?.status,
      );
    } else {
      console.error('未知错误:', error);
    }
  }
}
```

### 使用本封装后 (清晰、统一的错误处理)

```typescript
// 假设 MyData 接口和 GET 方法已按前述方式定义和导入
async function getMyDataModern(id: string) {
  const [err, res] = await GET<MyData>(`/api/mydata/${id}`);

  if (err) {
    console.error(
      `请求失败: ${err.message} (类型: ${err.type}, 代码: ${err.code})`,
    );
    if (err.type === 'business') {
      // 处理特定业务错误, err.data 类型为 MyData | undefined
      if (err.code === 12345) {
        /* ... */
      }
    }
    // 网络错误通常由全局处理
    return;
  }

  // 成功 (res 类型为 ApiResponse<MyData>)
  const myData = res.data; // myData 类型为 MyData
  console.log(myData);
}
```

## 高级使用

### 自定义请求配置

```typescript
import { GET, ExtendedAxiosRequestConfig } from '@/api/request';

async function getProductsWithTimeout() {
  const config: ExtendedAxiosRequestConfig = {
    timeout: 5000, // 5秒超时
    headers: {
      'X-Custom-Header': 'value',
    },
  };

  const [err, res] = await GET('/api/products', {}, config);
  if (err) {
    // ... 错误处理
    return;
  }
  // ... 处理结果
}
```

## 相关文档汇总

[API 数据转换函数命名规范](./docs/NAMING_CONVENTIONS.md)
