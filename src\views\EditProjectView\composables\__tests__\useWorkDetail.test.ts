/**
 * @fileoverview useWorkDetail composable 单元测试
 */

import { describe, it, expect, beforeEach, afterEach, vi, Mock } from 'vitest';
import { useWorkDetail } from '../useWorkDetail';
import { getWorkInfoWithTransform } from '@/api/EditProjectView/work';
import { VIEW_MODE } from '@/views/EditProjectView/constants';
import type { VideoWorkItem } from '@/types';

// Mock API 模块
vi.mock('@/api/EditProjectView/work', () => ({
  getWorkInfoWithTransform: vi.fn(),
}));

const mockGetWorkInfoWithTransform = getWorkInfoWithTransform as Mock;

// 创建测试用的工作项数据
const createMockWorkItem = (id: number, progress = 50): VideoWorkItem => ({
  id,
  aid: 123,
  projectId: 456,
  type: 0,
  name: `测试作品 ${id}`,
  coverImg: 'cover.jpg',
  duration: 30,
  progress,
  status: 1,
  createTime: '2023-01-01 10:00:00',
  script: {
    title: `测试作品 ${id} 的脚本`,
    hashtags: ['测试', '作品'],
    segments: [],
  },
  setting: {
    bgMusic: {
      open: false,
      vol: 50,
    },
    voice: {
      open: false,
      speed: 1.0,
    },
    style: [],
  },
});

describe('useWorkDetail', () => {
  beforeEach(() => {
    // 清除所有模拟函数的调用记录
    vi.clearAllMocks();
    // 重置定时器
    vi.useFakeTimers();
  });

  afterEach(() => {
    vi.useRealTimers();
  });

  describe('初始状态', () => {
    it('应该初始化正确的默认值', () => {
      const { currentWorkId, currentWork, isLoadingDetail } =
        useWorkDetail<VideoWorkItem>();

      expect(currentWorkId.value).toBe(-1);
      expect(currentWork.value).toBeUndefined();
      expect(isLoadingDetail.value).toBe(false);
    });
  });

  describe('setCurrentWorkId', () => {
    it('应该正确设置当前作品ID', () => {
      const { currentWorkId, setCurrentWorkId } =
        useWorkDetail<VideoWorkItem>();

      setCurrentWorkId(123);
      expect(currentWorkId.value).toBe(123);

      setCurrentWorkId(456);
      expect(currentWorkId.value).toBe(456);
    });
  });

  describe('fetchWorkDetail', () => {
    it('当workId无效时应该返回undefined并清空currentWork', async () => {
      const { fetchWorkDetail, currentWork } = useWorkDetail<VideoWorkItem>();

      // 测试各种无效workId
      let result = await fetchWorkDetail(0);
      expect(result).toBeUndefined();
      expect(currentWork.value).toBeUndefined();

      result = await fetchWorkDetail(-1);
      expect(result).toBeUndefined();
      expect(currentWork.value).toBeUndefined();

      // 确保API没有被调用
      expect(mockGetWorkInfoWithTransform).not.toHaveBeenCalled();
    });

    it('应该使用正确的参数调用API', async () => {
      const mockWorkItem = createMockWorkItem(123);
      mockGetWorkInfoWithTransform.mockResolvedValue([
        null,
        { data: mockWorkItem },
      ]);

      const { fetchWorkDetail } = useWorkDetail<VideoWorkItem>();

      await fetchWorkDetail(123);

      expect(mockGetWorkInfoWithTransform).toHaveBeenCalledWith({
        id: 123,
        viewMode: VIEW_MODE.EDIT,
      });
    });

    it('成功获取作品详情时应该更新状态并缓存数据', async () => {
      const mockWorkItem = createMockWorkItem(123);
      mockGetWorkInfoWithTransform.mockResolvedValue([
        null,
        { data: mockWorkItem },
      ]);

      const { fetchWorkDetail, currentWork, isLoadingDetail } =
        useWorkDetail<VideoWorkItem>();

      const result = await fetchWorkDetail(123);

      // 验证加载状态
      expect(isLoadingDetail.value).toBe(true);

      // 等待setTimeout执行
      vi.advanceTimersByTime(300);

      expect(isLoadingDetail.value).toBe(false);
      expect(currentWork.value).toEqual(mockWorkItem);
      expect(result).toEqual(mockWorkItem);
    });

    it('API错误时应该处理错误并返回undefined', async () => {
      const mockError = new Error('API请求失败');
      mockGetWorkInfoWithTransform.mockResolvedValue([mockError, null]);

      const consoleSpy = vi
        .spyOn(console, 'error')
        .mockImplementation(() => {});

      const { fetchWorkDetail, currentWork, isLoadingDetail } =
        useWorkDetail<VideoWorkItem>();

      const result = await fetchWorkDetail(123);

      expect(result).toBeUndefined();
      expect(currentWork.value).toBeUndefined();
      expect(isLoadingDetail.value).toBe(false);
      expect(consoleSpy).toHaveBeenCalledWith('获取作品详情失败:', mockError);

      consoleSpy.mockRestore();
    });

    it('API返回空数据时应该处理并返回undefined', async () => {
      mockGetWorkInfoWithTransform.mockResolvedValue([null, null]);

      const consoleSpy = vi
        .spyOn(console, 'error')
        .mockImplementation(() => {});

      const { fetchWorkDetail, currentWork, isLoadingDetail } =
        useWorkDetail<VideoWorkItem>();

      const result = await fetchWorkDetail(123);

      expect(result).toBeUndefined();
      expect(currentWork.value).toBeUndefined();
      expect(isLoadingDetail.value).toBe(false);
      expect(consoleSpy).toHaveBeenCalled();

      consoleSpy.mockRestore();
    });

    it('应该实现缓存机制，第二次获取同一作品时不调用API', async () => {
      const mockWorkItem = createMockWorkItem(123);
      mockGetWorkInfoWithTransform.mockResolvedValue([
        null,
        { data: mockWorkItem },
      ]);

      const { fetchWorkDetail } = useWorkDetail<VideoWorkItem>();

      // 第一次调用，应该调用API
      await fetchWorkDetail(123);
      expect(mockGetWorkInfoWithTransform).toHaveBeenCalledTimes(1);

      // 第二次调用同一ID，应该从缓存获取，不调用API
      const result = await fetchWorkDetail(123);
      expect(mockGetWorkInfoWithTransform).toHaveBeenCalledTimes(1);
      expect(result).toEqual(mockWorkItem);
    });

    it('获取不同作品时应该分别缓存', async () => {
      const mockWorkItem1 = createMockWorkItem(123);
      const mockWorkItem2 = createMockWorkItem(456);

      mockGetWorkInfoWithTransform
        .mockResolvedValueOnce([null, { data: mockWorkItem1 }])
        .mockResolvedValueOnce([null, { data: mockWorkItem2 }]);

      const { fetchWorkDetail } = useWorkDetail<VideoWorkItem>();

      // 获取第一个作品
      const result1 = await fetchWorkDetail(123);
      expect(result1).toEqual(mockWorkItem1);

      // 获取第二个作品
      const result2 = await fetchWorkDetail(456);
      expect(result2).toEqual(mockWorkItem2);

      // 再次获取第一个作品，应该从缓存获取
      const result3 = await fetchWorkDetail(123);
      expect(result3).toEqual(mockWorkItem1);

      // API应该只被调用两次
      expect(mockGetWorkInfoWithTransform).toHaveBeenCalledTimes(2);
    });
  });

  describe('缓存管理', () => {
    it('clearWorkDetailCacheEntry 应该清除指定作品的缓存', async () => {
      const mockWorkItem = createMockWorkItem(123);
      mockGetWorkInfoWithTransform.mockResolvedValue([
        null,
        { data: mockWorkItem },
      ]);

      const { fetchWorkDetail, clearWorkDetailCacheEntry } =
        useWorkDetail<VideoWorkItem>();

      // 先获取作品建立缓存
      await fetchWorkDetail(123);
      expect(mockGetWorkInfoWithTransform).toHaveBeenCalledTimes(1);

      // 再次获取，应该从缓存获取
      await fetchWorkDetail(123);
      expect(mockGetWorkInfoWithTransform).toHaveBeenCalledTimes(1);

      // 清除缓存
      clearWorkDetailCacheEntry(123);

      // 再次获取，应该重新调用API
      await fetchWorkDetail(123);
      expect(mockGetWorkInfoWithTransform).toHaveBeenCalledTimes(2);
    });

    it('clearWorkDetailCacheEntry 对不存在的缓存项应该安全处理', () => {
      const { clearWorkDetailCacheEntry } = useWorkDetail<VideoWorkItem>();

      // 清除不存在的缓存项，应该不报错
      expect(() => clearWorkDetailCacheEntry(999)).not.toThrow();
    });

    it('clearAllWorkDetailCache 应该清除所有缓存', async () => {
      const mockWorkItem1 = createMockWorkItem(123);
      const mockWorkItem2 = createMockWorkItem(456);

      mockGetWorkInfoWithTransform
        .mockResolvedValueOnce([null, { data: mockWorkItem1 }])
        .mockResolvedValueOnce([null, { data: mockWorkItem2 }])
        .mockResolvedValueOnce([null, { data: mockWorkItem1 }])
        .mockResolvedValueOnce([null, { data: mockWorkItem2 }]);

      const { fetchWorkDetail, clearAllWorkDetailCache } =
        useWorkDetail<VideoWorkItem>();

      // 建立两个作品的缓存
      await fetchWorkDetail(123);
      await fetchWorkDetail(456);
      expect(mockGetWorkInfoWithTransform).toHaveBeenCalledTimes(2);

      // 从缓存获取，不应该调用API
      await fetchWorkDetail(123);
      await fetchWorkDetail(456);
      expect(mockGetWorkInfoWithTransform).toHaveBeenCalledTimes(2);

      // 清除所有缓存
      clearAllWorkDetailCache();

      // 再次获取，应该重新调用API
      await fetchWorkDetail(123);
      await fetchWorkDetail(456);
      expect(mockGetWorkInfoWithTransform).toHaveBeenCalledTimes(4);
    });
  });

  describe('updateCurrentWorkProgress', () => {
    it('当没有当前作品时应该安全处理', () => {
      const { updateCurrentWorkProgress } = useWorkDetail<VideoWorkItem>();

      // 没有当前作品时调用，应该不报错
      expect(() => updateCurrentWorkProgress(80)).not.toThrow();
    });

    it('应该更新当前作品的进度', async () => {
      const mockWorkItem = createMockWorkItem(123, 50);
      mockGetWorkInfoWithTransform.mockResolvedValue([
        null,
        { data: mockWorkItem },
      ]);

      const {
        fetchWorkDetail,
        currentWork,
        updateCurrentWorkProgress,
        setCurrentWorkId,
      } = useWorkDetail<VideoWorkItem>();

      // 设置当前作品ID并获取作品
      setCurrentWorkId(123);
      await fetchWorkDetail(123);

      expect(currentWork.value?.progress).toBe(50);

      // 更新进度
      updateCurrentWorkProgress(80);

      expect(currentWork.value?.progress).toBe(80);
      // 其他属性应该保持不变
      expect(currentWork.value?.id).toBe(123);
      expect(currentWork.value?.name).toBe('测试作品 123');
    });

    it('应该同时更新缓存中的进度', async () => {
      const mockWorkItem = createMockWorkItem(123, 50);
      mockGetWorkInfoWithTransform.mockResolvedValue([
        null,
        { data: mockWorkItem },
      ]);

      const { fetchWorkDetail, updateCurrentWorkProgress, setCurrentWorkId } =
        useWorkDetail<VideoWorkItem>();

      // 设置当前作品ID并获取作品
      setCurrentWorkId(123);
      await fetchWorkDetail(123);

      // 更新进度
      updateCurrentWorkProgress(80);

      // 重新获取同一作品，应该从缓存获取已更新的数据
      const cachedWork = await fetchWorkDetail(123);
      expect(cachedWork?.progress).toBe(80);

      // API应该只被调用一次（第一次获取）
      expect(mockGetWorkInfoWithTransform).toHaveBeenCalledTimes(1);
    });

    it('当currentWorkId无效时不应该更新缓存', async () => {
      const mockWorkItem = createMockWorkItem(123, 50);
      mockGetWorkInfoWithTransform.mockResolvedValue([
        null,
        { data: mockWorkItem },
      ]);

      const { fetchWorkDetail, currentWork, updateCurrentWorkProgress } =
        useWorkDetail<VideoWorkItem>();

      // 不设置currentWorkId，直接获取作品
      await fetchWorkDetail(123);

      // 更新进度
      updateCurrentWorkProgress(80);

      expect(currentWork.value?.progress).toBe(80);

      // 重新获取作品，应该获取原始数据（因为缓存没有被更新）
      const workFromCache = await fetchWorkDetail(123);
      expect(workFromCache?.progress).toBe(50); // 原始进度

      // API应该只被调用一次
      expect(mockGetWorkInfoWithTransform).toHaveBeenCalledTimes(1);
    });
  });

  describe('hasWorkDetailCache', () => {
    it('应该正确检查缓存是否存在', async () => {
      const mockWorkItem = createMockWorkItem(123);
      mockGetWorkInfoWithTransform.mockResolvedValue([
        null,
        { data: mockWorkItem },
      ]);

      const { fetchWorkDetail, hasWorkDetailCache } =
        useWorkDetail<VideoWorkItem>();

      // 初始状态下没有缓存
      expect(hasWorkDetailCache(123)).toBe(false);

      // 获取作品详情后应该有缓存
      await fetchWorkDetail(123);
      expect(hasWorkDetailCache(123)).toBe(true);

      // 其他作品ID应该没有缓存
      expect(hasWorkDetailCache(456)).toBe(false);
    });
  });

  describe('batchUpdateWorkProgress', () => {
    it('应该批量更新作品进度', async () => {
      const mockWorkItem1 = createMockWorkItem(123, 30);
      const mockWorkItem2 = createMockWorkItem(456, 60);

      mockGetWorkInfoWithTransform
        .mockResolvedValueOnce([null, { data: mockWorkItem1 }])
        .mockResolvedValueOnce([null, { data: mockWorkItem2 }]);

      const {
        fetchWorkDetail,
        batchUpdateWorkProgress,
        setCurrentWorkId,
        currentWork,
      } = useWorkDetail<VideoWorkItem>();

      // 先获取两个作品的详情
      await fetchWorkDetail(123);
      await fetchWorkDetail(456);

      // 设置当前作品为123
      setCurrentWorkId(123);
      await fetchWorkDetail(123);

      // 批量更新进度
      batchUpdateWorkProgress([
        { workId: 123, progress: 80 },
        { workId: 456, progress: 90 },
      ]);

      // 当前作品的进度应该被更新
      expect(currentWork.value?.progress).toBe(80);

      // 切换到另一个作品，验证其进度也被更新
      setCurrentWorkId(456);
      await fetchWorkDetail(456);
      expect(currentWork.value?.progress).toBe(90);
    });

    it('应该跳过没有缓存的作品', () => {
      const { batchUpdateWorkProgress, hasWorkDetailCache } =
        useWorkDetail<VideoWorkItem>();

      // 对没有缓存的作品进行更新，应该安全处理
      expect(() => {
        batchUpdateWorkProgress([{ workId: 999, progress: 50 }]);
      }).not.toThrow();

      // 确认没有创建缓存
      expect(hasWorkDetailCache(999)).toBe(false);
    });

    it('应该正确更新当前选中作品的currentWork', async () => {
      const mockWorkItem = createMockWorkItem(123, 40);
      mockGetWorkInfoWithTransform.mockResolvedValue([
        null,
        { data: mockWorkItem },
      ]);

      const {
        fetchWorkDetail,
        batchUpdateWorkProgress,
        setCurrentWorkId,
        currentWork,
      } = useWorkDetail<VideoWorkItem>();

      // 设置当前作品并获取详情
      setCurrentWorkId(123);
      await fetchWorkDetail(123);

      expect(currentWork.value?.progress).toBe(40);

      // 批量更新进度
      batchUpdateWorkProgress([{ workId: 123, progress: 75 }]);

      // currentWork应该被同步更新
      expect(currentWork.value?.progress).toBe(75);
    });
  });

  describe('类型安全', () => {
    it('应该支持泛型类型约束', () => {
      // 测试类型推断是否正确
      const workDetail = useWorkDetail<VideoWorkItem>();

      // TypeScript 编译时验证，运行时无需额外断言
      expect(workDetail.currentWorkId).toBeDefined();
      expect(workDetail.currentWork).toBeDefined();
      expect(workDetail.isLoadingDetail).toBeDefined();
      expect(typeof workDetail.fetchWorkDetail).toBe('function');
      expect(typeof workDetail.setCurrentWorkId).toBe('function');
      expect(typeof workDetail.clearWorkDetailCacheEntry).toBe('function');
      expect(typeof workDetail.clearAllWorkDetailCache).toBe('function');
      expect(typeof workDetail.updateCurrentWorkProgress).toBe('function');
      expect(typeof workDetail.hasWorkDetailCache).toBe('function');
      expect(typeof workDetail.batchUpdateWorkProgress).toBe('function');
    });
  });

  describe('并发处理', () => {
    it('多个并发请求相同作品时应该正确处理', async () => {
      const mockWorkItem = createMockWorkItem(123);
      mockGetWorkInfoWithTransform.mockResolvedValue([
        null,
        { data: mockWorkItem },
      ]);

      const { fetchWorkDetail } = useWorkDetail<VideoWorkItem>();

      // 同时发起多个相同作品的请求
      const promises = [
        fetchWorkDetail(123),
        fetchWorkDetail(123),
        fetchWorkDetail(123),
      ];

      const results = await Promise.all(promises);

      // 所有结果应该相同
      results.forEach(result => {
        expect(result).toEqual(mockWorkItem);
      });

      // API应该只被调用一次（第一个请求），后续请求从缓存获取
      expect(mockGetWorkInfoWithTransform).toHaveBeenCalledTimes(1);
    });
  });
});
