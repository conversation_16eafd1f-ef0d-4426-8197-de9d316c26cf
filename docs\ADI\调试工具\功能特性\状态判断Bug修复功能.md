# 状态判断 Bug 修复记录

## 问题描述

在前端作品状态处理重构过程中，发现了一个关键 bug：某些组件在判断重新生成状态时，只传入了 `status` 字段而没有传入 `isRegenerate` 字段，导致无法正确判断是否为重新生成状态。

## 受影响的组件

### 1. VideoPlayer Hook (`useVideoPlayerState.ts`)

**问题**：Hook 函数缺少 `isRegenerate` 参数，导致状态判断函数无法获取完整的状态信息。

**修复前**：

```typescript
export function useVideoPlayerState(
  videoStatus: Ref<number>,
  progress: Ref<number>,
  isLoading: Ref<boolean>,
  isError: Ref<boolean>,
) {
  const showFailedState = computed(() => {
    const statusInfo = { status: videoStatus.value }; // ❌ 缺少 isRegenerate
    return isAnyFailedStatus(statusInfo);
  });
}
```

**修复后**：

```typescript
export function useVideoPlayerState(
  videoStatus: Ref<number>,
  progress: Ref<number>,
  isLoading: Ref<boolean>,
  isError: Ref<boolean>,
  isRegenerate: Ref<boolean | undefined>, // ✅ 新增参数
) {
  const showFailedState = computed(() => {
    const statusInfo = {
      status: videoStatus.value,
      isRegenerate: isRegenerate.value, // ✅ 包含完整状态信息
    };
    return isAnyFailedStatus(statusInfo);
  });
}
```

### 2. VideoPlayer 组件调用

**修复前**：

```typescript
const {
  /* ... */
} = useVideoPlayerState(
  videoStatusValue,
  progressValue,
  isLoading,
  isError, // ❌ 缺少 isRegenerate 参数
);
```

**修复后**：

```typescript
const isRegenerateValue = computed(() => props.workData?.isRegenerate);

const {
  /* ... */
} = useVideoPlayerState(
  videoStatusValue,
  progressValue,
  isLoading,
  isError,
  isRegenerateValue, // ✅ 传入 isRegenerate 参数
);
```

### 3. 重新生成完成状态判断

**修复前**：

```typescript
const showRegeneratedState = computed(() => {
  return videoStatus.value === WORK_STATUS.RECOMPLETED; // ❌ 直接状态比较
});
```

**修复后**：

```typescript
const showRegeneratedState = computed(() => {
  const statusInfo = {
    status: videoStatus.value,
    isRegenerate: isRegenerate.value,
  };
  return isRecompletedStatus(statusInfo); // ✅ 使用状态判断函数
});
```

## 修复的具体文件

1. **`src/views/EditProjectView/pages/SecondStepVideo/components/VideoPlayer/hooks/useVideoPlayerState.ts`**

   - 添加 `isRegenerate` 参数
   - 更新 `showFailedState` 计算属性
   - 更新 `showRegeneratedState` 计算属性
   - 更新 `statusText` 计算属性
   - 添加 `isRecompletedStatus` 导入

2. **`src/views/EditProjectView/pages/SecondStepVideo/components/VideoPlayer/index.vue`**
   - 添加 `isRegenerateValue` 计算属性
   - 更新 `useVideoPlayerState` 调用，传入新参数

## 根本原因

后端状态优化后，重新生成相关状态需要通过 **主状态 + isRegenerate 布尔值** 的组合来判断，而不是单独的状态值。如果只传入 `status` 而不传入 `isRegenerate`，状态判断函数无法区分：

- 普通生成中 vs 重新生成中
- 普通完成 vs 重新生成完成
- 普通失败 vs 重试生成失败

## 验证结果

- ✅ 所有单元测试通过 (14/14)
- ✅ 无编译错误
- ✅ 状态判断逻辑正确

## 预防措施

1. **类型检查**：确保所有状态判断函数调用都传入完整的 `WorkStatusInfo` 对象
2. **代码审查**：重点检查直接使用 `{ status: xxx }` 的地方
3. **测试覆盖**：为所有状态相关组件添加单元测试

## 补充修复的遗漏问题

### 4. 轮询状态变化判断 (`useWorkPolling.ts`)

**问题**：失败状态变化判断使用了直接状态比较，没有考虑 `isRegenerate` 字段。

**修复前**：

```typescript
if (
  previousStatus !== undefined &&
  isPollingStatus(previousStatus) &&
  (currentStatus === WORK_STATUS.FAILED ||
    currentStatus === WORK_STATUS.FAILED_AGAIN)
) {
  onWorkFailed?.(work.id);
}
```

**修复后**：

```typescript
if (previousStatus !== undefined) {
  const previousStatusInfo = {
    status: previousStatus,
    isRegenerate: work.isRegenerate,
  };
  const currentStatusInfo = getWorkStatusInfo(work);

  if (
    isEnhancedPollingStatus(previousStatusInfo) &&
    isAnyFailedStatus(currentStatusInfo)
  ) {
    onWorkFailed?.(work.id);
  }
}
```

### 5. 图片预览状态判断 (`ImagePreview/index.vue`)

**问题**：`showLoadingState` 和 `loadingText` 使用了直接状态比较。

**修复前**：

```typescript
const showLoadingState = computed(() => {
  return isPollingStatus(props.workInfo.status);
});

const loadingText = computed(() => {
  if (props.workInfo.status === WORK_STATUS.GENERATING) {
    return ['图片生成中', '请稍后预览'];
  } else if (props.workInfo.status === WORK_STATUS.REGENERATING) {
    return ['图片重新生成中', '请稍后预览'];
  }
});
```

**修复后**：

```typescript
const showLoadingState = computed(() => {
  const statusInfo = getWorkStatusInfo(props.workInfo);
  return isEnhancedPollingStatus(statusInfo);
});

const loadingText = computed(() => {
  const statusInfo = getWorkStatusInfo(props.workInfo);
  const statusName = getEnhancedWorkStatusName(statusInfo);

  if (statusName === '生成中') {
    return ['图片生成中', '请稍后预览'];
  } else if (statusName === '重新生成中') {
    return ['图片重新生成中', '请稍后预览'];
  }
});
```

### 6. 内容面板状态判断 (`ContentPanel/index.vue`)

**问题**：`showLoadingState`、`showFailedState` 和 `isEditDisabled` 使用了直接状态比较。

**修复前**：

```typescript
showLoadingState() {
  return isPollingStatus(this.workStatus);
},
showFailedState() {
  return this.workStatus === this.WORK_STATUS.FAILED;
},
isEditDisabled() {
  return !(
    this.workData.status === this.WORK_STATUS.COMPLETED ||
    this.workData.status === this.WORK_STATUS.RECOMPLETED
  );
}
```

**修复后**：

```typescript
showLoadingState() {
  const statusInfo = getWorkStatusInfo(this.workData);
  return isEnhancedPollingStatus(statusInfo);
},
showFailedState() {
  const statusInfo = getWorkStatusInfo(this.workData);
  return isAnyFailedStatus(statusInfo);
},
isEditDisabled() {
  const statusInfo = getWorkStatusInfo(this.workData);
  return !(
    isEnhancedCompletedStatus(statusInfo) ||
    isRecompletedStatus(statusInfo)
  );
}
```

### 7. 视频工具函数 (`utils.ts`)

**问题**：`isVideoSavable` 和 `isVideoDeletable` 使用了直接状态比较。

**修复前**：

```typescript
export function isVideoSavable(video: VideoWorkItem): boolean {
  return video.status === WORK_STATUS.COMPLETED;
}

export function isVideoDeletable(video: VideoWorkItem): boolean {
  return (
    video.status !== WORK_STATUS.GENERATING &&
    video.status !== WORK_STATUS.REGENERATING
  );
}
```

**修复后**：

```typescript
export function isVideoSavable(video: VideoWorkItem): boolean {
  const statusInfo = getWorkStatusInfo(video);
  return isEnhancedCompletedStatus(statusInfo);
}

export function isVideoDeletable(video: VideoWorkItem): boolean {
  const statusInfo = getWorkStatusInfo(video);
  return !isEnhancedPollingStatus(statusInfo);
}
```

### 8. 视频播放器状态管理 (`useVideoPlayerState.ts`)

**问题**：多个计算属性使用了直接状态比较，包括 `showLoadingState`、`loadingText`、`isGenerating`、`containerClasses`。

**修复内容**：

- 添加 `isRegenerate` 参数到 Hook 函数
- 更新所有状态相关的计算属性使用增强版状态判断
- 修复容器 CSS 类名的状态判断逻辑

## 修复统计

总共修复了 **8 个组件/文件** 中的 **15 个状态判断问题**：

1. ✅ VideoPlayer Hook - 5 个问题
2. ✅ VideoPlayer 组件调用 - 1 个问题
3. ✅ 轮询状态变化 - 1 个问题
4. ✅ 图片预览组件 - 2 个问题
5. ✅ 内容面板组件 - 3 个问题
6. ✅ 视频工具函数 - 2 个问题
7. ✅ 编辑禁用逻辑 - 1 个问题

## 相关文档

- [前端作品状态处理重构文档](./前端作品状态处理重构.md)
- [状态判断函数 API 文档](../src/constants/workStatus.ts)
