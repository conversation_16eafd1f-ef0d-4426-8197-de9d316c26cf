/**
 * 上传临时值存取工具
 * 处理上传过程中的临时文件管理和进度更新
 */

import { FILE_EXTENSIONS, FILE_TYPES } from '@/constants/fileType';
import { FileData } from '@/types/Material';
import { MaterialManageInfo } from '@/components/MaterialManager/types/index.ts';
import { getMaterialFullUrl } from './index.ts';

export const TMP_ID_KEY = 'resId';
type FILE_LIST_TYPE = FileData[] | MaterialManageInfo[]; // 文件列表类型 素材弹窗、素材库通用

/**
 * 文件上传数据结构
 * @interface uploadFileData
 */
export interface uploadFileData {
  bssInfo: {
    folderId: string | number; // 业务这边的文件夹id,使用这个
  };
  fileName: string;
  fileSize: number;
  fileType: number;
  fullViewUrl: string;
  id: string;
  videoSnapInfo: {
    id?: string; // 封面id字符串
    duration?: number; // 视频时长（单位秒）
  };
  percent?: number;
}

/**
 * 获取文件唯一ID
 * @param file 文件对象
 * @returns 文件唯一ID
 */
export const getFileUniqueId = (file: File): string => {
  // 使用文件名、大小、修改时间生成唯一ID
  return `${file.name}_${file.size}_${file.lastModified}`;
};

/**
 * 获取文件在列表中的索引
 * @param fileList 文件列表
 * @param file 文件对象
 * @returns 文件索引，不存在返回-1
 */
export const getMaterialFileIndex = (
  fileList: FILE_LIST_TYPE,
  file: File,
): number => {
  return fileList.findIndex(item => item.resId === getFileUniqueId(file));
};

/**
 * 创建临时文件数据（素材弹窗用）
 * @param file 文件对象
 * @param currentFolder 当前文件夹ID
 * @param fileType 文件类型
 * @returns 临时文件数据
 */
export const createTempFileData = (
  file: File,
  currentFolder: number,
  fileType: FILE_TYPES,
): FileData => {
  return {
    id: Math.floor(Math.random() * 100000), // 新生成的图片/视频无法拿到id值, 随机生成用于保证key唯一
    name: file.name,
    folderId: currentFolder,
    type: fileType,
    fileType,
    fileSize: file.size,
    createTime: new Date().getTime(),
    updateTime: new Date().getTime(),
    resId: getFileUniqueId(file),
    extra: {},
    suffix: FILE_EXTENSIONS[fileType as keyof typeof FILE_EXTENSIONS] || '',
    percent: 0,
    url: '',
  };
};

/**
 * 更新文件上传进度
 * @param fileList 文件列表
 * @param file 文件对象
 * @param percent 上传进度
 */
export const updateFileProgress = (
  fileList: FILE_LIST_TYPE,
  file: File,
  percent: number,
): void => {
  const idx = getMaterialFileIndex(fileList, file);
  if (idx !== -1) {
    // 更新文件上传进度
    fileList[idx].percent = percent;
  }
};

/**
 * 更新文件上传完成信息
 * @param fileList 文件列表
 * @param file 文件对象
 * @param uploadFileData 上传返回的数据
 * @param transDataFormUpload 数据转换函数
 */
export const updateFileUploadComplete = (
  fileList: FILE_LIST_TYPE,
  file: File,
  uploadFileData: uploadFileData,
): void => {
  const idx = getMaterialFileIndex(fileList, file);
  if (idx !== -1) {
    const { extra, url, name, resId, fileType, suffix } =
      transDataFormUpload(uploadFileData);
    // 更新文件信息
    const fileData = fileList[idx];
    fileData.extra = extra;
    fileData.url = url;
    fileData.name = name;
    fileData.resId = resId;
    fileData.fileType = fileType;
    fileData.suffix = suffix;
  }
};

/**
 * 移除文件
 * @param fileList 文件列表
 * @param file 文件对象
 */
export const removeFileFromList = (
  fileList: FILE_LIST_TYPE,
  file: File,
): void => {
  const idx = getMaterialFileIndex(fileList, file);
  if (idx !== -1) {
    // 上传失败 移除文件
    fileList.splice(idx, 1);
  }
};

/**
 * 将上传后返回的数据转为统一的数据结构(跟接口返回的保持一致)
 * @param {} file
 * @returns {*} FileData 统一数据结构
 */
export function transDataFormUpload(file: uploadFileData) {
  const {
    fileName: name,
    fileSize,
    fileType,
    bssInfo: { folderId },
    id: resId,
    videoSnapInfo,
    percent,
  } = file;
  return {
    createTime: new Date().getTime(),
    extra: {
      cover: videoSnapInfo?.id,
      duration: videoSnapInfo?.duration,
    },
    fileSize,
    fileType,
    flag: 0,
    folder: false,
    folderId: Number(folderId),
    id: Math.floor(Math.random() * 100000), // 新生成的图片/视频无法拿到id值, 随机生成用于保证key唯一
    name,
    resId,
    type: fileType,
    updateTime: new Date().getTime(),
    url: getMaterialFullUrl(resId, FILE_TYPES.WEBP, 'user', 160),
    suffix: FILE_EXTENSIONS[fileType as keyof typeof FILE_EXTENSIONS] || '',
    percent,
  };
}
