<template>
  <FaFileManagerSidebar
    :resizable="true"
    :min="184"
    :max="maxWidth"
    :width.sync="width"
    class="p-[16px] h-full"
    @resize="handleResize"
  >
    <FaFileManagerTree
      class="file-manager-demo--sidebar-tree flex-1"
      :expanded-keys.sync="expandedKeys"
      :selected-keys.sync="selectedKeys"
      :tree-data="dirTreeData"
      @select="handleSelectTree"
    >
      <template #icon>
        <img
          class="file-manager-demo--sidebar-tree-icon"
          src="@/assets/Material/icon_folder.svg"
        />
      </template>
    </FaFileManagerTree>

    <!-- 一期先不做，隐藏入口 -->
    <!-- <div class="file-manager-demo--sidebar-item-box">
      <FileManagerSidebarItem
        v-for="item in sidebarItem"
        :key="item.id"
        :selected="item.id === category"
        @click="handleClick(item)"
      >
        <fa-icon slot="icon" :type="item.icon" />
        {{ item.name }}
      </FileManagerSidebarItem>
    </div> -->
  </FaFileManagerSidebar>
</template>

<script>
import { Icon as FaIcon } from '@fk/faicomponent';
import { FileManager as FaFileManager } from '@fk/fa-component-cus';
import FileManagerSidebarItem from '@/components/MaterialManager/layout/FileManagerSidebar/components/FileManagerSidebarItem.vue';
import { INFO_KEYS, ROOT_FOLDER_ID } from '@/constants/material';
import store from '@/store';

export default {
  name: 'FileManagerSidebar',
  components: {
    FaIcon,
    FileManagerSidebarItem,
    FaFileManagerTree: FaFileManager.FileManagerTree,
    FaFileManagerSidebar: FaFileManager.FileManagerSidebar,
  },

  data() {
    return {
      /** 初始宽度（px） */
      width: 250,
      maxWidth: 400,
      expandedKeys: [],

      sidebarItem: [
        // {
        //   id: "file",
        //   icon: "file",
        //   name: "全部",
        // },
        {
          id: 'delete',
          icon: 'delete',
          name: '回收站',
        },
      ],
      selectedKeys: [ROOT_FOLDER_ID],
    };
  },

  computed: {
    category() {
      return store.state.meta.category;
    },
    dirTreeData() {
      return store.getters.dirTreeData;
    },
    filesInfo() {
      return store.getters.filesInfo;
    },
    currDirPath() {
      return store.getters.currDirPath;
    },
  },

  watch: {
    currDirPath(newValue = []) {
      if (!newValue || newValue.length == 0) return;
      this.expandedKeys = [
        ...new Set([
          ...this.expandedKeys,
          ...newValue.map(item => item[INFO_KEYS.ID]),
        ]),
      ];
    },
  },

  created() {
    this.windowResize();
    window.addEventListener('resize', this.windowResize);
  },

  beforeDestroy() {
    window.removeEventListener('resize', this.windowResize);
  },

  methods: {
    handleResize() {},
    handleClick({ id }) {
      store.commit('changeAllFilesSelect', false);
      store.commit('setCategory', id);
      store.commit('setId', -1);
    },

    handleSelectTree(id = 0) {
      store.commit('changeDir', id);
      store.dispatch('updateFolderContent');
    },

    windowResize() {
      if (this.throttle) return;

      this.throttle = true;
      requestAnimationFrame(() => {
        this.throttle = false;
      });

      this.maxWidth = document.documentElement.clientWidth * 0.6;
    },
  },
};
</script>

<style lang="scss">
.fa-file-manager-sidebar--container {
  @apply flex-col h-full;
}
.fa-file-manager-sidebar {
  @apply relative;
}
.fa-file-manager-sidebar .border {
  @apply absolute top-0 right-[-1px] w-[1px] h-full z-1 bg-[#e8e8e8] border-none;
}
.fa-file-manager--tree-scroller {
  @apply h-full;
}
.file-manager-demo--sidebar {
  &-tree {
    li {
      .fa-tree-node-content-wrapper:hover {
        @apply bg-[#FAFAFA];
      }

      .fa-tree-node-content-wrapper.fa-tree-node-selected {
        @apply bg-[#EEF2FF];
      }
    }
  }

  &-item-box {
    @apply pt-[16px] border-t border-solid border-[#e8e8e8];
  }
}
.fa-tree-icon__customize {
  @apply relative top-[-4px];
}
.file-manager-demo--sidebar-tree-icon {
  @apply inline-block size-[16px];
}
</style>
