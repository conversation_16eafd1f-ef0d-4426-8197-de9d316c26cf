# 图文编辑器预览页ImageSidebar生成中样式修改

## 修改概述

为图文编辑器预览页的ImageSidebar添加生成中的进度条遮罩层样式，使其与WorkListBarItem组件中的`.media-file-item__progress-overlay`保持一致的生成中状态显示。进度条遮罩层直接在ImageSidebar的`.image-text-editor-sidebar__item`容器内显示。

## 修改文件

### 主要修改文件
- `src/components/ImageTextEditor/components/ImageSidebar.vue` - 添加进度条遮罩层支持
- `src/views/EditProjectView/pages/SecondStepImage/components/ContentPanel/index.vue` - 简化props传递

## 具体修改内容

### 1. ImageSidebar组件修改

#### 1.1 移除LoadingPreview导入
移除了不再需要的LoadingPreview组件导入。

#### 1.2 Props简化
简化了生成状态相关的props，只保留必要的：
```typescript
const props = withDefaults(
  defineProps<{
    // ... 原有props
    /** 是否显示加载状态 */
    showLoading?: boolean;
    /** 加载进度百分比 */
    loadingPercent?: number;
  }>(),
  {
    // ... 原有默认值
    showLoading: false,
    loadingPercent: 0,
  },
);
```

移除了不再需要的props：
- `loadingText` - 不再需要文案提示
- `coverImg` - 不再需要封面图

#### 1.3 模板结构修改
将LoadingPreview替换为进度条遮罩层：

**修改前：**
```vue
<div class="image-text-editor-sidebar__img-wrap">
  <!-- 生成中状态显示LoadingPreview -->
  <LoadingPreview
    v-if="showLoading"
    :imgSrc="coverImg"
    :percent="loadingPercent"
    :loadingText="loadingText"
    class="image-text-editor-sidebar__loading-preview"
  />
  <!-- 正常状态显示图片 -->
  <ScImg
    v-else
    :src="img"
    class="image-text-editor-sidebar__img"
    fit="contain"
  />
</div>
```

**修改后：**
```vue
<div class="image-text-editor-sidebar__img-wrap">
  <!-- 正常状态显示图片 -->
  <ScImg
    :src="img"
    class="image-text-editor-sidebar__img"
    :style="imgStyle"
    fit="contain"
  />
  <!-- 生成中状态显示进度条遮罩层 -->
  <div
    v-if="showLoading"
    class="image-text-editor-sidebar__progress-overlay"
  >
    <div class="image-text-editor-sidebar__progress-circle">
      <fa-progress
        type="circle"
        strokeLinecap="square"
        strokeColor="#FFFFFF"
        trailColor="#e9e9e9"
        :width="direction === 'horizontal' ? 40 : 51"
        :strokeWidth="4"
        :percent="Math.floor(loadingPercent || 0)"
      />
    </div>
  </div>
</div>
```

#### 1.4 样式修改
将LoadingPreview样式替换为进度条遮罩层样式，参考WorkListBarItem的实现：

```scss
// 进度条遮罩层样式
.image-text-editor-sidebar__progress-overlay {
  /* 布局相关 */
  @apply absolute top-0 left-0 flex items-center justify-center;
  /* 尺寸相关 */
  @apply w-full h-full;
  /* 外观相关 */
  background-image: url('@/assets/EditProject/loading-new.svg');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  @apply rounded-[12px] overflow-hidden;
}

.image-text-editor-sidebar__progress-circle {
  /* 布局相关 */
  @apply relative flex items-center justify-center;

  :deep(.fa-progress) {
    .fa-progress-circle-trail {
      @apply stroke-white stroke-[2px] opacity-[0.2] !important;
    }

    .fa-progress-circle-path {
      @apply stroke-white stroke-[6px] !important;
    }

    .fa-progress-text {
      @apply text-white text-[12px];
    }
  }
}
```

### 2. ContentPanel组件修改

#### 2.1 简化Props传递
移除了不再需要的props传递：
```vue
<ImageSidebar
  :images="puzzleImages"
  direction="horizontal"
  :showAddBtn="false"
  :showCover="true"
  :showReplaceBtn="false"
  :disabled="isEditDisabled"
  :selectedIndex="selectedImageIndex"
  :enableImagePreview="true"
  :showLoading="showLoadingState"
  :loadingPercent="currentProgress"
  @select="handleImageSelect"
/>
```

移除了以下props：
- `:loadingText="loadingText"`
- `:coverImg="workData?.coverImg"`

#### 2.2 移除不需要的计算属性
移除了`loadingText`计算属性，因为新的进度条遮罩层不需要文案提示。

## 功能特性

### 1. 状态检测
- 自动检测作品状态是否为生成中（GENERATING）或重新生成中（REGENERATING）
- 使用统一的`isPollingStatus`函数进行状态判断

### 2. 动态显示
- 生成中状态：在图片上方显示进度条遮罩层
- 正常状态：显示正常的图片内容

### 3. 进度显示
- 显示实时的生成进度百分比
- 使用圆形进度条，白色描边
- 根据方向调整进度条大小（横向40px，纵向51px）

### 4. 视觉效果
- 使用与WorkListBarItem相同的背景图案
- 半透明遮罩层效果
- 白色进度条在深色背景上清晰可见

## 与现有功能的一致性

### 1. 与WorkListBarItem保持一致
- 使用相同的背景图案和样式
- 相同的进度条样式和颜色
- 相同的遮罩层效果

### 2. 遵循现有代码规范
- 使用BEM命名规范
- 使用UnoCSS工具类
- 遵循组件结构规范

### 3. 保持向后兼容
- 不影响现有的ImageSidebar功能
- 保持原有的事件处理逻辑
- 简化了组件接口，减少不必要的props

## 测试验证

- TypeScript编译检查通过
- 组件导入和注册正确
- 样式定义符合规范
- 进度条显示正常

## 总结

此次修改成功将图文编辑器预览页的ImageSidebar生成中样式从LoadingPreview改为与WorkListBarItem一致的进度条遮罩层样式。新的实现更加简洁，减少了不必要的props和依赖，同时保持了与其他组件的视觉一致性。
