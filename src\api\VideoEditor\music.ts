import { GET } from '../request';
import {
  ApiDubbing,
  ApiMusic,
  transformDubbingFromApiToDubbing,
  transformMusicFromApiToMusic,
} from './utils/inputTransform';

/** 通过资源id获取背景音乐资源详情 */
export const getMusicInfo = async (resId: string) => {
  // 当resId为空或者0时，无需调用请求
  if (!resId || resId === '0') {
    return [new Error('音乐资源ID为空'), null] as const;
  }

  const [err, res] = await GET<ApiMusic>('/api/resource/info', { resId });
  if (err) {
    return [err, null] as const;
  }
  return [
    null,
    { ...res, data: transformMusicFromApiToMusic(res.data) },
  ] as const;
};

/** 获取音乐资源分类列表 */
export const getMusicCategory = () => {
  return GET<
    {
      /** 分类id */
      id: number;
      /** 分类名称 */
      name: string;
    }[]
  >('/api/folder/categoryList?type=13');
};

/** 获取音乐资源列表 */
export const getMusicList = async (
  categoryId: number,
  searchQuery: string,
  page: number,
) => {
  const [err, res] = await GET<ApiMusic[]>('/api/resource/bgmList', {
    categoryId,
    name: searchQuery,
    page,
    limit: 20,
  });
  if (err) {
    return [err, null] as const;
  }

  const afterTransform = res.data.map(i => transformMusicFromApiToMusic(i));
  return [null, { ...res, data: afterTransform }] as const;
};

/** 通过配音员ID获取配音资源详情 */
export const getDubbingInfo = async (voiceId: string) => {
  // 当voiceId为空时，无需调用请求
  if (!voiceId) {
    return [new Error('配音员ID为空'), null] as const;
  }

  const [err, res] = await GET<ApiDubbing>('/api/resource/info', {
    voiceType: voiceId,
  });
  if (err) {
    return [err, null] as const;
  }
  return [
    null,
    { ...res, data: transformDubbingFromApiToDubbing(res.data) },
  ] as const;
};

/** 获取配音资源分类列表 */
export const getDubbingCategory = () => {
  return GET<
    {
      /** 分类id */
      id: number;
      /** 分类名称 */
      name: string;
    }[]
  >('/api/folder/categoryList?type=14');
};

/** 获取配音资源列表 */
export const getDubbingList = async (categoryId: number, page: number) => {
  const [err, res] = await GET<ApiDubbing[]>('/api/resource/voiceList', {
    categoryId,
    page,
    limit: 20,
  });
  if (err) {
    return [err, null] as const;
  }
  const afterTransform = res.data.map(i => transformDubbingFromApiToDubbing(i));
  return [null, { ...res, data: afterTransform }] as const;
};
