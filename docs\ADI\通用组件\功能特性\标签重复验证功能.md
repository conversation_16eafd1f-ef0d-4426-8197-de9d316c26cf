# Select 组件 Tags 模式重复标签验证功能

## 功能概述

为 Select 组件在 tags 模式下添加了重复标签的友好提示功能，解决了用户输入重复标签时没有反馈的问题。

## 实现方案

### 1. 双事件监听策略

在 `DynamicFormItem.vue` 组件中为 `fa-select` 添加了两个事件监听：

```vue
<fa-select
  v-model="formValues[formItem.prop]"
  class="dynamic-form-item__tags-select"
  dropdownClassName="dynamic-form-item__tags-dropdown-hidden"
  mode="tags"
  :maxTagCount="
    Math.min(formItem?.maxTagCount || MAX_TAGS_LIMIT, MAX_TAGS_LIMIT)
  "
  :maxTagTextLength="formItem?.maxTagTextLength || 20"
  v-bind="formItem.attrs || {}"
  :placeholder="formItem.placeholder"
  :disabled="formItem.disabled"
  @change="handleTagsChange"
  @inputKeydown="handleTagsInputKeydown"
>
</fa-select>
```

### 2. 状态跟踪机制

使用 `previousTagsList` ref 来跟踪标签的历史状态：

```typescript
/**
 * 之前的标签列表
 * @description 用于检测重复标签，存储上一次的标签状态
 */
const previousTagsList = ref<string[]>([]);
```

### 3. 处理函数实现

#### 3.1 标签变化处理（正常添加）

`handleTagsChange` 方法处理正常的标签添加和数量限制：

```typescript
const handleTagsChange = (value: string[]): void => {
  if (!value || !Array.isArray(value)) {
    previousTagsList.value = [];
    handleFieldChange();
    return;
  }

  // 检查标签数量是否超过限制
  if (value.length > MAX_TAGS_LIMIT) {
    const limitedTags = value.slice(0, MAX_TAGS_LIMIT);
    props.formValues[props.formItem.prop] = limitedTags;
    previousTagsList.value = [...limitedTags];
    message.warning(`最多只能输入${MAX_TAGS_LIMIT}个标签`);
    return;
  }

  // 更新之前的标签列表状态
  previousTagsList.value = [...value];
  handleFieldChange();
};
```

#### 3.2 重复标签检测（键盘输入）

`handleTagsInputKeydown` 方法专门处理重复标签检测：

```typescript
const handleTagsInputKeydown = (event: KeyboardEvent): void => {
  // 只处理回车键事件
  if (event.key !== 'Enter') return;

  // 获取输入框的值
  const target = event.target as HTMLInputElement;
  if (!target || !target.value) return;

  const inputValue = target.value.trim();
  if (!inputValue) return;

  // 获取当前已选择的标签
  const currentTags = (props.formValues[props.formItem.prop] as string[]) || [];

  // 检查是否为重复标签
  if (currentTags.includes(inputValue)) {
    message.warning(`标签"${inputValue}"已存在，请输入其他标签`);
    return;
  }

  // 检查是否已达到标签数量限制
  if (currentTags.length >= MAX_TAGS_LIMIT) {
    message.warning(`最多只能输入${MAX_TAGS_LIMIT}个标签`);
    return;
  }
};
```

## 功能特性

### 1. 重复标签检测

- 监听用户回车键输入
- 检查输入的标签是否已存在于当前标签列表中
- 如果重复，阻止添加并显示友好提示

### 2. 数量限制检测

- 同时检查标签数量是否已达到上限
- 如果达到上限，阻止添加并显示数量限制提示

### 3. 用户体验优化

- 提供明确的错误提示信息
- 阻止无效操作的默认行为
- 保持界面响应性

## 提示消息

### 重复标签提示

```text
标签"[标签名]"已存在，请输入其他标签
```

### 数量限制提示

```text
最多只能输入[MAX_TAGS_LIMIT]个标签
```

## 技术细节

### 事件处理策略

- **双事件监听**：同时监听 `change` 和 `inputKeydown` 事件
- **时序控制**：使用 `setTimeout` 延迟更新状态，确保事件执行顺序正确
- **状态跟踪**：使用 `previousTagsList` ref 跟踪标签的历史状态

### 核心解决方案

1. **事件时序问题**：fa-select 组件的 `change` 事件在 `inputKeydown` 事件之前触发
2. **状态同步**：通过延迟更新 `previousTagsList` 确保重复检测使用正确的历史状态
3. **重复检测**：在 `inputKeydown` 事件中使用历史状态进行重复标签检测

### 数据验证

- 获取输入框当前值并去除首尾空格
- 与历史标签列表进行比较（而非当前标签列表）
- 检查标签数量限制

### 错误处理

- 使用 `message.warning()` 显示用户友好的提示信息
- 不同错误情况显示不同的提示消息

## 兼容性

- 与现有的 `handleTagsChange` 方法兼容
- 不影响其他表单项类型的功能
- 保持与 AI 推荐功能的兼容性

## 测试建议

1. **重复标签测试**

   - 输入已存在的标签，按回车键
   - 验证是否显示重复标签提示
   - 验证标签是否未被添加

2. **数量限制测试**

   - 添加标签至上限数量
   - 尝试继续添加新标签
   - 验证是否显示数量限制提示

3. **正常添加测试**
   - 输入新的、不重复的标签
   - 验证标签是否正常添加
   - 验证不会显示错误提示

## 更新日期

2025-06-18
