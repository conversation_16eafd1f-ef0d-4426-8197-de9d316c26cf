<template>
  <BaseScrollContainer class="p-[0_20px]">
    <fa-spin :spinning="spinning" wrapperClassName="spin100vh">
      <div class="bg-white rounded-[16px] p-[24px] m-[20px_0]">
        <!-- 顶部栏 -->
        <div class="header-wrapper">
          <!-- 顶部活动状态筛选 -->
          <div class="status-tab">
            <div
              v-for="(tab, index) in tabList"
              :key="index"
              class="tab-item"
              :class="[{ 'tab-checked': tab.status == status }]"
              @click="handleTabChange(tab.status)"
            >
              {{ tab.text }}
            </div>
          </div>
          <div class="flex items-center">
            <SearchInput
              class="w-[240px] mr-[12px]"
              placeholder="请输入项目名称或项目ID"
              @search="handleSearchText"
            />
            <RefreshBtn @refresh="handleRefresh" />
          </div>
        </div>
        <ProjectEmptyState v-if="isShowEmptyState" @create="handleCreate" />
        <template v-else>
          <div class="project-list-wrapper">
            <div class="project-card">
              <create-card @create="handleCreate" />
            </div>
            <div
              v-for="project in projectList"
              :key="project.id"
              class="project-card"
            >
              <ProjectCard
                :project="project"
                @detail="handleShowDetail"
                @copy="handleCopy"
                @edit="handleEdit"
                @update="handleUpdateProjectName"
                @delete="handleDelete"
              />
            </div>
            <!-- 当数量小于6时，用空卡片填充，保证样式一致 -->
            <div
              v-for="n in emptyCount"
              :key="'empty-' + n"
              class="project-card empty"
            ></div>
          </div>
          <div class="pagination-container mt-[24px]">
            <fa-pagination
              v-show="total > 0"
              showQuickJumper
              totalTextPosition="right"
              :current="currentPage"
              :pageSize="pageSize"
              :total="total"
              @change="handlePageChange"
              @showSizeChange="handleSizeChange"
            />
          </div>
        </template>

        <!-- 项目详情弹窗 -->
        <ProjectDetailModal
          v-if="currentProject"
          :visible.sync="isShowDetailModal"
          :projectData="currentProject"
        />
      </div>
    </fa-spin>
  </BaseScrollContainer>
</template>

<script setup lang="ts">
import router from '@/router';
import { onMounted, getCurrentInstance, computed, onBeforeUnmount } from 'vue';
import SearchInput from '@/components/ProjectView/SearchInput.vue';
import BaseScrollContainer from '@/components/comm/BaseScrollContainer.vue';
import ProjectCard from '@/components/ProjectView/ProjectCard.vue';
import CreateCard from '@/components/ProjectView/CreateCard.vue';
import ProjectDetailModal from '@/components/ProjectView/ProjectDetailModal.vue';
import RefreshBtn from '@/components/ProjectView/RefreshBtn.vue';
import ProjectEmptyState from '@/components/ProjectView/ProjectEmptyState.vue';
import { ProjectData } from '@/types/Project';
import {
  tabList,
  status,
  spinning,
  currentPage,
  pageSize,
  total,
  projectList,
  isShowDetailModal,
  isShowEmptyState,
  currentProject,
  resetProjectViewState,
  fetchProjectList,
  handleTabChange,
  handleSearchText,
  handleRefresh,
  handleShowDetail,
  handleUpdateProjectName,
  handleCopy,
  handleDelete,
  handlePageChange,
  handleSizeChange,
  setupVisibilityListener,
} from '@/hook/useProjectView';
import { PROJECT_TYPE, PROJECT_STATUS } from '@/constants/project';
import { PAGE_SOURCE } from '@/constants/navigation';

const emptyCount = computed(() =>
  Math.max(0, 6 - (projectList.value.length + 1)),
);

const instance = getCurrentInstance();
// 处理创建新项目
const handleCreate = () => {
  const router = instance?.proxy?.$router;
  if (router?.push) {
    router.push('/');
  }
};

// 处理编辑项目
const handleEdit = (project: ProjectData) => {
  const { type, status } = project;
  const resolvedPath = router.resolve({
    path: `/${type === PROJECT_TYPE.VIDEO ? 'video-project' : 'image-project'}`,
    query: {
      projectId: String(project.id),
      templateId: String(project.templateId),
      step: String(status == PROJECT_STATUS.DRAFT ? 0 : 1), //【草稿】状态时进入第一步，其他状态均进入第二步
      from: PAGE_SOURCE.PROJECT, // 添加来源标识：项目列表
    },
  });
  window.open(resolvedPath.href, '_blank');
};

// 自动执行监听器设置和清理
onMounted(() => {
  const cleanup = setupVisibilityListener();
  onBeforeUnmount(cleanup);
  // 初始化时获取一次数据
  resetProjectViewState();
  fetchProjectList();
});
</script>

<style lang="scss" scoped>
.header-wrapper {
  @apply flex justify-between items-center mb-[24px];
  .status-tab {
    @apply flex items-center h-[40px] box-border p-[4px] rounded-[20px] bg-[#f3f3f5] text-[15px] text-[#999];
  }

  .tab-item {
    @apply relative h-[32px] leading-[32px] px-[16px] box-border cursor-pointer hover:text-[#111];

    &.tab-checked {
      @apply rounded-[16px] bg-white text-[#111];
    }
  }
}
.project-list-wrapper {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(252px, 1fr));
  gap: 24px;
  max-width: 1776px;
  min-width: 0;
  width: 100%;
  margin: 0 auto;
}
.project-card {
  min-width: 0;
  box-sizing: border-box;
}
.project-card.empty {
  background: none;
  border: none;
  box-shadow: none;
  pointer-events: none;
}
</style>
