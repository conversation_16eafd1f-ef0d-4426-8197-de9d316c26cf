/**
 * 版本控制组件库入口文件
 * @description 统一导出所有版本控制相关的组件和工具
 */

// 导出组件
export { default as VersionSwitch } from './VersionSwitch.vue';
export { default as VersionIcon } from '@/components/comm/ScVersionIcon.vue';

// 导出工具类和配置
export { default as VersionController } from '@/utils/versionControl';
export {
  VERSION_FEATURE_CONFIG,
  type VersionFeatureConfig,
} from '@/utils/versionControl/config';

// 导出常用的版本校验函数
export {
  checkVersionPermission,
  checkMouthShapeFeature,
} from '@/utils/versionControl';
export { showVersionMsg } from '@/utils/version';

// 导出类型定义
export type {
  VersionPermissionChecker,
  VersionDowngradeCallback,
  FeatureVersionChecker,
  DowngradeHandler,
} from '@/utils/versionControl/types';
