<template>
  <BaseScrollContainer>
    <!-- 页面左右的边距不放在BaseScrollContainer滚动容器上，避免列表内挂载的弹窗超出容器时，部分内容被裁切掉 -->
    <div class="p-[0_20px]">
      <div class="bg-white rounded-[16px] m-[20px_0] min-w-[1068px] size-full">
        <div class="mb-[24px]">
          <fa-menu
            class="fa-menu__spec-horizontal-round fa-menu__spec-horizontal"
            :selectedKeys="[currentMenu]"
            mode="horizontal"
          >
            <fa-menu-item
              v-for="item in menuList"
              :key="item.componentName"
              @click="handleSelect(item.componentName)"
            >
              {{ item.name }}
            </fa-menu-item>
          </fa-menu>
        </div>
        <div class="p-[0_24px_20px]">
          <component :is="componentMap[currentMenu]" :projectId="projectId" />
        </div>
      </div>
    </div>
  </BaseScrollContainer>
</template>

<script setup lang="ts">
import { ref, defineProps } from 'vue';
import BaseScrollContainer from '@/components/comm/BaseScrollContainer.vue';
import WorkList from '@/components/WorkView/WorkList.vue';
// import WorkDownloadRecord from '@/components/WorkView/WorkDownloadRecord.vue';

// Define Props
const props = defineProps<{ projectId?: number }>();
const { projectId } = props;

// State
const componentMap = {
  WorkList,
  // WorkDownloadRecord,
};
const menuList = [
  { name: '作品列表', componentName: 'WorkList' },
  // { name: '下载记录', componentName: 'WorkDownloadRecord' },
];
const currentMenu = ref<keyof typeof componentMap>('WorkList');

// Methods
const handleSelect = (key: string) => {
  currentMenu.value = key as keyof typeof componentMap;
};
</script>

<style lang="scss" scoped>
:deep(.fa-menu) {
  .fa-menu-item {
    @apply h-[60px] lh-[60px] text-[17px] text-text ml-0 pl-[4px] pr-[4px] mr-[32px] border-none;

    &.fa-menu-item-active {
      @apply border-b-0 text-primary;
    }

    &.fa-menu-item-selected {
      @apply text-text text-primary font-bold border-b-[3px] border-solid;
    }
  }
}
</style>
