import { ref, computed, watch, Ref } from 'vue';
import type Player from 'video.js/dist/types/player';
import videojs from 'video.js';
import { PlayerState, type VideoPlayerConfig } from '../types';
import {
  getWorkStatusInfo,
  isNormalCompletedWork,
  isPollingWork,
  isAnyCompletedWork,
} from '@/constants/workStatus';
import { PLAYER_DEFAULT_CONFIG } from '../constants/player.constants';

import { usePlaybackState } from './usePlaybackState';
import { useErrorHandling } from './useErrorHandling';
import { usePlayerConfig } from './usePlayerConfig';
import { memoryMonitor } from '../utils/memoryMonitor';
import { logger } from '@/utils/logger';

interface UsePlayerOptions {
  /** 视频元素引用 */
  videoElement: Ref<HTMLVideoElement | null>;
  /** 视频源URL */
  videoUrl: Ref<string>;
  /** 作品数据对象（包含完整的状态信息） */
  workItem: Ref<{ id?: number; status: number; editAgain?: boolean } | null>;
  /** 封面图URL */
  posterUrl?: Ref<string>;
  /** 自定义配置 */
  config?: Partial<VideoPlayerConfig>;
  /** 初始化完成回调 */
  onInitialized?: () => void;
}

/**
 * 统一的播放器管理 hook
 * @description 整合了播放器的初始化、状态管理和生命周期管理
 */
export function usePlayer({
  videoElement,
  videoUrl,
  workItem,
  posterUrl,
  config: customConfig = {},
  onInitialized,
}: UsePlayerOptions) {
  // 播放器实例和状态
  const player = ref<Player | null>(null);
  const playerState = ref<PlayerState>(PlayerState.UNINITIALIZED);
  const isInitialized = ref(false);

  // 视频加载状态管理
  const isVideoLoading = ref(false);
  const isVideoReadyToPlay = ref(false);
  const shouldShowPoster = ref(true);
  const hasUserClickedPlay = ref(false); // 标记用户是否已点击播放
  const isSeekingLoading = ref(false); // 进度条切换时的加载状态

  // 防抖相关状态
  let initializationTimer: ReturnType<typeof setTimeout> | null = null;

  // 使用配置管理Hook
  const { getVideoJSConfig, isConfigValid } = usePlayerConfig(
    videoUrl,
    posterUrl,
    customConfig,
  );

  // 使用错误处理Hook
  const {
    hasError,
    setError,
    clearError,
    retry: retryWithErrorHandling,
  } = useErrorHandling({
    maxRetries: 3,
    retryDelay: 1000,
    onError: () => {
      playerState.value = PlayerState.ERROR;
    },
  });

  // 计算属性
  const isLoading = computed(() => playerState.value === PlayerState.LOADING);
  const isError = computed(
    () => hasError.value || playerState.value === PlayerState.ERROR,
  );
  // 控制条加载状态：包括视频加载和进度条切换加载
  const isControlsLoading = computed(
    () => isVideoLoading.value || isSeekingLoading.value,
  );
  const canShowVideo = computed(() => {
    if (!videoUrl.value || !isConfigValid.value) return false;

    // 只有在普通完成状态时才能显示视频（排除生成中、重新生成中、重新生成完成、失败状态）
    if (!workItem.value) return false;

    const statusInfo = getWorkStatusInfo(workItem.value);
    return isNormalCompletedWork(statusInfo);
  });

  // 使用播放状态管理 hook
  const playbackState = usePlaybackState({
    canShowVideo,
    isInitialized,
    isError,
  });

  /**
   * 设置视频加载事件监听器
   * @description 监听视频加载相关事件，管理封面图显示状态
   */
  const setupVideoLoadingEvents = () => {
    if (!player.value) return;

    // 监听播放开始事件 - 用户点击播放时触发
    player.value.on('play', () => {
      hasUserClickedPlay.value = true;

      // 只有在视频还未准备好播放时才显示loading
      if (!isVideoReadyToPlay.value) {
        isVideoLoading.value = true;
        // 保持封面图显示，直到视频准备好播放
        shouldShowPoster.value = true;
      } else {
        // 视频已经准备好，不需要loading
        isVideoLoading.value = false;
      }
    });

    // 监听视频开始加载事件 - 只有用户点击播放后且视频未准备好才设置加载状态
    player.value.on('loadstart', () => {
      // 只有用户已点击播放且视频还未准备好才显示加载状态
      if (hasUserClickedPlay.value && !isVideoReadyToPlay.value) {
        isVideoLoading.value = true;
        isVideoReadyToPlay.value = false;
      }
    });

    // 监听视频可以开始播放事件（已缓冲足够数据）
    player.value.on('canplay', () => {
      isVideoReadyToPlay.value = true;
      isVideoLoading.value = false; // 合并：停止loading动画
      // 视频准备好后隐藏封面图
      shouldShowPoster.value = false;
    });

    // 监听暂停事件
    player.value.on('pause', () => {
      isVideoLoading.value = false;
      // 暂停时不显示封面图，保持当前帧
    });

    // 监听视频结束事件
    player.value.on('ended', () => {
      isVideoLoading.value = false;
      isSeekingLoading.value = false; // 重置进度条加载状态
      hasUserClickedPlay.value = false; // 重置用户点击状态
      // 视频结束后可以选择显示封面图
      shouldShowPoster.value = true;
    });

    // 监听进度条切换开始事件
    player.value.on('seeking', () => {
      // 当用户切换进度条时，显示控制条loading效果
      isSeekingLoading.value = true;
    });

    // 监听进度条切换完成事件
    player.value.on('seeked', () => {
      // 当进度条切换完成且视频准备好播放时，隐藏控制条loading效果
      isSeekingLoading.value = false;
    });

    // 监听加载错误事件
    player.value.on('error', () => {
      // 确保所有状态都重置到安全状态
      isVideoLoading.value = false;
      isVideoReadyToPlay.value = false;
      hasUserClickedPlay.value = false; // 重置用户点击状态
      shouldShowPoster.value = true;
      isSeekingLoading.value = false; // 重置进度条加载状态

      // 错误边界处理：确保播放器状态一致
      try {
        if (player.value && !player.value.paused()) {
          player.value.pause();
        }
      } catch {
        // 静默处理暂停错误
      }
    });
  };

  /**
   * 清理视频加载事件监听器
   * @description 移除所有视频加载相关的事件监听器
   */
  const cleanupVideoLoadingEvents = () => {
    if (!player.value) return;

    try {
      player.value.off('play');
      player.value.off('loadstart');
      player.value.off('canplay');
      player.value.off('pause');
      player.value.off('ended');
      player.value.off('seeking');
      player.value.off('seeked');
      player.value.off('error');
    } catch {
      // 静默处理清理错误
    }
  };

  /**
   * 重置播放器状态
   * @description 重置所有状态到初始值，但不销毁播放器实例
   */
  const resetPlayerState = () => {
    if (player.value) {
      // 暂停播放
      player.value.pause();
      // 重置播放时间
      player.value.currentTime(0);
      // 重置音量到默认值 1
      player.value.volume(1);
      // 重置播放速率
      player.value.playbackRate(1);
      // 重置错误状态
      player.value.error(undefined);
    }

    // 重置播放相关状态
    playbackState.resetState();

    // 重置视频加载状态
    isVideoLoading.value = false;
    isVideoReadyToPlay.value = false;
    shouldShowPoster.value = true;
    isSeekingLoading.value = false;

    // 重置状态标志
    isInitialized.value = false;
    playerState.value = PlayerState.UNINITIALIZED;
  };

  /**
   * 销毁播放器
   * @description 清理播放器实例和相关资源，防止内存泄漏
   */
  const destroyPlayer = async (cleanupEvents?: () => void) => {
    // 清理防抖定时器
    if (initializationTimer) {
      clearTimeout(initializationTimer);
      initializationTimer = null;
      logger.memory(
        `VideoPlayer: 已清理初始化定时器，作品ID: ${workItem.value?.id}`,
      );
    }

    if (player.value) {
      const currentWorkId = workItem.value?.id;

      try {
        // 暂停播放
        if (!player.value.paused()) {
          player.value.pause();
        }

        // 清理视频加载事件监听器
        cleanupVideoLoadingEvents();

        // 清理事件监听器（如果提供了清理函数）
        if (cleanupEvents) {
          try {
            cleanupEvents();
          } catch {
            // 静默处理清理错误
          }
        }

        // 清空视频源，释放媒体资源
        try {
          player.value.src('');
          player.value.load();
        } catch {
          // 静默处理清空错误
        }

        // 销毁播放器实例
        try {
          player.value.dispose();
        } catch {
          // 静默处理销毁错误
        } finally {
          player.value = null;
        }

        logger.memory(
          `VideoPlayer: 播放器实例已销毁，作品ID: ${currentWorkId}`,
        );

        // 记录播放器销毁（用于内存监控）- 使用销毁时的workId
        if (currentWorkId) {
          memoryMonitor.recordPlayerDestroyed(currentWorkId);
        }

        // 在开发环境下尝试强制垃圾回收
        if (import.meta.env.DEV) {
          setTimeout(() => {
            memoryMonitor.forceGarbageCollection();
          }, 100);
        }
      } catch (error) {
        logger.memoryError('VideoPlayer: 销毁播放器时出错', error);
        // 即使出错也要清空引用，防止内存泄漏
        player.value = null;

        // 即使出错也要记录销毁，避免内存监控数据不一致
        if (currentWorkId) {
          memoryMonitor.recordPlayerDestroyed(currentWorkId);
        }
      }
    }

    // 重置所有状态
    resetPlayerState();
  };

  /**
   * 实际的播放器初始化逻辑
   * @description 创建并配置播放器实例，依赖组件key策略和Vue生命周期保护
   */
  const _doInitializePlayer = async (cleanupEvents?: () => void) => {
    const currentWorkId = workItem.value?.id;

    // 检查基本条件
    if (!videoElement.value || !canShowVideo.value || !currentWorkId) {
      logger.memory('VideoPlayer: 初始化条件不满足，跳过初始化', {
        hasVideoElement: !!videoElement.value,
        canShowVideo: canShowVideo.value,
        currentWorkId,
      });
      return;
    }

    try {
      // 如果已经存在实例且URL没有变化，不需要重新初始化
      if (
        player.value &&
        player.value.currentSrc() === videoUrl.value &&
        playerState.value === PlayerState.READY &&
        currentWorkId === workItem.value?.id
      ) {
        logger.memory('VideoPlayer: 播放器已就绪且URL未变化，跳过重新初始化');
        return;
      }

      playerState.value = PlayerState.LOADING;

      // 销毁现有实例（包括事件监听器清理）
      await destroyPlayer(cleanupEvents);

      // 获取配置并创建新实例
      const config = getVideoJSConfig();
      if (!config.sources || config.sources.length === 0) {
        logger.memory('VideoPlayer: 无有效视频源，跳过初始化');
        playerState.value = PlayerState.UNINITIALIZED;
        return;
      }

      try {
        player.value = videojs(videoElement.value, config);

        // 预加载设置 - 设置为none，只有用户点击播放时才加载
        if (player.value) {
          player.value.preload('none');
        }

        // 设置视频加载事件监听器
        setupVideoLoadingEvents();
      } catch (error) {
        playerState.value = PlayerState.ERROR;
        // 重置所有加载状态
        isVideoLoading.value = false;
        isVideoReadyToPlay.value = false;
        shouldShowPoster.value = true;
        throw error; // 重新抛出错误，让上层处理
      }

      isInitialized.value = true;
      playerState.value = PlayerState.READY;

      // 记录播放器创建（用于内存监控）
      memoryMonitor.recordPlayerCreated(currentWorkId);

      logger.memory(`VideoPlayer: 播放器初始化成功，作品ID: ${currentWorkId}`);
      onInitialized?.();
    } catch (error) {
      logger.memoryError('初始化播放器失败', error);
      setError(error as Error);
      await destroyPlayer(cleanupEvents);
      playerState.value = PlayerState.ERROR;
    }
  };

  /**
   * 初始化播放器（带防抖和超时保护）
   * @description 创建并配置播放器实例，使用防抖减少频繁重建的性能开销
   */
  const initializePlayer = async (cleanupEvents?: () => void) => {
    // 清除之前的防抖定时器
    if (initializationTimer) {
      clearTimeout(initializationTimer);
      initializationTimer = null;
    }

    // 使用防抖延迟，减少频繁切换时的性能开销
    return new Promise<void>((resolve, reject) => {
      initializationTimer = setTimeout(async () => {
        // 添加超时保护，防止初始化卡住
        const timeoutPromise = new Promise<void>((_, timeoutReject) => {
          const timeoutId = setTimeout(() => {
            timeoutReject(new Error('VideoPlayer initialization timeout'));
          }, PLAYER_DEFAULT_CONFIG.INIT_TIMEOUT);

          // 清理函数
          return timeoutId;
        });

        try {
          await Promise.race([
            _doInitializePlayer(cleanupEvents),
            timeoutPromise,
          ]);
          resolve();
        } catch (error) {
          reject(error);
        } finally {
          initializationTimer = null;
        }
      }, PLAYER_DEFAULT_CONFIG.INIT_DEBOUNCE_DELAY); // 防抖延迟，略大于切换间隔
    });
  };

  // 计算当前状态信息，用于状态变化检测
  const currentStatusInfo = computed(() => {
    if (!workItem.value) return null;
    return getWorkStatusInfo(workItem.value);
  });

  // 使用 ref 存储上一次的状态信息，用于状态变化比较
  const previousStatusInfo = ref<ReturnType<typeof getWorkStatusInfo> | null>(
    null,
  );

  /**
   * 判断是否需要重新初始化播放器
   * 当作品从生成中状态变为完成状态时，需要重新初始化播放器
   */
  const shouldReinitializePlayer = (
    oldStatusInfo: ReturnType<typeof getWorkStatusInfo> | null,
    newStatusInfo: ReturnType<typeof getWorkStatusInfo> | null,
  ): boolean => {
    if (!oldStatusInfo || !newStatusInfo) return false;

    return isPollingWork(oldStatusInfo) && isAnyCompletedWork(newStatusInfo);
  };

  // 监听视频URL变化
  watch(videoUrl, async (newUrl, oldUrl) => {
    if (newUrl !== oldUrl) {
      // 在重新初始化之前，先重置状态
      resetPlayerState();
      await initializePlayer();
    }
  });

  // 监听作品状态变化
  watch(
    currentStatusInfo,
    async newStatusInfo => {
      const oldStatusInfo = previousStatusInfo.value;

      // 更新上一次状态信息
      previousStatusInfo.value = newStatusInfo;

      // 检查是否需要重新初始化播放器
      if (shouldReinitializePlayer(oldStatusInfo, newStatusInfo)) {
        // 在重新初始化之前，先重置状态
        resetPlayerState();
        await initializePlayer();
      }
    },
    { immediate: true },
  );

  /**
   * 重试初始化播放器
   */
  const retryInitialization = () => {
    clearError();
    return retryWithErrorHandling(() => initializePlayer());
  };

  return {
    player,
    playerState,
    isInitialized,
    isLoading,
    isError,
    canShowVideo,
    initializePlayer,
    destroyPlayer,
    resetPlayerState,
    retryInitialization,
    // 视频加载状态
    isVideoLoading,
    isVideoReadyToPlay,
    shouldShowPoster,
    // 控制条加载状态
    isControlsLoading,
    // 导出播放状态
    ...playbackState,
  };
}
