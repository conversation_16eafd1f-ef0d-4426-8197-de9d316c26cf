import { ref, onUnmounted, type Ref } from 'vue';
import type { CarouselInstance } from '@/types/element-ui';

/**
 * 轮播图鼠标滑动功能 Hook
 * @param carouselRef 轮播图组件引用
 * @param options 配置选项
 * @returns 鼠标事件处理函数和状态
 */
export function useCarouselSwipe(
  carouselRef: Ref<CarouselInstance | null>,
  options: {
    /** 是否禁用滑动功能 */
    disabled?: Ref<boolean>;
    /** 滑动阈值，超过此距离才触发切换，默认50px */
    threshold?: number;
    /** 滑动完成回调 */
    onSwipe?: (direction: 'prev' | 'next') => void;
  } = {},
) {
  const { disabled, threshold = 50, onSwipe } = options;

  // 鼠标滑动相关状态
  const isDragging = ref(false);
  const startX = ref(0);
  const currentX = ref(0);

  /**
   * 重置拖拽状态
   */
  const resetDragState = () => {
    isDragging.value = false;
    startX.value = 0;
    currentX.value = 0;
  };

  /**
   * 处理全局鼠标移动事件
   * @param event 鼠标事件
   */
  const handleGlobalMouseMove = (event: MouseEvent) => {
    if (!isDragging.value) {
      return;
    }

    currentX.value = event.clientX;
    event.preventDefault();
  };

  /**
   * 处理全局鼠标抬起事件
   * @param event 鼠标事件
   */
  const handleGlobalMouseUp = (event: MouseEvent) => {
    if (!isDragging.value) {
      return;
    }

    const deltaX = currentX.value - startX.value;

    // 判断滑动方向和距离
    if (Math.abs(deltaX) > threshold) {
      if (deltaX > 0) {
        // 向右滑动，显示上一张
        carouselRef.value?.prev();
        onSwipe?.('prev');
      } else {
        // 向左滑动，显示下一张
        carouselRef.value?.next();
        onSwipe?.('next');
      }
    }

    // 重置状态
    resetDragState();

    // 移除全局事件监听
    document.removeEventListener('mousemove', handleGlobalMouseMove);
    document.removeEventListener('mouseup', handleGlobalMouseUp);

    event.preventDefault();
  };

  /**
   * 处理鼠标按下事件
   * @param event 鼠标事件
   */
  const handleMouseDown = (event: MouseEvent) => {
    // 如果功能被禁用，直接返回
    if (disabled?.value) {
      return;
    }

    isDragging.value = true;
    startX.value = event.clientX;
    currentX.value = event.clientX;

    // 阻止默认行为，避免拖拽选择文本
    event.preventDefault();

    // 添加全局鼠标事件监听
    document.addEventListener('mousemove', handleGlobalMouseMove);
    document.addEventListener('mouseup', handleGlobalMouseUp);
  };

  /**
   * 处理鼠标移动事件（轮播图容器内）
   * @param event 鼠标事件
   */
  const handleMouseMove = (event: MouseEvent) => {
    // 这个函数主要用于阻止默认行为
    if (isDragging.value) {
      event.preventDefault();
    }
  };

  /**
   * 处理鼠标抬起事件（轮播图容器内）
   * @param event 鼠标事件
   */
  const handleMouseUp = (event: MouseEvent) => {
    // 这个函数主要用于阻止默认行为
    if (isDragging.value) {
      event.preventDefault();
    }
  };

  /**
   * 处理鼠标离开事件
   */
  const handleMouseLeave = () => {
    // 鼠标离开轮播图区域时重置拖拽状态并移除全局监听
    if (isDragging.value) {
      resetDragState();
      document.removeEventListener('mousemove', handleGlobalMouseMove);
      document.removeEventListener('mouseup', handleGlobalMouseUp);
    }
  };

  // 组件卸载时清理全局鼠标事件监听器
  onUnmounted(() => {
    document.removeEventListener('mousemove', handleGlobalMouseMove);
    document.removeEventListener('mouseup', handleGlobalMouseUp);
  });

  return {
    // 状态
    isDragging,

    // 事件处理函数
    handleMouseDown,
    handleMouseMove,
    handleMouseUp,
    handleMouseLeave,

    // 工具函数
    resetDragState,
  };
}
