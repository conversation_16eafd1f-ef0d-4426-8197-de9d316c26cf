<template>
  <div class="bg-background p-[20px]">
    <MaterialManager />
  </div>
</template>

<script>
import MaterialManager from '@/components/MaterialManager/MaterialManager.vue';

export default {
  name: 'MetaView',
  components: {
    MaterialManager,
  },
  data() {
    return {
      // Define reactive data properties here
    };
  },
  methods: {
    // Define component methods here
  },
};
</script>

<style scoped>
/* Add component-specific styles here */
</style>
