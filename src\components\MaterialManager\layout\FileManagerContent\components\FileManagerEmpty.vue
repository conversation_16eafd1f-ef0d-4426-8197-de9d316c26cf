<template>
  <div class="file-manager-demo-empty flex jc-center ai-center flex-column">
    <div class="file-empty-state__img-box">
      <img class="file-empty-state__img" src="@/assets/common/empty.webp" />
    </div>
    <div v-if="searchKey" class="file-empty-state__title">
      无 “{{ searchKey }}” 相关搜索结果，请尝试其他关键词
    </div>
    <div v-else class="file-empty-state__title">没有文件数据</div>
  </div>
</template>

<script>
export default {
  name: 'FileManagerEmpty',

  props: {
    searchKey: {
      type: String,
      default: '',
    },
  },
};
</script>

<style lang="scss" scoped>
.file-manager-demo-empty {
  height: calc(100% - 100px);
  .file-empty-state__img-box {
    @apply mb-[27px];
    .file-empty-state__img {
      @apply w-[159px] h-[117px] object-contain;
    }
  }
  .file-empty-state__title {
    @apply text-assist text-[15px] mb-[16px];
  }
}
</style>
