import { PROJECT_STATUS } from '@/constants/project';

export interface ProjectStatusInfoParams {
  status: number; // 项目状态
  sucNum: number; // 成功生成作品的数量
  failNum: number; // 失败作品的数量
  totalNum: number; // 总生成作品数量
  saveNum: number; // 已保存作品的数量
}

/**
 * 获取项目状态相关信息
 * @param params - 项目状态相关参数
 * @returns 包含状态标签、值和键的对象
 * @property label 状态标签
 * @property value 状态值
 */
export function getProjectStatusInfo({
  status,
  sucNum,
  failNum,
  totalNum,
  saveNum,
}: ProjectStatusInfoParams) {
  if (status === PROJECT_STATUS.DRAFT) {
    return {
      label: '剩余生成数量',
      value: '暂无',
    };
  } else if (status === PROJECT_STATUS.GENERATING) {
    return {
      label: '剩余生成数量',
      value: totalNum - sucNum - failNum,
    };
  } else if (status === PROJECT_STATUS.TO_BE_SAVED) {
    return {
      label: '待保存作品数量',
      value: sucNum - saveNum,
    };
  } else if (status === PROJECT_STATUS.COMPLETED) {
    return {
      label: '已保存作品数量',
      value: saveNum,
    };
  }
  return {
    label: '',
    value: '',
  };
}
