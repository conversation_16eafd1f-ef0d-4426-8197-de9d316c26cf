<template>
  <BaseStepLayout>
    <!-- 左侧作品列表 -->
    <template #left-panel>
      <WorkListBar
        :workList="displayedWorkList"
        :loading="isFirstPageLoading"
        :workListBarType="WorkType.IMAGE"
        :selectedWorkIds="selectedWorkIds"
        :current-page="currentPage"
        :page-size="pageSize"
        :total-items="sucNum"
        :is-loading-more="isLoadingMoreData"
        :has-loaded-all="hasLoadedAll"
        :has-loading-error="hasLoadingError"
        :loading-error="loadingError"
        :can-retry-loading="canRetryLoading"
        @refresh="fetchWorkList"
        @select-change="handleSelectChange"
        @all-select-change="handleAllSelectChange"
        @scroll-end="handleScrollEnd"
        @retry="retryLoad"
      />
    </template>

    <!-- 中间图片预览 -->
    <template #center-panel>
      <!-- 骨架屏 -->
      <fa-skeleton
        v-if="shouldShowSkeleton"
        :loading="shouldShowSkeleton"
        :title="false"
        active
        :paragraph="{ rows: 1, width: '100%' }"
        class="center-panel-skeleton"
      />
      <!-- 实际内容 -->
      <ImagePreview v-else :workInfo="currentWork" />
    </template>

    <!-- 右侧内容面板 -->
    <template #right-panel>
      <!-- 骨架屏 -->
      <fa-skeleton
        v-if="shouldShowSkeleton"
        :loading="shouldShowSkeleton"
        :title="false"
        active
        :paragraph="{
          rows: 4,
          width: ['100%', '100%', '100%', '100%'],
        }"
        class="right-panel-skeleton"
      />
      <!-- 实际内容 -->
      <ContentPanel
        v-else
        :work-id="currentWorkId"
        :work-data="currentWork"
        :on-edit-update="handleEditUpdate"
        :on-generate-update="handleGenerateUpdate"
      />
    </template>
  </BaseStepLayout>
</template>

<script lang="ts">
import { defineComponent, watch, computed, onMounted, onUnmounted } from 'vue';
import BaseStepLayout from '@/views/EditProjectView/components/BaseStepLayout/index.vue';
import WorkListBar from '@/components/WorkListBar/index.vue';
import { WorkType } from '@/components/WorkListBar/types';
import { useInfiniteImageWorkList } from '@/views/EditProjectView/composables/useInfiniteWorkList';
import ImagePreview from './components/ImagePreview/index.vue';
import ContentPanel from './components/ContentPanel/index.vue';
import { eventBus, EVENT_NAMES } from '@/utils/eventBus';

export default defineComponent({
  name: 'SecondStepImage',
  components: {
    BaseStepLayout,
    WorkListBar,
    ImagePreview,
    ContentPanel,
  },
  emits: ['selected-works-change', 'all-select-change'],
  props: {
    // 类型（视频：0，图文：1）
    type: {
      type: Number,
      default: 1,
    },
    // 状态：add-新建项目，edit-编辑项目
    status: {
      type: String,
      default: 'edit',
    },
    // 表单数据（从父组件传入）
    formData: {
      type: Object,
      default: () => null,
    },
    // 项目ID
    projectId: {
      type: Number,
      required: true,
    },
  },
  setup(props, { emit }) {
    const {
      currentWorkId,
      isLoading,
      displayedWorkList,
      currentWork,
      fetchWorkList,
      selectedWorkIds,
      handleSelectChange,
      // 分页相关
      pageSize,
      currentPage,
      totalItems,
      sucNum,
      isLoadingMore,
      hasLoadedAll,
      handleScrollEnd,
      retryLoad,
      onPageChange,
      isLoadingDetail,
      // 新的加载状态
      isFirstPageLoading,
      isLoadingMoreData,
      // 错误处理相关
      hasLoadingError,
      loadingError,
      canRetryLoading,
      // 轮询相关
      polling,
      // 编辑后更新方法
      handleEditUpdate,
      // 生成完成后更新方法
      handleGenerateUpdate,
      // 刷新作品列表方法
      refreshWorkList,
    } = useInfiniteImageWorkList(props.projectId);

    // 监听 selectedWorkIds 变化，向父组件发送事件
    watch(
      selectedWorkIds,
      newIds => {
        emit('selected-works-change', newIds);
      },
      { immediate: true },
    );

    /**
     * 处理全选状态变化事件
     * @param isAll 是否全选
     */
    const handleAllSelectChange = (isAll: boolean): void => {
      emit('all-select-change', isAll);
    };

    // 监听projectId变化，重新获取数据
    watch(
      () => props.projectId,
      async newProjectId => {
        if (newProjectId) {
          // 项目切换时，清空所有作品详情缓存并重新刷新作品列表，清空选中状态
          const [err] = await refreshWorkList();
          if (err) {
            console.error('项目切换时刷新作品列表失败:', err.message);
          }
        }
      },
      { immediate: true },
    );

    /**
     * 处理刷新作品列表事件
     * @description 当用户在预览弹窗中选择保存时触发，清空所有作品详情缓存并重新刷新作品列表
     */
    const handleRefreshWorkList = async (): Promise<void> => {
      console.log('处理刷新作品列表事件');
      // 清空所有作品详情缓存并重新刷新作品列表，清空选中状态
      const [err] = await refreshWorkList();
      if (err) {
        console.error('刷新作品列表失败:', err.message);
      }
    };

    /**
     * 是否应该显示骨架屏
     * 当作品列表首次加载或作品详情加载时显示骨架屏
     */
    const shouldShowSkeleton = computed(() => {
      return isLoadingDetail.value || isFirstPageLoading.value;
    });

    // 监听 EventBus 刷新事件
    onMounted(() => {
      eventBus.on(EVENT_NAMES.REFRESH_WORK_LIST, handleRefreshWorkList);
    });

    // 组件卸载时清理事件监听
    onUnmounted(() => {
      eventBus.off(EVENT_NAMES.REFRESH_WORK_LIST, handleRefreshWorkList);
    });

    // 已移除未使用的 handlePageSizeChange 函数

    return {
      currentWorkId,
      isLoading,
      currentWork,
      WorkType,
      fetchWorkList,
      selectedWorkIds,
      handleSelectChange,
      handleAllSelectChange,
      // 分页相关
      currentPage,
      pageSize,
      totalItems,
      sucNum,
      onPageChange,
      displayedWorkList,
      // 无限滚动相关
      isLoadingMore,
      hasLoadedAll,
      handleScrollEnd,
      retryLoad,
      // 错误处理相关
      hasLoadingError,
      loadingError,
      canRetryLoading,
      // 详情相关
      isLoadingDetail,
      // 新的加载状态
      isFirstPageLoading,
      isLoadingMoreData,
      // 骨架屏显示状态
      shouldShowSkeleton,
      // 轮询相关
      polling,
      // 编辑后更新方法
      handleEditUpdate,
      // 生成完成后更新方法
      handleGenerateUpdate,
      // 暴露作品列表供外部访问（用于检查作品状态）
      workList: displayedWorkList,
    };
  },
});
</script>

<style lang="scss" scoped>
/* 中间面板骨架屏样式 */
.center-panel-skeleton {
  /* 布局相关 */
  @apply w-full h-full;
  /* 尺寸相关 */
  @apply p-0;

  ::v-deep .fa-skeleton-content {
    /* 完全填充容器 */
    @apply w-full h-full;

    .fa-skeleton-paragraph {
      /* 完全填充容器 */
      @apply w-full h-full m-0;

      li {
        /* 完全填充父容器 */
        @apply w-full h-full m-0;
      }
    }
  }
}

/* 右侧面板骨架屏样式 */
.right-panel-skeleton {
  /* 布局相关 */
  @apply w-full h-full;
  /* 尺寸相关 */
  @apply p-[32px_32px_0px];

  ::v-deep .fa-skeleton-content {
    .fa-skeleton-paragraph {
      li {
        /* 高度和间距设置，宽度由 paragraph.width 控制 */
        @apply mb-24px;

        &:nth-child(1) {
          /* 模拟标题骨架屏 */
          @apply h-32px mb-10px;
        }

        &:nth-child(2) {
          /* 模拟图文详情区域 */
          @apply h-120px;
        }

        &:nth-child(3) {
          /* 模拟标题内容区域 */
          @apply h-120px;
        }

        &:nth-child(4) {
          /* 模拟内容详情区域 - 增加高度 */
          @apply h-150px;
        }
      }
    }
  }
}
</style>
