import { onMounted, Ref } from 'vue';
import store from '@/store';

import {
  Props,
  UseMaterialBasicUploadStateReturns,
  useMaterialBasicUploadState,
} from './useMaterialBasicUploadState';
import {
  FaBasicUploadInstance,
  UseMaterialBasicUploadUtilsReturns,
  useMaterialBasicUploadUtils,
} from './useMaterialBasicUploadUtils';
import {
  UseMaterialBasicUploadEventsReturns,
  useMaterialBasicUploadEvents,
} from './useMaterialBasicUploadEvents';

// Emits 类型
type EmitFunction = (
  event: 'update:visible' | 'close' | 'file-choose-over',
  ...args: unknown[]
) => void;

/**
 * 组合MaterialBasicUpload所有状态、工具和事件hook
 * @param props 组件props
 * @param emit 事件触发函数
 * @param FaBasicUploadRef 组件实例引用
 * @returns 所有状态、工具和事件方法集合
 */
export function useMaterialBasicUpload(
  props: Props,
  emit: EmitFunction,
  FaBasicUploadRef: Ref<FaBasicUploadInstance | null>,
) {
  // 状态
  const state: UseMaterialBasicUploadStateReturns =
    useMaterialBasicUploadState(props);

  // 工具函数和操作
  const utils: UseMaterialBasicUploadUtilsReturns = useMaterialBasicUploadUtils(
    state,
    emit,
    FaBasicUploadRef,
  );

  // 事件
  const events: UseMaterialBasicUploadEventsReturns =
    useMaterialBasicUploadEvents(state, emit, utils);

  // 生命周期钩子
  onMounted(async () => {
    state.loading.value = true;
    // 获取存储状态
    await store.dispatch('materialUpload/updateSpaceUsage');
    // 获取文件夹内容列表
    await utils.loadFolderContent();
    state.loading.value = false;
    utils.listenerScroll(state, utils.loadFolderContent);
    utils.watchOwnerFileViewType();
  });

  return {
    ...state,
    ...utils,
    ...events,
  } as const;
}
