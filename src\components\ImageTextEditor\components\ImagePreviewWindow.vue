<template>
  <!-- 预览窗 -->
  <div class="image-preview-window">
    <!-- 预览区域 -->
    <div class="image-preview-window__container">
      <div class="image-preview-window__container-area">
        <!-- 图片/花字编辑预览区域 -->
        <div
          class="image-preview-window__image-area"
          :style="{
            height: previewImageBoxSize.height,
          }"
        >
          <div class="image-preview-window__image-content">
            <!-- 图片背景容器 -->
            <div
              class="image-preview-window__background"
              :style="{
                width: previewImageBoxSize.width,
                height: previewImageBoxSize.height,
              }"
            >
              <!-- 单图布局 -->
              <div
                v-if="puzzleLayout === 1"
                class="image-preview-window__single-layout"
                :style="getPreviewImageStyle()"
              >
                <ImageZoomContainer
                  v-for="(img, index) in puzzleImages.slice(
                    0,
                    LAYOUT_TO_IMAGE_COUNT[1],
                  )"
                  :key="`${puzzleLayout}-${index}`"
                  :puzzle-image="img"
                  :is-active="activeImageIndex === index"
                  :image-index="index"
                  :is-cover-single-image="true"
                  :preview-image-box-size="previewImageBoxSize"
                  :preview-image-size="getPreviewImageStyle()"
                  @select="selectImage(index)"
                  @updateHeight="handleImageHeightUpdate"
                  :ref="createRefCallback(index)"
                />
              </div>

              <!-- 双图布局 -->
              <div
                v-else-if="puzzleLayout === 2"
                class="image-preview-window__double-layout"
                :style="getPreviewImageStyle()"
              >
                <ImageZoomContainer
                  v-for="(img, index) in puzzleImages.slice(
                    0,
                    LAYOUT_TO_IMAGE_COUNT[2],
                  )"
                  :key="`${puzzleLayout}-${index}`"
                  :puzzle-image="img"
                  :is-active="activeImageIndex === index"
                  :image-index="index"
                  :is-cover-single-image="false"
                  :preview-image-box-size="previewImageBoxSize"
                  :preview-image-size="getPreviewImageStyle()"
                  @select="selectImage(index)"
                  @updateHeight="handleImageHeightUpdate"
                  :ref="createRefCallback(index)"
                />
              </div>

              <!-- 4图布局(四宫格) -->
              <div
                v-else-if="puzzleLayout === 3"
                class="image-preview-window__quad-layout"
                :style="getPreviewImageStyle()"
              >
                <ImageZoomContainer
                  v-for="(img, index) in puzzleImages.slice(
                    0,
                    LAYOUT_TO_IMAGE_COUNT[3],
                  )"
                  :key="`${puzzleLayout}-${index}`"
                  :puzzle-image="img"
                  :is-active="activeImageIndex === index"
                  :image-index="index"
                  :is-cover-single-image="false"
                  :preview-image-box-size="previewImageBoxSize"
                  :preview-image-size="getPreviewImageStyle()"
                  @select="selectImage(index)"
                  @updateHeight="handleImageHeightUpdate"
                  :ref="createRefCallback(index)"
                />
              </div>

              <!-- 6图布局(3行2列) -->
              <div
                v-else-if="puzzleLayout === 4"
                class="image-preview-window__six-layout"
                :style="getPreviewImageStyle()"
              >
                <ImageZoomContainer
                  v-for="(img, index) in puzzleImages.slice(
                    0,
                    LAYOUT_TO_IMAGE_COUNT[4],
                  )"
                  :key="`${puzzleLayout}-${index}`"
                  :puzzle-image="img"
                  :is-active="activeImageIndex === index"
                  :image-index="index"
                  :is-cover-single-image="false"
                  :preview-image-box-size="previewImageBoxSize"
                  :preview-image-size="getPreviewImageStyle()"
                  @select="selectImage(index)"
                  @updateHeight="handleImageHeightUpdate"
                  :ref="createRefCallback(index)"
                />
              </div>
            </div>

            <!-- 花字层 - 修改为不阻止背景点击 -->
            <div class="image-preview-window__sticker-layer">
              <ImageStickerItem
                v-for="(item, index) in imageStickerList"
                :key="'imageSticker-' + index"
                :stickerInfo="item"
                :isLimit="true"
              />
              <TextStickerItem
                v-for="(item, index) in textStickerList"
                :key="index"
                :textInfo="item"
                :isLimit="true"
                :parentHeight="previewImageBoxSize.height"
              />
            </div>

            <!-- 模拟轮播效果 -->
            <div class="image-preview-window__carousel-layer">
              <div class="carousel-indicator">
                <div
                  v-for="idx in carouselNum"
                  :key="idx"
                  class="carousel-indicator__dot"
                  :class="{ 'is-active': idx - 1 === selectedSettingIndex }"
                ></div>
              </div>
            </div>
          </div>
        </div>
        <!-- 文案编辑预览区域 -->
        <div class="image-preview-window__text-area">
          <div class="image-preview-window__title">{{ title }}</div>
          <div class="image-preview-window__content">
            {{ content }}
          </div>
        </div>
      </div>
      <img
        src="@/assets/TemplatePreview/bottomSumulator.webp"
        class="absolute zi-footer bottom-0 left-0 right-0"
      />
    </div>
    <TextStickerDebugPreview v-if="workInfo" :workInfo="workInfo" />
  </div>
</template>

<script lang="ts" setup>
import { computed, ref, onMounted, onUnmounted, watch, nextTick } from 'vue';
import {
  workInfo,
  puzzleImages,
  currentSetting,
  LAYOUT_TO_IMAGE_COUNT,
  RATIO_TO_SCALE,
  isFirstImgSetting,
  isSingleImage,
  setCurrentSpace,
  selectedSettingIndex,
  globalPreviewImageBoxSize,
} from '../hook/useWorkInfo';
import TextStickerItem from '@/components/VideoEditor/StickerEditor/TextStickerItem.vue';
import ImageStickerItem from '@/components/VideoEditor/StickerEditor/ImageStickerItem.vue';
import { fixStickerPos } from '@/components/VideoEditor/StickerEditor/utils';
import { STYLE_TYPE } from '@/types/Work';
import ImageZoomContainer from './ImageZoomContainer.vue';
import { eventBus, EVENT_NAMES } from '@/utils/eventBus';
import TextStickerDebugPreview from '@/components/DevMode/TextStickerDebugPreview.vue';

const handleBodyClick = (event: MouseEvent) => {
  const target = event.target as HTMLElement;
  const isImageContainer = target.closest('.image-zoom-container');
  if (!isImageContainer) {
    setActiveImageIndex(-1);
  }
};

onMounted(() => {
  // 组件挂载时设置视口尺寸
  const height = parseInt(previewImageBoxSize.value.height, 10);
  const width = parseInt(previewImageBoxSize.value.width, 10);
  if (!isNaN(height) && !isNaN(width)) {
    // 更新所有图片缩放器的视口尺寸
    imageZoomers.value.forEach(zoomer => {
      if (zoomer) {
        zoomer.setViewportSize(width, height);
      }
    });
  }
  eventBus.on(EVENT_NAMES.RESET_ACTIVE_IMAGE_INDEX, resetActiveImageIndex);
  document.body.addEventListener('click', handleBodyClick);
});

onUnmounted(() => {
  eventBus.off(EVENT_NAMES.RESET_ACTIVE_IMAGE_INDEX, resetActiveImageIndex);
  document.body.removeEventListener('click', handleBodyClick);
  setActiveImageIndex(-1);
});

const resetActiveImageIndex = () => {
  setActiveImageIndex(-1);
};

// 图片缩放器引用
const imageZoomers = ref<ImageZoomerMethods[]>([]);
// 定义图片缩放器实例类型
interface ImageZoomerMethods {
  setViewportSize: (width: number, height: number) => void;
  setOriginalImageSize: (width: number, height: number) => void;
}
// 处理图片缩放器引用
const handleZoomerRef = (el: unknown, index: number): void => {
  if (
    el &&
    typeof el === 'object' &&
    'setViewportSize' in el &&
    'setOriginalImageSize' in el
  ) {
    const zoomer = el as ImageZoomerMethods;
    const zoomers = [...imageZoomers.value];
    zoomers[index] = zoomer;
    imageZoomers.value = zoomers;
  }
};
// 定义 ref 回调函数的类型
type RefCallback = (el: ImageZoomerMethods | null) => void;
// 创建类型安全的 ref 回调函数
const createRefCallback = (index: number): RefCallback => {
  return (el: ImageZoomerMethods | null) => handleZoomerRef(el, index);
};

// 当前选中的图片索引
const activeImageIndex = ref(-1);
const CONTAINER_WIDTH = 324;

// 添加图片高度的响应式引用
const imageHeight = ref(324);

// 记录初始的预览尺寸
const initialPreviewSize = ref<{ height: string; width: string }>({
  height: `${CONTAINER_WIDTH}px`,
  width: `${CONTAINER_WIDTH}px`,
});

const carouselNum = computed(() => {
  return workInfo.value?.setting?.graphicList?.length ?? 0;
});

/**
 * 根据puzzleType获取拼图布局
 */
const puzzleLayout = computed(() => {
  return currentSetting.value?.puzzleType ?? 1;
});

/** 花字列表 */
const textStickerList = computed(() => {
  return (
    currentSetting.value?.style?.filter(sticker => {
      return sticker.type === STYLE_TYPE.FONT;
    }) || []
  );
});

/** 贴图列表 */
const imageStickerList = computed(() => {
  return (
    currentSetting.value?.style?.filter(sticker => {
      return sticker.type === STYLE_TYPE.PIC;
    }) || []
  );
});

// 标题
const title = computed<string>(() => workInfo.value?.script?.title ?? '');

// 内容
const content = computed<string>(() => workInfo.value?.script?.content ?? '');

// 预览区域图片高度
const previewImageBoxSize = computed<{ height: string; width: string }>(() => {
  if (isFirstImgSetting.value) {
    // 如果是封面图
    if (isSingleImage.value) {
      // 单图模式
      // const MAX_HEIGHT = 432;
      // const MIN_HEIGHT = 324;
      // let height = imageHeight.value;
      // // 根据高度限制调整
      // if (height > MAX_HEIGHT) {
      //   height = MAX_HEIGHT;
      // } else if (height < MIN_HEIGHT) {
      //   height = MIN_HEIGHT;
      // }
      const result = {
        height: `${imageHeight.value}px`,
        width: `${CONTAINER_WIDTH}px`,
      };
      // 记录初始尺寸
      initialPreviewSize.value = result;
      return result;
    } else {
      // 拼图模式
      const puzzleRatio = RATIO_TO_SCALE[currentSetting.value?.ratio ?? 1] ?? 1;
      // 先按宽度324计算高度
      let height = CONTAINER_WIDTH / puzzleRatio;
      let width = CONTAINER_WIDTH;
      const result = {
        height: `${height}px`,
        width: `${width}px`,
      };
      // 记录初始尺寸
      initialPreviewSize.value = result;
      return result;
    }
  } else {
    // 如果是次图，返回之前记录的尺寸
    return initialPreviewSize.value;
  }
});
const setActiveImageIndex = (val: number) => {
  activeImageIndex.value = val;
};

// 初始化图片缩放器引用数组
const initImageZoomers = (count: number) => {
  imageZoomers.value = Array(count).fill(null);
};

// 处理子组件高度更新
const handleImageHeightUpdate = (height: number) => {
  imageHeight.value = height;
};

// 选择图片
const selectImage = (index: number) => {
  setActiveImageIndex(index);

  // 获取当前图片
  const currentImage = puzzleImages.value[index];
  if (currentImage) {
    imageZoomers.value[index]?.setOriginalImageSize(
      CONTAINER_WIDTH,
      parseInt(previewImageBoxSize.value.height),
    );
  }
};
// 计算次图（拼图）的容器尺寸
const calculateSize = () => {
  // 获取画布尺寸
  const canvasWidth = parseInt(previewImageBoxSize.value.width, 10);
  const canvasHeight = parseInt(previewImageBoxSize.value.height, 10);
  let containerWidth: number;
  let containerHeight: number;
  const ratio = currentSetting.value?.ratio ?? 1;
  const puzzleRatio = RATIO_TO_SCALE[ratio] ?? 1;
  // 先让容器宽度跟随画布尺寸
  containerWidth = canvasWidth;
  // 以宽度为基准，按比例算出高度
  const calculatedHeight = containerWidth / puzzleRatio;
  if (calculatedHeight <= canvasHeight) {
    containerHeight = calculatedHeight;
  } else {
    containerHeight = canvasHeight;
    containerWidth = containerHeight * puzzleRatio;
  }
  return {
    containerWidth: containerWidth,
    containerHeight: containerHeight,
  };
};

// 获取容器样式
const getPreviewImageStyle = () => {
  // 判断是否为封面图
  const isCoverImage = isFirstImgSetting.value;
  // 判断是否为单图
  const isSingleImageLayout = isSingleImage.value;

  let containerWidth: number;
  let containerHeight: number;

  // 封面图（单图 or 拼图） || 次图（单图）：直接拿画布尺寸
  if (isCoverImage || (!isCoverImage && isSingleImageLayout)) {
    containerWidth = parseInt(previewImageBoxSize.value.width, 10);
    containerHeight = parseInt(previewImageBoxSize.value.height, 10);
  } else {
    // 如果是次图（拼图）：重新按比例计算容器尺寸
    containerWidth = calculateSize().containerWidth;
    containerHeight = calculateSize().containerHeight;
  }

  return {
    width: `${containerWidth}px`,
    height: `${containerHeight}px`,
  };
};

// 监听布局变化，初始化对应数量的引用
watch(
  () => currentSetting.value?.puzzleType,
  layout => {
    const count = LAYOUT_TO_IMAGE_COUNT[layout ?? 0] || 0;
    initImageZoomers(count);
    // 更新 puzzleStyle
    if (
      currentSetting.value &&
      Array.isArray(currentSetting.value.puzzleStyle)
    ) {
      currentSetting.value.puzzleStyle = [...puzzleImages.value];
    }
  },
  { immediate: true },
);

// 监听布局变化，更新视口尺寸
watch(
  () => previewImageBoxSize.value,
  newSize => {
    globalPreviewImageBoxSize.value = newSize;
    // 从像素字符串中提取数值
    const height = parseInt(newSize.height, 10);
    const width = parseInt(newSize.width, 10);
    if (!isNaN(height) && !isNaN(width)) {
      // 更新所有图片缩放器的视口尺寸
      imageZoomers.value.forEach(zoomer => {
        if (zoomer) {
          zoomer.setViewportSize(width, height);
        }
      });
    }
  },
  { immediate: true },
);

// 监听图片比例变化
watch(
  () => currentSetting.value?.ratio,
  async newVal => {
    await nextTick(); // 等待布局和响应式数据更新
    eventBus.emit(EVENT_NAMES.CHANGE_RATIO, newVal);
  },
);

// 监听布局和比例变化，自动设置 currentSpace 并重置坐标
watch(
  [
    () => selectedSettingIndex.value,
    () => currentSetting.value?.puzzleType,
    () => currentSetting.value?.ratio,
  ],
  ([index, type, ratio], [oldIndex, oldType, oldRatio]) => {
    const oldValUndefined =
      oldIndex === undefined || oldType === undefined || oldRatio === undefined;

    // 同一张图的情况下，【布局】或者【图片比例】改变
    if (!oldValUndefined && oldIndex == index) {
      if (oldType != type || oldRatio != ratio) {
        // 重置坐标和样式
        const handleCurrentSpaceChange = () => {
          // puzzleStyle中每一项，isNewCreate重置为true（load图片之后就会触发定位默认居中了），scale重置为默认值1
          if (currentSetting.value?.puzzleStyle) {
            currentSetting.value.puzzleStyle.forEach(item => {
              item.isNewCreate = true;
              item.scale = 1;
            });
          }
          // 花字/贴图如果超出预览窗， 位置重置为贴边状态
          if (currentSetting.value?.style) {
            currentSetting.value.style.forEach(item => {
              // 替换图片之后，花字贴图位置保持不变，如果原位置对于新图片来说，超出预览窗的话，重置位置为预览窗最近的贴边状态
              const newStickerPos = fixStickerPos(
                item.x,
                item.y,
                item.type as STYLE_TYPE,
              );
              if (newStickerPos.x.isResetX) {
                item.x = newStickerPos.x.val;
              }
              if (newStickerPos.y.isResetY) {
                item.y = newStickerPos.y.val;
              }
            });
          }

          // 解绑
          eventBus.off(
            EVENT_NAMES.CURRENT_SPACE_CHANGE,
            handleCurrentSpaceChange,
          );
        };
        // 动态绑定监听，setCurrentSpace设置了新的原图尺寸之后（拼图在下面setCurrentSpace，单图在ImageZoomContainer的图片Load完后setCurrentSpace），再handleCurrentSpaceChange（pxToSize需要基于新的尺寸计算）
        eventBus.on(EVENT_NAMES.CURRENT_SPACE_CHANGE, handleCurrentSpaceChange);
      }
    }
    // 设置 currentSpace（只处理拼图（非单图））
    if (currentSetting.value && !isSingleImage.value) {
      const canvasWidth = parseInt(previewImageBoxSize.value?.width ?? '0', 10);
      const canvasHeight = parseInt(
        previewImageBoxSize.value?.height ?? '0',
        10,
      );
      let containerWidth = calculateSize().containerWidth;
      let containerHeight = calculateSize().containerHeight;

      const spaceH = Math.max(0, (canvasWidth - containerWidth) / 2);
      const spaceV = Math.max(0, (canvasHeight - containerHeight) / 2);
      const renderW = containerWidth;
      const renderH = containerHeight;
      const originW = 1080;
      setCurrentSpace(
        selectedSettingIndex.value,
        spaceH,
        spaceV,
        renderW,
        renderH,
        originW,
      );
    }
  },
  { immediate: true },
);
</script>

<style lang="scss" scoped>
.image-preview-window {
  /* 布局相关 */
  @apply relative h-full min-h-0 overflow-auto flex py-[16px];
  /* 外观相关 */
  @apply bg-[#f3f3f5];
}

.image-preview-window__container {
  /* 布局相关 */
  @apply relative m-auto;
  /* 尺寸相关 */
  // @apply w-[324px] h-[672px];
  /* 外观相关 */
  @apply bg-[#fff];
}

.image-preview-window__container-area {
  /* 尺寸相关 */
  @apply w-[324px] h-[588px] pb-48px;
  /* 外观相关 */
  @apply overflow-x-hidden overflow-y-scroll;
  /* 滚动条相关，隐藏滚动条 */
  &::-webkit-scrollbar {
    @apply w-[0];
  }
}

.image-preview-window__image-area {
  /* 尺寸相关 */
  @apply w-full;
  /* 布局相关 */
  @apply flex items-center justify-center;
}

.image-preview-window__image-content {
  /* 布局相关 */
  @apply relative;
  /* 尺寸相关 */
  @apply w-full h-full;
  /* 布局相关 */
  @apply flex items-center justify-center;
}

.image-preview-window__background {
  /* 布局相关 */
  @apply relative flex items-center justify-center;
  /* 确保在花字层下方 */
  @apply z-10;
}

.image-preview-window__single-layout {
  /* 布局相关 */
  @apply flex items-center justify-center;
  /* 尺寸相关 - 通过动态样式控制 */
  /* 外观相关 */
  @apply bg-transparent;
}

.image-preview-window__double-layout {
  /* 布局相关 */
  @apply flex flex-col;
  /* 尺寸相关 - 通过动态样式控制 */
  /* 外观相关 */
  @apply bg-transparent;
}

.image-preview-window__quad-layout {
  /* 布局相关 */
  @apply grid grid-cols-2 grid-rows-2;
  /* 尺寸相关 - 通过动态样式控制 */
  /* 外观相关 */
  @apply bg-transparent;
}

.image-preview-window__six-layout {
  /* 布局相关 */
  @apply grid grid-cols-2 grid-rows-3;
  /* 尺寸相关 - 通过动态样式控制 */
  /* 外观相关 */
  @apply bg-transparent;
}

.image-preview-window__sticker-layer {
  /* 布局相关 */
  @apply absolute top-0 left-0;
  /* 尺寸相关 */
  @apply w-full h-full;
  /* 层级相关 */
  @apply z-20;
  /* 允许点击穿透到底层 */
  @apply pointer-events-none;
}

/* 确保花字项可以点击 */
.image-preview-window__sticker-layer > * {
  /* 交互相关 */
  @apply pointer-events-auto;
}

.image-preview-window__text-area {
  /* 尺寸相关 */
  @apply p-10px;
}

.image-preview-window__title {
  /* 文字相关 */
  @apply font-bold text-14px mb-5px text-[#111];
}

.image-preview-window__content {
  /* 文字相关 */
  @apply text-12px text-[#333] sc-text-wrap-smart;
}

.image-preview-window__carousel-layer {
  @apply absolute w-[100%] h-[100%];
}
.carousel-indicator {
  /* 布局相关 */
  @apply absolute left-1/2 bottom-5px -translate-x-1/2 z-10;
  /* 尺寸相关 */
  @apply px-9px py-0 w-full;
  /* 布局方式 */
  @apply flex gap-0 justify-center items-center;
}

.carousel-indicator__dot {
  /* 布局相关 */
  @apply flex-1 flex-basis-0;
  /* 尺寸相关 */
  @apply h-2.4px min-w-0;
  /* 外观相关 */
  @apply rounded-[1.2px] bg-[#d9d9d9] op-30;
  /* 间距相关 */
  @apply mx-2px;

  &.is-active {
    @apply op-90;
  }
}
</style>
