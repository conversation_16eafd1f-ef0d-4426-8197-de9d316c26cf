import Vue, { ComponentOptions } from 'vue';
import VideoEditor from '@/components/VideoEditor/VideoEditor.vue';
import { VideoWorkItem } from '@/types/Work';
import {
  originWorkInfo,
  workInfo,
  loading,
  closeStatus,
  updateConsumePoint,
} from './hook/useWorkInfo';
import { cloneDeep } from 'lodash-es';
import { VIEW_MODE } from '@/views/EditProjectView/constants';
import { getWorkInfoWithTransform } from '@/api/EditProjectView/work';
import { message } from '@fk/faicomponent';
import { VIDEO_EDITOR_TAB, VIDEO_EDITOR_CLOSE_STATUS } from './constants';
import { setEditorType } from '@/constants/project';
import { DEFAULT_FONT, loadFont } from '@/utils/loadFont';

// 重新导出常量以保持向后兼容性
export { VIDEO_EDITOR_TAB, VIDEO_EDITOR_CLOSE_STATUS };

// 当前视频编辑器实例，单例模式
let instance: InstanceType<typeof Vue> | null = null;
// 当前请求的AbortController
let currentAbortController: AbortController | null = null;

/**
 * 显示视频编辑器弹窗
 * @param workId 作品id
 * @param defaultTab 默认选中的编辑器标签
 * @param closeCallback 关闭回调
 */
export const showVideoEditor = (
  workId: number,
  defaultTab?: (typeof VIDEO_EDITOR_TAB)[keyof typeof VIDEO_EDITOR_TAB],
  closeCallback?: (
    closeStatus: (typeof VIDEO_EDITOR_CLOSE_STATUS)[keyof typeof VIDEO_EDITOR_CLOSE_STATUS],
  ) => void,
) => {
  if (instance || loading.value) return;
  loading.value = true;

  // 取消上一个请求
  if (currentAbortController) {
    currentAbortController.abort();
  }
  currentAbortController = new AbortController();

  // 预加载字体
  loadFont(DEFAULT_FONT);

  getWorkInfoWithTransform(
    {
      id: workId,
      viewMode: VIEW_MODE.EDIT,
    },
    { signal: currentAbortController.signal },
  ).then(async ([err, res]) => {
    loading.value = false;
    if (err) {
      // 如果是主动取消，不提示错误
      if (
        err.message === 'canceled' ||
        err.message === 'canceled by user' ||
        err.message === 'canceled by AbortController'
      )
        return;
      message.error(err.message);
      return;
    }
    workInfo.value = cloneDeep(res.data) as VideoWorkItem;
    originWorkInfo.value = cloneDeep(res.data) as VideoWorkItem;
    updateConsumePoint();
  });

  closeStatus.value = VIDEO_EDITOR_CLOSE_STATUS.UNMODIFIED;
  // 进入视频编辑器前设置全局编辑器类型
  setEditorType('video');
  const Constructor = Vue.extend(VideoEditor as ComponentOptions<Vue>);
  instance = new Constructor({
    propsData: {
      defaultTab,
    },
  });

  instance.$mount();
  document.body.appendChild(instance.$el);

  instance.$on('close', () => {
    closeCallback?.(closeStatus.value);
    closeVideoEditor();
  });
};

/** 关闭视频编辑器 */
export const closeVideoEditor = () => {
  if (currentAbortController) {
    currentAbortController.abort();
    currentAbortController = null;
  }
  workInfo.value = undefined;
  originWorkInfo.value = undefined;
  if (instance) {
    document.body.removeChild(instance.$el);
    instance.$destroy();
    instance = null;
  }
};
