/**
 * OpenAI 推荐词组件配置
 * @description 集中管理组件的配置常量和默认值
 */

/**
 * 组件配置常量
 */
export const OPENAI_TIP_CONFIG = {
  /** 每次显示的推荐词数量 */
  DISPLAY_COUNT: 3,
  /** 防抖延迟时间（毫秒） */
  DEBOUNCE_DELAY: 300,
  /** 动画延迟增量（毫秒） */
  ANIMATION_DELAY_INCREMENT: 50,
  /** 最大文本长度 */
  MAX_TEXT_LENGTH: 20,
  /** 动画持续时间配置 */
  ANIMATION_DURATION: {
    enter: 400,
    leave: 350,
    move: 300,
  },
} as const;

/**
 * 动画配置
 */
export const ANIMATION_CONFIG = {
  /** 基础延迟时间 */
  BASE_DELAY: 0,
  /** 延迟增量 */
  INCREMENT_DELAY: OPENAI_TIP_CONFIG.ANIMATION_DELAY_INCREMENT,
  /** 最大文本长度 */
  MAX_TEXT_LENGTH: OPENAI_TIP_CONFIG.MAX_TEXT_LENGTH,
} as const;

/**
 * 字段类型枚举
 */
export const FIELD_TYPES = {
  INPUT: 'input',
  TEXTAREA: 'textarea',
  SELECT: 'select',
  SELECT_TAGS: 'selectTags',
} as const;

/**
 * 组件状态枚举
 */
export const COMPONENT_STATES = {
  /** 未开始 */
  NOT_STARTED: 'not_started',
  /** 加载中 */
  LOADING: 'loading',
  /** 已加载 */
  LOADED: 'loaded',
  /** 错误 */
  ERROR: 'error',
} as const;

/**
 * 依赖字段处理策略
 */
export const DEPENDENCY_STRATEGY = {
  /** 未获得焦点时：清空缓存并获取新数据 */
  NOT_FOCUSED: 'clear_and_fetch',
  /** 已获得焦点时：标记变化，保持显示 */
  FOCUSED: 'mark_and_keep',
} as const;

/**
 * 获取动画配置
 * @returns 动画配置对象
 */
export const getAnimationConfig = () => ({
  baseDelay: ANIMATION_CONFIG.BASE_DELAY,
  incrementDelay: ANIMATION_CONFIG.INCREMENT_DELAY,
  maxTextLength: ANIMATION_CONFIG.MAX_TEXT_LENGTH,
});

/**
 * 获取动画持续时间配置
 * @returns 动画持续时间配置对象
 */
export const getAnimationDuration = () => OPENAI_TIP_CONFIG.ANIMATION_DURATION;
