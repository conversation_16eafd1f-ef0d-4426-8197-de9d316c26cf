/**
 * @fileoverview 作品详情管理的可复用 Composable
 * @description 提供作品详情获取和管理的状态和方法，包含缓存机制
 */

import { ref, shallowRef, Ref, ShallowRef } from 'vue';
import { getWorkInfoWithTransform } from '@/api/EditProjectView/work';
import { WorkItem } from '@/types';
import { VIEW_MODE } from '@/views/EditProjectView/constants';

export interface UseWorkDetailReturn<T extends WorkItem> {
  /** 当前选中的作品ID */
  currentWorkId: Ref<number>;
  /** 当前选中的作品数据 */
  currentWork: ShallowRef<T | undefined>;
  /** 是否正在加载详情 */
  isLoadingDetail: Ref<boolean>;
  /** 获取作品详情 (优先从缓存获取) */
  fetchWorkDetail: (workId: number) => Promise<T | undefined>;
  /** 设置当前作品ID */
  setCurrentWorkId: (workId: number) => void;
  /** 清除指定作品的详情缓存 */
  clearWorkDetailCacheEntry: (workId: number) => void;
  /** 清除所有作品详情缓存 */
  clearAllWorkDetailCache: () => void;
  /** 局部更新当前作品的进度 */
  updateCurrentWorkProgress: (progress: number) => void;
  /** 检查指定作品是否有详情缓存 */
  hasWorkDetailCache: (workId: number) => boolean;
  /** 批量更新作品详情缓存中的进度 */
  batchUpdateWorkProgress: (
    updates: Array<{ workId: number; progress: number }>,
  ) => void;
}

/**
 * 作品详情管理的可复用 Composable
 * @template T WorkItem的子类型，表示具体的作品类型
 * @returns {UseWorkDetailReturn<T>} 作品详情相关的状态和方法
 */
export function useWorkDetail<T extends WorkItem>(): UseWorkDetailReturn<T> {
  // 当前选中的作品ID
  const currentWorkId = ref<number>(-1);
  // 当前作品数据 - 使用shallowRef避免深层响应式转换
  const currentWork = shallowRef<T | undefined>(undefined);
  // 是否正在加载详情数据
  const isLoadingDetail = ref<boolean>(false);

  // 作品详情缓存
  const workDetailCache = new Map<number, T>();

  /**
   * 设置当前作品ID
   * @param workId 作品ID
   */
  const setCurrentWorkId = (workId: number): void => {
    currentWorkId.value = workId;
  };

  /**
   * 获取作品详情
   * - 优先从缓存读取
   * - 缓存未命中则调用API获取，并存入缓存
   * @param workId 作品ID
   * @returns {Promise<T | undefined>} 获取到的作品详情
   */
  const fetchWorkDetail = async (workId: number): Promise<T | undefined> => {
    if (!workId || workId <= 0) {
      currentWork.value = undefined;
      return undefined;
    }

    // 1. 尝试从缓存获取
    if (workDetailCache.has(workId)) {
      currentWork.value = workDetailCache.get(workId)!;
      return currentWork.value;
    }

    // 2. 缓存未命中，调用API（使用带转换的接口）
    isLoadingDetail.value = true;
    const [err, res] = await getWorkInfoWithTransform({
      id: workId,
      viewMode: VIEW_MODE.EDIT,
    });

    if (err || !res?.data) {
      console.error('获取作品详情失败:', err);
      currentWork.value = undefined; // 获取失败时也清空当前作品
      isLoadingDetail.value = false;
      return undefined;
    }
    const convertedWorkInfo = res.data as T;

    // 3. 更新当前作品数据并存入缓存
    currentWork.value = convertedWorkInfo;
    workDetailCache.set(workId, convertedWorkInfo);

    setTimeout(() => {
      isLoadingDetail.value = false;
    }, 300);

    return currentWork.value;
  };

  /**
   * 清除指定作品的详情缓存
   * @param workId 要清除缓存的作品ID
   */
  const clearWorkDetailCacheEntry = (workId: number): void => {
    if (workDetailCache.has(workId)) {
      workDetailCache.delete(workId);
    }
  };

  /**
   * 清除所有作品详情缓存
   */
  const clearAllWorkDetailCache = (): void => {
    workDetailCache.clear();
  };

  /**
   * 局部更新当前作品的进度
   * @param progress 新的进度值
   */
  const updateCurrentWorkProgress = (progress: number): void => {
    if (!currentWork.value) {
      return;
    }

    // 创建新的作品对象，只更新进度
    const updatedWork = {
      ...currentWork.value,
      progress,
    } as T;

    // 更新当前作品数据
    currentWork.value = updatedWork;

    // 同时更新缓存中的数据
    if (currentWorkId.value > 0) {
      workDetailCache.set(currentWorkId.value, updatedWork);
    }
  };

  /**
   * 检查指定作品是否有详情缓存
   * @param workId 作品ID
   * @returns 是否有缓存
   */
  const hasWorkDetailCache = (workId: number): boolean => {
    return workDetailCache.has(workId);
  };

  /**
   * 批量更新作品详情缓存中的进度
   * @param updates 更新列表，包含作品ID和新进度
   */
  const batchUpdateWorkProgress = (
    updates: Array<{ workId: number; progress: number }>,
  ): void => {
    updates.forEach(({ workId, progress }) => {
      // 检查该作品是否有详情缓存
      const cachedDetail = workDetailCache.get(workId);
      if (!cachedDetail) {
        return; // 没有缓存则跳过
      }

      // 创建更新后的作品详情对象
      const updatedDetail = {
        ...cachedDetail,
        progress,
      } as T;

      // 更新缓存中的数据
      workDetailCache.set(workId, updatedDetail);

      // 如果是当前选中的作品，同时更新 currentWork
      if (workId === currentWorkId.value) {
        currentWork.value = updatedDetail;
      }
    });
  };

  return {
    currentWorkId,
    currentWork,
    isLoadingDetail,
    fetchWorkDetail,
    setCurrentWorkId,
    clearWorkDetailCacheEntry,
    clearAllWorkDetailCache,
    updateCurrentWorkProgress,
    hasWorkDetailCache,
    batchUpdateWorkProgress,
  };
}
