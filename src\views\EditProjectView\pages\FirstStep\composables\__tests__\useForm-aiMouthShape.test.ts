/**
 * @fileoverview AI改嘴型功能视频时长校验测试
 * @description 测试开启AI改嘴型功能时单个视频时长低于5秒的表单校验规则
 */

import { describe, it, expect, vi, beforeEach } from 'vitest';
import { useForm } from '../useForm';
import type { ResFormItem, FormItemInternal } from '../../../../types/index';

// 直接定义常量，避免导入问题
const PROJECT_TYPE_VIDEO = 0;

describe('useForm - AI改嘴型视频时长校验规则', () => {
  let formHook: ReturnType<typeof useForm>;

  beforeEach(() => {
    // 重置所有mock
    vi.clearAllMocks();

    // 创建表单Hook实例
    formHook = useForm(PROJECT_TYPE_VIDEO);
  });

  describe('AI改嘴型视频时长校验规则', () => {
    it('应该为开启AI改嘴型的视频文件添加时长校验规则', () => {
      // 准备测试数据：开启AI改嘴型的视频素材配置
      const resFormData: ResFormItem[] = [
        {
          id: 1,
          type: PROJECT_TYPE_VIDEO,
          variable: 'video_files',
          label: '视频素材',
          required: true,
          value: [],
          minLength: 0,
          maxLength: 5,
          description: '上传视频素材',
          hasModifyMouth: true,
          openModifyMouth: true, // 开启AI改嘴型
        },
      ];

      // 处理表单配置
      formHook.processFormConfig({
        templateId: 1,
        type: PROJECT_TYPE_VIDEO,
        name: 'test-project',
        inputForm: [],
        resForm: resFormData,
      });

      // 检查生成的文件表单项
      const fileFormItems = formHook.fileFormItems.value;
      expect(fileFormItems).toHaveLength(1);

      const videoFormItem = fileFormItems[0] as FormItemInternal;
      expect(videoFormItem.hasModifyMouth).toBe(true);
      expect(videoFormItem.openModifyMouth).toBe(true);

      // 检查是否添加了AI改嘴型校验规则
      // 视频类型应该有3个规则：必填、总时长、AI改嘴型单个时长
      expect(videoFormItem.rules).toHaveLength(3);
    });

    it('应该通过校验 - 未开启AI改嘴型功能', async () => {
      // 准备测试数据：未开启AI改嘴型
      const resFormData: ResFormItem[] = [
        {
          id: 1,
          type: PROJECT_TYPE_VIDEO,
          variable: 'video_files',
          label: '视频素材',
          required: true,
          value: [],
          minLength: 0,
          maxLength: 5,
          description: '上传视频素材',
          hasModifyMouth: true,
          openModifyMouth: false, // 未开启AI改嘴型
        },
      ];

      formHook.processFormConfig({
        templateId: 1,
        type: PROJECT_TYPE_VIDEO,
        name: 'test-project',
        inputForm: [],
        resForm: resFormData,
      });

      const fileFormItems = formHook.fileFormItems.value;
      const videoFormItem = fileFormItems[0] as FormItemInternal;

      // 获取AI改嘴型校验规则（第3个规则）
      const aiMouthShapeRule = videoFormItem.rules[2];
      expect(aiMouthShapeRule).toBeDefined();
      expect(aiMouthShapeRule.validator).toBeDefined();

      // 模拟校验短视频（3秒），但未开启AI改嘴型
      const testFiles = [{ duration: 3, name: 'short-video.mp4' }];

      // 执行校验
      const mockCallback = vi.fn();
      await aiMouthShapeRule.validator!(
        aiMouthShapeRule,
        testFiles,
        mockCallback,
      );

      // 应该通过校验（因为未开启AI改嘴型）
      expect(mockCallback).toHaveBeenCalledWith();
    });

    it('应该通过校验 - 开启AI改嘴型但视频时长符合要求', async () => {
      // 准备测试数据：开启AI改嘴型，视频时长符合要求
      const resFormData: ResFormItem[] = [
        {
          id: 1,
          type: PROJECT_TYPE_VIDEO,
          variable: 'video_files',
          label: '视频素材',
          required: true,
          value: [],
          minLength: 0,
          maxLength: 5,
          description: '上传视频素材',
          hasModifyMouth: true,
          openModifyMouth: true, // 开启AI改嘴型
        },
      ];

      formHook.processFormConfig({
        templateId: 1,
        type: PROJECT_TYPE_VIDEO,
        name: 'test-project',
        inputForm: [],
        resForm: resFormData,
      });

      const fileFormItems = formHook.fileFormItems.value;
      const videoFormItem = fileFormItems[0] as FormItemInternal;

      // 获取AI改嘴型校验规则
      const aiMouthShapeRule = videoFormItem.rules[2];
      expect(aiMouthShapeRule.validator).toBeDefined();

      // 模拟校验符合要求的视频（10秒和8秒）
      const testFiles = [
        { duration: 10, name: 'long-video1.mp4' },
        { duration: 8, name: 'long-video2.mp4' },
      ];

      // 执行校验
      const mockCallback = vi.fn();
      await aiMouthShapeRule.validator!(
        aiMouthShapeRule,
        testFiles,
        mockCallback,
      );

      // 应该通过校验
      expect(mockCallback).toHaveBeenCalledWith();
    });

    it('应该校验失败 - 开启AI改嘴型且存在时长低于5秒的视频', async () => {
      // 准备测试数据：开启AI改嘴型，存在短视频
      const resFormData: ResFormItem[] = [
        {
          id: 1,
          type: PROJECT_TYPE_VIDEO,
          variable: 'video_files',
          label: '视频素材',
          required: true,
          value: [],
          minLength: 0,
          maxLength: 5,
          description: '上传视频素材',
          hasModifyMouth: true,
          openModifyMouth: true, // 开启AI改嘴型
        },
      ];

      formHook.processFormConfig({
        templateId: 1,
        type: PROJECT_TYPE_VIDEO,
        name: 'test-project',
        inputForm: [],
        resForm: resFormData,
      });

      const fileFormItems = formHook.fileFormItems.value;
      const videoFormItem = fileFormItems[0] as FormItemInternal;

      // 获取AI改嘴型校验规则
      const aiMouthShapeRule = videoFormItem.rules[2];
      expect(aiMouthShapeRule.validator).toBeDefined();

      // 模拟校验包含短视频的文件列表
      const testFiles = [
        { duration: 10, name: 'long-video.mp4' },
        { duration: 3, name: 'short-video.mp4' }, // 3秒视频，不符合要求
      ];

      // 执行校验
      const mockCallback = vi.fn();
      await aiMouthShapeRule.validator!(
        aiMouthShapeRule,
        testFiles,
        mockCallback,
      );

      // 应该校验失败
      expect(mockCallback).toHaveBeenCalledWith(
        new Error('开启Ai改嘴型的单个视频时长需超过5s，请重新上传视频素材'),
      );
    });

    it('应该处理边界情况 - 视频时长正好等于5秒', async () => {
      // 准备测试数据：开启AI改嘴型，视频时长正好5秒
      const resFormData: ResFormItem[] = [
        {
          id: 1,
          type: PROJECT_TYPE_VIDEO,
          variable: 'video_files',
          label: '视频素材',
          required: true,
          value: [],
          minLength: 0,
          maxLength: 5,
          description: '上传视频素材',
          hasModifyMouth: true,
          openModifyMouth: true, // 开启AI改嘴型
        },
      ];

      formHook.processFormConfig({
        templateId: 1,
        type: PROJECT_TYPE_VIDEO,
        name: 'test-project',
        inputForm: [],
        resForm: resFormData,
      });

      const fileFormItems = formHook.fileFormItems.value;
      const videoFormItem = fileFormItems[0] as FormItemInternal;

      // 获取AI改嘴型校验规则
      const aiMouthShapeRule = videoFormItem.rules[2];
      expect(aiMouthShapeRule.validator).toBeDefined();

      // 模拟校验正好5秒的视频
      const testFiles = [{ duration: 5, name: 'exact-5s-video.mp4' }];

      // 执行校验
      const mockCallback = vi.fn();
      await aiMouthShapeRule.validator!(
        aiMouthShapeRule,
        testFiles,
        mockCallback,
      );

      // 应该通过校验（5秒不小于5秒）
      expect(mockCallback).toHaveBeenCalledWith();
    });

    it('应该跳过校验 - 保存模式', async () => {
      // 准备测试数据：开启AI改嘴型，存在短视频
      const resFormData: ResFormItem[] = [
        {
          id: 1,
          type: PROJECT_TYPE_VIDEO,
          variable: 'video_files',
          label: '视频素材',
          required: true,
          value: [],
          minLength: 0,
          maxLength: 5,
          description: '上传视频素材',
          hasModifyMouth: true,
          openModifyMouth: true, // 开启AI改嘴型
        },
      ];

      formHook.processFormConfig({
        templateId: 1,
        type: PROJECT_TYPE_VIDEO,
        name: 'test-project',
        inputForm: [],
        resForm: resFormData,
      });

      // 模拟保存模式校验
      const mockDynamicForm = {
        validate: vi.fn().mockResolvedValue(true),
        getFormData: vi.fn().mockReturnValue({}),
      };

      const mockDynamicFormFiles = {
        validate: vi.fn().mockResolvedValue(true),
        getFormData: vi.fn().mockReturnValue({
          video_files: [
            { duration: 3, name: 'short-video.mp4' }, // 短视频
          ],
        }),
      };

      // 执行保存模式校验
      const result = await formHook.validateAllFormsForSave(
        mockDynamicForm as any,
        mockDynamicFormFiles as any,
      );

      // 保存模式应该跳过AI改嘴型校验，因此应该通过
      expect(result.valid).toBe(true);
    });
  });
});
