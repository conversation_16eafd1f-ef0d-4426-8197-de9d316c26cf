/**
 * @fileoverview 项目编辑视图中与Vue Router交互相关的类型定义。
 * @description 此文件定义了用于描述路由查询参数、模拟Vue Router实例和路由对象的类型。
 * <AUTHOR>
 * @since 1.0.0
 */

/**
 * @description 路由查询参数对象接口，定义了URL中可能的查询参数。
 * @interface RouterQuery
 */
export interface RouterQuery {
  /**
   * 当前步骤的字符串表示，例如 '1' 或 '2'。
   * @type {string}
   * @memberof RouterQuery
   * @optional
   */
  step?: string;
  /**
   * 项目ID的字符串表示。
   * @type {string}
   * @memberof RouterQuery
   * @optional
   */
  projectId?: string;
  /**
   * 模板ID的字符串表示。
   * @type {string}
   * @memberof RouterQuery
   * @optional
   */
  templateId?: string;
  /**
   * 允许包含其他任意字符串类型的查询参数。
   * @type {string | undefined}
   * @memberof RouterQuery
   */
  [key: string]: string | undefined;
}

/**
 * @description 模拟Vue Router实例的类型（Router-like object）。
 * @description 用于类型提示，表示一个拥有Vue Router部分核心方法的对象，主要用于组件或组合式API内部进行路由操作，而无需直接依赖完整的Vue Router实例类型。
 * @typedef {object | undefined} RouterLike
 * @property {(options: { query: RouterQuery }) => void} [replace] - 类似 `router.replace`，用于替换当前路由，并可更新查询参数。
 * @property {(path: string) => void} [push] - 类似 `router.push`，用于导航到新的路径。
 */
export type RouterLike =
  | {
      /**
       * 替换当前路由历史记录，常用于更新查询参数而不产生新的历史条目。
       * @param {{ query: RouterQuery }} options - 包含新查询参数的对象。
       * @returns {void}
       */
      replace?: (options: { query: RouterQuery }) => void;
      /**
       * 导航到一个新的URL路径，并会产生新的历史记录。
       * @param {string} path - 目标路径字符串。
       * @returns {void}
       */
      push?: (path: string) => void;
      /**
       * 允许包含其他任意属性，以兼容实际Vue Router实例可能包含的其他成员。
       * @type {unknown}
       */
      [key: string]: unknown;
    }
  | undefined;

/**
 * @description 模拟Vue Route对象的类型（Route-like object）。
 * @description 用于类型提示，表示一个拥有Vue Route对象部分核心属性的对象，主要用于从当前路由信息中读取数据，如查询参数或路由名称。
 * @typedef {object | undefined} RouteLike
 * @property {RouterQuery} [query] - 当前路由的查询参数对象。
 * @property {string} [name] - 当前路由的名称。
 */
export type RouteLike =
  | {
      /**
       * 当前路由的查询参数对象。
       * @type {RouterQuery}
       * @see {@link RouterQuery}
       * @optional
       */
      query?: RouterQuery;
      /**
       * 当前路由的名称（如果定义了）。
       * @type {string}
       * @optional
       */
      name?: string;
      /**
       * 允许包含其他任意属性，以兼容实际Vue Route对象可能包含的其他成员。
       * @type {unknown}
       */
      [key: string]: unknown;
    }
  | undefined;
