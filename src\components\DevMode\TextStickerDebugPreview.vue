<template>
  <fa-popover
    title="花字生成效果"
    placement="rightBottom"
    overlayClassName="dev-mode__popover"
    trigger="click"
  >
    <template #content>
      <div class="dev-mode__content">
        <!-- 预览图片区域 -->
        <template v-if="imgSrc">
          <div class="dev-mode__preview-container">
            <ScImg
              class="dev-mode__preview-coverImg"
              :src="workInfo?.coverImg"
              fit="cover"
              :maxWidth="324"
            />
            <img
              class="dev-mode__preview-image"
              :src="imgSrc"
              alt="花字生成预览"
            />
          </div>
          <div
            class="dev-mode__regenerate-btn"
            @click.stop="handleGenerateText"
          >
            重新生成
          </div>
        </template>
        <!-- 生成状态区域 -->
        <div v-else class="dev-mode__status-container">
          <!-- 生成中状态 -->
          <fa-spin v-if="isGenerating" class="dev-mode__loading">
            <template #tip>生成中</template>
          </fa-spin>

          <!-- 错误状态 -->
          <div v-else-if="errMsg" class="dev-mode__error">
            <div class="dev-mode__error-message">{{ errMsg }}</div>
            <fa-button class="dev-mode__retry-btn" @click="handleGenerateText">
              重新生成花字
            </fa-button>
          </div>

          <!-- 初始状态 -->
          <fa-button
            v-else
            class="dev-mode__generate-btn"
            @click="handleGenerateText"
          >
            生成花字
          </fa-button>
        </div>
      </div>
    </template>

    <!-- 悬浮圆球调试按钮 -->
    <div v-if="isShow" class="dev-mode__debug-ball">
      <div class="dev-mode__debug-ball__content" title="点击打开调试弹窗">
        <div>花字调试</div>
      </div>
    </div>
  </fa-popover>
</template>

<script lang="ts" setup>
import { defineProps, ref, computed } from 'vue';
import store from '@/store';
import { debugTextSticker } from '@/api/VideoEditor/save';
import { WorkItem, StickerSetting, STYLE_TYPE } from '@/types';
import { currentSetting } from '@/components/ImageTextEditor/hook/useWorkInfo';
import { PROJECT_TYPE_IMAGE } from '@/views/EditProjectView/constants/index';

// 组件属性定义
const props = defineProps<{
  workInfo: WorkItem;
}>();

// 响应式状态
const imgSrc = ref<string>('');
const isGenerating = ref<boolean>(false);
const errMsg = ref<string>('');

// 是否是开发环境或者本地环境
// const isDev = import.meta.env.MODE === 'development';
// const isTestDomain =
//   window.location.hostname ===
//   import.meta.env.VITE_TEST_API_DOMAIN.split('http://')[1];
// const shouldShow = isDev || isTestDomain;

/**
 * 是否显示悬浮圆球调试按钮
 * 显示条件：
 * 1、开发环境或者本地环境
 * 2、内部用户
 * 3、设置了花字
 */
const isShow = computed<boolean>(() => {
  return store.state.user.isStaff && textStickerList.value.length > 0;
});

const currentSettingTmp = computed(() => {
  if (props.workInfo?.type === PROJECT_TYPE_IMAGE) {
    return currentSetting.value;
  } else {
    return props.workInfo?.setting;
  }
});

/**
 * 获取花字列表
 * 根据作品类型获取对应的设置数据
 */
const textStickerList = computed<StickerSetting[]>(() => {
  return (
    currentSettingTmp.value?.style?.filter((sticker: StickerSetting) => {
      return sticker.type === STYLE_TYPE.FONT;
    }) || []
  );
});

/**
 * 生成花字预览图片
 * 模拟生成花字的过程，调用调试接口获取预览图
 */
const handleGenerateText = async (): Promise<void> => {
  try {
    // 重置状态
    imgSrc.value = '';
    errMsg.value = '';
    isGenerating.value = true;

    // 调用调试接口
    const [err, res] = await debugTextSticker(props.workInfo);

    if (err) {
      // 错误处理
      errMsg.value = `生成失败，${err.message}`;
      return;
    }

    // 成功处理
    imgSrc.value = res.data || '';
  } catch (error) {
    // 异常处理
    console.error('生成花字预览失败:', error);
    errMsg.value = '生成过程中发生异常，请重试';
  } finally {
    // 确保状态重置
    isGenerating.value = false;
  }
};
</script>

<style lang="scss" scoped>
.dev-mode {
  /* 内容容器 */
  &__content {
    /* 布局相关 */
    @apply flex flex-col;
    /* 尺寸相关 */
    @apply w-full;
    /* 文字相关 */
    @apply text-center;
  }

  /* 预览容器 */
  &__preview-container {
    /* 布局相关 */
    @apply relative flex flex-col justify-center items-center;
    /* 尺寸相关 */
    @apply w-324px h-576px;
    /* 外观相关 */
    @apply overflow-hidden;
  }

  &__preview-coverImg {
    /* 尺寸相关 */
    @apply w-full h-full;
    /* 外观相关 */
    @apply absolute top-0 left-0 z-0;
  }

  /* 预览图片 */
  &__preview-image {
    /* 尺寸相关 */
    @apply w-full h-full;
    /* 定位相关 */
    @apply absolute top-0 left-0 z-10;
  }

  /* 重新生成按钮 */
  &__regenerate-btn {
    /* 布局相关 */
    @apply mt-10px;
  }

  /* 状态容器 */
  &__status-container {
    /* 布局相关 */
    @apply flex flex-col items-center justify-center;
    /* 尺寸相关 */
    @apply w-full;
  }

  /* 加载状态 */
  &__loading {
    /* 布局相关 */
    @apply flex items-center justify-center;
    /* 尺寸相关 */
    @apply w-full py-20px;
  }

  /* 错误状态 */
  &__error {
    /* 布局相关 */
    @apply flex flex-col items-center;
    /* 尺寸相关 */
    @apply w-full;
  }

  /* 错误消息 */
  &__error-message {
    /* 文字相关 */
    @apply text-danger text-14px;
  }

  /* 重试按钮 */
  &__retry-btn {
    /* 布局相关 */
    @apply mt-10px;
  }

  /* 生成按钮 */
  &__generate-btn {
    /* 布局相关 */
    @apply flex items-center justify-center;
    /* 尺寸相关 */
    @apply w-full py-10px;
  }

  &__regenerate-btn {
    /* 布局相关 */
    @apply mt-5px cursor-pointer;
    /* 文字相关 */
    @apply text-primary hover:text-secondary;
  }

  /* 悬浮圆球样式 */
  &__debug-ball {
    /* 定位相关 */
    @apply absolute bottom-[60px] right-[13.5px] zi-popover;
    /* 尺寸相关 */
    @apply size-60px;
    /* 外观相关 */
    @apply btn-special rounded-full cursor-pointer;
    /* 交互相关 */
    @apply transition-all duration-200 ease-in-out;

    &:hover {
      /* 外观相关 */
      @apply shadow-lg transform scale-105;
    }

    /* 圆球内容 */
    &__content {
      /* 布局相关 */
      @apply flex flex-col items-center justify-center;
      /* 尺寸相关 */
      @apply w-full h-full;
      /* 文字相关 */
      @apply text-white text-12px font-medium leading-none;
    }
  }
}
</style>
