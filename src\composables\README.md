# usePointsRecharge Composable

## 概述

`usePointsRecharge` 是一个用于处理充值后自动更新积分的简单 composable。当用户从充值页面返回时自动更新积分。

## 功能特性

- 🔄 **自动积分更新**：用户从充值页面返回时自动更新积分
- 🚀 **新窗口跳转**：在新窗口打开充值页面
- 🧹 **自动清理**：组件卸载时自动清理资源

## 基本用法

```typescript
import { usePointsRecharge } from '@/composables/usePointsRecharge';

export default defineComponent({
  setup() {
    const updateUserPoints = () => {
      store.dispatch('updatePoints');
    };

    const { handleRecharge } = usePointsRecharge({
      updatePoints: updateUserPoints,
      rechargeUrl: '/recharge',
    });

    return {
      handleRecharge,
    };
  },
});
```

## 配置选项

| 参数         | 类型         | 默认值        | 必填 | 说明         |
| ------------ | ------------ | ------------- | ---- | ------------ |
| updatePoints | `() => void` | -             | ✅   | 积分更新函数 |
| rechargeUrl  | `string`     | `'/recharge'` | ❌   | 充值页面 URL |

## 返回值

| 属性           | 类型         | 说明               |
| -------------- | ------------ | ------------------ |
| handleRecharge | `() => void` | 处理充值跳转的方法 |

## 使用示例

### 在点数不足弹窗中使用

```vue
<script>
import { usePointsRecharge } from '@/composables/usePointsRecharge';

export default defineComponent({
  setup() {
    const { handleRecharge: performRecharge } = usePointsRecharge({
      updatePoints: () => store.dispatch('updatePoints'),
      rechargeUrl: '/recharge',
    });

    const handleRecharge = () => {
      modalVisible.value = false;
      performRecharge();
    };

    return {
      handleRecharge,
    };
  },
});
</script>
```

## 技术原理

当用户点击充值按钮时，composable 会：

1. 设置内部标记表示用户即将去充值
2. 在新窗口打开充值页面
3. 监听页面可见性变化
4. 当用户返回原页面时，自动调用积分更新函数
5. 清除标记

## 相关文件

- `src/composables/usePointsRecharge.ts` - 主要实现文件
- `src/views/EditProjectView/index.vue` - 使用示例
- `src/components/InsufficientPointsModal/index.vue` - 配合使用的弹窗组件
