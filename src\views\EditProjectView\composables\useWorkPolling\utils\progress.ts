/**
 * @fileoverview 进度计算工具函数
 * @description 提供进度相关的计算功能
 */

import { WorkItem } from '@/types';

/**
 * 计算作品列表中的最大进度
 * @param works 作品列表
 * @returns 最大进度百分比，如果列表为空则返回0，进度值会被限制在0-100范围内
 */
export const calculateMaxProgress = (works: WorkItem[]): number => {
  if (works.length === 0) {
    return 0;
  }

  // 找出所有作品中的最大进度，并处理异常值
  const maxProgress = works.reduce((max, work) => {
    // 确保进度值是有效数字，并限制在0-100范围内
    const progress =
      typeof work.progress === 'number' && !isNaN(work.progress)
        ? Math.max(0, Math.min(100, work.progress))
        : 0;
    return Math.max(max, progress);
  }, 0);

  return maxProgress;
};
