<template>
  <div class="pl-[40px] pr-[40px] pb-[24px]">
    <SettingItem label="标题" required :value="title">
      <div class="text-editor__input-wrap">
        <fa-input
          v-model="title"
          :maxLength="20"
          placeholder="请输入标题内容"
          class="text-editor__input"
        />
        <Icon
          type="fuzhi"
          class="text-editor__input-copy"
          @click="handleCopyButtonClick(title)"
        />
      </div>
    </SettingItem>
    <SettingItem
      label="内容"
      required
      :value="content"
      otherClass="items-start!"
    >
      <div class="relative">
        <fa-textarea
          v-model="content"
          :maxLength="1000"
          placeholder="请输入文本内容"
          class="text-editor__textarea"
        />
        <div class="text-editor__copy-bar">
          <div class="text-editor__copy-barBtn">
            <Icon
              type="fuzhi"
              class="text-editor__copy-barBtn-icon"
              @click="handleCopyButtonClick(content)"
            />
            <span
              class="text-editor__copy-barBtn-text"
              @click="handleCopyButtonClick(content)"
              >一键复制</span
            >
          </div>
        </div>
      </div>
    </SettingItem>
  </div>
</template>

<script lang="ts" setup>
import { computed } from 'vue';
import { workInfo } from '../hook/useWorkInfo';
import SettingItem from './SettingItem.vue';
import copy from 'clipboard-copy';
import { message } from '@fk/faicomponent';

// 标题
const title = computed<string>({
  get: () => workInfo.value?.script?.title ?? '',
  set: val => {
    if (workInfo.value?.script) {
      workInfo.value.script.title = val;
    }
  },
});

// 内容
const content = computed<string>({
  get: () => workInfo.value?.script?.content ?? '',
  set: val => {
    if (workInfo.value?.script) {
      workInfo.value.script.content = val;
    }
  },
});

/**
 * 复制传入内容到剪贴板
 * @param value 需要复制的内容
 */
async function handleCopyButtonClick(value: string) {
  try {
    if (!value) {
      message.warning('没有可复制的内容');
      return;
    }
    await copy(value);
    message.success('复制成功');
  } catch (err) {
    console.error('复制失败:', err);
    message.error('复制失败');
  }
}
</script>

<style lang="scss" scoped>
.text-editor__input-wrap {
  @apply relative w-[532px] h-[40px];
}

.text-editor__input {
  @apply w-[532px] h-[40px] rounded-[8px] bg-[#fff] border border-solid border-[#d9d9d9] pr-[40px];
}

.text-editor__input-copy {
  @apply absolute right-0 top-1/2 -translate-y-1/2 text-[#999] w-[20px] h-[20px] mr-[11px] cursor-pointer;
  &:hover {
    @apply color-[#666666];
  }
}

:deep(.text-editor__textarea) {
  // &.fa-input-show-count:after {
  //   @apply bottom-40px;
  // }

  &.fa-input {
    @apply w-[532px] h-[320px] max-h-320px overflow-y-auto px-12px pt-12px pb-35px rounded-[8px] bg-[#fff] border border-solid border-[#d9d9d9] whitespace-pre-wrap;
    &::-webkit-scrollbar {
      @apply hidden;
    }
    -ms-overflow-style: none; /* IE and Edge */
    scrollbar-width: none; /* Firefox */
  }
}

.text-editor__copy-bar {
  @apply absolute left-1px right-1px bottom-1px h-35px pr-11px bg-[#fff] rounded-b-[8px];
  @apply border-b-1px border-b-solid border-b-[#d9d9d9] box-border z-1;

  .text-editor__copy-barBtn {
    @apply flex items-center justify-end w-[80px] float-right cursor-pointer;
    &:hover {
      .text-editor__copy-barBtn-icon,
      .text-editor__copy-barBtn-text {
        @apply text-[#666666];
      }
    }
  }
}

.text-editor__copy-barBtn-icon {
  @apply text-[#999] w-[20px] h-[20px] mr-[4px] inline-block;
}

.text-editor__copy-barBtn-text {
  @apply text-[14px] text-[#999] select-none;
}
</style>
