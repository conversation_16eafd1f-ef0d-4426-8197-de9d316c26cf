import { GET, POST } from '../request';

export interface SystemInfo {
  /** 公司域名 */
  homeDomain: string;
  /** 公司首页URL */
  homeRoot: string;
  /** 公司登录页 */
  portal: string;
  /** 资源域名，用于前端的js、css、图片等静态资源 */
  scPortalResRoot: string;
  /** CDN鉴权域名，用于素材库 */
  scUsrResAuthRoot: string;
  /** O系统资源域名，用于获取O系统的静态资源，如图片、视频等 */
  scUsrResOssRoot: string;
  /** 归属当前用户的资源域名，用于用户上传的或者生成的资源 */
  scUsrResRoot: string;
  /** 用户临时资源域名，暂未使用 */
  scUsrTmpResRoot: string;
  /** token */
  token: string;
}
/** 获取域名、token */
export const getDomainAndToken = () => {
  return GET<SystemInfo>('/api/constant/getDomain');
};

/** 开发时登录接口 */
export const loginForDev = (
  cacct: string,
  sacct: string,
  pwd: string,
  staffLogin: boolean,
) => {
  return POST<{
    /** token */
    token: string;
  }>('/portal/ajax/login_h.jsp', {
    cacct,
    sacct,
    pwd,
    staffLogin,
    autoLogin: false,
    bizType: 5,
    dogId: 0,
    fromsite: false,
    cmd: 'loginCorpNews',
  });
};
