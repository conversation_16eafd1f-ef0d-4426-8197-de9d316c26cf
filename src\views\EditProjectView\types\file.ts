/**
 * @fileoverview 文件上传及处理相关类型定义
 * @description 包含文件基本信息、上传配置、文件状态、文件处理回调等与文件操作相关的类型定义。
 * <AUTHOR>
 * @since 1.0.0
 */

import type { FileStatus, MediaType } from './base';
import { FILE_TYPES } from '@/constants/fileType';

// ============= 文件信息接口 =============

/**
 * @description 表示上传文件的完整信息结构，支持图片和视频类型。
 * @interface FileInfo
 * @property {string} name 文件原始名称，包含扩展名。
 * @property {FileStatus} status 文件状态码 (0: NORMAL, 1: TEMP, 2: RECYCLE, 3: DELETE)。RECYCLE 和 DELETE 前端通常视为失效。
 * @property {string} url 文件访问URL，用于预览和下载。
 * @property {string} [thumbUrl] 缩略图URL，主要用于视频文件的预览图。
 * @property {number} [duration] 视频时长，单位为秒，仅视频文件有效。
 * @property {FILE_TYPES} resType 资源类型，源自后端定义的具体文件类型 (例如：`FILE_TYPES.JPEG`, `FILE_TYPES.MP4`)。
 * @property {string} resId 资源ID，源自后端的原始资源标识符。
 * @property {FILE_TYPES} [coverType] 封面类型，源自后端定义的封面图文件类型，视频文件封面图专用。
 * @property {string} [coverId] 封面ID，源自后端的原始封面图资源标识符，视频文件封面图专用。
 * @property {number} [size] 文件大小，单位为字节。
 * @property {MediaType} [mediaType] 文件的业务类型标识 (例如：'image', 'video')，主要用于前端组件逻辑判断。
 * @property {number} [percent] 上传进度百分比，0-100，仅在上传过程中有效。
 */
export interface FileInfo {
  /**
   * 文件原始名称，包含扩展名。
   * @type {string}
   * @memberof FileInfo
   */
  name: string;
  /**
   * 文件上传状态标识，对应 {@link FileStatus}。
   * @type {FileStatus}
   * @memberof FileInfo
   */
  status: FileStatus;
  /**
   * 文件访问URL，用于预览和下载。
   * @type {string}
   * @memberof FileInfo
   */
  url: string;
  /**
   * 缩略图URL，主要用于视频文件的预览图。
   * @type {string}
   * @memberof FileInfo
   * @optional
   */
  thumbUrl?: string;
  /**
   * 视频时长，单位为秒，仅视频文件有效。
   * @type {number}
   * @memberof FileInfo
   * @optional
   */
  duration?: number;
  /**
   * 资源类型，对应全局 `FILE_TYPES` 枚举，表示文件的具体格式（如 JPEG, MP4）。
   * @type {FILE_TYPES}
   * @memberof FileInfo
   */
  resType: FILE_TYPES;
  /**
   * 资源ID，通常是文件在服务器端的唯一标识。
   * @type {string}
   * @memberof FileInfo
   */
  resId: string;
  /**
   * 封面类型，视频文件封面图的特定文件类型，对应全局 `FILE_TYPES` 枚举。
   * @type {FILE_TYPES}
   * @memberof FileInfo
   * @optional
   */
  coverType?: FILE_TYPES;
  /**
   * 封面ID，视频文件封面图在服务器端的唯一标识。
   * @type {string}
   * @memberof FileInfo
   * @optional
   */
  coverId?: string;
  /**
   * 文件大小，单位为字节。
   * @type {number}
   * @memberof FileInfo
   * @optional
   */
  size?: number;
  /**
   * 文件的业务类型标识（例如：'image', 'video'），主要用于前端组件逻辑判断，区别于具体的 `resType`。
   * @type {MediaType}
   * @memberof FileInfo
   * @optional
   */
  mediaType?: MediaType;
  /**
   * 上传进度百分比，范围 0-100，仅在上传过程中有效。
   * @type {number}
   * @memberof FileInfo
   * @optional
   */
  percent?: number;
}

// ============= 上传配置接口 =============

/**
 * @description 文件上传组件的配置选项。
 * @interface UploadConfig
 */
export interface UploadConfig {
  /**
   * 接受的文件类型，MIME类型字符串数组，例如 `['image/jpeg', 'image/png']`。
   * @type {string[]}
   * @memberof UploadConfig
   * @optional
   */
  accept?: string[];
  /**
   * 单个文件的最大允许大小，单位为字节。
   * @type {number}
   * @memberof UploadConfig
   * @optional
   */
  maxFileSize?: number;
  /**
   * 允许上传的最大文件数量。
   * @type {number}
   * @memberof UploadConfig
   * @optional
   */
  maxLength?: number;
  /**
   * 是否允许同时选择多个文件进行上传。
   * @type {boolean}
   * @memberof UploadConfig
   * @default false
   * @optional
   */
  multiple?: boolean;
  /**
   * 是否启用拖拽上传功能。
   * @type {boolean}
   * @memberof UploadConfig
   * @default false
   * @optional
   */
  dragUpload?: boolean;
  /**
   * 上传组件处理文件的默认业务媒体类型，如 'image' 或 'video'。
   * @type {MediaType}
   * @memberof UploadConfig
   * @optional
   */
  defaultMediaType?: MediaType;
}

// ============= 文件类型工具 =============

/**
 * @description 文件类型检查器函数类型定义。
 * @typedef {(file: FileInfo) => 'image' | 'video' | 'unknown'} FileTypeChecker
 * @param {FileInfo} file - 需要检查类型的文件信息对象。
 * @returns {'image' | 'video' | 'unknown'} 返回判断出的文件类型字符串。
 */
export type FileTypeChecker = (file: FileInfo) => 'image' | 'video' | 'unknown';

/**
 * @description 文件处理回调函数集合接口，定义了文件上传、删除、替换等操作的事件处理函数。
 * @interface FileHandlers
 */
export interface FileHandlers {
  /**
   * 文件（或多个文件）成功上传后的回调函数。
   * @type {(files: FileInfo[]) => void}
   * @memberof FileHandlers
   * @param {FileInfo[]} files - 已成功上传的文件信息数组。
   * @optional
   */
  onUploadSuccess?: (files: FileInfo[]) => void;
  /**
   * 文件上传过程中发生错误时的回调函数。
   * @type {(error: Error) => void}
   * @memberof FileHandlers
   * @param {Error} error - 上传过程中抛出的错误对象。
   * @optional
   */
  onUploadError?: (error: Error) => void;
  /**
   * 删除一个已上传文件后的回调函数。
   * @type {(file: FileInfo) => void}
   * @memberof FileHandlers
   * @param {FileInfo} file - 已被删除的文件信息对象。
   * @optional
   */
  onFileDelete?: (file: FileInfo) => void;
  /**
   * 替换一个已上传文件后的回调函数。
   * @type {(oldFile: FileInfo, newFile: FileInfo) => void}
   * @memberof FileHandlers
   * @param {FileInfo} oldFile - 被替换的旧文件信息对象。
   * @param {FileInfo} newFile - 用于替换的新文件信息对象。
   * @optional
   */
  onFileReplace?: (oldFile: FileInfo, newFile: FileInfo) => void;
}
