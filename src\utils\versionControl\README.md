# 通用版本校验工具

## 概述

通用版本校验工具提供了一套统一的版本控制解决方案，支持三种使用模式：UI交互式校验、消息提示式校验和接口级自动降级。

## 核心特性

- **易用性优先** - 新功能只需几行代码即可接入版本校验
- **统一维护** - 版本配置、校验逻辑、UI展示等集中管理
- **性能优化** - 接口级降级避免运行时检查，无UI闪烁
- **类型安全** - 完整的TypeScript类型支持

## 快速开始

### 1. 添加功能配置

在 `src/utils/versionControl/config.ts` 中添加新功能配置：

```typescript
export const VERSION_FEATURE_CONFIG = {
  // 现有配置...
  
  NEW_FEATURE: {
    featureKey: 'NEW_FEATURE',
    requiredVersion: VERSION.PRO,
    featureName: '新功能',
    supportDowngrade: true,
    downgradeHandler: (data) => {
      // 自定义降级逻辑
      data.enabled = false;
      return data;
    },
  },
};
```

### 2. 选择使用模式

#### 模式1：UI交互式校验（推荐用于开关类功能）

```vue
<template>
  <VersionSwitch
    feature-key="AI_MOUTH_SHAPE"
    v-model="mouthShapeEnabled"
    label="AI改嘴型"
  />
</template>

<script setup>
import { VersionSwitch } from '@/components/version';
const mouthShapeEnabled = ref(false);
</script>
```

#### 模式2：消息提示式校验（推荐用于操作类功能）

```typescript
import VersionController from '@/utils/versionControl';

function handleFileUpload() {
  // 方式1：使用checkAndExecute
  VersionController.checkAndExecute('FILE_UPLOAD_SIZE', () => {
    performUpload();
  });
  
  // 方式2：手动检查
  if (!VersionController.hasPermission('FILE_UPLOAD_SIZE')) {
    VersionController.showUpgradeMessage('FILE_UPLOAD_SIZE');
    return;
  }
  performUpload();
}
```

#### 模式3：接口级自动降级（推荐用于数据驱动功能）

```typescript
import VersionController from '@/utils/versionControl';

// 单个数据处理
const processedData = VersionController.applyDowngrade(rawData, 'AI_MOUTH_SHAPE');

// 批量数据处理
const processedList = VersionController.applyDowngradeToList(rawDataList, 'AI_MOUTH_SHAPE');
```

## API 参考

### VersionController

#### 静态方法

- `hasPermission(featureKey: string): boolean` - 检查功能权限
- `getFeatureConfig(featureKey: string): VersionFeatureConfig | null` - 获取功能配置
- `showUpgradeMessage(featureKey: string): void` - 显示版本限制提示
- `applyDowngrade<T>(data: T, featureKey: string): T` - 应用降级处理
- `applyDowngradeToList<T>(dataList: T[], featureKey: string): T[]` - 批量应用降级处理
- `checkAndExecute(featureKey: string, operation: () => void): boolean` - 检查并执行操作

### VersionSwitch 组件

#### Props

- `featureKey: string` - 功能标识（必需）
- `modelValue: boolean` - 开关状态
- `label: string` - 开关显示文本
- `showIcon: boolean` - 是否显示版本图标

#### Events

- `update:modelValue` - 开关状态变化
- `permission-denied` - 权限拒绝事件

## 最佳实践

### 1. 功能配置命名规范

- 使用大写字母和下划线：`AI_MOUTH_SHAPE`
- 功能名称简洁明了：`AI改嘴型`
- 提供有意义的降级处理函数

### 2. 选择合适的使用模式

- **开关类功能**：使用 VersionSwitch 组件
- **操作类功能**：使用消息提示式校验
- **数据驱动功能**：使用接口级自动降级

### 3. 错误处理

- 配置验证：使用 `isValidFeatureKey` 检查功能标识有效性
- 降级处理：在 `downgradeHandler` 中添加 try-catch 错误处理
- 日志记录：在关键操作中添加适当的日志输出

## 迁移指南

### 从现有实现迁移到通用工具

1. **识别现有版本校验代码**
2. **添加功能配置**
3. **替换为新的API调用**
4. **测试功能正常性**

### 示例：AI改嘴型功能迁移

```typescript
// 旧代码
const hasMouthShapePermission = computed(() => {
  if (!props.formItem.hasModifyMouth) return true;
  return checkVersionPermission(VERSION.BASIC);
});

// 新代码
const hasMouthShapePermission = computed(() => {
  return VersionController.hasPermission('AI_MOUTH_SHAPE');
});
```

## 常见问题

### Q: 如何添加新的版本校验功能？
A: 在 `VERSION_FEATURE_CONFIG` 中添加配置，然后选择合适的使用模式即可。

### Q: 如何自定义降级逻辑？
A: 在功能配置中提供 `downgradeHandler` 函数，实现自定义的降级处理逻辑。

### Q: 如何处理复杂的版本校验场景？
A: 可以组合使用多种模式，或者在 `downgradeHandler` 中实现复杂的业务逻辑。
