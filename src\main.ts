// 导入 Vue 2 相关模块
import Vue from 'vue';
import '@/plugins/fa-component-cus/index.js';
import '@unocss/reset/tailwind-compat.css';
import '@/style/style.css';
import App from '@/App.vue';
import router from '@/router';
import store from '@/store';
import faicomponent from '@fk/faicomponent';
import { message } from '@fk/faicomponent';
import modalPlugin from '@/plugins/modalPlugin/index';

import '@/style/faTheme/theme.less'; // 引入主题样式
import '@/style/faTheme/fix.less';
import '@/style/faTheme/faicomponent-reset.less';
import '@fk/fa-component-cus/packages/theme-chalk/src/common/specification/flex.less';
import '@fk/fa-component-cus/packages/theme-chalk/src/file-manager.less';
import '@fk/fa-component-cus/packages/theme-chalk/src/qr-upload.less';
import '@fk/fa-component-cus/packages/theme-chalk/src/designer-icon.less';
import 'element-ui/lib/theme-chalk/index.css';
import '@/style/elementTheme/element-reset.scss';
import 'virtual:uno.css';
import '@/assets/iconfont.js';
import Icon from '@/components/comm/Icon.vue';
import ScImg from '@/components/comm/ScImg.vue';
import directives from '@/directives';
import { initLogger } from '@/config/logger';
import { skeletonManager } from '@/utils/skeletonManager';
import moment from 'moment';
import 'moment/dist/locale/zh-cn';
moment.locale('zh-cn'); // 设置moment语言为中文，处理faicomponent日期组件语言问题

message.config({
  maxCount: 1,
});
Vue.use(faicomponent);
Vue.use(modalPlugin);
Vue.use(directives);
Vue.component('Icon', Icon);
Vue.component('ScImg', ScImg);

// 初始化日志系统
initLogger();

// 主应用挂载
new Vue({
  router,
  store,
  render: h => h(App),
}).$mount('#app');

// 初始化骨架屏管理器
skeletonManager.init(router);
