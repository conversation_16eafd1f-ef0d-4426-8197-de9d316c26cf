/** 花字字体 */
export interface Font {
  /** 字体名称 */
  name: string;
  /** 语言分类 */
  languageCategory: 'zh' | 'en';
  /** 收费分类 */
  pricingCategory: 'free' | 'paid';
  /** 字体分类，为空则没有分类 */
  styleCategory: string;
  /** 字体文件名, 唯一标识 */
  fileName: string;
  /** fontFamily*/
  fontFamily: string;
  /** 后端使用的字体名 */
  fontName: string;
}

/** 字体语言分类 */
export enum FontLanguage {
  /** 中文 */
  ZH = 'zh',
  /** 英文 */
  EN = 'en',
}

export interface FontPreset {
  id: number;
  /** 颜色 */
  color: string;
  /** 描边颜色 */
  strokeColor?: string;
  /** 描边宽度 */
  strokeWidth?: number;
  /** 阴影填充颜色 */
  shadowColor?: string;
  /** 阴影X轴偏移 */
  shadowX?: number;
  /** 阴影Y轴偏移 */
  shadowY?: number;
  /** 阴影描边颜色 */
  shadowStrokeColor?: string;
  /** 阴影描边宽度 */
  shadowStrokeWidth?: number;
  /** 背景颜色 */
  boxColor?: string;
}
