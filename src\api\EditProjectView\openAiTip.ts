/**
 * AI推荐相关API模块
 * 提供获取AI推荐内容和反馈AI推荐结果的接口
 */
import { POST_JSON } from '@/api/request';
import type { AiTipParams, AiTipResult } from './types/ai'; // New import

/**
 * 获取AI推荐内容
 * @param params - 请求参数，包含模板ID、变量名和上下文字段映射
 * @returns Promise，包含可能的错误和返回结果
 */
export function getOpenAiTip(params: AiTipParams) {
  return POST_JSON<AiTipResult>('/api/template/runWorkFlows', params);
}

export default {
  getOpenAiTip,
};
