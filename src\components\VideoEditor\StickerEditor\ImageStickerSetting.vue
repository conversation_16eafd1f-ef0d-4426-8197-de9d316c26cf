<template>
  <div
    class="custom-upload__image-item"
    :class="{
      'custom-upload__image-item--invalid': false,
    }"
  >
    <div
      v-if="stickerInfo.resId !== ''"
      class="custom-upload__image-wrapper"
      @click="previewMedia"
    >
      <ScImg
        :src="stickerInfo.resId || ''"
        :maxWidth="maxWidth || 100"
        fit="contain"
      >
      </ScImg>
    </div>
    <!-- 失效状态显示 -->
    <div v-else class="custom-upload__invalid-mask">
      <Icon
        type="sucaiyixiao"
        class="w-[24px] h-[24px] custom-upload__invalid-icon"
      />
      <div class="custom-upload__invalid-text">素材已失效</div>
      <div
        class="custom-upload__replace-button select-none"
        @click.stop="handleReplace()"
      >
        <Icon type="huanyihuan" class="w-[14px] h-[14px]" /><span
          >更换素材</span
        >
      </div>
    </div>
    <!-- 删除按钮 -->
    <div class="custom-upload__delete-button" @click.stop="handleDelete">
      <Icon type="shanshu_xiao" class="w-[10px] h-[10px]" />
    </div>
    <!-- 替换按钮 -->
    <div
      v-if="stickerInfo.resId !== ''"
      class="custom-upload__replace-button"
      @click.stop="handleReplace"
    >
      <Icon type="tihuan" class="w-[13px] h-[13px]" /><span class="">替换</span>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { currentEditorType } from '@/constants/project';
import { showImageViewer } from '@/components/comm/ImageViewer';
import ScImg from '@/components/comm/ScImg.vue';
import { showMaterialBasicUpload } from '@/components/MaterialBasicUpload';
import { MaterialUploadFile } from '@/components/MaterialBasicUpload/types';
import { getMaterialFullUrl } from '@/components/MaterialBasicUpload/utils/index';
import { FILE_TYPES } from '@/constants/fileType';
import { ImageSticker } from '@/types';
import Vue from 'vue';
import { getCleanScale } from '@/components/VideoEditor/StickerEditor/utils';

const props = defineProps<{
  /** 贴图信息 */
  stickerInfo: ImageSticker;
  /** 可选图片压缩宽度 */
  maxWidth?: number;
}>();

const emit = defineEmits<{
  (e: 'delete'): void;
}>();

function previewMedia() {
  if (props.stickerInfo.resId && props.stickerInfo.resType !== undefined) {
    // 预览贴图
    const imgList = [
      getMaterialFullUrl(props.stickerInfo.resId, FILE_TYPES.WEBP),
    ];
    showImageViewer({
      imgList,
    });
  }
}
function handleReplace() {
  showMaterialBasicUpload({
    title: '添加贴图',
    maxChosenFileCount: 1,
    isVideo: false,
    onConfirm: (files: MaterialUploadFile[]) => {
      if (files.length === 0) {
        return;
      }
      const file = files[0];
      const img = new Image();
      img.src = getMaterialFullUrl(file.data.resId, FILE_TYPES.WEBP, 'user');
      img.onload = () => {
        Vue.set(props.stickerInfo, 'resId', file.data.resId);
        Vue.set(props.stickerInfo, 'resType', file.data.type);
        if (currentEditorType == 'image') {
          // 图文编辑器-贴图宽/高需要清洗尺寸至固定宽高内
          const w = img.width * getCleanScale(img.width, img.height);
          const h = img.height * getCleanScale(img.width, img.height);
          Vue.set(props.stickerInfo, 'width', w);
          Vue.set(props.stickerInfo, 'height', h);
        }
      };
    },
  });
}
function handleDelete() {
  emit('delete');
}
</script>

<style lang="scss" scoped>
.custom-upload__image-item {
  @apply relative w-[88px] h-[88px] rounded-[8px] b-[#E8E8E8] border-[1px] border-solid;
  @apply cursor-pointer;

  &:hover .custom-upload__delete-button {
    @apply opacity-100;
  }
  &:hover .custom-upload__replace-button {
    @apply flex;
  }
}
.custom-upload__image-wrapper {
  @apply w-full h-full overflow-hidden rounded-[8px];
}

.custom-upload__image-wrapper :deep(.el-image) {
  @apply w-full h-full;
}

.custom-upload__image-wrapper :deep(.el-image img) {
  @apply transition-transform duration-300 ease-in-out;

  &:hover {
    @apply transform scale-[1.05];
  }
}
.custom-upload__replace-button {
  @apply absolute bottom-0 left-0 right-0 p-[4px_8px] h-[24px] z-10 rounded-b-[8px];
  @apply bg-[rgba(0,0,0,0.75)] text-white;
  @apply items-center justify-center cursor-pointer hidden;
  &:hover {
    @apply bg-[rgba(0,0,0,1)];
  }
}

.custom-upload__replace-button span {
  @apply ml-1 text-[12px] leading-[22px] text-left text-white;
}
// 删除按钮
.custom-upload__delete-button {
  @apply absolute top-[-5px] right-[-5px] w-[20px] h-[20px];
  @apply bg-[#f5222d] text-white rounded-full;
  @apply flex justify-center items-center z-[10] opacity-0;
  @apply transition-opacity duration-200 ease-in-out;
}
// 加载状态
.custom-upload__loading {
  @apply flex justify-center items-center w-full h-full;
}

// 失效状态蒙层
.custom-upload__invalid-mask {
  /* 布局相关 */
  @apply absolute top-0 left-0 flex flex-col justify-center items-center;
  /* 尺寸相关 */
  @apply w-full h-full;
  /* 外观相关 */
  @apply bg-white border border-solid border-[#fa3534] rounded-[8px];
  /* 文字相关 */
  @apply text-white;

  &:hover .custom-upload__replace-button {
    /* 布局相关 */
    @apply flex;
  }
}
.custom-upload__invalid-icon {
  /* 文字相关 */
  @apply text-[#ffd2d2];
}

.custom-upload__invalid-text {
  /* 尺寸相关 */
  @apply mt-[6px];
  /* 文字相关 */
  @apply font-normal text-[12px] text-center text-[#fa3534];
}
</style>
