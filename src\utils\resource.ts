import { FILE_EXTENSIONS } from '@/constants/fileType';
import { FileData, FolderData } from '@/types/Material';
import { INFO_KEYS } from '@/constants/material';
import { MaterialManageInfo } from '@/components/MaterialManager/types/index.ts';

/** 获取文件后缀名 */
export function getFileExtension(type: keyof typeof FILE_EXTENSIONS): string {
  // 判断属性是否存在
  if (!Object.prototype.hasOwnProperty.call(FILE_EXTENSIONS, type)) {
    throw new Error(`无该类型文件: ${type}`);
  }
  const fileExt = FILE_EXTENSIONS[type];
  return fileExt;
}

/**
 * 判断是否为文件夹
 * 判断条件：没有resId字段
 * @param data 文件数据
 * @returns 是否为文件夹
 */
export function isFolder(
  data: FileData | FolderData | MaterialManageInfo,
): boolean {
  return !data?.[INFO_KEYS.RES_ID];
}
