/**
 * @fileoverview 输出数据转换工具函数
 * @description 将前端业务数据结构转换为后端接口数据格式
 */

import type { EditProjectViewData } from '@/views/EditProjectView/types/index';
import type {
  UpdateProjectParams,
  UpdateProjectResFormItem,
  UpdateProjectSetting,
} from '@/api/EditProjectView/types/request';

/**
 * @description 将前端项目数据格式转换为API请求格式
 * @param data 前端项目数据
 * @param uniqueId 可选的唯一标识，用于幂等性控制
 * @returns 转换后的API请求数据
 */
export function transformProjectDataToEditProjectApiFormat(
  data: EditProjectViewData,
  uniqueId?: string,
): UpdateProjectParams {
  // 1. 将输入表单项转换为JSON字符串
  const inputFormObj: Record<string, string | number> = {};
  data.inputForm.forEach(item => {
    if (Array.isArray(item.value)) {
      inputFormObj[item.variable] = item.value.join(',');
    } else {
      inputFormObj[item.variable] = item.value ?? '';
    }
  });

  // 2. 转换资源表单项
  const resForm: UpdateProjectResFormItem[] = data.resForm.map(item => {
    const resIds = Array.isArray(item.value)
      ? item.value.map(file => ({
          uploadId: String(file.resId),
          uploadType: Number(file.resType),
          coverId: String(file.coverId ?? ''),
          coverType: Number(file.coverType ?? ''),
          duration: Number(file?.duration),
        }))
      : [];

    return {
      id: item.id,
      resIds,
      modifyMouth: item.openModifyMouth,
    };
  });

  // 3. 构建项目设置
  const setting: UpdateProjectSetting = {
    bgm: {
      open: data.bgMusic?.open || false,
      auto: data.bgMusic?.useAiRecommend || false,
      resIds: data.bgMusic?.resIds || [],
    },
    voice: {
      auto: data.voice?.useAiRecommend || false,
      voiceType: data.voice?.voiceId || '',
      extraType: data.voice?.extraType,
    },
  };

  const requestParams: UpdateProjectParams = {
    id: data.projectId!,
    templateId: data.templateId,
    inputForm: JSON.stringify(inputFormObj),
    resFormList: resForm,
    setting,
  };

  // 添加幂等性唯一标识
  if (uniqueId) {
    requestParams.uniqueIdForModify = uniqueId;
  }

  if (data.projectId) {
    delete requestParams.templateId;
  }

  // 4. 构建最终请求数据
  return requestParams;
}
