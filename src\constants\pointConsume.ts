import {
  PROJECT_TYPE_VIDEO,
  PROJECT_TYPE_IMAGE,
} from '@/views/EditProjectView/constants';

/**
 * 消耗点数类型常量
 * 参考后端 PointConsumeType 枚举定义
 * 用于 getConsumePoint API 接口的 typeList 参数
 */
export const POINT_CONSUME_TYPE = {
  /** AI生成视频 */
  VIDEO: 1,
  /** AI图文编辑 */
  GRAPHIC: 2,
  /** AI改嘴型 */
  MOUTH: 3,
} as const;

/**
 * 消耗点数类型
 */
export type PointConsumeType =
  (typeof POINT_CONSUME_TYPE)[keyof typeof POINT_CONSUME_TYPE];

/**
 * 根据项目类型和功能开关生成 typeList 参数
 * @param projectType 项目类型常量 (PROJECT_TYPE_VIDEO 或 PROJECT_TYPE_IMAGE)
 * @param hasAiMouthShape 是否开启AI改嘴型功能
 * @returns typeList 字符串，多个类型用逗号分隔
 */
export const generateTypeList = (
  projectType: number,
  hasAiMouthShape: boolean = false,
): string => {
  const types: number[] = [];

  // 根据项目类型添加基础类型
  if (projectType === PROJECT_TYPE_VIDEO) {
    types.push(POINT_CONSUME_TYPE.VIDEO);
  } else if (projectType === PROJECT_TYPE_IMAGE) {
    types.push(POINT_CONSUME_TYPE.GRAPHIC);
  }

  // 如果开启AI改嘴型功能，添加改嘴型类型（仅视频类型支持）
  if (hasAiMouthShape && projectType === PROJECT_TYPE_VIDEO) {
    types.push(POINT_CONSUME_TYPE.MOUTH);
  }

  return types.join(',');
};
