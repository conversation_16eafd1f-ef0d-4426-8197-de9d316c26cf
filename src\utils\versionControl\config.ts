import { VERSION, VERSION_NAME } from '@/constants/version';

/**
 * 版本功能配置接口
 */
export interface VersionFeatureConfig {
  /** 功能标识 */
  featureKey: string;
  /** 要求的最低版本 */
  requiredVersion: VERSION;
  /** 功能名称 */
  featureName: string;
  /** 自定义提示消息 */
  customMessage?: string;
  /** 是否支持自动降级 */
  supportDowngrade?: boolean;
  /** 降级处理函数 */
  downgradeHandler?: <T extends Record<string, unknown>>(data: T) => T;
}

/**
 * 功能版本配置映射
 * @description 集中管理所有需要版本校验的功能配置
 */
export const VERSION_FEATURE_CONFIG: Record<string, VersionFeatureConfig> = {
  /** AI改嘴型功能 */
  AI_MOUTH_SHAPE: {
    featureKey: 'AI_MOUTH_SHAPE',
    requiredVersion: VERSION.BASIC,
    featureName: 'AI改嘴型',
    supportDowngrade: true,
    downgradeHandler: <T extends Record<string, unknown>>(data: T): T => {
      // AI改嘴型功能降级处理：只需要处理 openModifyMouth
      if ('openModifyMouth' in data && data.openModifyMouth !== undefined) {
        (data as T & { openModifyMouth: boolean }).openModifyMouth = false;
      }
      return data;
    },
  },

  /** 文件上传大小限制 */
  FILE_UPLOAD_SIZE: {
    featureKey: 'FILE_UPLOAD_SIZE',
    requiredVersion: VERSION.BASIC,
    featureName: '大文件上传',
    supportDowngrade: false,
  },
};

/**
 * 获取功能的默认提示消息
 * @param config 功能配置
 * @returns 提示消息
 */
export function getDefaultFeatureMessage(config: VersionFeatureConfig): string {
  return (
    config.customMessage ||
    `使用${config.featureName}功能需要升级至${
      VERSION_NAME[config.requiredVersion]
    }`
  );
}

/**
 * 验证功能配置的有效性
 * @param featureKey 功能标识
 * @returns 是否有效
 */
export function isValidFeatureKey(featureKey: string): boolean {
  return featureKey in VERSION_FEATURE_CONFIG;
}
