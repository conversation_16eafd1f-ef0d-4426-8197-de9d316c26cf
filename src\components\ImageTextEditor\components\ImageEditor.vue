<template>
  <div class="pl-[40px] pr-[40px] pb-[24px]">
    <!-- 排版设置 -->
    <SettingItem label="排版">
      <div>
        <div class="settingBox relative mb-[16px]" ref="puzzleTypeSettingRef">
          <fa-select
            v-model="puzzleType"
            :getPopupContainer="getPuzzleTypeSettingEl"
          >
            <fa-select-option :value="1">{{ '单图' }}</fa-select-option>
            <fa-select-option :value="2">{{ '2图' }}</fa-select-option>
            <fa-select-option :value="3">{{ '4图' }}</fa-select-option>
            <fa-select-option :value="4">{{ '6图' }}</fa-select-option>
          </fa-select>
        </div>
        <ImageSidebar
          :images="imageIds"
          :selectedIndex="selectedImgIdx"
          direction="horizontal"
          :needScroll="true"
          :needScrollButtons="true"
          :needInvalidFileStatus="true"
          @select="selectedImgIdx = $event"
          @edit="editImg"
        />
      </div>
    </SettingItem>

    <!-- 图片比例设置 -->
    <SettingItem label="图片比例" v-if="puzzleType != 1">
      <div class="settingBox relative" ref="puzzleZoomSettingRef">
        <fa-select
          v-model="puzzleZoom"
          :getPopupContainer="getPuzzleZoomSettingEl"
        >
          <fa-select-option :value="1">{{ '3:4' }}</fa-select-option>
          <fa-select-option :value="2">{{ '9:16' }}</fa-select-option>
          <fa-select-option :value="3">{{ '1:1' }}</fa-select-option>
        </fa-select>
      </div>
    </SettingItem>

    <!-- 花字设置 -->
    <SettingItem label="花字">
      <div class="w-full">
        <TextStickerSetting
          v-for="(item, index) in textStickerList"
          :textInfo="item"
          :class="{
            'mb-[16px]': index !== (textStickerList?.length || 0) - 1,
          }"
          @applyToAll="handleApplyToAll(item)"
          @deleteText="handleDeleteText(item)"
        />
        <fa-button
          type="dashed"
          class="add-text_button-wrap"
          @click="handleAddText"
          v-show="(textStickerList?.length || 0) < 10"
        >
          <div class="add-text_button">
            <fa-icon type="fa-add" class="mr-[4px]" />
            <span>添加花字</span>
          </div>
        </fa-button>
      </div>
    </SettingItem>
    <!-- 贴图设置 -->
    <SettingItem label="贴图">
      <div class="flex gap-[16px]">
        <!-- 上传按钮 -->
        <div
          class="custom-upload__button"
          :class="{ 'custom-upload__button--disabled': false }"
          @click="handleAddImage"
          v-show="(imageStickerList?.length || 0) + uploadingImgNum < 3"
        >
          <fa-icon type="plus" class="custom-upload__button-icon" />
        </div>
        <!-- 图片 -->
        <ImageStickerSetting
          v-for="(sticker, index) in imageStickerList"
          :key="'imageStickerSetting-' + index"
          :stickerInfo="sticker"
          @delete="handleDeleteSticker(sticker)"
        />
      </div>
    </SettingItem>
  </div>
</template>

<script lang="ts" setup>
import Vue, { ref, computed } from 'vue';
import { puzzleImages, currentSetting } from '../hook/useWorkInfo';
import SettingItem from './SettingItem.vue';
import TextStickerSetting from '@/components/VideoEditor/StickerEditor/TextStickerSetting.vue';
import ImageStickerSetting from '@/components/VideoEditor/StickerEditor/ImageStickerSetting.vue';
import { showMaterialBasicUpload } from '@/components/MaterialBasicUpload';
import type { MaterialUploadFile } from '@/components/MaterialBasicUpload/types';
import { getMaterialFullUrl } from '@/components/MaterialBasicUpload/utils/index';
import { FILE_TYPES } from '@/constants/fileType';
import { ImageSticker, STYLE_TYPE, StickerSetting, TextSticker } from '@/types';
import ImageSidebar from './ImageSidebar.vue';
import { useFontInfo } from '@/components/VideoEditor/StickerEditor/hook/useFontInfo';
import { useFontPreset } from '@/components/VideoEditor/StickerEditor/hook/useFontPreset';
import {
  pxToSize,
  getCleanScale,
  fixStickerPos,
} from '@/components/VideoEditor/StickerEditor/utils';
import { eventBus, EVENT_NAMES } from '@/utils/eventBus';

/********辅助fa组件弹窗层挂载 star***********/
const puzzleTypeSettingRef = ref<HTMLElement>();
const getPuzzleTypeSettingEl = () => puzzleTypeSettingRef.value;
const puzzleZoomSettingRef = ref<HTMLElement>();
const getPuzzleZoomSettingEl = () => puzzleZoomSettingRef.value;
/********辅助fa组件弹窗层挂载 end***********/
const selectedImgIdx = ref(0);

// 贴图上传中数量（上传完成前的数量）
const uploadingImgNum = ref<number>(0);

// 提取图片ID列表
const imageIds = computed(() => {
  return puzzleImages.value.map(img => img.resId);
});

// 排版
const puzzleType = computed<number>({
  get: () => currentSetting.value?.puzzleType ?? 1,
  set: val => {
    if (currentSetting.value) {
      currentSetting.value.puzzleType = val;
    }
  },
});
// 图片比例
const puzzleZoom = computed({
  get: () => currentSetting.value?.ratio,
  set: val => {
    if (currentSetting.value) {
      currentSetting.value.ratio = val || 1;
    }
  },
});

/** 花字列表 */
const textStickerList = computed(() => {
  return currentSetting.value?.style?.filter(sticker => {
    return sticker.type === STYLE_TYPE.FONT;
  });
});

const editImg = (idx: number) => {
  showMaterialBasicUpload({
    title: '替换图片',
    maxChosenFileCount: 1,
    isVideo: false,
    onConfirm: (files: MaterialUploadFile[]) => {
      if (files.length === 0) {
        return;
      }
      const file = files[0];
      const img = new Image();
      img.src = getMaterialFullUrl(file.data.resId, FILE_TYPES.WEBP, 'user');
      img.onload = () => {
        if (currentSetting.value?.puzzleStyle) {
          currentSetting.value.puzzleStyle[idx].resId = file.data.resId;
          currentSetting.value.puzzleStyle[idx].type = file.data.type;
          // isNewCreate重置为true（load图片之后就会触发定位默认居中了）
          currentSetting.value.puzzleStyle[idx].scale = 1;
          // scale重置为默认值1，避免影响新图片的布局
          currentSetting.value.puzzleStyle[idx].isNewCreate = true;
        }
        const handleCurrentSpaceChange = () => {
          if (currentSetting.value?.style) {
            currentSetting.value.style.forEach(item => {
              // 替换图片之后，花字贴图位置保持不变，如果原位置对于新图片来说，超出预览窗的话，重置位置为预览窗最近的贴边状态
              const newStickerPos = fixStickerPos(
                item.x,
                item.y,
                item.type as STYLE_TYPE,
              );
              if (newStickerPos.x.isResetX) {
                item.x = newStickerPos.x.val;
              }
              if (newStickerPos.y.isResetY) {
                item.y = newStickerPos.y.val;
              }

              if (
                item.type == STYLE_TYPE.PIC &&
                item.width !== undefined &&
                item.height !== undefined
              ) {
                const scale = getCleanScale(item.width, item.height);
                item.width *= scale;
                item.height *= scale;
              }
            });
          }
          // 解绑
          eventBus.off(
            EVENT_NAMES.CURRENT_SPACE_CHANGE,
            handleCurrentSpaceChange,
          );
        };
        // 动态绑定监听，一定要在setCurrentSpace设置了新的原图尺寸之后，再getCleanScale或者pxToSize
        eventBus.on(EVENT_NAMES.CURRENT_SPACE_CHANGE, handleCurrentSpaceChange);
      };
    },
  });
};

const handleApplyToAll = (textInfo: TextSticker) => {
  currentSetting.value?.style?.forEach(sticker => {
    if (sticker.type === STYLE_TYPE.FONT) {
      const textSticker = sticker as TextSticker;
      Vue.set(textSticker, 'align', textInfo.align);
      Vue.set(textSticker, 'fontName', textInfo.fontName);
      Vue.set(textSticker, 'fileName', textInfo.fileName);
      Vue.set(textSticker, 'fontSize', textInfo.fontSize);
      Vue.set(textSticker, 'color', textInfo.color);
      Vue.set(textSticker, 'styleId', textInfo.styleId);
      Vue.set(textSticker, 'boxColor', textInfo.boxColor);
      Vue.set(textSticker, 'strokeColor', textInfo.strokeColor);
      Vue.set(textSticker, 'strokeWidth', textInfo.strokeWidth);
      Vue.set(textSticker, 'shadowColor', textInfo.shadowColor);
      Vue.set(textSticker, 'shadowX', textInfo.shadowX);
      Vue.set(textSticker, 'shadowY', textInfo.shadowY);
      Vue.set(textSticker, 'shadowStrokeColor', textInfo.shadowStrokeColor);
      Vue.set(textSticker, 'shadowStrokeWidth', textInfo.shadowStrokeWidth);
    }
  });
};

const handleDeleteText = (textInfo: TextSticker) => {
  const delIndex = currentSetting.value?.style?.findIndex(
    item => item === textInfo,
  );
  if (typeof delIndex === 'number' && delIndex !== -1) {
    currentSetting.value?.style?.splice(delIndex, 1);
  }
};

/********************贴图 star***********************/
/** 贴图列表 */
const imageStickerList = computed(() => {
  return (
    currentSetting.value?.style?.filter(sticker => {
      return sticker.type === STYLE_TYPE.PIC;
    }) || []
  );
});
/** 点击添加贴图 */
const handleAddImage = () => {
  showMaterialBasicUpload({
    title: '添加贴图',
    maxChosenFileCount: 1,
    isVideo: false,
    onConfirm: (files: MaterialUploadFile[]) => {
      if (files.length === 0) {
        return;
      }
      const file = files[0];
      const img = new Image();
      img.src = getMaterialFullUrl(file.data.resId, FILE_TYPES.WEBP, 'user');
      img.onload = () => {
        if (currentSetting.value && currentSetting.value.style) {
          // 这个贴图宽/高需要清洗尺寸至固定宽高内
          const w = img.width * getCleanScale(img.width, img.height);
          const h = img.height * getCleanScale(img.width, img.height);
          // 找到最后一个贴图的索引，插入到后面，以保证花字都放在贴图的后面（花字层级需要在贴图上面，而后端生成是根据索引顺序叠上去的，索引越往后层级越高）
          const lastImageIndex = currentSetting.value.style
            .map(item => item.type)
            .lastIndexOf(STYLE_TYPE.PIC);
          const newImageSticker: ImageSticker = {
            type: STYLE_TYPE.PIC,
            resId: file.data.resId,
            x: 0,
            y: 0,
            width: w,
            height: h,
            resType: file.data.type,
          };
          currentSetting.value.style.splice(
            lastImageIndex + 1,
            0,
            newImageSticker,
          );
        }
        uploadingImgNum.value--;
      };
      uploadingImgNum.value++;
    },
  });
};
/** 删除贴图 */
const handleDeleteSticker = (sticker: StickerSetting) => {
  const index = currentSetting.value?.style?.findIndex(
    item => item === sticker,
  );
  console.log('delete image sticker', index);
  if (index !== undefined && index !== -1) {
    currentSetting.value?.style?.splice(index, 1);
  }
};
/********************贴图 end***********************/
/** 字体列表 */
const { fontList } = useFontInfo();
/** 花字预设表 */
const { fontPresetList } = useFontPreset();
/** 点击添加花字 */
const handleAddText = () => {
  if (currentSetting.value?.style) {
    /** 默认预设 */
    const defaultPreset = fontPresetList.value[0];
    const newText: TextSticker = {
      type: STYLE_TYPE.FONT,
      text: '花字#1',
      x: 0,
      y: 0,
      color: '',
      fontSize: pxToSize(20),
      align: 'center',
      fileName: fontList.value[0]?.fileName || '',
      fontName: fontList.value[0]?.fontName || '',
      styleId: 1,
    };
    if (defaultPreset) {
      // 应用默认预设的属性到新文本，但保持原有的id
      Object.assign(newText, {
        ...defaultPreset,
        styleId: defaultPreset.id,
        type: newText.type,
        text: newText.text,
        x: newText.x,
        y: newText.y,
      });
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      delete (newText as any).id; // 删除id属性
    }
    console.log('add text sticker', defaultPreset);
    currentSetting.value.style.push(newText);
  }
};
</script>

<style lang="scss" scoped>
::v-deep {
  .settingBox {
    .fa-select-selection {
      @apply w-[160px] h-[40px] rounded-[8px];

      .fa-select-selection__rendered {
        @apply lh-40px;
      }
    }
  }

  .add-text_button-wrap {
    @apply w-full mt-[16px];
    &:hover {
      @apply border-[#3261FD] text-[#3261FD];
    }

    .add-text_button {
      @apply flex items-center justify-center;
    }
  }
}
// 上传按钮
.custom-upload__button {
  @apply flex flex-col justify-center items-center cursor-pointer;
  @apply w-[88px] h-[88px] rounded-[8px];
  @apply bg-white border border-solid border-[#d9d9d9] border-dashed;
  @apply transition-all duration-300 ease-in-out;

  &:hover:not(.custom-upload__button--disabled) {
    @apply border-[#3261FD];

    .custom-upload__button-icon {
      @apply text-[#3261FD];
    }
  }
}

.custom-upload__button--disabled {
  @apply cursor-not-allowed opacity-60 bg-[#f5f5f5];
}

.custom-upload__button-icon {
  @apply text-[#666] text-[20px];
}

.custom-upload__button-text {
  @apply mt-0 text-[#666] font-normal text-[14px];
}
</style>
